<?php require_once dirname(__DIR__, 2) . '/includes/header-modular.php'; ?>

<div class="main-content">
    <?php require_once dirname(__DIR__, 2) . '/includes/sidebar-modular.php'; ?>
    
    <div class="content">
        <div class="content-header">
            <h1>
                <i class="fas fa-truck"></i>
                供应商管理
            </h1>
            <div class="header-actions">
                <a href="?action=create" class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    添加供应商
                </a>
                <a href="javascript:void(0)" class="btn btn-success" onclick="alert('批量导入功能开发中'); return false;">
                    <i class="fas fa-upload"></i>
                    批量导入
                </a>
                <button type="button" class="btn btn-secondary" onclick="exportData()">
                    <i class="fas fa-download"></i>
                    导出
                </button>
                <button type="button" class="btn btn-secondary" onclick="refreshData()">
                    <i class="fas fa-sync-alt"></i>
                    刷新
                </button>
            </div>
        </div>

        <!-- 搜索筛选 -->
        <div class="search-bar" style="padding: 15px !important;">
            <form method="GET" class="search-bar-form" style="display: flex !important; flex-wrap: nowrap !important; gap: 10px !important; align-items: center !important; padding: 0 !important;">
                <div class="form-field text-input" style="display: flex !important; align-items: center !important; gap: 8px !important; flex: 0 0 auto !important; margin: 0 !important;">
                    <label style="font-size: 14px !important; font-weight: 500 !important; color: #374151 !important; white-space: nowrap !important; margin: 0 !important;">供应商信息</label>
                    <input type="text" class="form-control" name="search" value="<?= htmlspecialchars($search ?? '') ?>" placeholder="搜索供应商名称或联系人" style="height: 36px !important; width: 200px !important; padding: 6px 10px !important; border: 1px solid #d1d5db !important; border-radius: 6px !important; font-size: 14px !important; box-sizing: border-box !important;">
                </div>
                <div class="form-field select-input" style="display: flex !important; align-items: center !important; gap: 8px !important; flex: 0 0 auto !important; margin: 0 !important;">
                    <label style="font-size: 14px !important; font-weight: 500 !important; color: #374151 !important; white-space: nowrap !important; margin: 0 !important;">状态</label>
                    <select class="form-control" name="status" style="height: 36px !important; width: 120px !important; padding: 6px 10px !important; border: 1px solid #d1d5db !important; border-radius: 6px !important; font-size: 14px !important; box-sizing: border-box !important; background: white !important;">
                        <option value="">全部状态</option>
                        <option value="1" <?= ($status ?? '') === '1' ? 'selected' : '' ?>>启用</option>
                        <option value="0" <?= ($status ?? '') === '0' ? 'selected' : '' ?>>禁用</option>
                    </select>
                </div>
                <div class="form-field select-input" style="display: flex !important; align-items: center !important; gap: 8px !important; flex: 0 0 auto !important; margin: 0 !important;">
                    <label style="font-size: 14px !important; font-weight: 500 !important; color: #374151 !important; white-space: nowrap !important; margin: 0 !important;">城市</label>
                    <select class="form-control" name="city" style="height: 36px !important; width: 120px !important; padding: 6px 10px !important; border: 1px solid #d1d5db !important; border-radius: 6px !important; font-size: 14px !important; box-sizing: border-box !important; background: white !important;">
                        <option value="">全部城市</option>
                        <?php if (!empty($cities)): ?>
                            <?php foreach ($cities as $city): ?>
                            <option value="<?= htmlspecialchars($city) ?>" <?= ($selected_city ?? '') === $city ? 'selected' : '' ?>>
                                <?= htmlspecialchars($city) ?>
                            </option>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </select>
                </div>
                <button type="submit" class="btn btn-primary" style="flex: 0 0 auto !important; height: 36px !important; padding: 0 14px !important; white-space: nowrap !important; margin: 0 !important; border: none !important; border-radius: 6px !important; background: #3b82f6 !important; color: white !important; font-size: 14px !important; font-weight: 500 !important; cursor: pointer !important; display: inline-flex !important; align-items: center !important; gap: 6px !important; text-decoration: none !important;">
                    <i class="fas fa-search"></i>
                    搜索
                </button>
                <a href="index.php" class="btn btn-secondary" style="flex: 0 0 auto !important; height: 36px !important; padding: 0 14px !important; white-space: nowrap !important; margin: 0 !important; border: none !important; border-radius: 6px !important; background: #6b7280 !important; color: white !important; font-size: 14px !important; font-weight: 500 !important; cursor: pointer !important; display: inline-flex !important; align-items: center !important; gap: 6px !important; text-decoration: none !important;">
                    <i class="fas fa-refresh"></i>
                    重置
                </a>
            </form>
        </div>

        <!-- 供应商列表 -->
        <div class="table-container">
            <table class="table">
                <thead>
                    <tr>
                        <th>供应商信息</th>
                        <th>联系方式</th>
                        <th>地址信息</th>
                        <th>合作状态</th>
                        <th>信用等级</th>
                        <th>供应食材</th>
                        <th>状态</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($suppliers)): ?>
                        <?php foreach ($suppliers as $supplier): ?>
                            <tr>
                                <td>
                                    <div class="supplier-info">
                                        <div class="supplier-name"><?= htmlspecialchars($supplier['name']) ?></div>
                                        <?php if (!empty($supplier['company_code'])): ?>
                                            <div class="supplier-code">编号: <?= htmlspecialchars($supplier['company_code']) ?></div>
                                        <?php endif; ?>
                                        <?php if (!empty($supplier['description'])): ?>
                                            <div class="supplier-desc"><?= htmlspecialchars($supplier['description']) ?></div>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <div class="contact-info">
                                        <?php if (!empty($supplier['contact_person'])): ?>
                                            <div class="contact-person">
                                                <i class="fas fa-user"></i>
                                                <?= htmlspecialchars($supplier['contact_person']) ?>
                                            </div>
                                        <?php endif; ?>
                                        <?php if (!empty($supplier['phone'])): ?>
                                            <div class="contact-phone">
                                                <i class="fas fa-phone"></i>
                                                <?= htmlspecialchars($supplier['phone']) ?>
                                            </div>
                                        <?php endif; ?>
                                        <?php if (!empty($supplier['email'])): ?>
                                            <div class="contact-email">
                                                <i class="fas fa-envelope"></i>
                                                <?= htmlspecialchars($supplier['email']) ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <div class="address-info">
                                        <?php if (!empty($supplier['address'])): ?>
                                            <div class="address"><?= htmlspecialchars($supplier['address']) ?></div>
                                        <?php endif; ?>
                                        <?php if (!empty($supplier['city'])): ?>
                                            <div class="city"><?= htmlspecialchars($supplier['city']) ?></div>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <?php
                                    $cooperationStatus = $supplier['cooperation_status'] ?? 'active';
                                    $statusConfig = [
                                        'active' => ['class' => 'status-active', 'text' => '正常合作'],
                                        'trial' => ['class' => 'status-trial', 'text' => '试用期'],
                                        'suspended' => ['class' => 'status-suspended', 'text' => '暂停合作'],
                                        'terminated' => ['class' => 'status-terminated', 'text' => '终止合作']
                                    ];
                                    $config = $statusConfig[$cooperationStatus] ?? $statusConfig['active'];
                                    ?>
                                    <span class="cooperation-status <?= $config['class'] ?>"><?= $config['text'] ?></span>
                                </td>
                                <td>
                                    <?php 
                                    $creditLevel = $supplier['credit_level'] ?? 'A';
                                    $creditConfig = [
                                        'A' => ['class' => 'credit-a', 'text' => 'A级'],
                                        'B' => ['class' => 'credit-b', 'text' => 'B级'],
                                        'C' => ['class' => 'credit-c', 'text' => 'C级'],
                                        'D' => ['class' => 'credit-d', 'text' => 'D级']
                                    ];
                                    $creditConf = $creditConfig[$creditLevel] ?? $creditConfig['A'];
                                    ?>
                                    <span class="credit-level <?= $creditConf['class'] ?>"><?= $creditConf['text'] ?></span>
                                </td>
                                <td>
                                    <span class="ingredient-count"><?= $supplier['ingredient_count'] ?? 0 ?> 种</span>
                                </td>
                                <td>
                                    <label class="status-switch">
                                        <input type="checkbox" <?= $supplier['status'] == 1 ? 'checked' : '' ?> 
                                               onchange="toggleStatus(<?= $supplier['id'] ?>, this.checked)">
                                        <span class="switch-slider"></span>
                                    </label>
                                </td>
                                <td>
                                    <div class="time-info"><?= date('m-d H:i', strtotime($supplier['created_at'])) ?></div>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <a href="?action=view&id=<?= $supplier['id'] ?>"
                                           class="btn btn-sm btn-info" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                            <span>查看</span>
                                        </a>
                                        <a href="?action=edit&id=<?= $supplier['id'] ?>"
                                           class="btn btn-sm btn-warning" title="编辑供应商">
                                            <i class="fas fa-edit"></i>
                                            <span>编辑</span>
                                        </a>
                                        <a href="../purchase/index.php?action=create&supplier_id=<?= $supplier['id'] ?>"
                                           class="btn btn-sm btn-success" title="创建采购单">
                                            <i class="fas fa-shopping-cart"></i>
                                            <span>采购</span>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="9" class="text-center text-muted">
                                <i class="fas fa-inbox"></i>
                                暂无供应商数据
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<style>
/* 供应商管理模块特有样式 */

.supplier-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.supplier-name {
    font-weight: 600;
    color: #2d3748;
    font-size: 15px;
}

.supplier-code {
    font-size: 12px;
    color: #718096;
    background: #f7fafc;
    padding: 2px 6px;
    border-radius: 4px;
    display: inline-block;
    width: fit-content;
    font-family: 'Courier New', monospace;
}

.supplier-desc {
    font-size: 12px;
    color: #4a5568;
    background: #e2e8f0;
    padding: 2px 6px;
    border-radius: 4px;
    display: inline-block;
    width: fit-content;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.contact-person, .contact-phone, .contact-email {
    font-size: 13px;
    color: #4a5568;
    display: flex;
    align-items: center;
    gap: 6px;
}

.contact-person i { color: #667eea; }
.contact-phone i { color: #48bb78; }
.contact-email i { color: #ed8936; }

.address-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.address {
    font-size: 13px;
    color: #4a5568;
}

.city {
    font-size: 12px;
    color: #718096;
    background: #f7fafc;
    padding: 2px 6px;
    border-radius: 4px;
    display: inline-block;
    width: fit-content;
}

.cooperation-status {
    padding: 6px 10px;
    border-radius: 12px;
    font-size: 13px;
    font-weight: 500;
}

.status-active { background: #c6f6d5; color: #22543d; }
.status-trial { background: #fef5e7; color: #c05621; }
.status-suspended { background: #fed7aa; color: #c05621; }
.status-terminated { background: #fed7d7; color: #c53030; }

.credit-level {
    padding: 6px 10px;
    border-radius: 12px;
    font-size: 13px;
    font-weight: 500;
}

.credit-a { background: #f0fff4; color: #22543d; border: 1px solid #48bb78; }
.credit-b { background: #e6fffa; color: #234e52; border: 1px solid #38b2ac; }
.credit-c { background: #fff5d6; color: #975a16; border: 1px solid #f6e05e; }
.credit-d { background: #fed7d7; color: #c53030; border: 1px solid #f56565; }

.ingredient-count {
    background: #e2e8f0;
    color: #4a5568;
    padding: 4px 8px;
    border-radius: 8px;
    font-size: 13px;
    font-weight: 500;
}

.status-switch {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;
}

.status-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.switch-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}

.switch-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .switch-slider {
    background-color: #48bb78;
}

input:checked + .switch-slider:before {
    transform: translateX(20px);
}

.time-info {
    font-size: 14px;
    color: #4a5568;
}

.action-buttons {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

.table-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
}

@media (max-width: 768px) {
    .action-buttons {
        flex-direction: column;
    }
    
    .table-container {
        overflow-x: auto;
    }
    
    .table {
        min-width: 1200px;
    }
}
</style>

<script>
// 导出数据
function exportData() {
    alert('导出功能开发中...');
}

// 刷新数据
function refreshData() {
    location.reload();
}

// 切换状态
function toggleStatus(id, checked) {
    const status = checked ? 1 : 0;

    fetch('index.php?action=toggle_status', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `id=${id}&status=${status}`
    })
    .then(response => {
        // 检查响应是否为JSON
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
            throw new Error('服务器返回了非JSON响应，可能是页面错误');
        }
        return response.json();
    })
    .then(data => {
        if (!data.success) {
            alert('状态更新失败: ' + (data.message || '未知错误'));
            // 恢复原状态
            document.querySelector(`input[onchange*="${id}"]`).checked = !checked;
        } else {
            console.log('状态更新成功:', data.message);
        }
    })
    .catch(error => {
        console.error('状态更新错误:', error);
        alert('状态更新失败: ' + error.message);
        // 恢复原状态
        document.querySelector(`input[onchange*="${id}"]`).checked = !checked;
    });
}
</script>

<?php require_once dirname(__DIR__, 2) . '/includes/footer-modular.php'; ?>