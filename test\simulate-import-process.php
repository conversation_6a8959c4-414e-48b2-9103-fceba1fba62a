<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模拟导入过程调试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .code-block { background: #f4f4f4; padding: 10px; border-radius: 3px; font-family: monospace; margin: 5px 0; white-space: pre-wrap; }
        .step { background: #f9f9f9; padding: 10px; margin: 10px 0; border-left: 4px solid #007cba; }
        .process-log { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; max-height: 400px; overflow-y: auto; }
        .log-entry { margin: 5px 0; padding: 5px; border-left: 3px solid #007cba; }
        .log-success { border-left-color: #28a745; }
        .log-error { border-left-color: #dc3545; }
        .log-warning { border-left-color: #ffc107; }
        .btn { display: inline-block; padding: 8px 16px; background: #007cba; color: white; text-decoration: none; border-radius: 4px; margin: 5px; }
    </style>
</head>
<body>
    <h1>🔄 模拟导入过程调试</h1>
    
    <?php
    require_once '../includes/Database.php';
    require_once '../includes/ExcelReader.php';
    
    try {
        $db = Database::getInstance();
        $testFile = '../test.xlsx';
        
        echo "<div class='test-section'>";
        echo "<h2>🚀 完整模拟导入过程</h2>";
        
        if (!file_exists($testFile)) {
            echo "<p class='error'>❌ test.xlsx 文件不存在</p>";
            exit;
        }
        
        echo "<div class='process-log'>";
        echo "<div class='log-entry'>📁 开始读取文件: " . basename($testFile) . "</div>";
        
        // 步骤1: 读取Excel文件
        $reader = new ExcelReader();
        $data = $reader->read($testFile, 'test.xlsx');
        echo "<div class='log-entry log-success'>✅ Excel文件读取成功，总行数: " . count($data) . "</div>";
        
        if (empty($data)) {
            echo "<div class='log-entry log-error'>❌ Excel文件为空</div>";
            exit;
        }
        
        // 步骤2: 格式检测
        echo "<div class='log-entry'>🔍 开始格式检测...</div>";
        
        $importType = 'simple_list';
        if (count($data) >= 4) {
            $firstRow = $data[0] ?? [];
            $secondRow = $data[1] ?? [];
            
            echo "<div class='log-entry'>📊 第1行列数: " . count($firstRow) . ", 第2行列数: " . count($secondRow) . "</div>";
            
            if (count($firstRow) > 4 && count($secondRow) > 11) {
                $orderNumber = trim($firstRow[1] ?? '');
                echo "<div class='log-entry'>🔢 第1行第2列内容: '" . htmlspecialchars($orderNumber) . "'</div>";
                
                if (!empty($orderNumber) && (strlen($orderNumber) > 10 || preg_match('/^[A-Z0-9]+/', $orderNumber))) {
                    $importType = 'order_form';
                    echo "<div class='log-entry log-success'>✅ 检测为订货单格式</div>";
                } else {
                    echo "<div class='log-entry log-warning'>⚠️ 订单号不符合条件，使用简单列表格式</div>";
                }
            } else {
                echo "<div class='log-entry log-warning'>⚠️ 列数不足，使用简单列表格式</div>";
            }
        }
        
        echo "<div class='log-entry'>📋 最终格式: <strong>{$importType}</strong></div>";
        
        // 步骤3: 根据格式处理数据
        $successCount = 0;
        $errorCount = 0;
        $errors = [];
        
        if ($importType === 'order_form') {
            echo "<div class='log-entry'>📦 开始处理订货单格式...</div>";
            
            // 提取头部信息
            try {
                echo "<div class='log-entry'>📋 提取头部信息...</div>";
                
                $orderNumber = trim($data[0][1] ?? '');
                $orderDateStr = trim($data[0][4] ?? '');
                $contactPerson = trim($data[1][11] ?? '');
                $deliveryAddress = trim($data[2][1] ?? '');
                $contactPhone = trim($data[2][11] ?? '');
                $orderAmount = floatval($data[3][1] ?? 0);
                $actualAmount = floatval($data[3][5] ?? 0);
                $expectedDateStr = trim($data[3][11] ?? '');
                
                echo "<div class='log-entry'>📝 订单号: '{$orderNumber}'</div>";
                echo "<div class='log-entry'>📅 订单日期: '{$orderDateStr}'</div>";
                echo "<div class='log-entry'>👤 联系人: '{$contactPerson}'</div>";
                echo "<div class='log-entry'>📍 送货地址: '{$deliveryAddress}'</div>";
                echo "<div class='log-entry'>📞 联系电话: '{$contactPhone}'</div>";
                echo "<div class='log-entry'>💰 订单金额: {$orderAmount}</div>";
                echo "<div class='log-entry'>💵 实际金额: {$actualAmount}</div>";
                
                if (empty($orderNumber)) {
                    throw new Exception('订单号不能为空');
                }
                
                echo "<div class='log-entry log-success'>✅ 头部信息提取成功</div>";
                
                // 模拟创建订单（不实际插入数据库）
                echo "<div class='log-entry'>🏗️ 模拟创建采购订单...</div>";
                echo "<div class='log-entry log-success'>✅ 订单创建成功（模拟）</div>";
                
                // 处理明细数据
                echo "<div class='log-entry'>📦 开始处理明细数据（从第7行开始）...</div>";
                
                for ($i = 6; $i < count($data); $i++) {
                    $row = $data[$i];
                    $rowNumber = $i + 1;
                    
                    echo "<div class='log-entry'>🔍 处理第{$rowNumber}行...</div>";
                    
                    // 检查空行
                    $filteredRow = array_filter($row, function($cell) {
                        return !empty(trim($cell));
                    });
                    
                    if (empty($filteredRow)) {
                        echo "<div class='log-entry log-warning'>⚠️ 第{$rowNumber}行：完全空行，跳过</div>";
                        continue;
                    }
                    
                    // 提取明细字段
                    $itemCode = trim($row[0] ?? '');
                    $itemName = trim($row[1] ?? '');
                    $unitPrice = floatval($row[7] ?? 0);
                    $quantity = floatval($row[8] ?? 0);
                    $totalPrice = floatval($row[9] ?? 0);
                    $receivedQuantity = floatval($row[11] ?? 0);
                    $notes = trim($row[16] ?? '');
                    
                    echo "<div class='log-entry'>📝 商品编码: '{$itemCode}'</div>";
                    echo "<div class='log-entry'>📝 商品名称: '{$itemName}'</div>";
                    echo "<div class='log-entry'>💰 单价: {$unitPrice}</div>";
                    echo "<div class='log-entry'>📊 数量: {$quantity}</div>";
                    echo "<div class='log-entry'>💵 小计: {$totalPrice}</div>";
                    
                    // 验证必填字段
                    try {
                        if (empty($itemCode)) {
                            throw new Exception('商品编码不能为空');
                        }
                        
                        if ($quantity <= 0) {
                            throw new Exception('数量必须大于0');
                        }
                        
                        if ($unitPrice < 0) {
                            throw new Exception('单价不能为负数');
                        }
                        
                        // 模拟查找食材
                        echo "<div class='log-entry'>🔍 查找食材（编码: {$itemCode}）...</div>";
                        
                        // 检查是否存在
                        $ingredient = $db->fetchOne("SELECT id FROM ingredients WHERE code = ?", [$itemCode]);
                        if ($ingredient) {
                            echo "<div class='log-entry log-success'>✅ 找到现有食材 ID: {$ingredient['id']}</div>";
                        } else {
                            echo "<div class='log-entry log-warning'>⚠️ 食材不存在，将创建新食材</div>";
                        }
                        
                        echo "<div class='log-entry log-success'>✅ 第{$rowNumber}行验证通过</div>";
                        $successCount++;
                        
                    } catch (Exception $e) {
                        $errorCount++;
                        $errorMsg = "第{$rowNumber}行: " . $e->getMessage();
                        $errors[] = $errorMsg;
                        echo "<div class='log-entry log-error'>❌ {$errorMsg}</div>";
                    }
                }
                
            } catch (Exception $e) {
                echo "<div class='log-entry log-error'>❌ 头部信息处理失败: " . $e->getMessage() . "</div>";
            }
            
        } else {
            echo "<div class='log-entry'>📝 开始处理简单列表格式...</div>";
            echo "<div class='log-entry log-warning'>⚠️ 但您要求使用订货单格式，这可能是问题所在</div>";
        }
        
        echo "<div class='log-entry'>📊 处理完成</div>";
        echo "<div class='log-entry'>✅ 成功处理: <strong>{$successCount}</strong> 条明细</div>";
        echo "<div class='log-entry'>❌ 失败: <strong>{$errorCount}</strong> 条明细</div>";
        
        if (!empty($errors)) {
            echo "<div class='log-entry log-error'>❌ 错误详情:</div>";
            foreach ($errors as $error) {
                echo "<div class='log-entry log-error'>  - {$error}</div>";
            }
        }
        
        echo "</div>";
        echo "</div>";
        
        // 总结和建议
        echo "<div class='test-section'>";
        echo "<h2>📊 诊断结果</h2>";
        
        echo "<div class='step'>";
        if ($importType !== 'order_form') {
            echo "<h4>❌ 主要问题：格式检测失败</h4>";
            echo "<p class='error'>您的Excel文件没有被识别为订货单格式</p>";
            echo "<p class='info'>解决方案：</p>";
            echo "<ol>";
            echo "<li>确保第1行第2列有订单号（长度>10或以字母数字开头）</li>";
            echo "<li>确保至少有4行数据，第1行至少5列，第2行至少12列</li>";
            echo "<li>使用标准模板：<a href='../modules/purchase/download_template_based_on_test.php' class='btn'>下载模板</a></li>";
            echo "</ol>";
            
        } elseif ($successCount === 0) {
            echo "<h4>❌ 主要问题：没有有效的明细数据</h4>";
            echo "<p class='error'>虽然格式检测通过，但所有明细行都无效</p>";
            echo "<p class='info'>解决方案：</p>";
            echo "<ol>";
            echo "<li>从第7行开始填写明细数据</li>";
            echo "<li>确保商品编码（第1列）不为空</li>";
            echo "<li>确保数量（第9列）大于0</li>";
            echo "<li>确保单价（第8列）不为负数</li>";
            echo "</ol>";
            
        } else {
            echo "<h4>✅ 格式和数据都正常</h4>";
            echo "<p class='success'>应该能够成功导入 {$successCount} 条明细</p>";
            if ($errorCount > 0) {
                echo "<p class='warning'>有 {$errorCount} 条明细存在问题，请检查错误详情</p>";
            }
        }
        echo "</div>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ 模拟过程出错: " . $e->getMessage() . "</p>";
    }
    ?>
    
    <div class="test-section">
        <h2>🛠️ 下一步操作</h2>
        <div class="step">
            <p><strong>基于上面的诊断结果：</strong></p>
            <ol>
                <li><strong>修正问题</strong>：根据诊断结果修正您的Excel文件</li>
                <li><strong>重新测试</strong>：<a href="../modules/purchase/index.php?action=import" class="btn">重新导入</a></li>
                <li><strong>如果还有问题</strong>：<a href="../test/debug-order-form-import.php" class="btn">运行详细调试</a></li>
            </ol>
        </div>
    </div>
    
    <p><a href="../modules/purchase/index.php">返回采购管理</a></p>
</body>
</html>
