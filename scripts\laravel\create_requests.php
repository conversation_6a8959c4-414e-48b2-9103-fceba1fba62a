<?php

/**
 * 学校食堂食材出入库管理系统 - 请求验证类创建脚本
 * 根据开发文档自动生成Laravel请求验证类文件
 */

// 食材请求验证类
$ingredientRequest = '<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class IngredientRequest extends FormRequest
{
    /**
     * 确定用户是否有权限进行此请求
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * 获取适用于请求的验证规则
     */
    public function rules(): array
    {
        $ingredientId = $this->route(\'ingredient\') ? $this->route(\'ingredient\')->id : null;
        
        return [
            \'name\' => [
                \'required\',
                \'string\',
                \'max:100\',
                Rule::unique(\'ingredients\')->ignore($ingredientId)
            ],
            \'category_id\' => \'required|integer|exists:ingredient_categories,id\',
            \'unit\' => \'required|string|max:20\',
            \'shelf_life_days\' => \'required|integer|min:1|max:3650\',
            \'min_stock\' => \'required|numeric|min:0|max:999999.99\',
            \'image_path\' => \'nullable|string|max:255\',
            \'description\' => \'nullable|string|max:1000\',
            \'status\' => \'boolean\'
        ];
    }

    /**
     * 获取验证错误的自定义属性名称
     */
    public function attributes(): array
    {
        return [
            \'name\' => \'食材名称\',
            \'category_id\' => \'食材分类\',
            \'unit\' => \'计量单位\',
            \'shelf_life_days\' => \'保质期天数\',
            \'min_stock\' => \'最低库存预警值\',
            \'image_path\' => \'食材图片\',
            \'description\' => \'食材描述\',
            \'status\' => \'状态\'
        ];
    }

    /**
     * 获取验证错误的自定义消息
     */
    public function messages(): array
    {
        return [
            \'name.required\' => \'食材名称不能为空\',
            \'name.unique\' => \'食材名称已存在\',
            \'name.max\' => \'食材名称不能超过100个字符\',
            \'category_id.required\' => \'请选择食材分类\',
            \'category_id.exists\' => \'选择的食材分类不存在\',
            \'unit.required\' => \'计量单位不能为空\',
            \'unit.max\' => \'计量单位不能超过20个字符\',
            \'shelf_life_days.required\' => \'保质期天数不能为空\',
            \'shelf_life_days.min\' => \'保质期天数至少为1天\',
            \'shelf_life_days.max\' => \'保质期天数不能超过3650天\',
            \'min_stock.required\' => \'最低库存预警值不能为空\',
            \'min_stock.min\' => \'最低库存预警值不能小于0\',
            \'min_stock.max\' => \'最低库存预警值不能超过999999.99\',
            \'description.max\' => \'食材描述不能超过1000个字符\'
        ];
    }

    /**
     * 配置验证器实例
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // 自定义验证逻辑
            if ($this->has(\'shelf_life_days\') && $this->has(\'category_id\')) {
                $categoryId = $this->input(\'category_id\');
                $shelfLifeDays = $this->input(\'shelf_life_days\');
                
                // 根据分类验证保质期合理性
                $this->validateShelfLifeByCategory($validator, $categoryId, $shelfLifeDays);
            }
        });
    }

    /**
     * 根据分类验证保质期合理性
     */
    protected function validateShelfLifeByCategory($validator, int $categoryId, int $shelfLifeDays): void
    {
        $categoryLimits = [
            1 => [\'min\' => 1, \'max\' => 30, \'name\' => \'蔬菜类\'],    // 蔬菜类
            2 => [\'min\' => 1, \'max\' => 7, \'name\' => \'肉类\'],      // 肉类
            3 => [\'min\' => 1, \'max\' => 3, \'name\' => \'水产类\'],    // 水产类
            4 => [\'min\' => 30, \'max\' => 365, \'name\' => \'粮食类\'], // 粮食类
            5 => [\'min\' => 30, \'max\' => 730, \'name\' => \'调料类\']  // 调料类
        ];
        
        if (isset($categoryLimits[$categoryId])) {
            $limit = $categoryLimits[$categoryId];
            
            if ($shelfLifeDays < $limit[\'min\'] || $shelfLifeDays > $limit[\'max\']) {
                $validator->errors()->add(
                    \'shelf_life_days\',
                    "{$limit[\'name\']}的保质期应在{$limit[\'min\']}-{$limit[\'max\']}天之间"
                );
            }
        }
    }
}';

// 入库请求验证类
$inboundRequest = '<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Carbon\Carbon;

class InboundRequest extends FormRequest
{
    /**
     * 确定用户是否有权限进行此请求
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * 获取适用于请求的验证规则
     */
    public function rules(): array
    {
        return [
            \'ingredient_id\' => \'required|integer|exists:ingredients,id\',
            \'supplier_id\' => \'required|integer|exists:suppliers,id\',
            \'quantity\' => \'required|numeric|min:0.01|max:999999.99\',
            \'unit_price\' => \'required|numeric|min:0.01|max:999999.99\',
            \'production_date\' => \'nullable|date|before_or_equal:today\',
            \'expired_at\' => \'nullable|date|after:production_date\',
            \'weight_images\' => \'nullable|array|max:3\',
            \'weight_images.*\' => \'image|mimes:jpeg,png,jpg,webp|max:2048\',
            \'purchase_invoice\' => \'required|file|mimes:pdf,jpeg,png,jpg|max:5120\',
            \'quality_check\' => \'boolean\',
            \'notes\' => \'nullable|string|max:1000\'
        ];
    }

    /**
     * 获取验证错误的自定义属性名称
     */
    public function attributes(): array
    {
        return [
            \'ingredient_id\' => \'食材\',
            \'supplier_id\' => \'供应商\',
            \'quantity\' => \'入库数量\',
            \'unit_price\' => \'单价\',
            \'production_date\' => \'生产日期\',
            \'expired_at\' => \'过期日期\',
            \'weight_images\' => \'称重图片\',
            \'purchase_invoice\' => \'采购单据\',
            \'quality_check\' => \'质检状态\',
            \'notes\' => \'备注\'
        ];
    }

    /**
     * 获取验证错误的自定义消息
     */
    public function messages(): array
    {
        return [
            \'ingredient_id.required\' => \'请选择食材\',
            \'ingredient_id.exists\' => \'选择的食材不存在\',
            \'supplier_id.required\' => \'请选择供应商\',
            \'supplier_id.exists\' => \'选择的供应商不存在\',
            \'quantity.required\' => \'入库数量不能为空\',
            \'quantity.min\' => \'入库数量必须大于0\',
            \'quantity.max\' => \'入库数量不能超过999999.99\',
            \'unit_price.required\' => \'单价不能为空\',
            \'unit_price.min\' => \'单价必须大于0\',
            \'unit_price.max\' => \'单价不能超过999999.99\',
            \'production_date.date\' => \'生产日期格式不正确\',
            \'production_date.before_or_equal\' => \'生产日期不能晚于今天\',
            \'expired_at.date\' => \'过期日期格式不正确\',
            \'expired_at.after\' => \'过期日期必须晚于生产日期\',
            \'weight_images.max\' => \'称重图片最多上传3张\',
            \'weight_images.*.image\' => \'称重图片必须是图片文件\',
            \'weight_images.*.mimes\' => \'称重图片格式必须是jpeg、png、jpg或webp\',
            \'weight_images.*.max\' => \'称重图片大小不能超过2MB\',
            \'purchase_invoice.required\' => \'采购单据不能为空\',
            \'purchase_invoice.file\' => \'采购单据必须是文件\',
            \'purchase_invoice.mimes\' => \'采购单据格式必须是pdf、jpeg、png或jpg\',
            \'purchase_invoice.max\' => \'采购单据大小不能超过5MB\',
            \'notes.max\' => \'备注不能超过1000个字符\'
        ];
    }

    /**
     * 配置验证器实例
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // 验证供应商状态
            if ($this->has(\'supplier_id\')) {
                $this->validateSupplierStatus($validator);
            }
            
            // 验证食材状态
            if ($this->has(\'ingredient_id\')) {
                $this->validateIngredientStatus($validator);
            }
            
            // 验证过期日期合理性
            if ($this->has(\'ingredient_id\') && $this->has(\'production_date\')) {
                $this->validateExpiredDate($validator);
            }
        });
    }

    /**
     * 验证供应商状态
     */
    protected function validateSupplierStatus($validator): void
    {
        $supplier = \App\Models\Supplier::find($this->input(\'supplier_id\'));
        
        if ($supplier && $supplier->status != 1) {
            $validator->errors()->add(\'supplier_id\', \'选择的供应商已被禁用\');
        }
        
        if ($supplier && $supplier->rating < 3) {
            $validator->errors()->add(\'supplier_id\', \'选择的供应商评级过低，不建议采购\');
        }
    }

    /**
     * 验证食材状态
     */
    protected function validateIngredientStatus($validator): void
    {
        $ingredient = \App\Models\Ingredient::find($this->input(\'ingredient_id\'));
        
        if ($ingredient && $ingredient->status != 1) {
            $validator->errors()->add(\'ingredient_id\', \'选择的食材已被停用\');
        }
    }

    /**
     * 验证过期日期合理性
     */
    protected function validateExpiredDate($validator): void
    {
        $ingredient = \App\Models\Ingredient::find($this->input(\'ingredient_id\'));
        $productionDate = $this->input(\'production_date\');
        
        if ($ingredient && $productionDate) {
            $expectedExpiredDate = Carbon::parse($productionDate)
                ->addDays($ingredient->shelf_life_days);
            
            $inputExpiredDate = $this->input(\'expired_at\');
            
            if ($inputExpiredDate && Carbon::parse($inputExpiredDate)->ne($expectedExpiredDate)) {
                $validator->errors()->add(
                    \'expired_at\',
                    "根据该食材的保质期({$ingredient->shelf_life_days}天)，过期日期应为：" . 
                    $expectedExpiredDate->format(\'Y-m-d\')
                );
            }
        }
    }

    /**
     * 准备验证数据
     */
    protected function prepareForValidation(): void
    {
        // 如果没有提供过期日期，自动计算
        if ($this->has(\'ingredient_id\') && $this->has(\'production_date\') && !$this->has(\'expired_at\')) {
            $ingredient = \App\Models\Ingredient::find($this->input(\'ingredient_id\'));
            
            if ($ingredient) {
                $expiredAt = Carbon::parse($this->input(\'production_date\'))
                    ->addDays($ingredient->shelf_life_days)
                    ->format(\'Y-m-d\');
                
                $this->merge([\'expired_at\' => $expiredAt]);
            }
        }
    }
}';

// 创建文件
if (!is_dir('app/Http/Requests')) {
    mkdir('app/Http/Requests', 0755, true);
}

file_put_contents('app/Http/Requests/IngredientRequest.php', $ingredientRequest);
file_put_contents('app/Http/Requests/InboundRequest.php', $inboundRequest);

echo "✅ 请求验证类文件创建完成！\n";
echo "已创建以下请求验证类：\n";
echo "- IngredientRequest (食材请求验证)\n";
echo "- InboundRequest (入库请求验证)\n";
echo "\n继续创建其他请求验证类...\n";
