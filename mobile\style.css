/* 移动端全局样式 - 优化版 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* 主色调 */
    --primary-color: #667eea;
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-color: #764ba2;

    /* 功能色彩 */
    --success-color: #56ab2f;
    --success-gradient: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
    --warning-color: #f093fb;
    --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --danger-color: #ff4757;
    --danger-gradient: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    --info-color: #4facfe;
    --info-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);

    /* 中性色 */
    --text-primary: #333;
    --text-secondary: #666;
    --text-muted: #999;
    --border-color: #e9ecef;
    --background-light: rgba(255, 255, 255, 0.95);
    --background-glass: rgba(255, 255, 255, 0.1);

    /* 间距 */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;

    /* 圆角 */
    --radius-sm: 8px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 20px;

    /* 阴影 */
    --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 16px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 12px 40px rgba(0, 0, 0, 0.15);

    /* 动画 */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    background: var(--primary-gradient);
    background-attachment: fixed;
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
}

/* 顶部导航 - 优化版 */
.mobile-header {
    background: var(--background-light);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    box-shadow: var(--shadow-md);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    transition: all var(--transition-normal);
}

.mobile-header.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: var(--shadow-lg);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md) var(--spacing-lg);
    max-width: 100%;
    position: relative;
}

.logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 18px;
    font-weight: 700;
    color: var(--primary-color);
    transition: all var(--transition-normal);
}

.logo i {
    font-size: 24px;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.notification-btn, .profile-btn {
    position: relative;
    background: none;
    border: none;
    font-size: 20px;
    color: var(--primary-color);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: 50%;
    transition: all var(--transition-normal);
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.notification-btn:hover, .profile-btn:hover,
.notification-btn:active, .profile-btn:active {
    background: rgba(102, 126, 234, 0.1);
    transform: scale(1.05);
}

.notification-btn:active, .profile-btn:active {
    transform: scale(0.95);
}

.badge {
    position: absolute;
    top: 2px;
    right: 2px;
    background: var(--danger-color);
    color: white;
    font-size: 10px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 16px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(255, 71, 87, 0.3);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* 主要内容 - 优化版 */
.mobile-main {
    padding-top: 10px;
    padding-bottom: 10px;
    min-height: 100vh;
    position: relative;
}

/* 欢迎区域 - 优化版 */
.welcome-section {
    background: var(--background-glass);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    margin: var(--spacing-lg);
    padding: var(--spacing-xl) var(--spacing-lg);
    border-radius: var(--radius-xl);
    border: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: white;
    position: relative;
    overflow: hidden;
    transition: all var(--transition-normal);
}

.welcome-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.welcome-section:active {
    transform: scale(0.98);
}

.welcome-content {
    flex: 1;
    z-index: 1;
}

.welcome-content h1 {
    font-size: 26px;
    font-weight: 700;
    margin-bottom: var(--spacing-xs);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.welcome-content p {
    font-size: 14px;
    opacity: 0.9;
    font-weight: 500;
}

.welcome-illustration {
    font-size: 48px;
    opacity: 0.8;
    z-index: 1;
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* 统计卡片 - 优化版 */
.stats-section {
    padding: 0 var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
}

.stat-card {
    background: var(--background-light);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    box-shadow: var(--shadow-lg);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    cursor: pointer;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.stat-card:hover::before {
    opacity: 1;
}

.stat-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-xl);
}

.stat-card:active {
    transform: translateY(-4px) scale(0.98);
}

.stat-icon {
    width: 56px;
    height: 56px;
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 22px;
    color: white;
    position: relative;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.stat-card.primary .stat-icon { background: var(--primary-gradient); }
.stat-card.success .stat-icon { background: var(--success-gradient); }
.stat-card.warning .stat-icon { background: var(--warning-gradient); }
.stat-card.danger .stat-icon { background: var(--danger-gradient); }

.stat-content {
    flex: 1;
    min-width: 0;
}

.stat-number {
    font-size: 28px;
    font-weight: 800;
    color: var(--text-primary);
    line-height: 1;
    margin-bottom: var(--spacing-xs);
}

.stat-label {
    font-size: 12px;
    color: var(--text-secondary);
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 快捷操作 - 优化版 */
.quick-actions {
    padding: 0 var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.quick-actions h2 {
    color: white;
    font-size: 22px;
    font-weight: 700;
    margin-bottom: var(--spacing-lg);
    padding-left: var(--spacing-xs);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.actions-grid {
    display: grid;
    gap: var(--spacing-md);
}

.action-card {
    background: var(--background-light);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    text-decoration: none;
    color: inherit;
    box-shadow: var(--shadow-lg);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.action-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.action-card:hover::before {
    opacity: 1;
}

.action-card:hover {
    transform: translateY(-6px) scale(1.02);
    box-shadow: var(--shadow-xl);
    text-decoration: none;
    color: inherit;
}

.action-card:active {
    transform: translateY(-3px) scale(0.98);
}

.action-icon {
    width: 64px;
    height: 64px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 26px;
    color: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    position: relative;
}

.action-card.primary .action-icon { background: var(--primary-gradient); }
.action-card.success .action-icon { background: var(--success-gradient); }
.action-card.info .action-icon { background: var(--info-gradient); }
.action-card.warning .action-icon { background: var(--warning-gradient); }

.action-content {
    flex: 1;
    min-width: 0;
}

.action-content h3 {
    font-size: 17px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
    line-height: 1.3;
}

.action-content p {
    font-size: 13px;
    color: var(--text-secondary);
    margin: 0;
    line-height: 1.4;
    opacity: 0.8;
}

.action-arrow {
    color: var(--text-muted);
    font-size: 18px;
    transition: all var(--transition-normal);
}

.action-card:hover .action-arrow {
    color: var(--primary-color);
    transform: translateX(4px);
}

/* 最近活动 */
.recent-activity {
    padding: 0 20px;
    margin-bottom: 30px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.section-header h2 {
    color: white;
    font-size: 20px;
    font-weight: 700;
}

.view-all {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    text-decoration: none;
}

.view-all:hover {
    color: white;
    text-decoration: underline;
}

.activity-list {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: 16px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.activity-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 2px;
}

.activity-description {
    font-size: 13px;
    color: #666;
    margin-bottom: 4px;
}

.activity-meta {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #999;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #999;
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 15px;
    opacity: 0.5;
}

.empty-state p {
    font-size: 14px;
    margin: 0;
}

/* 底部导航 - 优化版 */
.bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: var(--background-light);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    display: flex;
    justify-content: space-around;
    padding: var(--spacing-sm) var(--spacing-xs) calc(var(--spacing-lg) + env(safe-area-inset-bottom)) var(--spacing-xs);
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    z-index: 1000;
}

.nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    color: var(--text-muted);
    font-size: 10px;
    font-weight: 500;
    padding: var(--spacing-xs) var(--spacing-xs);
    border-radius: var(--radius-md);
    transition: all var(--transition-normal);
    min-width: 48px;
    max-width: 56px;
    position: relative;
    overflow: hidden;
    flex: 1;
    text-align: center;
}

.nav-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--primary-gradient);
    transition: all var(--transition-normal);
    transform: translateX(-50%);
}

.nav-item.active::before {
    width: 24px;
}

.nav-item i {
    font-size: 18px;
    margin-bottom: 2px;
    transition: all var(--transition-normal);
}

.nav-item span {
    font-size: 10px;
    line-height: 1.2;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}

.nav-item.active {
    color: var(--primary-color);
    background: rgba(102, 126, 234, 0.08);
    transform: translateY(-2px);
}

.nav-item.active i {
    transform: scale(1.1);
}

.nav-item:hover {
    color: var(--primary-color);
    text-decoration: none;
    background: rgba(102, 126, 234, 0.05);
}

.nav-item:active {
    transform: translateY(0) scale(0.95);
}

/* 底部间距 */
.bottom-spacer {
    height: 20px;
}

/* 响应式设计 - 优化版 */
@media (max-width: 480px) {
    :root {
        --spacing-lg: 16px;
        --spacing-xl: 24px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .welcome-section {
        margin: var(--spacing-md);
        padding: var(--spacing-lg);
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-md);
    }

    .welcome-illustration {
        font-size: 36px;
    }

    .quick-actions, .recent-activity, .stats-section {
        padding: 0 var(--spacing-md);
    }

    .action-icon {
        width: 56px;
        height: 56px;
        font-size: 24px;
    }

    .stat-icon {
        width: 48px;
        height: 48px;
        font-size: 20px;
    }

    .stat-number {
        font-size: 24px;
    }

    .header-content {
        padding: var(--spacing-md);
    }
}

@media (max-width: 375px) {
    .welcome-content h1 {
        font-size: 22px;
    }

    .action-content h3 {
        font-size: 15px;
    }

    .nav-item {
        font-size: 9px;
        min-width: 40px;
        max-width: 48px;
        padding: var(--spacing-xs) 2px;
    }

    .nav-item i {
        font-size: 16px;
        margin-bottom: 1px;
    }

    .nav-item span {
        font-size: 9px;
        line-height: 1.1;
    }

    .bottom-nav {
        padding: 6px 2px calc(var(--spacing-md) + env(safe-area-inset-bottom)) 2px;
    }
}

/* 超小屏幕优化 (320px及以下) */
@media (max-width: 320px) {
    .nav-item {
        font-size: 8px;
        min-width: 36px;
        max-width: 44px;
        padding: 4px 1px;
    }

    .nav-item i {
        font-size: 14px;
        margin-bottom: 1px;
    }

    .nav-item span {
        font-size: 8px;
        line-height: 1;
    }

    .bottom-nav {
        padding: 4px 1px calc(var(--spacing-sm) + env(safe-area-inset-bottom)) 1px;
    }
}

/* 高分辨率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .logo i, .stat-icon, .action-icon {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
}

/* 横屏适配 */
@media (orientation: landscape) and (max-height: 500px) {
    .welcome-section {
        padding: var(--spacing-md) var(--spacing-lg);
    }

    .welcome-content h1 {
        font-size: 20px;
    }

    .welcome-illustration {
        font-size: 32px;
    }

    .mobile-main {
        padding-top: 70px;
    }
}

/* 动画效果 - 优化版 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(40px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.welcome-section {
    animation: fadeInUp 0.8s ease-out;
}

.stat-card {
    animation: fadeInUp 0.6s ease-out;
    animation-fill-mode: both;
}

.action-card {
    animation: slideInLeft 0.6s ease-out;
    animation-fill-mode: both;
}

.activity-list {
    animation: slideInRight 0.6s ease-out;
    animation-fill-mode: both;
}

.stat-card:nth-child(1) { animation-delay: 0.1s; }
.stat-card:nth-child(2) { animation-delay: 0.2s; }
.stat-card:nth-child(3) { animation-delay: 0.3s; }
.stat-card:nth-child(4) { animation-delay: 0.4s; }

.action-card:nth-child(1) { animation-delay: 0.2s; }
.action-card:nth-child(2) { animation-delay: 0.3s; }
.action-card:nth-child(3) { animation-delay: 0.4s; }
.action-card:nth-child(4) { animation-delay: 0.5s; }

/* 减少动画效果（用户偏好） */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .welcome-illustration {
        animation: none;
    }

    .badge {
        animation: none;
    }
}

/* 通用按钮样式 - 优化版 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    border-radius: var(--radius-md);
    font-size: 16px;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-normal);
    min-height: 48px;
    position: relative;
    overflow: hidden;
    -webkit-tap-highlight-color: transparent;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.2);
    opacity: 0;
    transition: opacity var(--transition-fast);
}

.btn:hover::before {
    opacity: 1;
}

.btn:hover {
    text-decoration: none;
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

.btn:active {
    transform: translateY(-1px) scale(0.98);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.btn:disabled::before {
    display: none;
}

.btn-primary {
    background: var(--primary-gradient);
    color: white;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
    background: var(--background-light);
    color: var(--text-secondary);
    border: 2px solid var(--border-color);
    box-shadow: var(--shadow-sm);
}

.btn-success {
    background: var(--success-gradient);
    color: white;
    box-shadow: 0 4px 12px rgba(86, 171, 47, 0.3);
}

.btn-warning {
    background: var(--warning-gradient);
    color: white;
    box-shadow: 0 4px 12px rgba(240, 147, 251, 0.3);
}

.btn-danger {
    background: var(--danger-gradient);
    color: white;
    box-shadow: 0 4px 12px rgba(255, 71, 87, 0.3);
}

.btn-info {
    background: var(--info-gradient);
    color: white;
    box-shadow: 0 4px 12px rgba(79, 172, 254, 0.3);
}

.btn-block {
    width: 100%;
}

.btn-sm {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 14px;
    min-height: 40px;
}

.btn-lg {
    padding: var(--spacing-lg) var(--spacing-xl);
    font-size: 18px;
    min-height: 56px;
}

/* 表单样式 - 优化版 */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    font-size: 15px;
    position: relative;
    padding-left: 4px;
}

.form-label::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 16px;
    background: var(--primary-gradient);
    border-radius: 2px;
    opacity: 0.7;
}

.form-label i {
    color: var(--primary-color);
    font-size: 14px;
    opacity: 0.8;
}

.form-label.required::after {
    content: '*';
    color: var(--danger-color);
    margin-left: var(--spacing-xs);
    font-weight: 700;
    font-size: 16px;
}

.form-control {
    width: 100%;
    padding: var(--spacing-md);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 16px;
    line-height: 1.5;
    background: white;
    transition: all var(--transition-normal);
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    min-height: 48px;
    box-sizing: border-box;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    transform: translateY(-1px);
}

.form-control:invalid {
    border-color: var(--danger-color);
}

.form-control:invalid:focus {
    box-shadow: 0 0 0 3px rgba(255, 71, 87, 0.1);
}

.form-control::placeholder {
    color: var(--text-muted);
    opacity: 0.7;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
}

.form-row-3 {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: var(--spacing-md);
}

.form-error {
    color: var(--danger-color);
    font-size: 13px;
    margin-top: var(--spacing-xs);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.form-success {
    color: var(--success-color);
    font-size: 13px;
    margin-top: var(--spacing-xs);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.form-help {
    color: var(--text-muted);
    font-size: 13px;
    margin-top: var(--spacing-xs);
}

/* 选择框样式 */
.form-select, select.form-control {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px 12px;
    padding-right: 40px;
    cursor: pointer;
}

select.form-control option {
    padding: 8px 12px;
    line-height: 1.5;
    background: white;
    color: var(--text-primary);
    min-height: 40px;
    display: flex;
    align-items: center;
}

/* 修复iOS Safari中select的显示问题 */
select.form-control {
    background-color: white;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

/* 确保select在所有浏览器中的一致性 */
@supports (-webkit-appearance: none) {
    select.form-control {
        background-color: white;
    }
}

/* 修复Android Chrome中的select样式 */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
    select.form-control {
        background-color: white;
        padding-top: 12px;
        padding-bottom: 12px;
    }
}

/* 文件上传样式 */
.form-file {
    position: relative;
    overflow: hidden;
    display: inline-block;
    cursor: pointer;
}

.form-file input[type="file"] {
    position: absolute;
    left: -9999px;
}

.form-file-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    border: 2px dashed var(--border-color);
    border-radius: var(--radius-md);
    background: var(--background-light);
    transition: all var(--transition-normal);
    cursor: pointer;
}

.form-file-label:hover {
    border-color: var(--primary-color);
    background: rgba(102, 126, 234, 0.05);
}

/* 开关样式 */
.form-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 28px;
}

.form-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.form-switch-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--border-color);
    transition: var(--transition-normal);
    border-radius: 28px;
}

.form-switch-slider:before {
    position: absolute;
    content: "";
    height: 20px;
    width: 20px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: var(--transition-normal);
    border-radius: 50%;
    box-shadow: var(--shadow-sm);
}

.form-switch input:checked + .form-switch-slider {
    background: var(--primary-gradient);
}

.form-switch input:checked + .form-switch-slider:before {
    transform: translateX(22px);
}

/* 加载和交互效果 - 优化版 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    animation: fadeIn 0.3s ease-out;
}

.loading-overlay.show {
    display: flex;
}

.loading-content {
    background: white;
    padding: var(--spacing-xl);
    border-radius: var(--radius-xl);
    text-align: center;
    max-width: 280px;
    box-shadow: var(--shadow-xl);
    animation: scaleIn 0.3s ease-out;
}

.loading-spinner {
    width: 48px;
    height: 48px;
    border: 4px solid rgba(102, 126, 234, 0.1);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-lg) auto;
}

.loading-text {
    color: var(--text-primary);
    font-weight: 600;
    font-size: 16px;
}

/* 骨架屏加载 */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

.skeleton-text {
    height: 16px;
    border-radius: 4px;
    margin-bottom: var(--spacing-sm);
}

.skeleton-text.short {
    width: 60%;
}

.skeleton-text.medium {
    width: 80%;
}

.skeleton-text.long {
    width: 100%;
}

.skeleton-card {
    height: 120px;
    border-radius: var(--radius-lg);
    margin-bottom: var(--spacing-md);
}

/* 下拉刷新 */
.pull-refresh {
    position: relative;
    overflow: hidden;
}

.pull-refresh-indicator {
    position: absolute;
    top: -60px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
    border-radius: 50%;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
}

.pull-refresh.pulling .pull-refresh-indicator {
    top: 20px;
}

.pull-refresh.refreshing .pull-refresh-indicator {
    top: 20px;
    animation: spin 1s linear infinite;
}

/* 触摸反馈 */
.touch-feedback {
    position: relative;
    overflow: hidden;
}

.touch-feedback::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.3s, height 0.3s;
}

.touch-feedback:active::before {
    width: 200px;
    height: 200px;
}

/* 滑动操作 */
.swipe-action {
    position: relative;
    overflow: hidden;
}

.swipe-content {
    transition: transform var(--transition-normal);
}

.swipe-actions {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    background: var(--danger-color);
    padding: 0 var(--spacing-lg);
    color: white;
    font-weight: 600;
}

/* 动画关键帧 */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -8px, 0);
    }
    70% {
        transform: translate3d(0, -4px, 0);
    }
    90% {
        transform: translate3d(0, -2px, 0);
    }
}

/* 工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-primary { color: var(--primary-color); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-danger { color: var(--danger-color); }
.text-info { color: var(--info-color); }
.text-muted { color: var(--text-muted); }

.bg-primary { background: var(--primary-gradient); }
.bg-success { background: var(--success-gradient); }
.bg-warning { background: var(--warning-gradient); }
.bg-danger { background: var(--danger-gradient); }
.bg-info { background: var(--info-gradient); }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-grid { display: grid; }

.flex-column { flex-direction: column; }
.flex-row { flex-direction: row; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
.align-center { align-items: center; }
.align-start { align-items: flex-start; }
.align-end { align-items: flex-end; }

.w-100 { width: 100%; }
.h-100 { height: 100%; }

.m-0 { margin: 0; }
.m-1 { margin: var(--spacing-xs); }
.m-2 { margin: var(--spacing-sm); }
.m-3 { margin: var(--spacing-md); }
.m-4 { margin: var(--spacing-lg); }
.m-5 { margin: var(--spacing-xl); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }
.mt-5 { margin-top: var(--spacing-xl); }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }
.mb-5 { margin-bottom: var(--spacing-xl); }

.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-xs); }
.p-2 { padding: var(--spacing-sm); }
.p-3 { padding: var(--spacing-md); }
.p-4 { padding: var(--spacing-lg); }
.p-5 { padding: var(--spacing-xl); }

.rounded { border-radius: var(--radius-md); }
.rounded-sm { border-radius: var(--radius-sm); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-full { border-radius: 50%; }

.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }

.opacity-0 { opacity: 0; }
.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }
.opacity-100 { opacity: 1; }

.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }
.overflow-scroll { overflow: scroll; }

.position-relative { position: relative; }
.position-absolute { position: absolute; }
.position-fixed { position: fixed; }

.z-10 { z-index: 10; }
.z-20 { z-index: 20; }
.z-30 { z-index: 30; }
.z-40 { z-index: 40; }
.z-50 { z-index: 50; }

/* 安全区域适配 */
.safe-area-top {
    padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
}

.safe-area-left {
    padding-left: env(safe-area-inset-left);
}

.safe-area-right {
    padding-right: env(safe-area-inset-right);
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    :root {
        --text-primary: #f8f9fa;
        --text-secondary: #e9ecef;
        --text-muted: #adb5bd;
        --border-color: #495057;
        --background-light: rgba(33, 37, 41, 0.95);
        --background-glass: rgba(33, 37, 41, 0.1);
    }

    body {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    }

    .form-control {
        background: #495057;
        color: var(--text-primary);
        border-color: var(--border-color);
    }

    .form-control::placeholder {
        color: var(--text-muted);
    }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    :root {
        --primary-color: #0066cc;
        --success-color: #008000;
        --warning-color: #ff8c00;
        --danger-color: #dc143c;
        --text-primary: #000000;
        --text-secondary: #333333;
        --border-color: #666666;
    }

    .btn {
        border: 2px solid currentColor;
    }

    .form-control {
        border-width: 3px;
    }
}

/* 入库页面专用样式 */
.back-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    color: var(--primary-color);
    text-decoration: none;
    transition: all var(--transition-normal);
}

/* 分类筛选器样式 */
.category-filter {
    background: white;
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    margin: var(--spacing-md) 0;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.filter-header {
    margin-bottom: var(--spacing-md);
}

.filter-header h4 {
    margin: 0;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.filter-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.filter-label {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
}

.filter-select {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 14px;
    line-height: 1.4;
    background: white;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 8px center;
    background-repeat: no-repeat;
    background-size: 14px 10px;
    padding-right: 32px;
    cursor: pointer;
    transition: all var(--transition-normal);
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    min-height: 40px;
    box-sizing: border-box;
}

.filter-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.filter-select option {
    padding: 8px 12px;
    line-height: 1.4;
    background: white;
    color: var(--text-primary);
}

/* 响应式筛选器 */
@media (max-width: 480px) {
    .filter-row {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }

    .filter-select {
        font-size: 16px; /* 防止iOS缩放 */
    }
}

.back-btn:hover {
    background: rgba(102, 126, 234, 0.1);
    transform: scale(1.1);
    text-decoration: none;
    color: var(--primary-color);
}

.header-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 18px;
    font-weight: 700;
    color: var(--primary-color);
}

.help-btn, .scan-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: none;
    border: none;
    color: var(--primary-color);
    font-size: 18px;
    cursor: pointer;
    transition: all var(--transition-normal);
}

.help-btn:hover, .scan-btn:hover {
    background: rgba(102, 126, 234, 0.1);
    transform: scale(1.1);
}

/* 进度指示器样式 */
.progress-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-lg) var(--spacing-lg) 0;
    background: white;
    margin-top: 80px;
}

.progress-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xs);
    position: relative;
    z-index: 2;
}

.step-number {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: var(--border-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 14px;
    transition: all var(--transition-normal);
}

.progress-step.active .step-number {
    background: var(--primary-gradient);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    transform: scale(1.1);
}

.progress-step.completed .step-number {
    background: var(--success-gradient);
    box-shadow: 0 4px 12px rgba(86, 171, 47, 0.3);
}

.step-label {
    font-size: 12px;
    color: var(--text-muted);
    font-weight: 500;
    text-align: center;
    white-space: nowrap;
}

.progress-step.active .step-label {
    color: var(--primary-color);
    font-weight: 600;
}

.progress-step.completed .step-label {
    color: var(--success-color);
    font-weight: 600;
}

.progress-line {
    flex: 1;
    height: 3px;
    background: var(--border-color);
    margin: 0 var(--spacing-md);
    border-radius: 2px;
    position: relative;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: var(--primary-gradient);
    width: 0;
    transition: width 0.5s ease-out;
    border-radius: 2px;
}

.progress-step.completed ~ .progress-line .progress-fill {
    width: 100%;
}

/* 步骤卡片样式 */
.step-section {
    display: none;
    padding: var(--spacing-lg);
    animation: fadeInUp 0.5s ease-out;
}

.step-section.active {
    display: block;
}

.step-card {
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    margin-bottom: var(--spacing-lg);
    border: 1px solid rgba(102, 126, 234, 0.1);
    transition: all var(--transition-normal);
}

.step-card:hover {
    box-shadow: var(--shadow-xl);
    transform: translateY(-2px);
}

.card-header {
    background: var(--primary-gradient);
    color: white;
    padding: var(--spacing-lg);
    text-align: center;
    position: relative;
}

.card-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.1);
    pointer-events: none;
}

.card-header > * {
    position: relative;
    z-index: 1;
}

.card-header h2 {
    font-size: 20px;
    font-weight: 700;
    margin-bottom: var(--spacing-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    color: #ffffff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
}

.card-header p {
    font-size: 14px;
    margin: 0;
    color: #ffffff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
    font-weight: 500;
    line-height: 1.4;
}

/* 不同步骤的主题色彩 */
#step1 .card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

#step2 .card-header {
    background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
}

#step3 .card-header {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

/* 增强卡片头部的视觉效果 */
.card-header {
    position: relative;
    overflow: hidden;
}

.card-header::after {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    pointer-events: none;
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% {
        opacity: 0;
        transform: translateX(-100%) translateY(-100%);
    }
    50% {
        opacity: 1;
        transform: translateX(0) translateY(0);
    }
}

/* 卡片头部图标动画 */
.card-header h2 i {
    animation: iconPulse 2s ease-in-out infinite;
}

@keyframes iconPulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

/* 改善文字可读性 */
.card-header h2,
.card-header p {
    color: #ffffff !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    filter: drop-shadow(0 1px 3px rgba(0, 0, 0, 0.3));
}

.card-body {
    padding: var(--spacing-lg);
    background: linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(248, 250, 252, 1) 100%);
    position: relative;
}

.card-body::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, rgba(102, 126, 234, 0.2) 50%, transparent 100%);
}

/* 食材列表样式 */
.ingredient-item {
    background: white;
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-md);
    box-shadow: var(--shadow-sm);
    border: 2px solid transparent;
    transition: all var(--transition-normal);
}

.ingredient-item:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.ingredient-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.ingredient-name {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 16px;
}

.ingredient-category {
    font-size: 12px;
    color: var(--text-muted);
    background: rgba(102, 126, 234, 0.1);
    padding: 2px 8px;
    border-radius: 12px;
}

.ingredient-details {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-sm);
}

.detail-item {
    text-align: center;
}

.detail-label {
    font-size: 11px;
    color: var(--text-muted);
    margin-bottom: 2px;
}

.detail-value {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
}

.remove-btn {
    background: var(--danger-color);
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    cursor: pointer;
    transition: all var(--transition-normal);
}

.remove-btn:hover {
    background: #dc2626;
    transform: scale(1.1);
}

/* 拍照和文件上传样式 */
.photo-upload-area {
    border: 2px dashed var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    text-align: center;
    background: rgba(102, 126, 234, 0.02);
    transition: all var(--transition-normal);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.photo-upload-area:hover {
    border-color: var(--primary-color);
    background: rgba(102, 126, 234, 0.05);
}

.photo-upload-area.dragover {
    border-color: var(--primary-color);
    background: rgba(102, 126, 234, 0.1);
    transform: scale(1.02);
}

.upload-icon {
    font-size: 48px;
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
    opacity: 0.7;
}

.upload-text {
    color: var(--text-secondary);
    font-size: 16px;
    font-weight: 500;
    margin-bottom: var(--spacing-sm);
}

.upload-hint {
    color: var(--text-muted);
    font-size: 13px;
}

.photo-preview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: var(--spacing-md);
    margin-top: var(--spacing-lg);
}

.photo-preview {
    position: relative;
    aspect-ratio: 1;
    border-radius: var(--radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    background: var(--background-light);
}

.photo-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-normal);
}

.photo-preview:hover img {
    transform: scale(1.05);
}

.photo-remove {
    position: absolute;
    top: 4px;
    right: 4px;
    background: rgba(220, 38, 38, 0.9);
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    cursor: pointer;
    transition: all var(--transition-normal);
}

.photo-remove:hover {
    background: #dc2626;
    transform: scale(1.1);
}

.photo-loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(2px);
}

.photo-loading .loading-spinner {
    width: 24px;
    height: 24px;
    border-width: 2px;
    margin: 0;
}

/* 相机控制样式 */
.camera-container {
    position: relative;
    width: 100%;
    max-width: 400px;
    margin: 0 auto;
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
}

.camera-video {
    width: 100%;
    height: auto;
    display: block;
}

.camera-controls {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    padding: var(--spacing-lg);
    display: flex;
    justify-content: center;
    gap: var(--spacing-lg);
}

.camera-btn {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: 3px solid white;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    font-size: 24px;
    cursor: pointer;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
}

.camera-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.camera-btn.capture {
    background: var(--danger-color);
    border-color: var(--danger-color);
}

.camera-btn.capture:hover {
    background: #dc2626;
    border-color: #dc2626;
}

/* 扫码样式 */
.scanner-container {
    position: relative;
    width: 100%;
    max-width: 300px;
    margin: 0 auto;
    aspect-ratio: 1;
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
}

.scanner-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.scanner-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
}

.scanner-frame {
    width: 200px;
    height: 200px;
    border: 2px solid var(--primary-color);
    border-radius: var(--radius-md);
    position: relative;
    background: transparent;
}

.scanner-frame::before,
.scanner-frame::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    border: 3px solid var(--primary-color);
}

.scanner-frame::before {
    top: -3px;
    left: -3px;
    border-right: none;
    border-bottom: none;
}

.scanner-frame::after {
    bottom: -3px;
    right: -3px;
    border-left: none;
    border-top: none;
}

.scanner-line {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--primary-gradient);
    animation: scannerMove 2s linear infinite;
}

@keyframes scannerMove {
    0% { top: 0; }
    50% { top: calc(100% - 2px); }
    100% { top: 0; }
}

/* 步骤导航按钮 */
.step-navigation {
    position: fixed;
    bottom: 100px;
    left: 0;
    right: 0;
    padding: 0 var(--spacing-lg);
    background: white;
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
    z-index: 100;
}

.nav-buttons {
    display: flex;
    gap: var(--spacing-md);
    padding: var(--spacing-md) 0;
}

.btn-prev {
    flex: 1;
    background: var(--background-light);
    color: var(--text-secondary);
    border: 2px solid var(--border-color);
}

.btn-next {
    flex: 2;
}

.btn-submit {
    flex: 1;
    background: var(--success-gradient);
}
