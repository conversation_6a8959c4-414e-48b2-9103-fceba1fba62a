<?php
/**
 * 为ingredients表添加min_stock字段
 */

require_once '../includes/Database.php';

try {
    $db = Database::getInstance();
    
    echo "<h1>为ingredients表添加min_stock字段</h1>";
    
    // 检查当前表结构
    echo "<h2>1. 检查当前表结构</h2>";
    
    $columns = $db->fetchAll("DESCRIBE ingredients");
    $columnNames = array_column($columns, 'Field');
    
    echo "<p>当前字段: " . implode(', ', $columnNames) . "</p>";
    
    // 检查min_stock字段是否存在
    if (!in_array('min_stock', $columnNames)) {
        echo "<h2>2. 添加min_stock字段</h2>";
        
        try {
            $sql = "ALTER TABLE ingredients ADD COLUMN min_stock INT DEFAULT 10 COMMENT '最小库存量'";
            $db->query($sql);
            echo "<p style='color: green;'>✅ 添加min_stock字段成功</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ 添加min_stock字段失败: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<h2>2. min_stock字段已存在</h2>";
        echo "<p style='color: blue;'>ℹ️ min_stock字段已存在，检查默认值</p>";
        
        // 检查并修改默认值
        try {
            $sql = "ALTER TABLE ingredients MODIFY COLUMN min_stock INT DEFAULT 10 COMMENT '最小库存量'";
            $db->query($sql);
            echo "<p style='color: green;'>✅ 修改min_stock字段默认值成功</p>";
        } catch (Exception $e) {
            echo "<p style='color: orange;'>⚠️ 修改min_stock字段默认值失败: " . $e->getMessage() . "</p>";
        }
    }
    
    // 同时检查和修改shelf_life_days的默认值
    echo "<h2>3. 检查shelf_life_days字段默认值</h2>";
    
    if (in_array('shelf_life_days', $columnNames)) {
        try {
            $sql = "ALTER TABLE ingredients MODIFY COLUMN shelf_life_days INT DEFAULT 1 COMMENT '保质期天数'";
            $db->query($sql);
            echo "<p style='color: green;'>✅ 修改shelf_life_days字段默认值为1成功</p>";
        } catch (Exception $e) {
            echo "<p style='color: orange;'>⚠️ 修改shelf_life_days字段默认值失败: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ shelf_life_days字段不存在</p>";
    }
    
    echo "<h2>4. 验证修复结果</h2>";
    
    $newColumns = $db->fetchAll("DESCRIBE ingredients");
    
    echo "<h4>关键字段详情：</h4>";
    echo "<table style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
    echo "<tr style='background: #f5f5f5;'>";
    echo "<th style='border: 1px solid #ddd; padding: 6px;'>字段名</th>";
    echo "<th style='border: 1px solid #ddd; padding: 6px;'>类型</th>";
    echo "<th style='border: 1px solid #ddd; padding: 6px;'>允许NULL</th>";
    echo "<th style='border: 1px solid #ddd; padding: 6px;'>默认值</th>";
    echo "<th style='border: 1px solid #ddd; padding: 6px;'>注释</th>";
    echo "</tr>";
    
    $keyFields = ['shelf_life_days', 'min_stock', 'brand', 'origin', 'category_id'];
    
    foreach ($newColumns as $column) {
        if (in_array($column['Field'], $keyFields)) {
            $bgColor = 'background: #e8f5e8;';
            echo "<tr style='{$bgColor}'>";
            echo "<td style='border: 1px solid #ddd; padding: 6px;'>{$column['Field']}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 6px;'>{$column['Type']}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 6px;'>{$column['Null']}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 6px;'>" . ($column['Default'] ?? 'NULL') . "</td>";
            echo "<td style='border: 1px solid #ddd; padding: 6px;'>" . ($column['Comment'] ?? '') . "</td>";
            echo "</tr>";
        }
    }
    echo "</table>";
    
    echo "<h2>5. 测试插入数据</h2>";
    
    // 测试插入一条完整数据
    try {
        $testData = [
            'code' => 'TEST_STOCK_' . time(),
            'name' => '库存测试食材_' . date('H:i:s'),
            'specification' => '测试规格',
            'unit' => '个',
            'category_id' => 1,
            'brand' => '测试品牌',
            'origin' => '测试产地',
            'shelf_life' => '1天',
            'shelf_life_days' => 1,
            'min_stock' => 10,
            'purchase_price' => 5.50,
            'created_by' => 1,
            'status' => 1
        ];
        
        echo "<p>测试数据: " . json_encode($testData, JSON_UNESCAPED_UNICODE) . "</p>";
        
        $testId = $db->insert('ingredients', $testData);
        
        if ($testId) {
            echo "<p style='color: green;'>✅ 测试插入成功: ID={$testId}</p>";
            
            // 查询插入的数据
            $insertedData = $db->fetchOne("SELECT * FROM ingredients WHERE id = ?", [$testId]);
            echo "<p>插入的数据验证:</p>";
            echo "<ul>";
            echo "<li>shelf_life_days: {$insertedData['shelf_life_days']}</li>";
            echo "<li>min_stock: {$insertedData['min_stock']}</li>";
            echo "<li>brand: {$insertedData['brand']}</li>";
            echo "<li>origin: {$insertedData['origin']}</li>";
            echo "</ul>";
            
            // 删除测试数据
            $db->query("DELETE FROM ingredients WHERE id = ?", [$testId]);
            echo "<p style='color: blue;'>ℹ️ 测试数据已清理</p>";
        } else {
            echo "<p style='color: red;'>❌ 测试插入失败</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ 测试插入异常: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>✅ 修复完成！</h2>";
    echo "<h4>默认值设置：</h4>";
    echo "<ul>";
    echo "<li><strong>shelf_life_days</strong>: 1天（保质期天数）</li>";
    echo "<li><strong>min_stock</strong>: 10（最小库存量）</li>";
    echo "</ul>";
    
    echo "<p><a href='../test/test-auto-create-ingredients.php'>重新测试自动创建食材功能</a></p>";
    echo "<p><a href='../modules/purchase/index.php?action=import'>重新测试Excel导入</a></p>";
    echo "<p><a href='../test/view-import-log.php?clear_log=1'>清空日志重新测试</a></p>";
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>❌ 修复失败</h2>";
    echo "<p style='color: red;'>错误: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
