<?php require_once dirname(__DIR__, 2) . '/includes/header-modular.php'; ?>

<div class="main-content">
    <?php require_once dirname(__DIR__, 2) . '/includes/sidebar-modular.php'; ?>
    
    <div class="content">
        <div class="content-header">
            <h1>
                <i class="fas fa-shield-alt"></i>
                安全设置
            </h1>
            <div class="header-actions">
                <a href="index.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i>
                    返回设置
                </a>
            </div>
        </div>

        <?php if (!empty($error)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle"></i>
            <?= htmlspecialchars($error) ?>
        </div>
        <?php elseif (!empty($_GET['success'])): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i>
            安全设置保存成功
        </div>
        <?php endif; ?>

        <div class="settings-container">
            <form method="POST" action="index.php?action=security" class="settings-form">
                <!-- 会话安全 -->
                <div class="setting-section">
                    <h2><i class="fas fa-clock"></i> 会话安全</h2>
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="session_timeout">会话超时时间（秒）</label>
                            <select id="session_timeout" name="session_timeout" class="form-control">
                                <option value="1800" <?= ($settings['session_timeout'] ?? 3600) == 1800 ? 'selected' : '' ?>>30分钟</option>
                                <option value="3600" <?= ($settings['session_timeout'] ?? 3600) == 3600 ? 'selected' : '' ?>>1小时</option>
                                <option value="7200" <?= ($settings['session_timeout'] ?? 3600) == 7200 ? 'selected' : '' ?>>2小时</option>
                                <option value="14400" <?= ($settings['session_timeout'] ?? 3600) == 14400 ? 'selected' : '' ?>>4小时</option>
                                <option value="28800" <?= ($settings['session_timeout'] ?? 3600) == 28800 ? 'selected' : '' ?>>8小时</option>
                            </select>
                            <small class="form-text">用户无操作后自动退出登录的时间</small>
                        </div>
                        <div class="form-group">
                            <label for="force_https">强制HTTPS</label>
                            <select id="force_https" name="force_https" class="form-control">
                                <option value="0" <?= ($settings['force_https'] ?? 0) == 0 ? 'selected' : '' ?>>关闭</option>
                                <option value="1" <?= ($settings['force_https'] ?? 0) == 1 ? 'selected' : '' ?>>开启</option>
                            </select>
                            <small class="form-text">强制使用HTTPS协议访问系统</small>
                        </div>
                    </div>
                </div>

                <!-- 密码策略 -->
                <div class="setting-section">
                    <h2><i class="fas fa-key"></i> 密码策略</h2>
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="password_min_length">最小密码长度</label>
                            <select id="password_min_length" name="password_min_length" class="form-control">
                                <option value="6" <?= ($settings['password_min_length'] ?? 6) == 6 ? 'selected' : '' ?>>6位</option>
                                <option value="8" <?= ($settings['password_min_length'] ?? 6) == 8 ? 'selected' : '' ?>>8位</option>
                                <option value="10" <?= ($settings['password_min_length'] ?? 6) == 10 ? 'selected' : '' ?>>10位</option>
                                <option value="12" <?= ($settings['password_min_length'] ?? 6) == 12 ? 'selected' : '' ?>>12位</option>
                            </select>
                            <small class="form-text">用户密码的最小长度要求</small>
                        </div>
                        <div class="form-group">
                            <label for="enable_2fa">双因素认证</label>
                            <select id="enable_2fa" name="enable_2fa" class="form-control">
                                <option value="0" <?= ($settings['enable_2fa'] ?? 0) == 0 ? 'selected' : '' ?>>关闭</option>
                                <option value="1" <?= ($settings['enable_2fa'] ?? 0) == 1 ? 'selected' : '' ?>>开启</option>
                            </select>
                            <small class="form-text">启用双因素认证增强账户安全</small>
                        </div>
                    </div>
                </div>

                <!-- 登录安全 -->
                <div class="setting-section">
                    <h2><i class="fas fa-sign-in-alt"></i> 登录安全</h2>
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="login_attempts">最大登录尝试次数</label>
                            <select id="login_attempts" name="login_attempts" class="form-control">
                                <option value="3" <?= ($settings['login_attempts'] ?? 5) == 3 ? 'selected' : '' ?>>3次</option>
                                <option value="5" <?= ($settings['login_attempts'] ?? 5) == 5 ? 'selected' : '' ?>>5次</option>
                                <option value="10" <?= ($settings['login_attempts'] ?? 5) == 10 ? 'selected' : '' ?>>10次</option>
                                <option value="0" <?= ($settings['login_attempts'] ?? 5) == 0 ? 'selected' : '' ?>>不限制</option>
                            </select>
                            <small class="form-text">超过次数后将锁定账户</small>
                        </div>
                        <div class="form-group">
                            <label for="lockout_duration">账户锁定时间（秒）</label>
                            <select id="lockout_duration" name="lockout_duration" class="form-control">
                                <option value="300" <?= ($settings['lockout_duration'] ?? 900) == 300 ? 'selected' : '' ?>>5分钟</option>
                                <option value="900" <?= ($settings['lockout_duration'] ?? 900) == 900 ? 'selected' : '' ?>>15分钟</option>
                                <option value="1800" <?= ($settings['lockout_duration'] ?? 900) == 1800 ? 'selected' : '' ?>>30分钟</option>
                                <option value="3600" <?= ($settings['lockout_duration'] ?? 900) == 3600 ? 'selected' : '' ?>>1小时</option>
                            </select>
                            <small class="form-text">账户被锁定后的解锁时间</small>
                        </div>
                    </div>
                </div>

                <!-- 安全提示 -->
                <div class="security-tips">
                    <h3><i class="fas fa-lightbulb"></i> 安全建议</h3>
                    <ul>
                        <li>建议启用HTTPS协议，确保数据传输安全</li>
                        <li>设置合理的会话超时时间，平衡安全性和用户体验</li>
                        <li>要求用户使用强密码，包含大小写字母、数字和特殊字符</li>
                        <li>定期检查系统日志，监控异常登录行为</li>
                        <li>为重要账户启用双因素认证</li>
                    </ul>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        保存设置
                    </button>
                    <button type="reset" class="btn btn-secondary">
                        <i class="fas fa-undo"></i>
                        重置
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.settings-container {
    max-width: 800px;
}

.settings-form {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
}

.setting-section {
    margin-bottom: 32px;
    padding-bottom: 24px;
    border-bottom: 1px solid #e5e7eb;
}

.setting-section:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.setting-section h2 {
    margin: 0 0 20px 0;
    color: #1f2937;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.form-group label {
    font-weight: 500;
    color: #374151;
}

.form-control {
    padding: 10px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.form-control:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-text {
    font-size: 12px;
    color: #6b7280;
}

.security-tips {
    background: #f0f9ff;
    border: 1px solid #bae6fd;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 24px;
}

.security-tips h3 {
    margin: 0 0 12px 0;
    color: #0369a1;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.security-tips ul {
    margin: 0;
    padding-left: 20px;
    color: #0c4a6e;
}

.security-tips li {
    margin-bottom: 8px;
    line-height: 1.5;
}

.form-actions {
    display: flex;
    gap: 12px;
    padding-top: 24px;
    border-top: 1px solid #e5e7eb;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #f3f4f6;
    color: #6b7280;
    border: 1px solid #d1d5db;
}

.btn-secondary:hover {
    background: #e5e7eb;
}

.alert {
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.alert-success {
    background: #dcfce7;
    color: #166534;
    border: 1px solid #bbf7d0;
}

.alert-danger {
    background: #fee2e2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

@media (max-width: 768px) {
    .form-grid {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
    }
}
</style>

<?php require_once dirname(__DIR__, 2) . '/includes/footer-modular.php'; ?>
