<?php

/**
 * 学校食堂食材出入库管理系统 - 模型创建脚本
 * 根据开发文档自动生成Laravel模型文件
 */

// 食材分类模型
$ingredientCategoryModel = '<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class IngredientCategory extends Model
{
    use HasFactory;

    protected $fillable = [
        \'name\',
        \'code\',
        \'sort_order\'
    ];

    protected $casts = [
        \'sort_order\' => \'integer\'
    ];

    /**
     * 关联食材
     */
    public function ingredients()
    {
        return $this->hasMany(Ingredient::class, \'category_id\');
    }

    /**
     * 按排序获取分类
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy(\'sort_order\');
    }
}';

// 供应商模型
$supplierModel = '<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Supplier extends Model
{
    use HasFactory;

    protected $fillable = [
        \'name\',
        \'contact_person\',
        \'phone\',
        \'address\',
        \'license_number\',
        \'qualification_files\',
        \'rating\',
        \'status\'
    ];

    protected $casts = [
        \'qualification_files\' => \'array\',
        \'rating\' => \'integer\',
        \'status\' => \'boolean\'
    ];

    /**
     * 关联入库记录
     */
    public function inboundRecords()
    {
        return $this->hasMany(InboundRecord::class);
    }

    /**
     * 获取正常状态的供应商
     */
    public function scopeActive($query)
    {
        return $query->where(\'status\', 1);
    }

    /**
     * 获取评级范围内的供应商
     */
    public function scopeRating($query, $minRating = 3)
    {
        return $query->where(\'rating\', \'>=\', $minRating);
    }
}';

// 食材模型
$ingredientModel = '<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Ingredient extends Model
{
    use HasFactory;

    protected $fillable = [
        \'name\',
        \'category_id\',
        \'unit\',
        \'shelf_life_days\',
        \'min_stock\',
        \'image_path\',
        \'description\',
        \'status\',
        \'created_by\'
    ];

    protected $casts = [
        \'category_id\' => \'integer\',
        \'shelf_life_days\' => \'integer\',
        \'min_stock\' => \'decimal:2\',
        \'status\' => \'boolean\',
        \'created_by\' => \'integer\'
    ];

    /**
     * 关联分类
     */
    public function category()
    {
        return $this->belongsTo(IngredientCategory::class, \'category_id\');
    }

    /**
     * 关联创建人
     */
    public function creator()
    {
        return $this->belongsTo(User::class, \'created_by\');
    }

    /**
     * 关联入库记录
     */
    public function inboundRecords()
    {
        return $this->hasMany(InboundRecord::class);
    }

    /**
     * 关联出库记录
     */
    public function outboundRecords()
    {
        return $this->hasMany(OutboundRecord::class);
    }

    /**
     * 关联库存
     */
    public function inventory()
    {
        return $this->hasOne(Inventory::class);
    }

    /**
     * 关联库存批次
     */
    public function inventoryBatches()
    {
        return $this->hasMany(InventoryBatch::class);
    }

    /**
     * 获取启用状态的食材
     */
    public function scopeActive($query)
    {
        return $query->where(\'status\', 1);
    }

    /**
     * 按分类筛选
     */
    public function scopeByCategory($query, $categoryId)
    {
        return $query->where(\'category_id\', $categoryId);
    }

    /**
     * 获取当前库存数量
     */
    public function getCurrentStock()
    {
        return $this->inventory ? $this->inventory->current_quantity : 0;
    }

    /**
     * 检查是否低库存
     */
    public function isLowStock()
    {
        return $this->getCurrentStock() <= $this->min_stock;
    }
}';

// 入库记录模型
$inboundRecordModel = '<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class InboundRecord extends Model
{
    use HasFactory;

    protected $fillable = [
        \'ingredient_id\',
        \'supplier_id\',
        \'batch_number\',
        \'quantity\',
        \'unit_price\',
        \'production_date\',
        \'expired_at\',
        \'weight_images\',
        \'purchase_invoice\',
        \'quality_check\',
        \'notes\',
        \'status\',
        \'created_by\',
        \'approved_by\',
        \'approved_at\'
    ];

    protected $casts = [
        \'ingredient_id\' => \'integer\',
        \'supplier_id\' => \'integer\',
        \'quantity\' => \'decimal:2\',
        \'unit_price\' => \'decimal:2\',
        \'production_date\' => \'date\',
        \'expired_at\' => \'date\',
        \'weight_images\' => \'array\',
        \'quality_check\' => \'boolean\',
        \'status\' => \'boolean\',
        \'created_by\' => \'integer\',
        \'approved_by\' => \'integer\',
        \'approved_at\' => \'datetime\'
    ];

    /**
     * 关联食材
     */
    public function ingredient()
    {
        return $this->belongsTo(Ingredient::class);
    }

    /**
     * 关联供应商
     */
    public function supplier()
    {
        return $this->belongsTo(Supplier::class);
    }

    /**
     * 关联创建人
     */
    public function creator()
    {
        return $this->belongsTo(User::class, \'created_by\');
    }

    /**
     * 关联审核人
     */
    public function approver()
    {
        return $this->belongsTo(User::class, \'approved_by\');
    }

    /**
     * 获取总价
     */
    public function getTotalPriceAttribute()
    {
        return $this->quantity * $this->unit_price;
    }

    /**
     * 生成批次号
     */
    public static function generateBatchNumber($ingredientId)
    {
        $date = date(\'Ymd\');
        $ingredientCode = str_pad($ingredientId, 3, \'0\', STR_PAD_LEFT);
        $random = strtoupper(substr(md5(uniqid()), 0, 6));
        
        return "{$date}-{$ingredientCode}-{$random}";
    }

    /**
     * 计算过期日期
     */
    public function calculateExpiredDate($productionDate, $shelfLifeDays)
    {
        return Carbon::parse($productionDate)->addDays($shelfLifeDays);
    }

    /**
     * 获取正常状态的记录
     */
    public function scopeActive($query)
    {
        return $query->where(\'status\', 1);
    }

    /**
     * 获取已审核的记录
     */
    public function scopeApproved($query)
    {
        return $query->whereNotNull(\'approved_by\');
    }

    /**
     * 按日期范围筛选
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween(\'created_at\', [$startDate, $endDate]);
    }
}';

// 创建文件
file_put_contents(\'app/Models/IngredientCategory.php\', $ingredientCategoryModel);
file_put_contents(\'app/Models/Supplier.php\', $supplierModel);
file_put_contents(\'app/Models/Ingredient.php\', $ingredientModel);
file_put_contents(\'app/Models/InboundRecord.php\', $inboundRecordModel);

echo "✅ 模型文件创建完成！\n";
echo "已创建以下模型：\n";
echo "- IngredientCategory (食材分类)\n";
echo "- Supplier (供应商)\n";
echo "- Ingredient (食材)\n";
echo "- InboundRecord (入库记录)\n";
echo "\n继续创建其他模型...\n";
