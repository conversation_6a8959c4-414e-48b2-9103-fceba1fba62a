<?php
/**
 * 会话安全类
 * 提供安全的会话管理功能
 */

class SessionSecurity
{
    /**
     * 安全启动会话
     */
    public static function secureStart()
    {
        if (session_status() === PHP_SESSION_NONE) {
            // 设置安全的会话配置
            ini_set('session.cookie_httponly', 1);
            ini_set('session.cookie_secure', isset($_SERVER['HTTPS']) ? 1 : 0);
            ini_set('session.use_strict_mode', 1);
            ini_set('session.cookie_samesite', 'Strict');
            
            session_start();
            
            // 会话固定攻击防护
            if (!isset($_SESSION['initiated'])) {
                session_regenerate_id(true);
                $_SESSION['initiated'] = true;
            }
            
            // 会话超时检查
            self::checkTimeout();
            
            // 会话劫持防护
            self::validateSession();
        }
    }

    /**
     * 检查会话超时
     */
    private static function checkTimeout()
    {
        $timeout = 3600; // 默认1小时
        
        // 从配置文件读取超时设置
        $configFile = dirname(__DIR__) . '/config/settings.php';
        if (file_exists($configFile)) {
            $settings = include $configFile;
            $timeout = $settings['session_timeout'] ?? 3600;
        }
        
        if (isset($_SESSION['last_activity'])) {
            if (time() - $_SESSION['last_activity'] > $timeout) {
                self::destroy();
                throw new Exception('会话已超时，请重新登录');
            }
        }
        
        $_SESSION['last_activity'] = time();
    }

    /**
     * 验证会话有效性
     */
    private static function validateSession()
    {
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        $remoteAddr = $_SERVER['REMOTE_ADDR'] ?? '';
        
        if (!isset($_SESSION['user_agent'])) {
            $_SESSION['user_agent'] = $userAgent;
            $_SESSION['remote_addr'] = $remoteAddr;
        } else {
            // 检查用户代理是否变化（简单的会话劫持检测）
            if ($_SESSION['user_agent'] !== $userAgent) {
                self::destroy();
                throw new Exception('会话安全验证失败，请重新登录');
            }
        }
    }

    /**
     * 安全销毁会话
     */
    public static function destroy()
    {
        if (session_status() === PHP_SESSION_ACTIVE) {
            $_SESSION = [];
            
            // 删除会话cookie
            if (ini_get("session.use_cookies")) {
                $params = session_get_cookie_params();
                setcookie(session_name(), '', time() - 42000,
                    $params["path"], $params["domain"],
                    $params["secure"], $params["httponly"]
                );
            }
            
            session_destroy();
        }
    }

    /**
     * 刷新会话ID
     */
    public static function regenerateId()
    {
        if (session_status() === PHP_SESSION_ACTIVE) {
            session_regenerate_id(true);
        }
    }

    /**
     * 检查用户是否已登录
     */
    public static function isLoggedIn()
    {
        self::secureStart();
        return !empty($_SESSION['user_id']);
    }

    /**
     * 获取当前用户ID
     */
    public static function getUserId()
    {
        self::secureStart();
        return $_SESSION['user_id'] ?? null;
    }

    /**
     * 设置用户登录状态
     */
    public static function setUser($userId, $userData = [])
    {
        self::secureStart();
        $_SESSION['user_id'] = $userId;
        $_SESSION['user_name'] = $userData['name'] ?? '';
        $_SESSION['user_email'] = $userData['email'] ?? '';
        $_SESSION['login_time'] = time();
        
        // 刷新会话ID防止会话固定攻击
        self::regenerateId();
    }

    /**
     * 记录安全事件
     */
    public static function logSecurityEvent($event, $details = [])
    {
        $logFile = dirname(__DIR__) . '/logs/security.log';
        $logDir = dirname($logFile);
        
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        $timestamp = date('Y-m-d H:i:s');
        $userId = self::getUserId() ?? 'anonymous';
        $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
        
        $logEntry = sprintf(
            "[%s] [%s] User: %s, IP: %s, Event: %s, Details: %s, UserAgent: %s\n",
            $timestamp,
            'SECURITY',
            $userId,
            $ip,
            $event,
            json_encode($details),
            $userAgent
        );
        
        file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
    }
}
