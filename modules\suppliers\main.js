/**
 * 供应商管理模块 JavaScript
 */

// DOM 加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    initializePage();
});

/**
 * 初始化页面
 */
function initializePage() {
    initializeSearch();
    initializeViewToggle();
    initializeAnimations();
    initializeContactLinks();
}

/**
 * 初始化搜索功能
 */
function initializeSearch() {
    const searchForm = document.getElementById('searchForm');
    const searchInput = document.querySelector('input[name="search"]');
    
    if (searchForm) {
        searchForm.addEventListener('submit', function(e) {
            showLoading();
        });
    }
    
    // 实时搜索（防抖）
    if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                if (this.value.length >= 2 || this.value.length === 0) {
                    filterSuppliers(this.value);
                }
            }, 300);
        });
    }
}

/**
 * 过滤供应商
 */
function filterSuppliers(searchTerm) {
    const tableRows = document.querySelectorAll('#tableView tbody tr');
    const supplierCards = document.querySelectorAll('.supplier-card');
    const searchLower = searchTerm.toLowerCase();
    
    // 过滤表格行
    tableRows.forEach(row => {
        const text = row.textContent.toLowerCase();
        if (text.includes(searchLower)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
    
    // 过滤卡片
    supplierCards.forEach(card => {
        const text = card.textContent.toLowerCase();
        if (text.includes(searchLower)) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
}

/**
 * 初始化视图切换
 */
function initializeViewToggle() {
    const toggleButtons = document.querySelectorAll('.view-toggle .btn');
    
    toggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            const viewType = this.onclick.toString().match(/switchView\('(\w+)'\)/)[1];
            switchView(viewType);
        });
    });
}

/**
 * 切换视图
 */
function switchView(viewType) {
    const tableView = document.getElementById('tableView');
    const cardView = document.getElementById('cardView');
    const toggleButtons = document.querySelectorAll('.view-toggle .btn');
    
    // 更新按钮状态
    toggleButtons.forEach(btn => {
        btn.classList.remove('active');
        if (btn.onclick.toString().includes(viewType)) {
            btn.classList.add('active');
        }
    });
    
    // 切换视图
    if (viewType === 'table') {
        tableView.style.display = 'block';
        cardView.style.display = 'none';
    } else {
        tableView.style.display = 'none';
        cardView.style.display = 'block';
    }
    
    // 保存用户偏好
    localStorage.setItem('suppliersViewType', viewType);
}

/**
 * 初始化联系方式链接
 */
function initializeContactLinks() {
    // 电话链接点击统计
    const phoneLinks = document.querySelectorAll('.phone-link');
    phoneLinks.forEach(link => {
        link.addEventListener('click', function() {
            console.log('电话拨打:', this.textContent);
        });
    });
    
    // 邮箱链接点击统计
    const emailLinks = document.querySelectorAll('.email-link');
    emailLinks.forEach(link => {
        link.addEventListener('click', function() {
            console.log('邮箱打开:', this.textContent);
        });
    });
}

/**
 * 初始化动画
 */
function initializeAnimations() {
    // 统计项动画
    const statItems = document.querySelectorAll('.stat-item');
    statItems.forEach((item, index) => {
        item.style.animationDelay = (index * 0.1) + 's';
        item.classList.add('slide-in-up');
    });
    
    // 供应商卡片动画
    const supplierCards = document.querySelectorAll('.supplier-card');
    supplierCards.forEach((card, index) => {
        card.style.animationDelay = (index * 0.1) + 's';
    });
    
    // 数字计数动画
    animateNumbers();
    
    // 恢复用户视图偏好
    const savedViewType = localStorage.getItem('suppliersViewType');
    if (savedViewType && savedViewType !== 'table') {
        switchView(savedViewType);
    }
}

/**
 * 数字计数动画
 */
function animateNumbers() {
    const statNumbers = document.querySelectorAll('.stat-number');
    
    statNumbers.forEach(element => {
        const finalValue = element.textContent.replace(/[^\d.]/g, '');
        if (finalValue && !isNaN(finalValue)) {
            animateNumber(element, 0, parseFloat(finalValue), 1000);
        }
    });
}

/**
 * 单个数字动画
 */
function animateNumber(element, start, end, duration) {
    const startTime = Date.now();
    const isDecimal = element.textContent.includes('¥');
    
    function update() {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        const current = start + (end - start) * easeOutQuart(progress);
        
        if (isDecimal) {
            element.textContent = '¥' + current.toLocaleString('zh-CN', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
        } else {
            element.textContent = Math.round(current).toLocaleString();
        }
        
        if (progress < 1) {
            requestAnimationFrame(update);
        }
    }
    
    requestAnimationFrame(update);
}

/**
 * 缓动函数
 */
function easeOutQuart(t) {
    return 1 - (--t) * t * t * t;
}

/**
 * 显示加载状态
 */
function showLoading() {
    const form = document.getElementById('searchForm');
    if (form) {
        form.classList.add('loading');
    }
}

/**
 * 隐藏加载状态
 */
function hideLoading() {
    const form = document.getElementById('searchForm');
    if (form) {
        form.classList.remove('loading');
    }
}

/**
 * 导出数据
 */
function exportData() {
    // 收集当前显示的供应商数据
    const suppliers = [];
    const tableRows = document.querySelectorAll('#tableView tbody tr');
    
    tableRows.forEach(row => {
        if (row.style.display !== 'none') {
            const cells = row.querySelectorAll('td');
            if (cells.length > 0) {
                suppliers.push({
                    name: cells[0].querySelector('strong').textContent,
                    contact: cells[1].textContent,
                    phone: cells[2].textContent,
                    email: cells[3].textContent,
                    orders: cells[4].textContent,
                    amount: cells[5].textContent,
                    lastOrder: cells[6].textContent,
                    status: cells[7].textContent
                });
            }
        }
    });
    
    // 生成CSV内容
    const csvContent = generateCSV(suppliers);
    
    // 下载文件
    downloadCSV(csvContent, '供应商列表.csv');
}

/**
 * 生成CSV内容
 */
function generateCSV(data) {
    const headers = ['供应商名称', '联系人', '联系电话', '邮箱', '订单数量', '采购总额', '最后订货', '状态'];
    const csvRows = [headers.join(',')];
    
    data.forEach(row => {
        const values = [
            row.name,
            row.contact,
            row.phone,
            row.email,
            row.orders,
            row.amount,
            row.lastOrder,
            row.status
        ];
        csvRows.push(values.join(','));
    });
    
    return csvRows.join('\n');
}

/**
 * 下载CSV文件
 */
function downloadCSV(content, filename) {
    const blob = new Blob(['\ufeff' + content], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    
    if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
}

/**
 * 刷新页面数据
 */
function refreshData() {
    showLoading();
    
    // 模拟数据刷新
    setTimeout(() => {
        location.reload();
    }, 500);
}

/**
 * 删除供应商确认
 */
function confirmDelete(supplierId, supplierName) {
    const message = `确定要删除供应商"${supplierName}"吗？\n\n删除后将无法恢复，请谨慎操作。`;
    
    if (confirm(message)) {
        // 显示加载状态
        const deleteBtn = document.querySelector(`a[href*="action=delete&id=${supplierId}"]`);
        if (deleteBtn) {
            deleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            deleteBtn.style.pointerEvents = 'none';
        }
        
        // 执行删除
        window.location.href = `?action=delete&id=${supplierId}`;
    }
}

// 全局错误处理
window.addEventListener('error', function(e) {
    console.error('页面错误:', e.error);
    hideLoading();
});

// 页面卸载前的清理
window.addEventListener('beforeunload', function() {
    // 清理定时器和事件监听器
    const loadingElements = document.querySelectorAll('.loading');
    loadingElements.forEach(element => {
        element.classList.remove('loading');
    });
});
