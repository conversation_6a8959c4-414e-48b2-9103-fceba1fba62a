<?php
/**
 * 测试食材状态切换功能
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 模拟POST请求
$_POST['id'] = 1;
$_POST['status'] = 0;
$_GET['action'] = 'toggle_status';
$_SERVER['REQUEST_METHOD'] = 'POST';

// 引入控制器
require_once 'IngredientsController.php';

try {
    $controller = new IngredientsController();
    $controller->handleRequest();
} catch (Exception $e) {
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode([
        'success' => false,
        'message' => '测试失败: ' . $e->getMessage(),
        'error_details' => [
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ]
    ]);
}
?>
