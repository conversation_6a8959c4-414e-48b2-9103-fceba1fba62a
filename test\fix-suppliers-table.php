<?php
/**
 * 修复suppliers表结构
 */

require_once '../includes/Database.php';

echo "<h2>修复suppliers表结构</h2>";

try {
    $db = Database::getInstance();
    echo "<p>✅ 数据库连接成功</p>";
    
    // 检查表是否存在
    $tableExists = $db->fetchOne("SHOW TABLES LIKE 'suppliers'");
    
    if (!$tableExists) {
        echo "<p style='color: orange;'>⚠️ suppliers表不存在，正在创建...</p>";
        
        $createSQL = "
        CREATE TABLE suppliers (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL COMMENT '供应商名称',
            contact_person VARCHAR(50) COMMENT '联系人',
            phone VARCHAR(20) COMMENT '联系电话',
            email VARCHAR(100) COMMENT '邮箱',
            address TEXT COMMENT '地址',
            business_license VARCHAR(50) COMMENT '营业执照号',
            tax_number VARCHAR(50) COMMENT '税号',
            bank_account VARCHAR(50) COMMENT '银行账号',
            bank_name VARCHAR(100) COMMENT '开户银行',
            credit_rating ENUM('A', 'B', 'C', 'D') DEFAULT 'B' COMMENT '信用等级',
            status TINYINT(1) DEFAULT 1 COMMENT '状态：1=启用，0=禁用',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_name (name),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='供应商表'
        ";
        
        $db->query($createSQL);
        echo "<p style='color: green;'>✅ suppliers表创建成功</p>";
        
    } else {
        echo "<p style='color: green;'>✅ suppliers表已存在</p>";
        
        // 检查并添加缺失的字段
        $columns = $db->fetchAll("DESCRIBE suppliers");
        $existingFields = array_column($columns, 'Field');
        
        $requiredFields = [
            'email' => "ADD COLUMN email VARCHAR(100) COMMENT '邮箱'",
            'business_license' => "ADD COLUMN business_license VARCHAR(50) COMMENT '营业执照号'",
            'tax_number' => "ADD COLUMN tax_number VARCHAR(50) COMMENT '税号'",
            'bank_account' => "ADD COLUMN bank_account VARCHAR(50) COMMENT '银行账号'",
            'bank_name' => "ADD COLUMN bank_name VARCHAR(100) COMMENT '开户银行'",
            'credit_rating' => "ADD COLUMN credit_rating ENUM('A', 'B', 'C', 'D') DEFAULT 'B' COMMENT '信用等级'"
        ];
        
        foreach ($requiredFields as $field => $alterSQL) {
            if (!in_array($field, $existingFields)) {
                try {
                    $db->query("ALTER TABLE suppliers $alterSQL");
                    echo "<p style='color: green;'>✅ 添加字段: $field</p>";
                } catch (Exception $e) {
                    echo "<p style='color: red;'>❌ 添加字段 $field 失败: " . $e->getMessage() . "</p>";
                }
            } else {
                echo "<p style='color: blue;'>ℹ️ 字段 $field 已存在</p>";
            }
        }
    }
    
    // 显示最终表结构
    echo "<h3>最终表结构：</h3>";
    $columns = $db->fetchAll("DESCRIBE suppliers");
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'><th>字段名</th><th>数据类型</th><th>是否为空</th><th>键</th><th>默认值</th><th>注释</th></tr>";
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td><strong>" . htmlspecialchars($column['Field']) . "</strong></td>";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Default'] ?? 'NULL') . "</td>";
        echo "<td>" . htmlspecialchars($column['Comment'] ?? '') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 插入示例数据（如果表为空）
    $count = $db->fetchOne("SELECT COUNT(*) as count FROM suppliers");
    if ($count['count'] == 0) {
        echo "<h3>插入示例数据：</h3>";
        
        $sampleData = [
            [
                'name' => '新鲜蔬菜供应商',
                'contact_person' => '张经理',
                'phone' => '13800138001',
                'email' => '<EMAIL>',
                'address' => '北京市朝阳区蔬菜批发市场A区101号',
                'credit_rating' => 'A'
            ],
            [
                'name' => '优质肉类供应商',
                'contact_person' => '李经理',
                'phone' => '13800138002',
                'email' => '<EMAIL>',
                'address' => '北京市丰台区肉类批发市场B区201号',
                'credit_rating' => 'A'
            ],
            [
                'name' => '海鲜水产供应商',
                'contact_person' => '王经理',
                'phone' => '13800138003',
                'email' => '<EMAIL>',
                'address' => '北京市西城区水产批发市场C区301号',
                'credit_rating' => 'B'
            ]
        ];
        
        foreach ($sampleData as $data) {
            $id = $db->insert('suppliers', $data);
            echo "<p style='color: green;'>✅ 插入供应商: {$data['name']} (ID: $id)</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ 操作失败: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='../modules/suppliers/index.php'>供应商管理</a> | <a href='test-supplier-fields.php'>测试供应商字段</a> | <a href='../index.php'>返回首页</a></p>";
?>
