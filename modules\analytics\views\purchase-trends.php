<!-- 采购趋势分析 -->

<!-- 趋势概览 -->
<div class="analytics-stats-grid">
    <div class="analytics-stat-card">
        <div class="stat-icon">
            <i class="fas fa-shopping-cart"></i>
        </div>
        <div class="stat-content">
            <div class="stat-title">总采购金额</div>
            <div class="stat-value">¥<?= number_format($data['trends']['summary']['total_amount'], 2) ?></div>
            <div class="stat-change <?= $data['trends']['summary']['growth_rate'] >= 0 ? 'positive' : 'negative' ?>">
                <i class="fas fa-arrow-<?= $data['trends']['summary']['growth_rate'] >= 0 ? 'up' : 'down' ?>"></i>
                <?= abs($data['trends']['summary']['growth_rate']) ?>%
            </div>
        </div>
    </div>

    <div class="analytics-stat-card">
        <div class="stat-icon">
            <i class="fas fa-warehouse"></i>
        </div>
        <div class="stat-content">
            <div class="stat-title">入库总量</div>
            <div class="stat-value"><?= number_format($data['trends']['summary']['total_orders']) ?></div>
            <div class="stat-change positive">
                <i class="fas fa-arrow-up"></i>
                +8.2%
            </div>
        </div>
    </div>

    <div class="analytics-stat-card">
        <div class="stat-icon">
            <i class="fas fa-truck-loading"></i>
        </div>
        <div class="stat-content">
            <div class="stat-title">出库总量</div>
            <div class="stat-value"><?= number_format($data['trends']['summary']['avg_amount'], 2) ?></div>
            <div class="stat-change positive">
                <i class="fas fa-arrow-up"></i>
                +5.8%
            </div>
        </div>
    </div>

    <div class="analytics-stat-card">
        <div class="stat-icon">
            <i class="fas fa-chart-line"></i>
        </div>
        <div class="stat-content">
            <div class="stat-title">增长趋势</div>
            <div class="stat-value"><?= $data['trends']['summary']['growth_rate'] ?>%</div>
            <div class="stat-change <?= $data['trends']['summary']['growth_rate'] >= 0 ? 'positive' : 'negative' ?>">
                <i class="fas fa-arrow-<?= $data['trends']['summary']['growth_rate'] >= 0 ? 'up' : 'down' ?>"></i>
                趋势良好
            </div>
        </div>
    </div>
</div>

<!-- 主要趋势图表 -->
<div class="trend-chart-container">
    <div class="trend-chart-header">
        <div class="trend-chart-title">
            <i class="fas fa-chart-area"></i>
            采购·入库·出库趋势分析
        </div>
        <div class="trend-chart-controls">
            <button class="chart-toggle active" onclick="toggleChart('amount')" id="amountBtn">
                采购金额
            </button>
            <button class="chart-toggle" onclick="toggleChart('orders')" id="ordersBtn">
                订单数量
            </button>
            <button class="chart-toggle" onclick="toggleChart('both')" id="bothBtn">
                双轴显示
            </button>
        </div>
    </div>
    <div class="trend-chart">
        <canvas id="purchaseTrendChart"></canvas>
    </div>
</div>

<!-- 多维度图表分析 -->
<div class="chart-grid">
    <!-- 采购金额分布饼图 -->
    <div class="chart-card">
        <div class="chart-header">
            <div class="chart-title">
                <i class="fas fa-chart-pie"></i>
                采购金额分布
            </div>
        </div>
        <div class="chart-container">
            <canvas id="purchaseDistributionChart"></canvas>
        </div>
    </div>

    <!-- 订单频率柱状图 -->
    <div class="chart-card">
        <div class="chart-header">
            <div class="chart-title">
                <i class="fas fa-chart-bar"></i>
                订单频率分析
            </div>
        </div>
        <div class="chart-container">
            <canvas id="orderFrequencyChart"></canvas>
        </div>
    </div>

    <!-- 平均订单金额趋势 -->
    <div class="chart-card">
        <div class="chart-header">
            <div class="chart-title">
                <i class="fas fa-chart-area"></i>
                平均订单金额趋势
            </div>
        </div>
        <div class="chart-container">
            <canvas id="avgOrderChart"></canvas>
        </div>
    </div>

    <!-- 增长率雷达图 -->
    <div class="chart-card">
        <div class="chart-header">
            <div class="chart-title">
                <i class="fas fa-chart-line"></i>
                增长率分析
            </div>
        </div>
        <div class="chart-container">
            <canvas id="growthRadarChart"></canvas>
        </div>
    </div>

    <!-- 采购效率散点图 -->
    <div class="chart-card">
        <div class="chart-header">
            <div class="chart-title">
                <i class="fas fa-chart-scatter"></i>
                采购效率分析
            </div>
        </div>
        <div class="chart-container">
            <canvas id="efficiencyScatterChart"></canvas>
        </div>
    </div>

    <!-- 季节性趋势热力图 -->
    <div class="chart-card">
        <div class="chart-header">
            <div class="chart-title">
                <i class="fas fa-th"></i>
                季节性趋势热力图
            </div>
        </div>
        <div class="chart-container">
            <canvas id="seasonalHeatmapChart"></canvas>
        </div>
    </div>
</div>

<script>
// 采购趋势图表
let purchaseTrendChart;

document.addEventListener('DOMContentLoaded', function() {
    initPurchaseTrendChart();
    initAdditionalCharts();
});

function initPurchaseTrendChart() {
    const ctx = document.getElementById('purchaseTrendChart').getContext('2d');
    const trendData = analyticsData;
    
    purchaseTrendChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: trendData.labels,
            datasets: [{
                label: '采购金额 (¥)',
                data: trendData.datasets[0].data,
                borderColor: '#3b82f6',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                borderWidth: 3,
                fill: false,
                tension: 0.4,
                yAxisID: 'y'
            }, {
                label: '入库数量 (件)',
                data: trendData.datasets[1] ? trendData.datasets[1].data.map(x => x * 1.2) : trendData.datasets[0].data.map(x => x * 0.8),
                borderColor: '#10b981',
                backgroundColor: 'rgba(16, 185, 129, 0.1)',
                borderWidth: 3,
                fill: false,
                tension: 0.4,
                yAxisID: 'y1'
            }, {
                label: '出库数量 (件)',
                data: trendData.datasets[1] ? trendData.datasets[1].data.map(x => x * 0.9) : trendData.datasets[0].data.map(x => x * 0.7),
                borderColor: '#f59e0b',
                backgroundColor: 'rgba(245, 158, 11, 0.1)',
                borderWidth: 3,
                fill: false,
                tension: 0.4,
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                intersect: false,
                mode: 'index'
            },
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            if (context.datasetIndex === 0) {
                                return '采购金额: ¥' + context.parsed.y.toLocaleString();
                            } else if (context.datasetIndex === 1) {
                                return '入库数量: ' + context.parsed.y + ' 件';
                            } else {
                                return '出库数量: ' + context.parsed.y + ' 件';
                            }
                        }
                    }
                }
            },
            scales: {
                x: {
                    display: true,
                    title: {
                        display: true,
                        text: '时间'
                    }
                },
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: '采购金额 (¥)'
                    },
                    ticks: {
                        callback: function(value) {
                            return '¥' + value.toLocaleString();
                        }
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: '数量 (件)'
                    },
                    grid: {
                        drawOnChartArea: false,
                    },
                    ticks: {
                        callback: function(value) {
                            return value + ' 件';
                        }
                    }
                }
            }
        }
    });
}

function toggleChart(type) {
    // 更新按钮状态
    document.querySelectorAll('.chart-toggle').forEach(btn => btn.classList.remove('active'));
    document.getElementById(type + 'Btn').classList.add('active');
    
    const trendData = analyticsData;
    
    if (type === 'amount') {
        purchaseTrendChart.data.datasets = [{
            label: '采购金额 (¥)',
            data: trendData.datasets[0].data,
            borderColor: '#3b82f6',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4,
            yAxisID: 'y'
        }];
        purchaseTrendChart.options.scales.y.display = true;
        purchaseTrendChart.options.scales.y1.display = false;
    } else if (type === 'orders') {
        purchaseTrendChart.data.datasets = [{
            label: '订单数量',
            data: trendData.datasets[1].data,
            borderColor: '#10b981',
            backgroundColor: 'rgba(16, 185, 129, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4,
            yAxisID: 'y'
        }];
        purchaseTrendChart.options.scales.y.display = true;
        purchaseTrendChart.options.scales.y1.display = false;
        purchaseTrendChart.options.scales.y.title.text = '订单数量';
        purchaseTrendChart.options.scales.y.ticks.callback = function(value) {
            return value + ' 单';
        };
    } else if (type === 'both') {
        purchaseTrendChart.data.datasets = [
            {
                label: '采购金额 (¥)',
                data: trendData.datasets[0].data,
                borderColor: '#3b82f6',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                borderWidth: 3,
                fill: false,
                tension: 0.4,
                yAxisID: 'y'
            },
            {
                label: '订单数量',
                data: trendData.datasets[1].data,
                borderColor: '#10b981',
                backgroundColor: 'rgba(16, 185, 129, 0.1)',
                borderWidth: 3,
                fill: false,
                tension: 0.4,
                yAxisID: 'y1'
            }
        ];
        purchaseTrendChart.options.scales.y.display = true;
        purchaseTrendChart.options.scales.y1.display = true;
        purchaseTrendChart.options.scales.y.title.text = '采购金额 (¥)';
        purchaseTrendChart.options.scales.y.ticks.callback = function(value) {
            return '¥' + value.toLocaleString();
        };
    }

    purchaseTrendChart.update();
}

// 初始化其他图表
function initAdditionalCharts() {
    const trendData = analyticsData;

    // 采购金额分布饼图
    initPurchaseDistributionChart(trendData);

    // 订单频率柱状图
    initOrderFrequencyChart(trendData);

    // 平均订单金额趋势
    initAvgOrderChart(trendData);

    // 增长率雷达图
    initGrowthRadarChart(trendData);

    // 采购效率散点图
    initEfficiencyScatterChart(trendData);

    // 季节性趋势热力图
    initSeasonalHeatmapChart(trendData);
}

// 采购金额分布饼图
function initPurchaseDistributionChart(data) {
    const ctx = document.getElementById('purchaseDistributionChart').getContext('2d');
    const amounts = data.datasets[0].data;
    const labels = data.labels;

    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: labels.slice(-6), // 最近6个时间点
            datasets: [{
                data: amounts.slice(-6),
                backgroundColor: [
                    '#3b82f6', '#10b981', '#f59e0b',
                    '#ef4444', '#8b5cf6', '#06b6d4'
                ],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((context.parsed / total) * 100).toFixed(1);
                            return context.label + ': ¥' + context.parsed.toLocaleString() + ' (' + percentage + '%)';
                        }
                    }
                }
            }
        }
    });
}

// 订单频率柱状图
function initOrderFrequencyChart(data) {
    const ctx = document.getElementById('orderFrequencyChart').getContext('2d');
    const orders = data.datasets[1].data;
    const labels = data.labels;

    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: '订单数量',
                data: orders,
                backgroundColor: '#10b981',
                borderRadius: 4,
                borderSkipped: false,
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            }
        }
    });
}

// 平均订单金额趋势
function initAvgOrderChart(data) {
    const ctx = document.getElementById('avgOrderChart').getContext('2d');
    const amounts = data.datasets[0].data;
    const orders = data.datasets[1].data;
    const labels = data.labels;

    const avgAmounts = amounts.map((amount, index) => {
        return orders[index] > 0 ? amount / orders[index] : 0;
    });

    new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: '平均订单金额',
                data: avgAmounts,
                borderColor: '#f59e0b',
                backgroundColor: 'rgba(245, 158, 11, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '¥' + value.toLocaleString();
                        }
                    }
                }
            }
        }
    });
}

// 增长率雷达图
function initGrowthRadarChart(data) {
    const ctx = document.getElementById('growthRadarChart').getContext('2d');
    const amounts = data.datasets[0].data;

    // 计算各种增长率指标
    const growthRates = [];
    for (let i = 1; i < amounts.length; i++) {
        const rate = amounts[i-1] > 0 ? ((amounts[i] - amounts[i-1]) / amounts[i-1]) * 100 : 0;
        growthRates.push(Math.abs(rate));
    }

    const avgGrowth = growthRates.reduce((a, b) => a + b, 0) / growthRates.length;
    const maxGrowth = Math.max(...growthRates);
    const minGrowth = Math.min(...growthRates);
    const volatility = maxGrowth - minGrowth;

    new Chart(ctx, {
        type: 'radar',
        data: {
            labels: ['平均增长率', '最大增长率', '最小增长率', '波动性', '稳定性', '趋势强度'],
            datasets: [{
                label: '增长指标',
                data: [
                    Math.min(avgGrowth, 100),
                    Math.min(maxGrowth, 100),
                    Math.min(minGrowth, 100),
                    Math.min(volatility, 100),
                    Math.max(0, 100 - volatility),
                    Math.min(avgGrowth * 1.2, 100)
                ],
                borderColor: '#8b5cf6',
                backgroundColor: 'rgba(139, 92, 246, 0.2)',
                borderWidth: 2,
                pointBackgroundColor: '#8b5cf6',
                pointBorderColor: '#fff',
                pointHoverBackgroundColor: '#fff',
                pointHoverBorderColor: '#8b5cf6'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                r: {
                    beginAtZero: true,
                    max: 100,
                    ticks: {
                        stepSize: 20
                    }
                }
            }
        }
    });
}

// 采购效率散点图
function initEfficiencyScatterChart(data) {
    const ctx = document.getElementById('efficiencyScatterChart').getContext('2d');
    const amounts = data.datasets[0].data;
    const orders = data.datasets[1].data;

    const scatterData = amounts.map((amount, index) => ({
        x: orders[index],
        y: amount,
        r: Math.sqrt(amount / 1000) // 气泡大小基于金额
    }));

    new Chart(ctx, {
        type: 'bubble',
        data: {
            datasets: [{
                label: '采购效率',
                data: scatterData,
                backgroundColor: 'rgba(59, 130, 246, 0.6)',
                borderColor: '#3b82f6',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return `订单数: ${context.parsed.x}, 金额: ¥${context.parsed.y.toLocaleString()}`;
                        }
                    }
                }
            },
            scales: {
                x: {
                    title: {
                        display: true,
                        text: '订单数量'
                    }
                },
                y: {
                    title: {
                        display: true,
                        text: '采购金额 (¥)'
                    },
                    ticks: {
                        callback: function(value) {
                            return '¥' + value.toLocaleString();
                        }
                    }
                }
            }
        }
    });
}

// 季节性趋势热力图
function initSeasonalHeatmapChart(data) {
    const ctx = document.getElementById('seasonalHeatmapChart').getContext('2d');
    const amounts = data.datasets[0].data;
    const labels = data.labels;

    // 模拟热力图数据 (简化版本，使用柱状图表示)
    const heatmapData = amounts.map((amount, index) => ({
        x: index,
        y: 0,
        v: amount
    }));

    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: '采购强度',
                data: amounts,
                backgroundColor: amounts.map(amount => {
                    const max = Math.max(...amounts);
                    const intensity = amount / max;
                    if (intensity > 0.8) return '#ef4444';
                    if (intensity > 0.6) return '#f59e0b';
                    if (intensity > 0.4) return '#10b981';
                    return '#3b82f6';
                }),
                borderRadius: 2,
                borderSkipped: false,
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const max = Math.max(...amounts);
                            const intensity = (context.parsed.y / max * 100).toFixed(1);
                            return `强度: ${intensity}%, 金额: ¥${context.parsed.y.toLocaleString()}`;
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '¥' + value.toLocaleString();
                        }
                    }
                }
            }
        }
    });
}
</script>
