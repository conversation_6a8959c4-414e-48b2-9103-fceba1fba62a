<?php require_once dirname(__DIR__, 2) . '/includes/header-modular.php'; ?>

<div class="main-content">
    <?php require_once dirname(__DIR__, 2) . '/includes/sidebar-modular.php'; ?>

    <div class="content">
        <div class="content-header">
            <h1>
                <i class="fas fa-shield-alt"></i>
                <?= $role ? ('角色权限授权 - ' . htmlspecialchars($role['display_name'] ?? $role['name'])) : '权限管理' ?>
            </h1>
            <div class="header-actions">
                <a href="index.php?action=roles" class="btn btn-secondary">
                    <i class="fas fa-id-badge"></i>
                    角色管理
                </a>
            </div>
        </div>

        <?php if (!empty($error)): ?>
        <div class="alert alert-danger"><i class="fas fa-exclamation-triangle"></i> <?= htmlspecialchars($error) ?></div>
        <?php elseif (!empty($_GET['success'])): ?>
        <div class="alert alert-success"><i class="fas fa-check-circle"></i> 操作成功</div>
        <?php endif; ?>

        <?php if ($role): ?>
        <!-- 授权面板 -->
        <div class="card">
            <div class="card-header">
                <i class="fas fa-key"></i> 为角色【<?= htmlspecialchars($role['display_name'] ?? $role['name']) ?>】分配权限
                <div class="header-actions" style="margin-left: auto;">
                    <a href="index.php?action=permission_manage" class="btn btn-sm btn-secondary">
                        <i class="fas fa-cog"></i> 权限维护
                    </a>
                </div>
            </div>
            <div class="card-body">
                <form method="POST" action="index.php?action=permissions&role_id=<?= intval($role['id']) ?>">
                    <input type="hidden" name="operation" value="assign">
                    <div class="perm-toolbar">
                        <div class="left">
                            <button type="button" class="btn btn-sm btn-secondary" onclick="toggleAll(true)"><i class="fas fa-check-square"></i> 全选</button>
                            <button type="button" class="btn btn-sm btn-secondary" onclick="toggleAll(false)"><i class="fas fa-square"></i> 全不选</button>
                        </div>
                        <div class="right">
                            <input type="text" class="form-control" id="permSearch" placeholder="搜索权限..." oninput="filterPerms()">
                        </div>
                    </div>

                    <div class="perm-grid" id="permGrid">
                        <?php
                        $grouped = [];
                        foreach (($permissions ?? []) as $p) {
                            $grouped[$p['module'] ?? '其他'] = $grouped[$p['module'] ?? '其他'] ?? [];
                            $grouped[$p['module'] ?? '其他'][] = $p;
                        }
                        foreach ($grouped as $module => $rows): ?>
                            <div class="perm-group">
                                <div class="perm-group-title"><i class="fas fa-folder"></i> <?= htmlspecialchars($module) ?></div>
                                <div class="perm-group-items">
                                    <?php foreach ($rows as $p): ?>
                                    <label class="perm-item" data-name="<?= strtolower(htmlspecialchars($p['display_name'] ?? $p['name'])) ?>">
                                        <input type="checkbox" name="permission_ids[]" value="<?= intval($p['id']) ?>" <?= in_array(intval($p['id']), $assignedPermissionIds ?? []) ? 'checked' : '' ?>>
                                        <span class="name"><?= htmlspecialchars($p['display_name'] ?? $p['name']) ?></span>
                                        <span class="code"><code><?= htmlspecialchars($p['name']) ?></code></span>
                                        <?= intval($p['status'] ?? 1) !== 1 ? '<span class="badge badge-secondary">停用</span>' : '' ?>
                                    </label>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary"><i class="fas fa-save"></i> 保存授权</button>
                    </div>
                </form>
            </div>
        </div>
        <?php else: ?>
        <!-- 无角色时的提示 -->
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-info-circle" style="font-size: 48px; color: #6c757d; margin-bottom: 16px;"></i>
                <h3>权限授权</h3>
                <p class="text-muted">请从角色管理页面选择要授权的角色，或直接访问权限维护功能。</p>
                <div style="margin-top: 20px;">
                    <a href="index.php?action=roles" class="btn btn-primary">
                        <i class="fas fa-id-badge"></i> 角色管理
                    </a>
                    <a href="index.php?action=permission_manage" class="btn btn-secondary">
                        <i class="fas fa-cog"></i> 权限维护
                    </a>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<style>
.card { background: white; border-radius: 8px; box-shadow: 0 2px 6px rgba(0,0,0,0.08); overflow: hidden; margin-bottom: 16px; }
.card-header { padding: 14px 18px; border-bottom: 1px solid #e5e7eb; font-weight: 600; color: #374151; display:flex; align-items:center; gap:8px; }
.card-body { padding: 16px 18px; }
.alert { padding: 10px 12px; border-radius: 6px; margin-bottom: 12px; display:flex; align-items:center; gap:8px; }
.alert-danger { background: #fee2e2; color: #b91c1c; }
.alert-success { background: #dcfce7; color: #166534; }
.form-grid { display: grid; grid-template-columns: 1fr 1fr 1fr 1fr; gap: 12px; margin-bottom: 12px; }
.form-group { display:flex; flex-direction: column; gap: 6px; }
.required { color: #e53e3e; }
.perm-toolbar { display:flex; justify-content: space-between; align-items:center; gap:12px; margin-bottom: 12px; }
.perm-grid { display:flex; flex-direction: column; gap: 12px; }
.perm-group { border: 1px solid #e5e7eb; border-radius: 8px; overflow: hidden; }
.perm-group-title { background: #f7fafc; padding: 8px 12px; font-weight: 600; color: #374151; display:flex; align-items:center; gap:8px; }
.perm-group-items { display:grid; grid-template-columns: repeat(auto-fill, minmax(260px, 1fr)); gap: 10px; padding: 10px; }
.perm-item { display:flex; align-items:center; gap:8px; padding: 10px 12px; border: 1px solid #e5e7eb; border-radius: 8px; background: #fafafa; }
.perm-item .name { font-weight: 600; color: #374151; }
.perm-item .code { color: #6b7280; font-size: 12px; }
@media (max-width: 1024px) { .form-grid { grid-template-columns: 1fr; } }
</style>

<script>
function toggleAll(checked) {
    document.querySelectorAll('#permGrid input[type="checkbox"]').forEach(cb => cb.checked = checked);
}
function filterPerms() {
    const q = (document.getElementById('permSearch').value || '').trim().toLowerCase();
    document.querySelectorAll('#permGrid .perm-item').forEach(item => {
        const name = item.getAttribute('data-name') || '';
        item.style.display = name.indexOf(q) !== -1 ? 'flex' : 'none';
    });
}
</script>

<?php require_once dirname(__DIR__, 2) . '/includes/footer-modular.php'; ?>

