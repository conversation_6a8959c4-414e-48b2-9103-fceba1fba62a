<?php
/**
 * 检查和处理重复的分类数据
 */

require_once '../includes/Database.php';

echo "<h2>检查重复分类数据</h2>";

try {
    $db = Database::getInstance();
    echo "<p>✅ 数据库连接成功</p>";
    
    // 检查重复的分类名称
    echo "<h3>检查重复的分类名称：</h3>";
    $duplicateNames = $db->fetchAll("
        SELECT name, COUNT(*) as count 
        FROM ingredient_categories 
        GROUP BY name 
        HAVING COUNT(*) > 1
        ORDER BY count DESC, name ASC
    ");
    
    if (empty($duplicateNames)) {
        echo "<p style='color: green;'>✅ 没有重复的分类名称</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ 发现 " . count($duplicateNames) . " 个重复的分类名称</p>";
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'><th>分类名称</th><th>重复次数</th><th>操作</th></tr>";
        
        foreach ($duplicateNames as $duplicate) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($duplicate['name']) . "</td>";
            echo "<td>" . $duplicate['count'] . "</td>";
            echo "<td><a href='?fix_name=" . urlencode($duplicate['name']) . "'>修复重复</a></td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // 检查重复的分类代码
    echo "<h3>检查重复的分类代码：</h3>";
    $duplicateCodes = $db->fetchAll("
        SELECT code, COUNT(*) as count 
        FROM ingredient_categories 
        WHERE code IS NOT NULL AND code != ''
        GROUP BY code 
        HAVING COUNT(*) > 1
        ORDER BY count DESC, code ASC
    ");
    
    if (empty($duplicateCodes)) {
        echo "<p style='color: green;'>✅ 没有重复的分类代码</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ 发现 " . count($duplicateCodes) . " 个重复的分类代码</p>";
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'><th>分类代码</th><th>重复次数</th><th>操作</th></tr>";
        
        foreach ($duplicateCodes as $duplicate) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($duplicate['code']) . "</td>";
            echo "<td>" . $duplicate['count'] . "</td>";
            echo "<td><a href='?fix_code=" . urlencode($duplicate['code']) . "'>修复重复</a></td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // 处理修复操作
    if (isset($_GET['fix_name'])) {
        $name = $_GET['fix_name'];
        echo "<h3>修复重复名称：" . htmlspecialchars($name) . "</h3>";
        
        $duplicates = $db->fetchAll("SELECT * FROM ingredient_categories WHERE name = ? ORDER BY id ASC", [$name]);
        
        if (count($duplicates) > 1) {
            echo "<p>找到 " . count($duplicates) . " 个重复记录，保留第一个，修改其他记录的名称：</p>";
            
            for ($i = 1; $i < count($duplicates); $i++) {
                $newName = $name . '_' . $i;
                $db->update('ingredient_categories', ['name' => $newName], 'id = ?', [$duplicates[$i]['id']]);
                echo "<p style='color: green;'>✅ 更新分类 ID: {$duplicates[$i]['id']} 的名称为: $newName</p>";
            }
            
            echo "<p><a href='?'>返回检查页面</a></p>";
        }
    }
    
    if (isset($_GET['fix_code'])) {
        $code = $_GET['fix_code'];
        echo "<h3>修复重复代码：" . htmlspecialchars($code) . "</h3>";
        
        $duplicates = $db->fetchAll("SELECT * FROM ingredient_categories WHERE code = ? ORDER BY id ASC", [$code]);
        
        if (count($duplicates) > 1) {
            echo "<p>找到 " . count($duplicates) . " 个重复记录，保留第一个，修改其他记录的代码：</p>";
            
            for ($i = 1; $i < count($duplicates); $i++) {
                $newCode = $code . '_' . $i;
                $db->update('ingredient_categories', ['code' => $newCode], 'id = ?', [$duplicates[$i]['id']]);
                echo "<p style='color: green;'>✅ 更新分类 ID: {$duplicates[$i]['id']} 的代码为: $newCode</p>";
            }
            
            echo "<p><a href='?'>返回检查页面</a></p>";
        }
    }
    
    // 数据完整性检查
    echo "<h3>数据完整性检查：</h3>";
    
    // 检查空名称
    $emptyNames = $db->fetchAll("SELECT id FROM ingredient_categories WHERE name IS NULL OR name = ''");
    if (!empty($emptyNames)) {
        echo "<p style='color: red;'>❌ 发现 " . count($emptyNames) . " 个空名称记录</p>";
        echo "<p>记录ID: " . implode(', ', array_column($emptyNames, 'id')) . "</p>";
    } else {
        echo "<p style='color: green;'>✅ 所有分类都有名称</p>";
    }
    
    // 检查状态字段
    $invalidStatus = $db->fetchAll("SELECT id, status FROM ingredient_categories WHERE status IS NULL");
    if (!empty($invalidStatus)) {
        echo "<p style='color: orange;'>⚠️ 发现 " . count($invalidStatus) . " 个状态为NULL的记录</p>";
        
        if (isset($_GET['fix_status'])) {
            foreach ($invalidStatus as $record) {
                $db->update('ingredient_categories', ['status' => 1], 'id = ?', [$record['id']]);
            }
            echo "<p style='color: green;'>✅ 已修复所有状态为NULL的记录</p>";
            echo "<p><a href='?'>返回检查页面</a></p>";
        } else {
            echo "<p><a href='?fix_status=1'>修复状态字段</a></p>";
        }
    } else {
        echo "<p style='color: green;'>✅ 所有分类状态字段正常</p>";
    }
    
    // 统计信息
    echo "<h3>统计信息：</h3>";
    $stats = [
        '总分类数' => $db->fetchOne("SELECT COUNT(*) as count FROM ingredient_categories")['count'],
        '启用分类' => $db->fetchOne("SELECT COUNT(*) as count FROM ingredient_categories WHERE status = 1")['count'],
        '禁用分类' => $db->fetchOne("SELECT COUNT(*) as count FROM ingredient_categories WHERE status = 0")['count'],
        '有代码的分类' => $db->fetchOne("SELECT COUNT(*) as count FROM ingredient_categories WHERE code IS NOT NULL AND code != ''")['count'],
        '有描述的分类' => $db->fetchOne("SELECT COUNT(*) as count FROM ingredient_categories WHERE description IS NOT NULL AND description != ''")['count']
    ];
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr style='background: #f0f0f0;'><th>项目</th><th>数量</th></tr>";
    foreach ($stats as $item => $count) {
        echo "<tr><td>$item</td><td>$count</td></tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ 操作失败: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='../modules/categories/index.php'>分类管理</a> | <a href='../modules/categories/index.php?action=create'>添加分类</a> | <a href='../index.php'>返回首页</a></p>";
?>
