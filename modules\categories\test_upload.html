<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件上传测试</title>
    <style>
        .upload-area {
            border: 2px dashed #ccc;
            padding: 40px;
            text-align: center;
            margin: 20px;
            cursor: pointer;
        }
        .upload-area:hover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .btn {
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .file-info {
            margin: 20px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>文件上传功能测试</h1>
    
    <div class="upload-area" id="uploadArea">
        <h3>点击此处或拖拽文件上传</h3>
        <p>支持 CSV 格式文件</p>
        <input type="file" id="fileInput" accept=".csv,.xlsx,.xls" style="display: none;">
        <button type="button" class="btn" id="selectBtn">选择文件</button>
    </div>
    
    <div id="fileInfo" class="file-info" style="display: none;">
        <h4>已选择文件：</h4>
        <p id="fileName"></p>
        <p id="fileSize"></p>
        <button type="button" class="btn" id="clearBtn">清除文件</button>
    </div>
    
    <div style="margin: 20px;">
        <h3>测试结果：</h3>
        <div id="testResults"></div>
    </div>

    <script>
        console.log('JavaScript 开始加载...');
        
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM 加载完成');
            
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('fileInput');
            const selectBtn = document.getElementById('selectBtn');
            const fileInfo = document.getElementById('fileInfo');
            const fileName = document.getElementById('fileName');
            const fileSize = document.getElementById('fileSize');
            const clearBtn = document.getElementById('clearBtn');
            const testResults = document.getElementById('testResults');
            
            // 检查元素是否存在
            console.log('uploadArea:', uploadArea);
            console.log('fileInput:', fileInput);
            console.log('selectBtn:', selectBtn);
            
            function logResult(message) {
                console.log(message);
                testResults.innerHTML += '<p>' + message + '</p>';
            }
            
            logResult('✅ JavaScript 初始化完成');
            
            // 选择文件按钮点击事件
            selectBtn.addEventListener('click', function() {
                logResult('🔘 选择文件按钮被点击');
                try {
                    fileInput.click();
                    logResult('✅ 文件输入点击成功');
                } catch (error) {
                    logResult('❌ 文件输入点击失败: ' + error.message);
                }
            });
            
            // 上传区域点击事件
            uploadArea.addEventListener('click', function(e) {
                if (e.target !== selectBtn) {
                    logResult('🔘 上传区域被点击');
                    try {
                        fileInput.click();
                        logResult('✅ 通过区域点击触发文件选择');
                    } catch (error) {
                        logResult('❌ 区域点击失败: ' + error.message);
                    }
                }
            });
            
            // 文件选择事件
            fileInput.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    logResult('✅ 文件选择成功: ' + file.name);
                    fileName.textContent = '文件名: ' + file.name;
                    fileSize.textContent = '文件大小: ' + formatFileSize(file.size);
                    fileInfo.style.display = 'block';
                } else {
                    logResult('⚠️ 没有选择文件');
                }
            });
            
            // 清除文件
            clearBtn.addEventListener('click', function() {
                fileInput.value = '';
                fileInfo.style.display = 'none';
                logResult('🗑️ 文件已清除');
            });
            
            // 拖拽事件
            uploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                uploadArea.style.borderColor = '#007bff';
                uploadArea.style.backgroundColor = '#e3f2fd';
            });
            
            uploadArea.addEventListener('dragleave', function(e) {
                e.preventDefault();
                uploadArea.style.borderColor = '#ccc';
                uploadArea.style.backgroundColor = '';
            });
            
            uploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                uploadArea.style.borderColor = '#ccc';
                uploadArea.style.backgroundColor = '';
                
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    fileInput.files = files;
                    const event = new Event('change', { bubbles: true });
                    fileInput.dispatchEvent(event);
                    logResult('✅ 拖拽文件成功: ' + files[0].name);
                }
            });
            
            function formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }
            
            logResult('🎉 所有事件监听器已绑定');
        });
        
        // 测试文件输入是否可以被程序触发
        function testFileInput() {
            const fileInput = document.getElementById('fileInput');
            try {
                fileInput.click();
                console.log('✅ 程序触发文件输入成功');
                return true;
            } catch (error) {
                console.log('❌ 程序触发文件输入失败:', error);
                return false;
            }
        }
        
        // 延迟测试
        setTimeout(function() {
            console.log('执行延迟测试...');
            testFileInput();
        }, 1000);
    </script>
</body>
</html>
