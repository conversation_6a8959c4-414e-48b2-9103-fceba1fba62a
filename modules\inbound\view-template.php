<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $page_title ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="style.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-2 sidebar">
                <?php include 'sidebar.php'; ?>
            </div>

            <!-- 主内容区 -->
            <div class="col-md-10 main-content">
                <div class="content-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h1><i class="fas fa-eye"></i> 查看入库记录</h1>
                        <div>
                            <a href="index.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> 返回列表
                            </a>
                            <a href="index.php?action=edit&id=<?= $record['id'] ?>" class="btn btn-primary">
                                <i class="fas fa-edit"></i> 编辑
                            </a>
                        </div>
                    </div>
                </div>

                <div class="content-body">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-info-circle"></i> 入库记录详情
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <!-- 基本信息 -->
                                <div class="col-md-6">
                                    <h6 class="text-primary mb-3">
                                        <i class="fas fa-clipboard-list"></i> 基本信息
                                    </h6>
                                    
                                    <div class="info-group">
                                        <label class="info-label">入库记录ID：</label>
                                        <span class="info-value"><?= $record['id'] ?></span>
                                    </div>
                                    
                                    <div class="info-group">
                                        <label class="info-label">食材名称：</label>
                                        <span class="info-value"><?= htmlspecialchars($record['ingredient_name']) ?></span>
                                    </div>
                                    
                                    <div class="info-group">
                                        <label class="info-label">供应商：</label>
                                        <span class="info-value"><?= htmlspecialchars($record['supplier_name']) ?></span>
                                    </div>
                                    
                                    <div class="info-group">
                                        <label class="info-label">批次号：</label>
                                        <span class="info-value badge bg-info"><?= htmlspecialchars($record['batch_number']) ?></span>
                                    </div>
                                    
                                    <div class="info-group">
                                        <label class="info-label">操作员：</label>
                                        <span class="info-value"><?= htmlspecialchars($record['operator_name']) ?></span>
                                    </div>
                                </div>

                                <!-- 数量和金额信息 -->
                                <div class="col-md-6">
                                    <h6 class="text-success mb-3">
                                        <i class="fas fa-calculator"></i> 数量和金额
                                    </h6>
                                    
                                    <div class="info-group">
                                        <label class="info-label">入库数量：</label>
                                        <span class="info-value text-primary">
                                            <strong><?= number_format($record['quantity'], 2) ?></strong> <?= htmlspecialchars($record['unit']) ?>
                                        </span>
                                    </div>
                                    
                                    <div class="info-group">
                                        <label class="info-label">单价：</label>
                                        <span class="info-value">¥<?= number_format($record['unit_price'], 2) ?></span>
                                    </div>
                                    
                                    <div class="info-group">
                                        <label class="info-label">总金额：</label>
                                        <span class="info-value text-success">
                                            <strong>¥<?= number_format($record['total_amount'], 2) ?></strong>
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <hr>

                            <div class="row">
                                <!-- 日期信息 -->
                                <div class="col-md-6">
                                    <h6 class="text-warning mb-3">
                                        <i class="fas fa-calendar-alt"></i> 日期信息
                                    </h6>
                                    
                                    <div class="info-group">
                                        <label class="info-label">生产日期：</label>
                                        <span class="info-value"><?= $record['production_date'] ?></span>
                                    </div>
                                    
                                    <div class="info-group">
                                        <label class="info-label">过期日期：</label>
                                        <span class="info-value"><?= $record['expiry_date'] ?></span>
                                    </div>
                                    
                                    <div class="info-group">
                                        <label class="info-label">入库日期：</label>
                                        <span class="info-value"><?= $record['inbound_date'] ?></span>
                                    </div>
                                </div>

                                <!-- 备注信息 -->
                                <div class="col-md-6">
                                    <h6 class="text-info mb-3">
                                        <i class="fas fa-sticky-note"></i> 备注信息
                                    </h6>
                                    
                                    <div class="info-group">
                                        <label class="info-label">备注：</label>
                                        <div class="info-value">
                                            <?php if (!empty($record['notes'])): ?>
                                                <div class="alert alert-light">
                                                    <?= nl2br(htmlspecialchars($record['notes'])) ?>
                                                </div>
                                            <?php else: ?>
                                                <span class="text-muted">无备注</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    
                                    <div class="info-group">
                                        <label class="info-label">创建时间：</label>
                                        <span class="info-value text-muted"><?= $record['created_at'] ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card-footer">
                            <div class="d-flex justify-content-between">
                                <a href="index.php" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left"></i> 返回列表
                                </a>
                                <div>
                                    <a href="index.php?action=edit&id=<?= $record['id'] ?>" class="btn btn-primary">
                                        <i class="fas fa-edit"></i> 编辑记录
                                    </a>
                                    <a href="index.php?action=delete&id=<?= $record['id'] ?>" 
                                       class="btn btn-danger"
                                       onclick="return confirm('确定要删除这条入库记录吗？删除后将回滚相应的库存。')">
                                        <i class="fas fa-trash"></i> 删除记录
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

<style>
.info-group {
    margin-bottom: 15px;
    display: flex;
    align-items: flex-start;
}

.info-label {
    font-weight: 600;
    color: #495057;
    min-width: 120px;
    margin-right: 10px;
    margin-bottom: 0;
}

.info-value {
    flex: 1;
    color: #212529;
}

.content-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #dee2e6;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.alert-light {
    background-color: #f8f9fa;
    border-color: #dee2e6;
    color: #495057;
    margin-bottom: 0;
}
</style>
