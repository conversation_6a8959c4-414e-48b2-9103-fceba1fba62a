<!-- 模块化侧边栏 -->
<div class="sidebar">
    <div class="sidebar-header">
        <h2>🏫 学校食堂管理系统</h2>
        <p>CANTEEN MANAGEMENT SYSTEM</p>
    </div>

    <nav class="sidebar-nav">
        <div class="nav-section">
            <div class="nav-section-title">主要功能</div>
            <a href="<?= getModuleUrl('dashboard') ?>" class="nav-item <?= isCurrentModule('dashboard') ? 'active' : '' ?>">
                <i class="fas fa-tachometer-alt"></i>
                仪表板
            </a>
        </div>

        <div class="nav-section">
            <div class="nav-section-title">基础数据</div>
            <a href="<?= getModuleUrl('ingredients') ?>" class="nav-item <?= isCurrentModule('ingredients') ? 'active' : '' ?>">
                <i class="fas fa-carrot"></i>
                食材管理
            </a>
            <a href="<?= getModuleUrl('categories') ?>" class="nav-item <?= isCurrentModule('categories') ? 'active' : '' ?>">
                <i class="fas fa-tags"></i>
                食材分类
            </a>
            <a href="<?= getModuleUrl('suppliers') ?>" class="nav-item <?= isCurrentModule('suppliers') ? 'active' : '' ?>">
                <i class="fas fa-truck"></i>
                供应商管理
            </a>
            <a href="<?= getModuleUrl('purchase') ?>" class="nav-item <?= isCurrentModule('purchase') ? 'active' : '' ?>">
                <i class="fas fa-shopping-cart"></i>
                采购管理
            </a>
        </div>

        <div class="nav-section">
            <div class="nav-section-title">库存管理</div>
            <a href="<?= getModuleUrl('inbound') ?>" class="nav-item <?= isCurrentModule('inbound') ? 'active' : '' ?>">
                <i class="fas fa-arrow-down"></i>
                食材入库
            </a>
            <a href="<?= getModuleUrl('outbound') ?>" class="nav-item <?= isCurrentModule('outbound') ? 'active' : '' ?>">
                <i class="fas fa-arrow-up"></i>
                食材出库
            </a>
            <a href="<?= getModuleUrl('stocktaking') ?>" class="nav-item <?= isCurrentModule('stocktaking') ? 'active' : '' ?>">
                <i class="fas fa-clipboard-check"></i>
                库存盘点
            </a>
            <a href="<?= getModuleUrl('inventory', 'query') ?>" class="nav-item <?= isCurrentAction('query') || (isCurrentModule('inventory') && !isCurrentAction('damage')) ? 'active' : '' ?>">
                <i class="fas fa-search"></i>
                库存查询
            </a>
            <a href="<?= getModuleUrl('inventory', 'damage') ?>" class="nav-item <?= isCurrentAction('damage') ? 'active' : '' ?>">
                <i class="fas fa-exclamation-triangle"></i>
                食材报损
            </a>
            <a href="<?= getModuleUrl('samples') ?>" class="nav-item <?= isCurrentModule('samples') ? 'active' : '' ?>">
                <i class="fas fa-vial"></i>
                食品留样
            </a>
        </div>

        <div class="nav-section">
            <div class="nav-section-title">报表分析</div>
            <a href="<?= getModuleUrl('reports') ?>" class="nav-item">
                <i class="fas fa-chart-bar"></i>
                数据报表
            </a>
            <a href="<?= getModuleUrl('analytics') ?>" class="nav-item">
                <i class="fas fa-chart-line"></i>
                趋势分析
            </a>
        </div>

        <div class="nav-section">
            <div class="nav-section-title">系统管理</div>
            <a href="<?= getModuleUrl('users') ?>" class="nav-item <?= isCurrentModule('users') && !isCurrentAction('roles') && !isCurrentAction('permissions') && !isCurrentAction('permission_manage') ? 'active' : '' ?>">
                <i class="fas fa-users"></i>
                用户管理
            </a>
            <a href="<?= getModuleUrl('users', 'roles') ?>" class="nav-item <?= isCurrentModule('users') && isCurrentAction('roles') ? 'active' : '' ?>">
                <i class="fas fa-id-badge"></i>
                角色管理
            </a>
            <a href="<?= getModuleUrl('users', 'permission_manage') ?>" class="nav-item <?= isCurrentModule('users') && isCurrentAction('permission_manage') ? 'active' : '' ?>">
                <i class="fas fa-cog"></i>
                权限维护
            </a>

            <a href="<?= getModuleUrl('settings') ?>" class="nav-item <?= isCurrentModule('settings') ? 'active' : '' ?>">
                <i class="fas fa-cog"></i>
                系统设置
            </a>
        </div>
    </nav>
</div>

<?php
/**
 * 获取模块URL的辅助函数
 */
function getModuleUrl($module, $action = null) {
    $currentDir = dirname($_SERVER['SCRIPT_NAME']);
    $basePath = str_replace('/modules/' . basename($currentDir), '', $currentDir);
    $url = $basePath . '/modules/' . $module . '/index.php';
    if ($action) {
        $url .= '?action=' . $action;
    }
    return $url;
}

/**
 * 检查是否为当前模块
 */
function isCurrentModule($module) {
    $currentDir = basename(dirname($_SERVER['SCRIPT_NAME']));
    return $currentDir === $module;
}

/**
 * 检查是否为当前操作
 */
function isCurrentAction($action) {
    return isset($_GET['action']) && $_GET['action'] === $action;
}
?>
