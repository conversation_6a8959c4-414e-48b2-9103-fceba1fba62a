<?php
/**
 * 修复ingredient_categories表结构
 */

require_once '../includes/Database.php';

try {
    $db = Database::getInstance();
    
    echo "<h1>修复ingredient_categories表结构</h1>";
    
    // 检查表是否存在
    $tables = $db->fetchAll("SHOW TABLES LIKE 'ingredient_categories'");
    if (empty($tables)) {
        echo "<h2>1. 创建ingredient_categories表</h2>";
        
        $createTableSql = "
        CREATE TABLE ingredient_categories (
            id INT AUTO_INCREMENT PRIMARY KEY,
            code VARCHAR(50) DEFAULT '' COMMENT '分类编码',
            name VARCHAR(100) NOT NULL COMMENT '分类名称',
            parent_id INT DEFAULT 0 COMMENT '父分类ID',
            description TEXT DEFAULT '' COMMENT '描述',
            status TINYINT DEFAULT 1 COMMENT '状态：1启用，0禁用',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='食材分类表'";
        
        $db->query($createTableSql);
        echo "<p style='color: green;'>✅ ingredient_categories表创建成功</p>";
        
    } else {
        echo "<h2>1. 检查ingredient_categories表结构</h2>";
        
        $columns = $db->fetchAll("DESCRIBE ingredient_categories");
        $columnNames = array_column($columns, 'Field');
        
        echo "<p>当前字段: " . implode(', ', $columnNames) . "</p>";
        
        // 检查并添加缺失字段
        $requiredColumns = [
            'code' => "VARCHAR(50) DEFAULT '' COMMENT '分类编码'",
            'description' => "TEXT DEFAULT '' COMMENT '描述'",
            'status' => "TINYINT DEFAULT 1 COMMENT '状态：1启用，0禁用'",
            'created_at' => "TIMESTAMP DEFAULT CURRENT_TIMESTAMP",
            'updated_at' => "TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
        ];
        
        foreach ($requiredColumns as $column => $definition) {
            if (!in_array($column, $columnNames)) {
                try {
                    $sql = "ALTER TABLE ingredient_categories ADD COLUMN {$column} {$definition}";
                    $db->query($sql);
                    echo "<p style='color: green;'>✅ 添加字段 {$column} 成功</p>";
                } catch (Exception $e) {
                    echo "<p style='color: red;'>❌ 添加字段 {$column} 失败: " . $e->getMessage() . "</p>";
                }
            } else {
                echo "<p style='color: blue;'>ℹ️ 字段 {$column} 已存在</p>";
            }
        }
        
        // 修改现有字段的默认值
        $fieldsToModify = [
            'code' => "VARCHAR(50) DEFAULT ''",
            'description' => "TEXT DEFAULT ''",
            'status' => "TINYINT DEFAULT 1",
            'parent_id' => "INT DEFAULT 0"
        ];
        
        foreach ($fieldsToModify as $column => $definition) {
            if (in_array($column, $columnNames)) {
                try {
                    $sql = "ALTER TABLE ingredient_categories MODIFY COLUMN {$column} {$definition}";
                    $db->query($sql);
                    echo "<p style='color: green;'>✅ 修改字段 {$column} 默认值成功</p>";
                } catch (Exception $e) {
                    echo "<p style='color: orange;'>⚠️ 修改字段 {$column} 默认值失败: " . $e->getMessage() . "</p>";
                }
            }
        }
    }
    
    echo "<h2>2. 插入默认分类数据</h2>";
    
    // 检查是否已有数据
    $count = $db->fetchOne("SELECT COUNT(*) as count FROM ingredient_categories")['count'];
    
    if ($count == 0) {
        $defaultCategories = [
            ['code' => 'DEFAULT', 'name' => '默认分类', 'parent_id' => 0, 'description' => '默认分类'],
            ['code' => 'VEG', 'name' => '蔬菜类', 'parent_id' => 0, 'description' => '蔬菜类食材'],
            ['code' => 'MEAT', 'name' => '肉类', 'parent_id' => 0, 'description' => '肉类食材'],
            ['code' => 'BEAN', 'name' => '豆制品', 'parent_id' => 0, 'description' => '豆制品类食材'],
            ['code' => 'SPICE', 'name' => '调料', 'parent_id' => 0, 'description' => '调料类食材'],
            ['code' => 'GRAIN', 'name' => '粮食', 'parent_id' => 0, 'description' => '粮食类食材'],
            ['code' => 'FRUIT', 'name' => '水果', 'parent_id' => 0, 'description' => '水果类食材'],
        ];
        
        foreach ($defaultCategories as $category) {
            $category['status'] = 1;
            $id = $db->insert('ingredient_categories', $category);
            echo "<p style='color: green;'>✅ 创建分类: {$category['name']} (ID: {$id})</p>";
        }
    } else {
        echo "<p style='color: blue;'>ℹ️ 已有 {$count} 个分类，跳过默认数据插入</p>";
    }
    
    echo "<h2>3. 验证修复结果</h2>";
    
    $newColumns = $db->fetchAll("DESCRIBE ingredient_categories");
    
    echo "<h4>字段详情：</h4>";
    echo "<table style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
    echo "<tr style='background: #f5f5f5;'>";
    echo "<th style='border: 1px solid #ddd; padding: 6px;'>字段名</th>";
    echo "<th style='border: 1px solid #ddd; padding: 6px;'>类型</th>";
    echo "<th style='border: 1px solid #ddd; padding: 6px;'>允许NULL</th>";
    echo "<th style='border: 1px solid #ddd; padding: 6px;'>默认值</th>";
    echo "<th style='border: 1px solid #ddd; padding: 6px;'>注释</th>";
    echo "</tr>";
    
    foreach ($newColumns as $column) {
        echo "<tr>";
        echo "<td style='border: 1px solid #ddd; padding: 6px;'>{$column['Field']}</td>";
        echo "<td style='border: 1px solid #ddd; padding: 6px;'>{$column['Type']}</td>";
        echo "<td style='border: 1px solid #ddd; padding: 6px;'>{$column['Null']}</td>";
        echo "<td style='border: 1px solid #ddd; padding: 6px;'>" . ($column['Default'] ?? 'NULL') . "</td>";
        echo "<td style='border: 1px solid #ddd; padding: 6px;'>" . ($column['Comment'] ?? '') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h4>现有分类：</h4>";
    $categories = $db->fetchAll("SELECT * FROM ingredient_categories ORDER BY id");
    
    echo "<table style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
    echo "<tr style='background: #f5f5f5;'>";
    echo "<th style='border: 1px solid #ddd; padding: 6px;'>ID</th>";
    echo "<th style='border: 1px solid #ddd; padding: 6px;'>编码</th>";
    echo "<th style='border: 1px solid #ddd; padding: 6px;'>名称</th>";
    echo "<th style='border: 1px solid #ddd; padding: 6px;'>父分类ID</th>";
    echo "<th style='border: 1px solid #ddd; padding: 6px;'>状态</th>";
    echo "</tr>";
    
    foreach ($categories as $category) {
        echo "<tr>";
        echo "<td style='border: 1px solid #ddd; padding: 6px;'>{$category['id']}</td>";
        echo "<td style='border: 1px solid #ddd; padding: 6px;'>{$category['code']}</td>";
        echo "<td style='border: 1px solid #ddd; padding: 6px;'>{$category['name']}</td>";
        echo "<td style='border: 1px solid #ddd; padding: 6px;'>{$category['parent_id']}</td>";
        echo "<td style='border: 1px solid #ddd; padding: 6px;'>" . ($category['status'] ? '启用' : '禁用') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>4. 测试插入数据</h2>";
    
    // 测试插入一条分类数据
    try {
        $testData = [
            'code' => 'TEST_' . time(),
            'name' => '测试分类_' . date('H:i:s'),
            'parent_id' => 0,
            'description' => '测试分类描述',
            'status' => 1
        ];
        
        $testId = $db->insert('ingredient_categories', $testData);
        
        if ($testId) {
            echo "<p style='color: green;'>✅ 测试插入成功: ID={$testId}</p>";
            
            // 删除测试数据
            $db->query("DELETE FROM ingredient_categories WHERE id = ?", [$testId]);
            echo "<p style='color: blue;'>ℹ️ 测试数据已清理</p>";
        } else {
            echo "<p style='color: red;'>❌ 测试插入失败</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ 测试插入异常: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>✅ 修复完成！</h2>";
    echo "<p><a href='../test/test-category-mapping.php'>重新测试分类映射功能</a></p>";
    echo "<p><a href='../modules/purchase/index.php?action=import'>重新测试Excel导入</a></p>";
    echo "<p><a href='../test/view-import-log.php?clear_log=1'>清空日志重新测试</a></p>";
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>❌ 修复失败</h2>";
    echo "<p style='color: red;'>错误: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
