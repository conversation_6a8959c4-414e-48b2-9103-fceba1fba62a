<?php
/**
 * 下载食材导入模板
 */

// 设置 CSV 文件头
header('Content-Type: text/csv; charset=UTF-8');
header('Content-Disposition: attachment; filename="食材导入模板.csv"');
header('Cache-Control: no-cache, must-revalidate');

// 输出 BOM 以支持中文
echo "\xEF\xBB\xBF";

// 创建文件句柄
$output = fopen('php://output', 'w');

// 写入标题行
$headers = [
    '食材名称',
    '分类ID',
    '单位',
    '单价',
    '最低库存'
];
fputcsv($output, $headers);

// 写入示例数据
$sampleData = [
    ['白菜', '1', '斤', '2.50', '10'],
    ['土豆', '1', '斤', '3.00', '20'],
    ['胡萝卜', '1', '斤', '4.00', '15'],
    ['猪肉', '2', '斤', '25.00', '5'],
    ['牛肉', '2', '斤', '35.00', '3'],
    ['鸡肉', '2', '斤', '18.00', '8'],
    ['草鱼', '3', '斤', '12.00', '5'],
    ['带鱼', '3', '斤', '15.00', '3'],
    ['大米', '4', '斤', '5.00', '50'],
    ['面粉', '4', '斤', '4.50', '30'],
    ['食用油', '5', '升', '8.00', '10'],
    ['盐', '5', '包', '3.00', '20'],
    ['生抽', '5', '瓶', '6.00', '5'],
    ['老抽', '5', '瓶', '7.00', '3'],
    ['料酒', '5', '瓶', '8.00', '5'],
    ['白糖', '5', '包', '4.00', '10'],
    ['鸡蛋', '6', '斤', '8.00', '20'],
    ['牛奶', '6', '盒', '3.50', '30'],
    ['豆腐', '6', '块', '2.00', '10'],
    ['豆浆', '6', '杯', '2.50', '15']
];

foreach ($sampleData as $row) {
    fputcsv($output, $row);
}

// 关闭文件句柄
fclose($output);
?>
