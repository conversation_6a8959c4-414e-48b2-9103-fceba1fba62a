<!-- 出库管理模块侧边栏 -->
<div class="module-sidebar">
    <div class="sidebar-header">
        <h3>
            <i class="fas fa-arrow-up"></i>
            出库管理
        </h3>
    </div>

    <nav class="sidebar-nav">
        <a href="index.php" class="nav-item <?= !isset($_GET['action']) || $_GET['action'] === 'index' ? 'active' : '' ?>">
            <i class="fas fa-list"></i>
            出库记录
        </a>
        
        <a href="index.php?action=create" class="nav-item <?= isset($_GET['action']) && $_GET['action'] === 'create' ? 'active' : '' ?>">
            <i class="fas fa-plus"></i>
            新增出库
        </a>
        
        <a href="index.php?action=batch_create" class="nav-item <?= isset($_GET['action']) && $_GET['action'] === 'batch_create' ? 'active' : '' ?>">
            <i class="fas fa-list-alt"></i>
            批量出库
        </a>
        
        <div class="nav-divider"></div>
        
        <a href="../inventory/index.php" class="nav-item">
            <i class="fas fa-search"></i>
            库存查询
        </a>
        
        <a href="../inbound/index.php" class="nav-item">
            <i class="fas fa-arrow-down"></i>
            入库管理
        </a>
        
        <a href="../dashboard/index.php" class="nav-item">
            <i class="fas fa-tachometer-alt"></i>
            返回首页
        </a>
    </nav>
</div>

<style>
.module-sidebar {
    width: 250px;
    background: #2d3748;
    color: white;
    min-height: 100vh;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 1000;
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid #4a5568;
    background: #1a202c;
}

.sidebar-header h3 {
    margin: 0;
    color: white;
    display: flex;
    align-items: center;
    gap: 10px;
}

.sidebar-nav {
    padding: 20px 0;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 20px;
    color: #e2e8f0;
    text-decoration: none;
    transition: all 0.2s;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
}

.nav-item:hover {
    background: #4a5568;
    color: white;
    text-decoration: none;
}

.nav-item.active {
    background: #4299e1;
    color: white;
}

.nav-item i {
    width: 20px;
    text-align: center;
}

.nav-divider {
    height: 1px;
    background: #4a5568;
    margin: 15px 20px;
}

@media (max-width: 768px) {
    .module-sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s;
    }
    
    .module-sidebar.open {
        transform: translateX(0);
    }
}
</style>