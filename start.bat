@echo off
setlocal enabledelayedexpansion

title Canteen Management System

echo.
echo ================================================================
echo           School Canteen Management System
echo                    Quick Start
echo ================================================================
echo.

REM Database configuration
set DB_HOST=************
set DB_PORT=3306
set DB_USER=sc
set DB_PASS=pw5K4SsM7kZsjdxy
set DB_NAME=sc

echo [1/6] Checking environment...

REM Check PHP
php --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] PHP not found. Please install PHP 7.2+ or XAMPP/WAMP
    pause
    exit /b 1
)
echo [OK] PHP found

REM Check Composer
composer --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Composer not found. Download from https://getcomposer.org/
    pause
    exit /b 1
)
echo [OK] Composer found

REM Check MySQL client
mysql --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] MySQL client not found
    pause
    exit /b 1
)
echo [OK] MySQL client found

echo.
echo [2/6] Testing database connection...
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASS% -e "SELECT 1;" %DB_NAME% >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Database connection failed
    echo Host: %DB_HOST%:%DB_PORT%
    echo Database: %DB_NAME%
    pause
    exit /b 1
)
echo [OK] Database connection successful

echo.
echo [3/6] Setting up Laravel project...

if not exist "composer.json" (
    echo Creating Laravel 8.x project...
    composer create-project laravel/laravel . "^8.0" --prefer-dist --quiet
    if errorlevel 1 (
        echo [ERROR] Laravel project creation failed
        pause
        exit /b 1
    )
    
    echo Installing dependencies...
    composer require "laravel/sanctum:^2.15" --quiet
    composer require "spatie/laravel-permission:^4.4" --quiet
    
    echo [OK] Laravel project created
) else (
    echo [OK] Laravel project exists
)

echo.
echo [4/6] Configuring environment...

if not exist ".env" (
    if exist ".env.example" (
        copy .env.example .env >nul
    ) else (
        echo APP_NAME="Canteen Management System" > .env
        echo APP_ENV=local >> .env
        echo APP_KEY= >> .env
        echo APP_DEBUG=true >> .env
        echo APP_URL=http://localhost:8000 >> .env
        echo. >> .env
        echo DB_CONNECTION=mysql >> .env
        echo DB_HOST=%DB_HOST% >> .env
        echo DB_PORT=%DB_PORT% >> .env
        echo DB_DATABASE=%DB_NAME% >> .env
        echo DB_USERNAME=%DB_USER% >> .env
        echo DB_PASSWORD=%DB_PASS% >> .env
        echo DB_CHARSET=utf8 >> .env
        echo DB_COLLATION=utf8_unicode_ci >> .env
    )
    
    php artisan key:generate --quiet
    echo [OK] Environment configured
) else (
    echo [OK] Environment file exists
)

REM Update database configuration
powershell -Command "(Get-Content .env) -replace 'DB_HOST=.*', 'DB_HOST=%DB_HOST%' | Set-Content .env" >nul 2>&1
powershell -Command "(Get-Content .env) -replace 'DB_DATABASE=.*', 'DB_DATABASE=%DB_NAME%' | Set-Content .env" >nul 2>&1
powershell -Command "(Get-Content .env) -replace 'DB_USERNAME=.*', 'DB_USERNAME=%DB_USER%' | Set-Content .env" >nul 2>&1
powershell -Command "(Get-Content .env) -replace 'DB_PASSWORD=.*', 'DB_PASSWORD=%DB_PASS%' | Set-Content .env" >nul 2>&1

echo.
echo [5/6] Setting up database...

REM Configure MySQL 5.6 compatibility
if exist "app\Providers\AppServiceProvider.php" (
    echo Configuring MySQL 5.6 compatibility...
    
    echo ^<?php > temp_provider.php
    echo. >> temp_provider.php
    echo namespace App\Providers; >> temp_provider.php
    echo. >> temp_provider.php
    echo use Illuminate\Support\ServiceProvider; >> temp_provider.php
    echo use Illuminate\Support\Facades\Schema; >> temp_provider.php
    echo. >> temp_provider.php
    echo class AppServiceProvider extends ServiceProvider >> temp_provider.php
    echo { >> temp_provider.php
    echo     public function register^(^) >> temp_provider.php
    echo     { >> temp_provider.php
    echo         // >> temp_provider.php
    echo     } >> temp_provider.php
    echo. >> temp_provider.php
    echo     public function boot^(^) >> temp_provider.php
    echo     { >> temp_provider.php
    echo         Schema::defaultStringLength^(191^); >> temp_provider.php
    echo     } >> temp_provider.php
    echo } >> temp_provider.php
    
    move temp_provider.php app\Providers\AppServiceProvider.php >nul
)

REM Initialize database if empty
mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASS% -e "SHOW TABLES;" %DB_NAME% > temp_tables.txt 2>&1
set table_count=0
for /f %%i in ('type temp_tables.txt ^| find /c /v ""') do set table_count=%%i
del temp_tables.txt >nul 2>&1

if %table_count% LEQ 1 (
    if exist "scripts\database\mysql56_compatible.sql" (
        echo Creating database tables...
        mysql -h %DB_HOST% -P %DB_PORT% -u %DB_USER% -p%DB_PASS% %DB_NAME% < scripts\database\mysql56_compatible.sql
        if errorlevel 1 (
            echo [WARNING] Database setup failed, will use Laravel migrations
        ) else (
            echo [OK] Database tables created
        )
    )
) else (
    echo [OK] Database tables exist
)

echo.
echo [6/6] Starting development server...

php artisan config:clear >nul 2>&1
php artisan route:clear >nul 2>&1
php artisan cache:clear >nul 2>&1
php artisan storage:link >nul 2>&1

echo.
echo ================================================================
echo                    System Ready!
echo ================================================================
echo.
echo Access URL: http://localhost:8000
echo Database: %DB_HOST%:%DB_PORT%/%DB_NAME%
echo.
echo Default accounts:
echo Admin: <EMAIL> / password
echo Auditor: <EMAIL> / password
echo Warehouse: <EMAIL> / password
echo.
echo Starting Laravel development server...
echo Press Ctrl+C to stop the server
echo ================================================================
echo.

php artisan serve
