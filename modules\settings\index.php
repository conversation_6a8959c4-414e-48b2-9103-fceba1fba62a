<?php
/**
 * 系统设置模块入口
 */

require_once dirname(__DIR__, 2) . '/includes/BaseController.php';

class SettingsController extends BaseController
{
    protected function init()
    {
        // 确保用户已登录且有权限
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        // 检查是否登录
        if (empty($_SESSION['user_id'])) {
            header('Location: ../users/index.php?action=login');
            exit;
        }
    }

    public function handleRequest()
    {
        $action = $this->request['action'];
        
        switch ($action) {
            case 'general':
                return $this->general();
            case 'security':
                return $this->security();
            case 'backup':
                return $this->backup();
            case 'logs':
                return $this->logs();
            case 'about':
                return $this->about();
            default:
                return $this->index();
        }
    }

    /**
     * 设置首页 - 显示设置导航
     */
    private function index()
    {
        $this->setTemplateData([
            'page_title' => '系统设置',
        ]);
        
        $this->render('index-template.php');
    }

    /**
     * 基本设置
     */
    private function general()
    {
        if ($this->request['method'] === 'POST') {
            try {
                $this->validateCSRF();
                $this->requirePermission('system.manage');
                
                $settings = [
                    'site_name' => trim($this->request['post']['site_name'] ?? ''),
                    'site_description' => trim($this->request['post']['site_description'] ?? ''),
                    'admin_email' => trim($this->request['post']['admin_email'] ?? ''),
                    'timezone' => trim($this->request['post']['timezone'] ?? 'Asia/Shanghai'),
                    'date_format' => trim($this->request['post']['date_format'] ?? 'Y-m-d'),
                    'time_format' => trim($this->request['post']['time_format'] ?? 'H:i:s'),
                    'currency_symbol' => trim($this->request['post']['currency_symbol'] ?? '¥'),
                    'items_per_page' => intval($this->request['post']['items_per_page'] ?? 20),
                ];

                // 验证必填项
                if (empty($settings['site_name'])) {
                    throw new Exception('网站名称不能为空');
                }
                if (!filter_var($settings['admin_email'], FILTER_VALIDATE_EMAIL)) {
                    throw new Exception('管理员邮箱格式不正确');
                }

                // 保存设置
                $this->saveSettings($settings);
                
                header('Location: index.php?action=general&success=1');
                exit;
                
            } catch (Exception $e) {
                $this->setTemplateData(['error' => $e->getMessage()]);
            }
        }

        // 加载当前设置
        $settings = $this->loadSettings();
        
        $this->setTemplateData([
            'page_title' => '基本设置',
            'settings' => $settings,
        ]);
        
        $this->render('general-template.php');
    }

    /**
     * 安全设置
     */
    private function security()
    {
        if ($this->request['method'] === 'POST') {
            try {
                $this->requirePermission('system.manage');
                
                $settings = [
                    'session_timeout' => intval($this->request['post']['session_timeout'] ?? 3600),
                    'password_min_length' => intval($this->request['post']['password_min_length'] ?? 6),
                    'login_attempts' => intval($this->request['post']['login_attempts'] ?? 5),
                    'lockout_duration' => intval($this->request['post']['lockout_duration'] ?? 900),
                    'force_https' => intval($this->request['post']['force_https'] ?? 0),
                    'enable_2fa' => intval($this->request['post']['enable_2fa'] ?? 0),
                ];

                $this->saveSettings($settings);
                
                header('Location: index.php?action=security&success=1');
                exit;
                
            } catch (Exception $e) {
                $this->setTemplateData(['error' => $e->getMessage()]);
            }
        }

        $settings = $this->loadSettings();
        
        $this->setTemplateData([
            'page_title' => '安全设置',
            'settings' => $settings,
        ]);
        
        $this->render('security-template.php');
    }

    /**
     * 备份管理
     */
    private function backup()
    {
        $operation = trim($this->request['get']['operation'] ?? $this->request['post']['operation'] ?? '');
        
        if ($operation === 'create' && $this->request['method'] === 'POST') {
            try {
                $this->requirePermission('system.manage');
                
                $backupFile = $this->createBackup();
                
                header('Location: index.php?action=backup&success=1&file=' . urlencode($backupFile));
                exit;
                
            } catch (Exception $e) {
                $this->setTemplateData(['error' => $e->getMessage()]);
            }
        }

        // 获取备份文件列表
        $backups = $this->getBackupList();
        
        $this->setTemplateData([
            'page_title' => '备份管理',
            'backups' => $backups,
        ]);
        
        $this->render('backup-template.php');
    }

    /**
     * 系统日志
     */
    private function logs()
    {
        $page = intval($this->request['get']['page'] ?? 1);
        $limit = 50;
        $offset = ($page - 1) * $limit;
        
        // 获取日志文件
        $logFile = dirname(__DIR__, 2) . '/logs/system.log';
        $logs = [];
        
        if (file_exists($logFile)) {
            $lines = file($logFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            $lines = array_reverse($lines); // 最新的在前
            $logs = array_slice($lines, $offset, $limit);
        }
        
        $this->setTemplateData([
            'page_title' => '系统日志',
            'logs' => $logs,
            'current_page' => $page,
        ]);
        
        $this->render('logs-template.php');
    }

    /**
     * 关于系统
     */
    private function about()
    {
        $systemInfo = [
            'version' => '1.0.0',
            'php_version' => PHP_VERSION,
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'database_version' => $this->getDatabaseVersion(),
            'upload_max_filesize' => ini_get('upload_max_filesize'),
            'post_max_size' => ini_get('post_max_size'),
            'memory_limit' => ini_get('memory_limit'),
            'max_execution_time' => ini_get('max_execution_time'),
        ];
        
        $this->setTemplateData([
            'page_title' => '关于系统',
            'system_info' => $systemInfo,
        ]);
        
        $this->render('about-template.php');
    }

    /**
     * 保存设置到配置文件
     */
    private function saveSettings($settings)
    {
        $configFile = dirname(__DIR__, 2) . '/config/settings.php';
        $configDir = dirname($configFile);
        
        if (!is_dir($configDir)) {
            mkdir($configDir, 0755, true);
        }
        
        $existingSettings = [];
        if (file_exists($configFile)) {
            $existingSettings = include $configFile;
        }
        
        $newSettings = array_merge($existingSettings, $settings);
        
        $content = "<?php\n/**\n * 系统设置配置文件\n * 自动生成，请勿手动编辑\n */\n\nreturn " . var_export($newSettings, true) . ";\n";
        
        if (file_put_contents($configFile, $content) === false) {
            throw new Exception('保存设置失败，请检查文件权限');
        }
    }

    /**
     * 加载设置
     */
    private function loadSettings()
    {
        $configFile = dirname(__DIR__, 2) . '/config/settings.php';
        
        $defaults = [
            'site_name' => '学校食堂管理系统',
            'site_description' => '专业的学校食堂管理解决方案',
            'admin_email' => '<EMAIL>',
            'timezone' => 'Asia/Shanghai',
            'date_format' => 'Y-m-d',
            'time_format' => 'H:i:s',
            'currency_symbol' => '¥',
            'items_per_page' => 20,
            'session_timeout' => 3600,
            'password_min_length' => 6,
            'login_attempts' => 5,
            'lockout_duration' => 900,
            'force_https' => 0,
            'enable_2fa' => 0,
        ];
        
        if (file_exists($configFile)) {
            $settings = include $configFile;
            return array_merge($defaults, $settings);
        }
        
        return $defaults;
    }

    /**
     * 创建数据库备份
     */
    private function createBackup()
    {
        $backupDir = dirname(__DIR__, 2) . '/backups';
        if (!is_dir($backupDir)) {
            mkdir($backupDir, 0755, true);
        }
        
        $filename = 'backup_' . date('Y-m-d_H-i-s') . '.sql';
        $filepath = $backupDir . '/' . $filename;
        
        // 这里应该实现数据库备份逻辑
        // 简化版本，实际应该使用 mysqldump 或类似工具
        $content = "-- 数据库备份文件\n-- 生成时间: " . date('Y-m-d H:i:s') . "\n\n";
        $content .= "-- 此为示例备份文件\n";
        
        if (file_put_contents($filepath, $content) === false) {
            throw new Exception('创建备份文件失败');
        }
        
        return $filename;
    }

    /**
     * 获取备份文件列表
     */
    private function getBackupList()
    {
        $backupDir = dirname(__DIR__, 2) . '/backups';
        $backups = [];
        
        if (is_dir($backupDir)) {
            $files = glob($backupDir . '/backup_*.sql');
            foreach ($files as $file) {
                $backups[] = [
                    'name' => basename($file),
                    'size' => filesize($file),
                    'created_at' => date('Y-m-d H:i:s', filemtime($file)),
                ];
            }
            
            // 按创建时间倒序排列
            usort($backups, function($a, $b) {
                return strcmp($b['created_at'], $a['created_at']);
            });
        }
        
        return $backups;
    }

    /**
     * 获取数据库版本
     */
    private function getDatabaseVersion()
    {
        try {
            $result = $this->db->fetchOne('SELECT VERSION() as version');
            return $result['version'] ?? 'Unknown';
        } catch (Exception $e) {
            return 'Unknown';
        }
    }
}

// 处理请求
$controller = new SettingsController();
$controller->handleRequest();
