<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试真实采购单数据</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .data-table { width: 100%; border-collapse: collapse; margin: 10px 0; font-size: 12px; }
        .data-table th, .data-table td { border: 1px solid #ddd; padding: 6px; text-align: left; }
        .data-table th { background: #f5f5f5; }
        .btn { display: inline-block; padding: 8px 16px; background: #007cba; color: white; text-decoration: none; border-radius: 4px; margin: 5px; }
        .query-box { background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 12px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>🔍 调试真实采购单数据</h1>
    
    <?php
    require_once '../includes/Database.php';
    
    try {
        $db = Database::getInstance();
        
        echo "<div class='test-section'>";
        echo "<h2>📊 采购单表数据检查</h2>";
        
        // 1. 检查采购单总数
        $totalOrders = $db->fetchOne("SELECT COUNT(*) as count FROM purchase_orders")['count'];
        echo "<p class='info'>采购单总数: <strong>{$totalOrders}</strong></p>";
        
        if ($totalOrders > 0) {
            // 2. 按状态统计
            $statusStats = $db->fetchAll("
                SELECT status, COUNT(*) as count 
                FROM purchase_orders 
                GROUP BY status 
                ORDER BY status
            ");
            
            echo "<h4>按状态统计：</h4>";
            echo "<table class='data-table'>";
            echo "<tr><th>状态值</th><th>数量</th><th>状态说明</th></tr>";
            foreach ($statusStats as $stat) {
                $statusName = '';
                switch ($stat['status']) {
                    case 1: $statusName = '待确认'; break;
                    case 2: $statusName = '已确认'; break;
                    case 4: $statusName = '已完成'; break;
                    case 5: $statusName = '已取消'; break;
                    default: $statusName = '未知状态';
                }
                
                $rowClass = ($stat['status'] == 1 || $stat['status'] == 2) ? 'success' : '';
                echo "<tr class='{$rowClass}'>";
                echo "<td>{$stat['status']}</td>";
                echo "<td>{$stat['count']}</td>";
                echo "<td>{$statusName}</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            // 3. 显示状态为1或2的采购单
            $validOrders = $db->fetchAll("
                SELECT po.*, s.name as supplier_name
                FROM purchase_orders po
                LEFT JOIN suppliers s ON po.supplier_id = s.id
                WHERE po.status IN (1, 2)
                ORDER BY po.created_at DESC
            ");
            
            echo "<h4>状态为1或2的采购单 (" . count($validOrders) . "个)：</h4>";
            if (!empty($validOrders)) {
                echo "<table class='data-table'>";
                echo "<tr><th>ID</th><th>订单号</th><th>供应商</th><th>状态</th><th>订单日期</th><th>总金额</th><th>创建时间</th></tr>";
                foreach ($validOrders as $order) {
                    echo "<tr>";
                    echo "<td>{$order['id']}</td>";
                    echo "<td>{$order['order_number']}</td>";
                    echo "<td>{$order['supplier_name']}</td>";
                    echo "<td>{$order['status']}</td>";
                    echo "<td>{$order['order_date']}</td>";
                    echo "<td>" . number_format($order['total_amount'] ?? 0, 2) . "</td>";
                    echo "<td>{$order['created_at']}</td>";
                    echo "</tr>";
                }
                echo "</table>";
            } else {
                echo "<p class='warning'>⚠️ 没有找到状态为1或2的采购单</p>";
                echo "<p>请先在采购管理中创建采购单并确认状态</p>";
            }
        }
        echo "</div>";
        
        echo "<div class='test-section'>";
        echo "<h2>📦 采购单商品数据检查</h2>";
        
        // 检查采购单商品
        $totalItems = $db->fetchOne("SELECT COUNT(*) as count FROM purchase_order_items")['count'];
        echo "<p class='info'>采购单商品总数: <strong>{$totalItems}</strong></p>";
        
        if ($totalItems > 0) {
            // 显示采购单商品
            $items = $db->fetchAll("
                SELECT poi.*, po.order_number, po.status as order_status, i.name as ingredient_name, i.unit
                FROM purchase_order_items poi
                LEFT JOIN purchase_orders po ON poi.order_id = po.id
                LEFT JOIN ingredients i ON poi.ingredient_id = i.id
                WHERE po.status IN (1, 2)
                ORDER BY poi.order_id DESC, poi.id ASC
                LIMIT 20
            ");
            
            echo "<h4>状态为1或2的采购单商品 (" . count($items) . "个)：</h4>";
            if (!empty($items)) {
                echo "<table class='data-table'>";
                echo "<tr><th>订单号</th><th>订单状态</th><th>食材名称</th><th>单位</th><th>数量</th><th>单价</th><th>备注</th></tr>";
                foreach ($items as $item) {
                    echo "<tr>";
                    echo "<td>{$item['order_number']}</td>";
                    echo "<td>{$item['order_status']}</td>";
                    echo "<td>{$item['ingredient_name']}</td>";
                    echo "<td>{$item['unit']}</td>";
                    echo "<td>{$item['quantity']}</td>";
                    echo "<td>" . number_format($item['unit_price'], 2) . "</td>";
                    echo "<td>{$item['notes']}</td>";
                    echo "</tr>";
                }
                echo "</table>";
            } else {
                echo "<p class='warning'>⚠️ 没有找到状态为1或2的采购单商品</p>";
            }
        }
        echo "</div>";
        
        echo "<div class='test-section'>";
        echo "<h2>🔍 入库模块查询测试</h2>";
        
        // 测试入库模块使用的查询
        echo "<h4>使用的查询语句：</h4>";
        echo "<div class='query-box'>";
        echo "SELECT <br>";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;po.id,<br>";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;po.order_number,<br>";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;po.supplier_id,<br>";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;s.name as supplier_name,<br>";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;po.order_date,<br>";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;COALESCE(po.total_amount, po.order_amount, 0) as order_amount,<br>";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;poi.ingredient_id,<br>";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;i.name as ingredient_name,<br>";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;i.unit,<br>";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;poi.quantity,<br>";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;poi.unit_price,<br>";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;poi.notes as purpose<br>";
        echo "FROM purchase_orders po<br>";
        echo "LEFT JOIN purchase_order_items poi ON po.id = poi.order_id<br>";
        echo "LEFT JOIN suppliers s ON po.supplier_id = s.id<br>";
        echo "LEFT JOIN ingredients i ON poi.ingredient_id = i.id<br>";
        echo "WHERE po.status IN (1, 2)<br>";
        echo "AND poi.ingredient_id IS NOT NULL<br>";
        echo "ORDER BY po.order_date DESC, po.order_number ASC";
        echo "</div>";
        
        $purchaseOrders = $db->fetchAll("
            SELECT 
                po.id,
                po.order_number,
                po.supplier_id,
                s.name as supplier_name,
                po.order_date,
                COALESCE(po.total_amount, po.order_amount, 0) as order_amount,
                poi.ingredient_id,
                i.name as ingredient_name,
                i.unit,
                poi.quantity,
                poi.unit_price,
                poi.notes as purpose
            FROM purchase_orders po
            LEFT JOIN purchase_order_items poi ON po.id = poi.order_id
            LEFT JOIN suppliers s ON po.supplier_id = s.id
            LEFT JOIN ingredients i ON poi.ingredient_id = i.id
            WHERE po.status IN (1, 2) 
            AND poi.ingredient_id IS NOT NULL
            ORDER BY po.order_date DESC, po.order_number ASC
        ");
        
        echo "<p class='info'>查询结果: <strong>" . count($purchaseOrders) . "</strong> 条记录</p>";
        
        if (!empty($purchaseOrders)) {
            echo "<h4>查询结果（前10条）：</h4>";
            echo "<table class='data-table'>";
            echo "<tr><th>订单号</th><th>供应商</th><th>食材</th><th>单位</th><th>数量</th><th>单价</th><th>用途</th></tr>";
            foreach (array_slice($purchaseOrders, 0, 10) as $order) {
                echo "<tr>";
                echo "<td>{$order['order_number']}</td>";
                echo "<td>{$order['supplier_name']}</td>";
                echo "<td>{$order['ingredient_name']}</td>";
                echo "<td>{$order['unit']}</td>";
                echo "<td>{$order['quantity']}</td>";
                echo "<td>" . number_format($order['unit_price'], 2) . "</td>";
                echo "<td>{$order['purpose']}</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            // 按采购单分组显示
            $groupedOrders = [];
            foreach ($purchaseOrders as $order) {
                $key = $order['id'] . '_' . $order['order_number'];
                if (!isset($groupedOrders[$key])) {
                    $groupedOrders[$key] = [
                        'order_info' => $order,
                        'items' => []
                    ];
                }
                $groupedOrders[$key]['items'][] = $order;
            }
            
            echo "<h4>按采购单分组（" . count($groupedOrders) . "个采购单）：</h4>";
            foreach ($groupedOrders as $key => $group) {
                $orderInfo = $group['order_info'];
                echo "<div style='background: #f0f8ff; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
                echo "<strong>{$orderInfo['order_number']}</strong> - {$orderInfo['supplier_name']} ({$orderInfo['order_date']}) - 共" . count($group['items']) . "种商品";
                echo "<ul>";
                foreach ($group['items'] as $item) {
                    echo "<li>{$item['ingredient_name']} - {$item['quantity']}{$item['unit']} - ¥{$item['unit_price']}/{$item['unit']}</li>";
                }
                echo "</ul>";
                echo "</div>";
            }
            
        } else {
            echo "<p class='error'>❌ 查询结果为空</p>";
            echo "<h4>可能的原因：</h4>";
            echo "<ul>";
            echo "<li>没有状态为1或2的采购单</li>";
            echo "<li>采购单没有关联的商品</li>";
            echo "<li>食材或供应商数据缺失</li>";
            echo "</ul>";
        }
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='test-section'>";
        echo "<h2 class='error'>❌ 数据库连接失败</h2>";
        echo "<p class='error'>错误信息: " . $e->getMessage() . "</p>";
        echo "</div>";
    }
    ?>
    
    <div class="test-section">
        <h2>🛠️ 解决方案</h2>
        
        <h4>如果查询结果为空，请按以下步骤操作：</h4>
        <ol>
            <li><strong>创建采购单</strong>：
                <a href="../modules/purchase/index.php?action=create" class="btn">创建新采购单</a>
            </li>
            <li><strong>确认采购单状态</strong>：确保采购单状态为1（待确认）或2（已确认）</li>
            <li><strong>添加商品到采购单</strong>：确保采购单包含商品项目</li>
            <li><strong>检查数据完整性</strong>：确保供应商和食材数据存在</li>
        </ol>
        
        <h4>测试入库功能：</h4>
        <p>
            <a href="../modules/inbound/index.php?action=create" class="btn">测试入库页面</a>
            <a href="../modules/purchase/index.php" class="btn">查看采购单列表</a>
        </p>
    </div>
    
    <p><a href="../modules/inbound/index.php">返回入库管理</a></p>
</body>
</html>
