/**
 * 通用JavaScript库
 */

// 全局应用对象
window.App = {
    config: {},
    modules: {},
    utils: {},
    ui: {}
};

/**
 * 工具函数
 */
App.utils = {
    /**
     * 格式化货币
     */
    formatCurrency: function(amount, symbol = '¥') {
        return symbol + parseFloat(amount).toLocaleString('zh-CN', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
    },

    /**
     * 格式化百分比
     */
    formatPercent: function(value, decimals = 1) {
        return parseFloat(value).toFixed(decimals) + '%';
    },

    /**
     * 防抖函数
     */
    debounce: function(func, wait, immediate) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                timeout = null;
                if (!immediate) func.apply(this, args);
            };
            const callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func.apply(this, args);
        };
    },

    /**
     * 节流函数
     */
    throttle: function(func, limit) {
        let inThrottle;
        return function(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },

    /**
     * 深度克隆对象
     */
    deepClone: function(obj) {
        if (obj === null || typeof obj !== 'object') return obj;
        if (obj instanceof Date) return new Date(obj.getTime());
        if (obj instanceof Array) return obj.map(item => this.deepClone(item));
        if (typeof obj === 'object') {
            const clonedObj = {};
            for (let key in obj) {
                if (obj.hasOwnProperty(key)) {
                    clonedObj[key] = this.deepClone(obj[key]);
                }
            }
            return clonedObj;
        }
    },

    /**
     * 生成随机ID
     */
    generateId: function(length = 8) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    },

    /**
     * 检查是否为移动设备
     */
    isMobile: function() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    },

    /**
     * 获取URL参数
     */
    getUrlParam: function(name) {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get(name);
    },

    /**
     * 设置URL参数
     */
    setUrlParam: function(name, value) {
        const url = new URL(window.location);
        url.searchParams.set(name, value);
        window.history.pushState({}, '', url);
    },

    /**
     * 复制到剪贴板
     */
    copyToClipboard: function(text) {
        if (navigator.clipboard) {
            return navigator.clipboard.writeText(text).then(() => {
                App.ui.showNotification('已复制到剪贴板', 'success');
                return true;
            }).catch(() => {
                return this.fallbackCopyToClipboard(text);
            });
        } else {
            return this.fallbackCopyToClipboard(text);
        }
    },

    /**
     * 降级复制方案
     */
    fallbackCopyToClipboard: function(text) {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        try {
            const successful = document.execCommand('copy');
            if (successful) {
                App.ui.showNotification('已复制到剪贴板', 'success');
            }
            return successful;
        } catch (err) {
            console.error('复制失败:', err);
            return false;
        } finally {
            document.body.removeChild(textArea);
        }
    }
};

/**
 * UI组件
 */
App.ui = {
    /**
     * 显示通知
     */
    showNotification: function(message, type = 'info', duration = 3000) {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} notification fade-in`;
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" onclick="this.parentElement.remove()">&times;</button>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.classList.add('fade-out');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, duration);
    },

    /**
     * 显示确认对话框
     */
    confirm: function(message, callback) {
        if (confirm(message)) {
            if (typeof callback === 'function') {
                callback();
            }
            return true;
        }
        return false;
    },

    /**
     * 显示加载状态
     */
    showLoading: function(element) {
        if (typeof element === 'string') {
            element = document.querySelector(element);
        }
        if (element) {
            element.classList.add('loading');
        }
    },

    /**
     * 隐藏加载状态
     */
    hideLoading: function(element) {
        if (typeof element === 'string') {
            element = document.querySelector(element);
        }
        if (element) {
            element.classList.remove('loading');
        }
    },

    /**
     * 创建模态框
     */
    createModal: function(content, options = {}) {
        const defaults = {
            title: '',
            size: 'medium',
            closable: true,
            backdrop: true
        };
        
        const config = Object.assign(defaults, options);
        
        // 创建背景
        const backdrop = document.createElement('div');
        backdrop.className = 'modal-backdrop';
        
        // 创建模态框
        const modal = document.createElement('div');
        modal.className = `modal modal-${config.size}`;
        modal.innerHTML = `
            <div class="modal-header">
                ${config.title ? `<h5 class="modal-title">${config.title}</h5>` : ''}
                ${config.closable ? '<button type="button" class="btn-close" data-dismiss="modal">&times;</button>' : ''}
            </div>
            <div class="modal-body">
                ${content}
            </div>
        `;
        
        // 添加到页面
        document.body.appendChild(backdrop);
        document.body.appendChild(modal);
        
        // 绑定关闭事件
        const closeModal = () => {
            backdrop.remove();
            modal.remove();
        };
        
        if (config.backdrop) {
            backdrop.addEventListener('click', closeModal);
        }
        
        if (config.closable) {
            modal.querySelector('.btn-close').addEventListener('click', closeModal);
        }
        
        return {
            element: modal,
            close: closeModal
        };
    },

    /**
     * 创建工具提示
     */
    createTooltip: function(element, text, position = 'top') {
        if (typeof element === 'string') {
            element = document.querySelector(element);
        }
        
        if (!element) return;
        
        let tooltip;
        
        const showTooltip = () => {
            tooltip = document.createElement('div');
            tooltip.className = 'tooltip';
            tooltip.textContent = text;
            document.body.appendChild(tooltip);
            
            const rect = element.getBoundingClientRect();
            const tooltipRect = tooltip.getBoundingClientRect();
            
            let left, top;
            
            switch (position) {
                case 'top':
                    left = rect.left + (rect.width / 2) - (tooltipRect.width / 2);
                    top = rect.top - tooltipRect.height - 8;
                    break;
                case 'bottom':
                    left = rect.left + (rect.width / 2) - (tooltipRect.width / 2);
                    top = rect.bottom + 8;
                    break;
                case 'left':
                    left = rect.left - tooltipRect.width - 8;
                    top = rect.top + (rect.height / 2) - (tooltipRect.height / 2);
                    break;
                case 'right':
                    left = rect.right + 8;
                    top = rect.top + (rect.height / 2) - (tooltipRect.height / 2);
                    break;
            }
            
            tooltip.style.left = left + 'px';
            tooltip.style.top = top + 'px';
            
            setTimeout(() => tooltip.classList.add('show'), 10);
        };
        
        const hideTooltip = () => {
            if (tooltip) {
                tooltip.classList.remove('show');
                setTimeout(() => {
                    if (tooltip.parentNode) {
                        tooltip.parentNode.removeChild(tooltip);
                    }
                }, 200);
            }
        };
        
        element.addEventListener('mouseenter', showTooltip);
        element.addEventListener('mouseleave', hideTooltip);
        
        return {
            destroy: () => {
                element.removeEventListener('mouseenter', showTooltip);
                element.removeEventListener('mouseleave', hideTooltip);
                hideTooltip();
            }
        };
    }
};

/**
 * AJAX请求封装
 */
App.ajax = {
    /**
     * 发送GET请求
     */
    get: function(url, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const fullUrl = queryString ? `${url}?${queryString}` : url;
        
        return fetch(fullUrl, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        }).then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        });
    },

    /**
     * 发送POST请求
     */
    post: function(url, data = {}) {
        const formData = new FormData();
        for (let key in data) {
            formData.append(key, data[key]);
        }
        
        return fetch(url, {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: formData
        }).then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        });
    },

    /**
     * 发送JSON请求
     */
    json: function(url, data = {}, method = 'POST') {
        return fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify(data)
        }).then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        });
    }
};

/**
 * 表单处理
 */
App.form = {
    /**
     * 序列化表单数据
     */
    serialize: function(form) {
        if (typeof form === 'string') {
            form = document.querySelector(form);
        }
        
        const formData = new FormData(form);
        const data = {};
        
        for (let [key, value] of formData.entries()) {
            if (data[key]) {
                if (Array.isArray(data[key])) {
                    data[key].push(value);
                } else {
                    data[key] = [data[key], value];
                }
            } else {
                data[key] = value;
            }
        }
        
        return data;
    },

    /**
     * 验证表单
     */
    validate: function(form, rules = {}) {
        if (typeof form === 'string') {
            form = document.querySelector(form);
        }
        
        const errors = {};
        const data = this.serialize(form);
        
        for (let field in rules) {
            const rule = rules[field];
            const value = data[field];
            
            if (rule.required && (!value || value.trim() === '')) {
                errors[field] = rule.message || `${field} 是必填项`;
                continue;
            }
            
            if (value && rule.pattern && !rule.pattern.test(value)) {
                errors[field] = rule.message || `${field} 格式不正确`;
                continue;
            }
            
            if (value && rule.min && value.length < rule.min) {
                errors[field] = rule.message || `${field} 长度不能少于 ${rule.min} 个字符`;
                continue;
            }
            
            if (value && rule.max && value.length > rule.max) {
                errors[field] = rule.message || `${field} 长度不能超过 ${rule.max} 个字符`;
                continue;
            }
        }
        
        return {
            valid: Object.keys(errors).length === 0,
            errors: errors,
            data: data
        };
    }
};

/**
 * 初始化应用
 */
App.init = function() {
    // 初始化工具提示
    document.querySelectorAll('[data-tooltip]').forEach(element => {
        const text = element.getAttribute('data-tooltip');
        const position = element.getAttribute('data-tooltip-position') || 'top';
        App.ui.createTooltip(element, text, position);
    });
    
    // 初始化确认对话框
    document.querySelectorAll('[data-confirm]').forEach(element => {
        element.addEventListener('click', function(e) {
            const message = this.getAttribute('data-confirm');
            if (!confirm(message)) {
                e.preventDefault();
                return false;
            }
        });
    });
    
    // 初始化复制功能
    document.querySelectorAll('[data-copy]').forEach(element => {
        element.addEventListener('click', function() {
            const text = this.getAttribute('data-copy') || this.textContent;
            App.utils.copyToClipboard(text);
        });
    });
    
    // 全局错误处理
    window.addEventListener('error', function(e) {
        console.error('页面错误:', e.error);
        if (App.config.debug) {
            App.ui.showNotification('页面出现错误，请刷新重试', 'danger');
        }
    });
    
    // 全局AJAX错误处理
    window.addEventListener('unhandledrejection', function(e) {
        console.error('未处理的Promise错误:', e.reason);
        if (App.config.debug) {
            App.ui.showNotification('请求失败，请重试', 'danger');
        }
    });
};

// DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', App.init);
