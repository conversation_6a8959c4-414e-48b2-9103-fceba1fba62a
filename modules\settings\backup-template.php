<?php require_once dirname(__DIR__, 2) . '/includes/header-modular.php'; ?>

<div class="main-content">
    <?php require_once dirname(__DIR__, 2) . '/includes/sidebar-modular.php'; ?>
    
    <div class="content">
        <div class="content-header">
            <h1>
                <i class="fas fa-database"></i>
                备份管理
            </h1>
            <div class="header-actions">
                <a href="index.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i>
                    返回设置
                </a>
            </div>
        </div>

        <?php if (!empty($error)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle"></i>
            <?= htmlspecialchars($error) ?>
        </div>
        <?php elseif (!empty($_GET['success'])): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i>
            备份创建成功
            <?php if (!empty($_GET['file'])): ?>
                <br>文件名：<?= htmlspecialchars($_GET['file']) ?>
            <?php endif; ?>
        </div>
        <?php endif; ?>

        <div class="backup-container">
            <!-- 创建备份 -->
            <div class="backup-section">
                <div class="section-header">
                    <h2><i class="fas fa-plus-circle"></i> 创建备份</h2>
                    <p>创建数据库的完整备份，建议定期执行以保护数据安全</p>
                </div>
                <div class="backup-card">
                    <div class="backup-info">
                        <div class="info-item">
                            <i class="fas fa-calendar"></i>
                            <span>建议频率：每周一次</span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-clock"></i>
                            <span>预计时间：1-5分钟</span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-hdd"></i>
                            <span>存储位置：/backups/</span>
                        </div>
                    </div>
                    <form method="POST" action="index.php?action=backup" onsubmit="return confirm('确定要创建数据库备份吗？')">
                        <input type="hidden" name="operation" value="create">
                        <button type="submit" class="btn btn-primary btn-large">
                            <i class="fas fa-download"></i>
                            立即创建备份
                        </button>
                    </form>
                </div>
            </div>

            <!-- 备份列表 -->
            <div class="backup-section">
                <div class="section-header">
                    <h2><i class="fas fa-list"></i> 备份文件列表</h2>
                    <p>管理已创建的备份文件，可以下载或删除不需要的备份</p>
                </div>
                
                <?php if (!empty($backups)): ?>
                <div class="backup-list">
                    <?php foreach ($backups as $backup): ?>
                    <div class="backup-item">
                        <div class="backup-icon">
                            <i class="fas fa-file-archive"></i>
                        </div>
                        <div class="backup-details">
                            <h4><?= htmlspecialchars($backup['name']) ?></h4>
                            <div class="backup-meta">
                                <span class="meta-item">
                                    <i class="fas fa-calendar"></i>
                                    <?= htmlspecialchars($backup['created_at']) ?>
                                </span>
                                <span class="meta-item">
                                    <i class="fas fa-weight"></i>
                                    <?= number_format($backup['size'] / 1024, 2) ?> KB
                                </span>
                            </div>
                        </div>
                        <div class="backup-actions">
                            <a href="#" class="btn btn-sm btn-secondary" onclick="alert('下载功能开发中'); return false;">
                                <i class="fas fa-download"></i>
                                下载
                            </a>
                            <a href="#" class="btn btn-sm btn-danger" onclick="return confirm('确定要删除这个备份文件吗？')">
                                <i class="fas fa-trash"></i>
                                删除
                            </a>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php else: ?>
                <div class="empty-state">
                    <i class="fas fa-inbox"></i>
                    <h3>暂无备份文件</h3>
                    <p>还没有创建任何备份文件，建议立即创建第一个备份</p>
                </div>
                <?php endif; ?>
            </div>

            <!-- 备份说明 -->
            <div class="backup-section">
                <div class="section-header">
                    <h2><i class="fas fa-info-circle"></i> 备份说明</h2>
                </div>
                <div class="backup-tips">
                    <div class="tip-grid">
                        <div class="tip-item">
                            <div class="tip-icon success">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="tip-content">
                                <h4>定期备份</h4>
                                <p>建议每周至少创建一次完整备份，重要数据变更后及时备份</p>
                            </div>
                        </div>
                        <div class="tip-item">
                            <div class="tip-icon warning">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="tip-content">
                                <h4>异地存储</h4>
                                <p>将备份文件下载并保存到其他安全位置，避免单点故障</p>
                            </div>
                        </div>
                        <div class="tip-item">
                            <div class="tip-icon info">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <div class="tip-content">
                                <h4>测试恢复</h4>
                                <p>定期测试备份文件的完整性和可恢复性</p>
                            </div>
                        </div>
                        <div class="tip-item">
                            <div class="tip-icon error">
                                <i class="fas fa-trash-alt"></i>
                            </div>
                            <div class="tip-content">
                                <h4>清理旧备份</h4>
                                <p>定期清理过期的备份文件，释放存储空间</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.backup-container {
    max-width: 900px;
}

.backup-section {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    margin-bottom: 24px;
}

.section-header {
    margin-bottom: 20px;
}

.section-header h2 {
    margin: 0 0 8px 0;
    color: #1f2937;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.section-header p {
    margin: 0;
    color: #6b7280;
    line-height: 1.5;
}

.backup-card {
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
}

.backup-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #6b7280;
    font-size: 14px;
}

.info-item i {
    width: 16px;
    color: #9ca3af;
}

.btn-large {
    padding: 12px 24px;
    font-size: 16px;
}

.backup-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.backup-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px;
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.backup-item:hover {
    background: #f3f4f6;
}

.backup-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.backup-details {
    flex: 1;
}

.backup-details h4 {
    margin: 0 0 4px 0;
    color: #1f2937;
    font-size: 16px;
    font-weight: 500;
}

.backup-meta {
    display: flex;
    gap: 16px;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 4px;
    color: #6b7280;
    font-size: 12px;
}

.meta-item i {
    width: 12px;
}

.backup-actions {
    display: flex;
    gap: 8px;
}

.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #6b7280;
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 16px;
    color: #d1d5db;
}

.empty-state h3 {
    margin: 0 0 8px 0;
    color: #374151;
}

.empty-state p {
    margin: 0;
}

.backup-tips {
    background: #f0f9ff;
    border: 1px solid #bae6fd;
    border-radius: 8px;
    padding: 20px;
}

.tip-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
}

.tip-item {
    display: flex;
    gap: 12px;
}

.tip-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    flex-shrink: 0;
}

.tip-icon.success {
    background: #dcfce7;
    color: #166534;
}

.tip-icon.warning {
    background: #fef3c7;
    color: #d97706;
}

.tip-icon.info {
    background: #dbeafe;
    color: #2563eb;
}

.tip-icon.error {
    background: #fee2e2;
    color: #dc2626;
}

.tip-content h4 {
    margin: 0 0 4px 0;
    color: #0c4a6e;
    font-size: 14px;
    font-weight: 600;
}

.tip-content p {
    margin: 0;
    color: #0c4a6e;
    font-size: 12px;
    line-height: 1.4;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #f3f4f6;
    color: #6b7280;
    border: 1px solid #d1d5db;
}

.btn-secondary:hover {
    background: #e5e7eb;
}

.btn-danger {
    background: #fee2e2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

.btn-danger:hover {
    background: #fecaca;
}

.alert {
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 20px;
    display: flex;
    align-items: flex-start;
    gap: 8px;
}

.alert-success {
    background: #dcfce7;
    color: #166534;
    border: 1px solid #bbf7d0;
}

.alert-danger {
    background: #fee2e2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

@media (max-width: 768px) {
    .backup-card {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
    }
    
    .backup-item {
        flex-direction: column;
        text-align: center;
    }
    
    .backup-meta {
        justify-content: center;
    }
    
    .tip-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<?php require_once dirname(__DIR__, 2) . '/includes/footer-modular.php'; ?>
