<?php require_once dirname(__DIR__, 2) . '/includes/header-modular.php'; ?>

<div class="main-content">
    <?php require_once dirname(__DIR__, 2) . '/includes/sidebar-modular.php'; ?>
    
    <div class="content">
        <div class="content-header">
            <h1>
                <i class="fas fa-warehouse"></i>
                库存查询
            </h1>
            <div class="header-actions">
                <a href="../inbound/index.php" class="btn btn-success">
                    <i class="fas fa-sign-in-alt"></i>
                    食材入库
                </a>
                <a href="../outbound/index.php" class="btn btn-primary">
                    <i class="fas fa-sign-out-alt"></i>
                    食材出库
                </a>
            </div>
        </div>

        <!-- 统计卡片 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon bg-blue">
                    <i class="fas fa-boxes"></i>
                </div>
                <div class="stat-content">
                    <h3><?= number_format($data['stats']['total_items'] ?? 0) ?></h3>
                    <p>食材品种</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon bg-green">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-content">
                    <h3><?= number_format($data['stats']['normal_stock'] ?? 0) ?></h3>
                    <p>库存正常</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon bg-orange">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="stat-content">
                    <h3><?= number_format(($data['stats']['low_stock'] ?? 0) + ($data['stats']['warning_stock'] ?? 0)) ?></h3>
                    <p>库存预警</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon bg-red">
                    <i class="fas fa-times-circle"></i>
                </div>
                <div class="stat-content">
                    <h3><?= number_format($data['stats']['out_of_stock'] ?? 0) ?></h3>
                    <p>缺货品种</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon bg-purple">
                    <i class="fas fa-calculator"></i>
                </div>
                <div class="stat-content">
                    <h3>¥<?= number_format($data['stats']['total_value'] ?? 0, 2) ?></h3>
                    <p>库存总价值</p>
                </div>
            </div>
        </div>

        <!-- 搜索筛选 -->
        <div class="search-bar" style="padding: 15px !important;">
            <form method="GET" action="index.php" class="search-bar-form" style="display: flex !important; flex-wrap: nowrap !important; gap: 10px !important; align-items: center !important; padding: 0 !important;">
                <div class="form-field text-input" style="display: flex !important; align-items: center !important; gap: 8px !important; flex: 0 0 auto !important; margin: 0 !important;">
                    <label style="font-size: 14px !important; font-weight: 500 !important; color: #374151 !important; white-space: nowrap !important; margin: 0 !important;">搜索食材</label>
                    <input type="text" name="search" placeholder="输入食材名称..." 
                           value="<?= htmlspecialchars($data['search'] ?? '') ?>" class="form-control" style="height: 36px !important; width: 180px !important; padding: 6px 10px !important; border: 1px solid #d1d5db !important; border-radius: 6px !important; font-size: 14px !important; box-sizing: border-box !important;">
                </div>
                
                <div class="form-field select-input" style="display: flex !important; align-items: center !important; gap: 8px !important; flex: 0 0 auto !important; margin: 0 !important;">
                    <label style="font-size: 14px !important; font-weight: 500 !important; color: #374151 !important; white-space: nowrap !important; margin: 0 !important;">分类</label>
                    <select name="category" class="form-control" style="height: 36px !important; width: 140px !important; padding: 6px 10px !important; border: 1px solid #d1d5db !important; border-radius: 6px !important; font-size: 14px !important; box-sizing: border-box !important; background: white !important;">
                        <option value="">全部分类</option>
                        <?php foreach ($data['categories'] as $category): ?>
                        <option value="<?= $category['id'] ?>" <?= ($data['category'] ?? '') == $category['id'] ? 'selected' : '' ?>>
                            <?= htmlspecialchars($category['name']) ?>
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="form-field select-input" style="display: flex !important; align-items: center !important; gap: 8px !important; flex: 0 0 auto !important; margin: 0 !important;">
                    <label style="font-size: 14px !important; font-weight: 500 !important; color: #374151 !important; white-space: nowrap !important; margin: 0 !important;">库存状态</label>
                    <select name="status" class="form-control" style="height: 36px !important; width: 140px !important; padding: 6px 10px !important; border: 1px solid #d1d5db !important; border-radius: 6px !important; font-size: 14px !important; box-sizing: border-box !important; background: white !important;">
                        <option value="">全部状态</option>
                        <option value="normal" <?= ($data['status'] ?? '') === 'normal' ? 'selected' : '' ?>>库存正常</option>
                        <option value="warning" <?= ($data['status'] ?? '') === 'warning' ? 'selected' : '' ?>>库存预警</option>
                        <option value="low_stock" <?= ($data['status'] ?? '') === 'low_stock' ? 'selected' : '' ?>>库存不足</option>
                        <option value="out_of_stock" <?= ($data['status'] ?? '') === 'out_of_stock' ? 'selected' : '' ?>>已缺货</option>
                    </select>
                </div>
                
                <button type="submit" class="btn btn-primary" style="flex: 0 0 auto !important; height: 36px !important; padding: 0 14px !important; white-space: nowrap !important; margin: 0 !important; border: none !important; border-radius: 6px !important; background: #3b82f6 !important; color: white !important; font-size: 14px !important; font-weight: 500 !important; cursor: pointer !important; display: inline-flex !important; align-items: center !important; gap: 6px !important; text-decoration: none !important;">
                    <i class="fas fa-search"></i>
                    搜索
                </button>
                
                <a href="index.php" class="btn btn-secondary" style="flex: 0 0 auto !important; height: 36px !important; padding: 0 14px !important; white-space: nowrap !important; margin: 0 !important; border: none !important; border-radius: 6px !important; background: #6b7280 !important; color: white !important; font-size: 14px !important; font-weight: 500 !important; cursor: pointer !important; display: inline-flex !important; align-items: center !important; gap: 6px !important; text-decoration: none !important;">
                    <i class="fas fa-refresh"></i>
                    重置
                </a>
            </form>
        </div>

        <!-- 库存列表 -->
        <div class="table-container">
            <table class="table">
                <thead>
                    <tr>
                        <th>食材信息</th>
                        <th>分类</th>
                        <th>当前库存</th>
                        <th>最低库存</th>
                        <th>库存状态</th>
                        <th>单价</th>
                        <th>库存价值</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($data['inventory'])): ?>
                        <?php foreach ($data['inventory'] as $item): ?>
                            <tr>
                                <td>
                                    <div class="ingredient-info">
                                        <div class="ingredient-name"><?= htmlspecialchars($item['name']) ?></div>
                                        <div class="ingredient-unit">单位: <?= htmlspecialchars($item['unit']) ?></div>
                                    </div>
                                </td>
                                <td>
                                    <?php if (!empty($item['category_name'])): ?>
                                        <span class="category-badge"><?= htmlspecialchars($item['category_name']) ?></span>
                                    <?php else: ?>
                                        <span class="text-muted">未分类</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="stock-info">
                                        <div class="stock-value"><?= number_format($item['current_stock'], 1) ?></div>
                                        <div class="stock-unit"><?= htmlspecialchars($item['unit']) ?></div>
                                    </div>
                                </td>
                                <td>
                                    <div class="min-stock-info"><?= number_format($item['min_stock'], 1) ?> <?= htmlspecialchars($item['unit']) ?></div>
                                </td>
                                <td>
                                    <?php
                                    $statusConfig = [
                                        'normal' => ['class' => 'status-normal', 'text' => '库存正常', 'icon' => 'fas fa-check-circle'],
                                        'warning' => ['class' => 'status-warning', 'text' => '库存预警', 'icon' => 'fas fa-exclamation-triangle'],
                                        'low_stock' => ['class' => 'status-low', 'text' => '库存不足', 'icon' => 'fas fa-exclamation-circle'],
                                        'out_of_stock' => ['class' => 'status-out', 'text' => '已缺货', 'icon' => 'fas fa-times-circle']
                                    ];
                                    $config = $statusConfig[$item['stock_status']] ?? $statusConfig['normal'];
                                    ?>
                                    <span class="status-badge <?= $config['class'] ?>">
                                        <i class="<?= $config['icon'] ?>"></i>
                                        <?= $config['text'] ?>
                                    </span>
                                </td>
                                <td>
                                    ¥<?= number_format($item['unit_price'], 2) ?>
                                </td>
                                <td>
                                    <strong>¥<?= number_format($item['current_stock'] * $item['unit_price'], 2) ?></strong>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <a href="../inbound/create.php?ingredient_id=<?= $item['id'] ?>"
                                           class="btn btn-sm btn-success" title="食材入库">
                                            <i class="fas fa-sign-in-alt"></i>
                                            <span>入库</span>
                                        </a>
                                        <a href="../outbound/index.php?action=create&ingredient_id=<?= $item['id'] ?>"
                                           class="btn btn-sm btn-info" title="食材出库">
                                            <i class="fas fa-sign-out-alt"></i>
                                            <span>出库</span>
                                        </a>
                                        <?php if ($item['stock_status'] === 'out_of_stock' || $item['stock_status'] === 'low_stock'): ?>
                                        <a href="../purchase/create.php?ingredient_id=<?= $item['id'] ?>"
                                           class="btn btn-sm btn-warning" title="创建采购单">
                                            <i class="fas fa-shopping-cart"></i>
                                            <span>采购</span>
                                        </a>
                                        <?php endif; ?>
                                        <a href="index.php?action=stock&ingredient_id=<?= $item['id'] ?>" class="btn btn-sm btn-secondary" title="库存情况">
                                            <i class="fas fa-chart-line"></i>
                                            <span>库存情况</span>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="8" class="text-center text-muted">
                                <i class="fas fa-inbox"></i>
                                暂无库存记录
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>


    </div>
</div>

<style>
/* 库存查询模块特有样式 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 24px;
    color: white;
}

.stat-icon.bg-blue { background: #4299e1; }
.stat-icon.bg-green { background: #48bb78; }
.stat-icon.bg-orange { background: #ed8936; }
.stat-icon.bg-red { background: #f56565; }
.stat-icon.bg-purple { background: #7c3aed; }

.stat-content h3 {
    margin: 0;
    font-size: 26px;
    font-weight: bold;
    color: #2d3748;
}

.stat-content p {
    margin: 5px 0 0 0;
    color: #718096;
    font-size: 16px;
}

.ingredient-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.ingredient-name {
    font-weight: 600;
    color: #2d3748;
    font-size: 15px;
}

.ingredient-unit {
    font-size: 12px;
    color: #718096;
    background: #f7fafc;
    padding: 2px 6px;
    border-radius: 4px;
    display: inline-block;
    width: fit-content;
}

.category-badge {
    background: #bee3f8;
    color: #2b6cb0;
    padding: 6px 10px;
    border-radius: 12px;
    font-size: 13px;
    font-weight: 500;
}

.stock-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
}

.stock-value {
    font-weight: bold;
    color: #2d3748;
    font-size: 16px;
}

.stock-unit {
    font-size: 12px;
    color: #718096;
}

.min-stock-info {
    font-size: 14px;
    color: #4a5568;
    text-align: center;
}

.status-badge {
    padding: 6px 10px;
    border-radius: 12px;
    font-size: 13px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 4px;
}

.status-normal {
    background: #c6f6d5;
    color: #22543d;
}

.status-warning {
    background: #fef5e7;
    color: #c05621;
}

.status-low {
    background: #fed7aa;
    color: #c05621;
}

.status-out {
    background: #fed7d7;
    color: #c53030;
}

.action-buttons {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

.table-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
}

.summary-section {
    margin-top: 30px;
}

.summary-card {
    background: white;
    border-radius: 8px;
    padding: 25px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    max-width: 400px;
    margin: 0 auto;
}

.summary-icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    font-size: 28px;
    color: white;
}

.summary-content h4 {
    margin: 0 0 10px 0;
    color: #2d3748;
    font-size: 18px;
}

.summary-value {
    font-size: 32px;
    font-weight: bold;
    color: #2d3748;
    margin: 0;
}

@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .table-container {
        overflow-x: auto;
    }
    
    .table {
        min-width: 1000px;
    }
    
    .summary-card {
        flex-direction: column;
        text-align: center;
    }
    
    .summary-icon {
        margin-right: 0;
        margin-bottom: 15px;
    }
}
</style>

<?php require_once dirname(__DIR__, 2) . '/includes/footer-modular.php'; ?>