<?php require_once dirname(__DIR__, 2) . '/includes/header-modular.php'; ?>

<div class="main-content">
    <?php require_once dirname(__DIR__, 2) . '/includes/sidebar-modular.php'; ?>
    
    <div class="content">
        <div class="content-header">
            <h1>
                <i class="fas fa-vial"></i>
                留样记录详情
            </h1>
            <div class="header-actions">
                <a href="index.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i>
                    返回列表
                </a>
                <a href="index.php?action=edit&id=<?= htmlspecialchars(($data['sample']['id'] ?? ($sample['id'] ?? 0))) ?>" class="btn btn-primary">
                    <i class="fas fa-edit"></i>
                    编辑
                </a>
            </div>
        </div>

        <?php if (!empty($data['error'])): ?>
        <div class="alert alert-danger">
            <?= htmlspecialchars($data['error']) ?>
        </div>
        <?php endif; ?>

        <div class="detail-container">
            <div class="detail-card">
                <h3 class="detail-title">基本信息</h3>
                <div class="detail-content">
                    <div class="detail-row">
                        <span class="detail-label">留样编号:</span>
                        <span class="detail-value"><?= htmlspecialchars(($data['sample']['sample_code'] ?? ($sample['sample_code'] ?? ''))) ?></span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">食材名称:</span>
                        <span class="detail-value"><?= htmlspecialchars(($data['sample']['ingredient_name'] ?? ($sample['ingredient_name'] ?? '未知'))) ?></span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">批次号:</span>
                        <span class="detail-value"><?= htmlspecialchars(($data['sample']['batch_number'] ?? ($sample['batch_number'] ?? ''))) ?></span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">留样数量:</span>
                        <span class="detail-value"><?= htmlspecialchars(($data['sample']['sample_quantity'] ?? ($sample['sample_quantity'] ?? ''))) ?> <?= htmlspecialchars(($data['sample']['sample_unit'] ?? ($sample['sample_unit'] ?? ''))) ?></span>
                    </div>
                </div>
            </div>
            
            <div class="detail-card">
                <h3 class="detail-title">时间信息</h3>
                <div class="detail-content">
                    <div class="detail-row">
                        <span class="detail-label">用餐日期:</span>
                        <span class="detail-value"><?= !empty($data['sample']['meal_date'] ?? ($sample['meal_date'] ?? '')) ? date('Y年m月d日', strtotime($data['sample']['meal_date'] ?? $sample['meal_date'])) : '' ?></span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">留样时间:</span>
                        <span class="detail-value"><?= !empty($data['sample']['sample_time'] ?? ($sample['sample_time'] ?? '')) ? date('Y年m月d日 H:i:s', strtotime($data['sample']['sample_time'] ?? $sample['sample_time'])) : '' ?></span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">过期日期:</span>
                        <?php $exp = $data['sample']['expiration_date'] ?? ($sample['expiration_date'] ?? ''); $status = $data['sample']['sample_status'] ?? ($sample['sample_status'] ?? 0); ?>
                        <span class="detail-value <?= (!empty($exp) && strtotime($exp) < time() && intval($status) == 1) ? 'text-danger' : '' ?>">
                            <?= !empty($exp) ? date('Y年m月d日', strtotime($exp)) : '' ?>
                        </span>
                    </div>
                </div>
            </div>
            
            <div class="detail-card">
                <h3 class="detail-title">存储信息</h3>
                <div class="detail-content">
                    <div class="detail-row">
                        <span class="detail-label">餐次:</span>
                        <span class="detail-value">
                            <?php
                            $mealTypes = [
                                'breakfast' => '早餐',
                                'lunch' => '午餐',
                                'dinner' => '晚餐',
                                'other' => '其他'
                            ];
                            $mt = $data['sample']['meal_type'] ?? ($sample['meal_type'] ?? '');
                            echo $mealTypes[$mt] ?? '未知';
                            ?>
                        </span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">存储位置:</span>
                        <span class="detail-value"><?= htmlspecialchars(($data['sample']['storage_location'] ?? ($sample['storage_location'] ?? '未指定'))) ?></span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">存储温度:</span>
                        <span class="detail-value"><?= htmlspecialchars(($data['sample']['storage_temperature'] ?? ($sample['storage_temperature'] ?? '未指定'))) ?></span>
                    </div>
                </div>
            </div>
            
            <div class="detail-card">
                <h3 class="detail-title">管理信息</h3>
                <div class="detail-content">
                    <div class="detail-row">
                        <span class="detail-label">责任人:</span>
                        <span class="detail-value"><?= htmlspecialchars(($data['sample']['responsible_person'] ?? ($sample['responsible_person'] ?? ''))) ?></span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">状态:</span>
                        <span class="detail-value">
                            <?php $st = intval($data['sample']['sample_status'] ?? ($sample['sample_status'] ?? 0)); if ($st == 1): ?>
                                <span class="status-badge status-active">有效</span>
                            <?php else: ?>
                                <span class="status-badge status-inactive">已处理</span>
                            <?php endif; ?>
                        </span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">创建时间:</span>
                        <span class="detail-value"><?= !empty($data['sample']['created_at'] ?? ($sample['created_at'] ?? '')) ? date('Y年m月d日 H:i:s', strtotime($data['sample']['created_at'] ?? $sample['created_at'])) : '' ?></span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">更新时间:</span>
                        <span class="detail-value"><?= !empty($data['sample']['updated_at'] ?? ($sample['updated_at'] ?? '')) ? date('Y年m月d日 H:i:s', strtotime($data['sample']['updated_at'] ?? $sample['updated_at'])) : '' ?></span>
                    </div>
                </div>
            </div>
            
            <?php if (!empty($data['sample']['notes'] ?? ($sample['notes'] ?? ''))): ?>
            <div class="detail-card">
                <h3 class="detail-title">备注信息</h3>
                <div class="detail-content">
                    <div class="detail-row">
                        <span class="detail-value"><?= nl2br(htmlspecialchars(($data['sample']['notes'] ?? ($sample['notes'] ?? '')))) ?></span>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php require_once dirname(__DIR__, 2) . '/includes/footer-modular.php'; ?>

<style>
/* 详情页统一风格（与主题一致） */
.detail-container { 
  display: grid; 
  grid-template-columns: repeat(auto-fit, minmax(360px, 1fr)); 
  gap: 20px; 
}
.detail-card { 
  background: #fff; 
  border-radius: 10px; 
  box-shadow: 0 2px 4px rgba(0,0,0,.08); 
  padding: 20px; 
}
.detail-title { 
  margin: 0 0 14px 0; 
  font-size: 18px; 
  font-weight: 700; 
  color: #1f2937; 
  display: flex; 
  align-items: center; 
  gap: 8px; 
}
.detail-content { display: flex; flex-direction: column; gap: 12px; }
.detail-row { display: grid; grid-template-columns: 120px 1fr; align-items: center; gap: 10px; }
.detail-label { color: #6b7280; font-weight: 600; font-size: 14px; }
.detail-value { color: #111827; font-weight: 600; font-size: 15px; }
.text-danger { color: #dc2626 !important; }

/* 状态徽标 */
.status-badge { display: inline-flex; align-items: center; gap: 6px; border-radius: 999px; padding: 4px 10px; font-size: 12px; font-weight: 600; }
.status-active { background: #dcfce7; color: #166534; }
.status-inactive { background: #fee2e2; color: #991b1b; }

/* 响应式优化 */
@media (max-width: 768px) {
  .detail-row { grid-template-columns: 100px 1fr; }
}
</style>