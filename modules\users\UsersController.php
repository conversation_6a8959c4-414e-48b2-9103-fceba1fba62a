<?php
/**
 * 用户权限管理控制器
 */
require_once dirname(__DIR__, 2) . '/includes/BaseController.php';
require_once dirname(__DIR__, 2) . '/includes/helpers.php';
require_once dirname(__DIR__, 2) . '/includes/SessionSecurity.php';

class UsersController extends BaseController
{
    protected function init()
    {
        $this->setTemplateData([
            'page_title' => '用户管理 - ' . $this->config['name'],
            'current_module' => 'users'
        ]);
    }

    public function handleRequest()
    {
        $action = $this->request['action'];
        
        switch ($action) {
            case 'create':
                return $this->create();
            case 'edit':
                return $this->edit();
            case 'delete':
                return $this->delete();
            case 'view':
                return $this->view();
            case 'store':
                return $this->store();
            case 'update':
                return $this->update();
            case 'reset_password':
                return $this->resetPassword();
            case 'change_status':
                return $this->changeStatus();
            case 'roles':
                return $this->roles();
            case 'permissions':
                return $this->permissions();
            case 'login':
                return $this->login();
            case 'logout':
                return $this->logout();
            case 'profile':
                return $this->profile();
            default:
                return $this->index();
        }
    }

    /**
     * 用户列表页面
     */
    private function index()
    {
        try {
            // 获取搜索参数
            $search = $this->request['get']['search'] ?? '';
            $role = $this->request['get']['role'] ?? '';
            $status = $this->request['get']['status'] ?? '';

            // 构建查询条件
            $where = ['1=1'];
            $params = [];

            if ($search) {
                $where[] = '(u.name LIKE ? OR u.email LIKE ? OR u.phone LIKE ?)';
                $params[] = '%' . $search . '%';
                $params[] = '%' . $search . '%';
                $params[] = '%' . $search . '%';
            }

            if ($role) {
                $where[] = 'ur.role_id = ?';
                $params[] = $role;
            }

            if ($status !== '') {
                $where[] = 'u.status = ?';
                $params[] = $status;
            }

            $whereClause = 'WHERE ' . implode(' AND ', $where);

            // 获取用户列表
            $users = $this->db->fetchAll("
                SELECT 
                    u.*,
                    r.name as role_name,
                    r.display_name as role_display_name,
                    ur.created_at as role_assigned_at
                FROM users u
                LEFT JOIN user_roles ur ON u.id = ur.user_id
                LEFT JOIN roles r ON ur.role_id = r.id
                $whereClause
                ORDER BY u.created_at DESC
                LIMIT 50
            ", $params);

            // 获取角色列表
            $roles = $this->db->fetchAll("
                SELECT id, name, display_name 
                FROM roles 
                WHERE status = 1 
                ORDER BY sort_order ASC, name ASC
            ");

            // 获取统计信息
            $stats = $this->getUserStats();

        } catch (Exception $e) {
            // 使用模拟数据
            $users = $this->getMockUsers();
            $roles = $this->getMockRoles();
            $stats = $this->getMockStats();
        }

        $this->setTemplateData([
            'users' => $users,
            'roles' => $roles,
            'stats' => $stats,
            'search' => $search,
            'selectedRole' => $role,
            'selectedStatus' => $status
        ]);

        $this->render('template.php');
    }

    /**
     * 创建用户页面
     */
    private function create()
    {
        if ($this->request['method'] === 'POST') {
            return $this->store();
        }

        $this->loadFormData();
        $this->setTemplateData([
            'action' => 'create',
            'user' => null
        ]);
        $this->render('create-template.php');
    }

    /**
     * 编辑用户页面
     */
    private function edit()
    {
        $id = intval($this->request['get']['id'] ?? 0);
        
        if (!$id) {
            $this->redirect('index.php', '参数错误', 'error');
            return;
        }

        if ($this->request['method'] === 'POST') {
            return $this->update();
        }

        try {
            $user = $this->db->fetchOne("
                SELECT u.*, ur.role_id
                FROM users u
                LEFT JOIN user_roles ur ON u.id = ur.user_id
                WHERE u.id = ?
            ", [$id]);
            
            if (!$user) {
                $this->redirect('index.php', '用户不存在', 'error');
                return;
            }
        } catch (Exception $e) {
            $user = $this->getMockUsers()[0] ?? null;
        }

        $this->loadFormData();
        $this->setTemplateData([
            'action' => 'edit',
            'user' => $user
        ]);
        $this->render('create-template.php');
    }

    /**
     * 查看用户详情
     */
    private function view()
    {
        $id = intval($this->request['get']['id'] ?? 0);
        
        if (!$id) {
            $this->redirect('index.php', '参数错误', 'error');
            return;
        }

        try {
            $user = $this->db->fetchOne("
                SELECT 
                    u.*,
                    r.name as role_name,
                    r.display_name as role_display_name,
                    GROUP_CONCAT(p.name) as permissions
                FROM users u
                LEFT JOIN user_roles ur ON u.id = ur.user_id
                LEFT JOIN roles r ON ur.role_id = r.id
                LEFT JOIN role_permissions rp ON r.id = rp.role_id
                LEFT JOIN permissions p ON rp.permission_id = p.id
                WHERE u.id = ?
                GROUP BY u.id
            ", [$id]);
            
            if (!$user) {
                $this->redirect('index.php', '用户不存在', 'error');
                return;
            }

            // 获取用户操作日志
            $logs = $this->db->fetchAll("
                SELECT * FROM user_logs 
                WHERE user_id = ? 
                ORDER BY created_at DESC 
                LIMIT 20
            ", [$id]);

        } catch (Exception $e) {
            $user = $this->getMockUsers()[0] ?? null;
            $logs = [];
        }

        $this->setTemplateData([
            'user' => $user,
            'logs' => $logs
        ]);
        $this->render('view-template.php');
    }

    /**
     * 保存用户
     */
    private function store()
    {
        try {
            // 字段兼容映射：表单提供 username/name、real_name 可能缺失
            $username = trim($this->request['post']['username'] ?? ($this->request['post']['name'] ?? ''));
            $realName = trim($this->request['post']['real_name'] ?? ($this->request['post']['name'] ?? ''));
            $email = trim($this->request['post']['email'] ?? '');
            $phone = trim($this->request['post']['phone'] ?? '');
            $passwordRaw = $this->request['post']['password'] ?? '';

            $data = [
                'name' => $username,
                'email' => $email,
                'phone' => $phone,
                'real_name' => $realName,
                'password' => $passwordRaw !== '' ? password_hash($passwordRaw, PASSWORD_DEFAULT) : '',
                'status' => intval($this->request['post']['status'] ?? 1),
                'created_at' => date('Y-m-d H:i:s')
            ];

            // 角色兼容：优先 role_id；若未提供，尝试从 role 名称解析
            $roleId = intval($this->request['post']['role_id'] ?? 0);
            if ($roleId === 0) {
                $roleName = trim($this->request['post']['role'] ?? '');
                if ($roleName !== '') {
                    try {
                        $roleRow = $this->db->fetchOne("SELECT id FROM roles WHERE name = ? OR display_name = ? LIMIT 1", [$roleName, $roleName]);
                        if ($roleRow && isset($roleRow['id'])) {
                            $roleId = intval($roleRow['id']);
                        }
                    } catch (Exception $e) {
                        // 忽略，保持为0
                    }
                }
            }

            // 验证数据
            $this->validateUserData($data, $roleId);

            // 检查邮箱是否已存在
            $existing = $this->db->fetchOne("SELECT id FROM users WHERE email = ?", [$data['email']]);
            if ($existing) {
                throw new Exception("邮箱已存在");
            }

            // 开始事务
            $this->db->beginTransaction();

            // 插入用户数据
            $userId = $this->db->insert('users', $data);

            // 分配角色
            if ($roleId) {
                $this->db->insert('user_roles', [
                    'user_id' => $userId,
                    'role_id' => $roleId,
                    'created_at' => date('Y-m-d H:i:s')
                ]);
            }

            $this->db->commit();
            $this->redirect('index.php', '用户创建成功', 'success');

        } catch (Exception $e) {
            try {
                $this->db->rollback();
            } catch (Exception $rollbackException) {
                // 忽略回滚错误
            }
            
            $this->loadFormData();
            $this->setTemplateData([
                'action' => 'create',
                'user' => $this->request['post'],
                'error' => $e->getMessage()
            ]);
            $this->render('create-template.php');
        }
    }

    /**
     * 更新用户
     */
    private function update()
    {
        $id = intval($this->request['post']['id'] ?? 0);
        
        if (!$id) {
            $this->redirect('index.php', '参数错误', 'error');
            return;
        }

        try {
            // 字段兼容映射
            $username = trim($this->request['post']['username'] ?? ($this->request['post']['name'] ?? ''));
            $realName = trim($this->request['post']['real_name'] ?? ($this->request['post']['name'] ?? ''));
            $email = trim($this->request['post']['email'] ?? '');
            $phone = trim($this->request['post']['phone'] ?? '');

            $data = [
                'name' => $username,
                'email' => $email,
                'phone' => $phone,
                'real_name' => $realName,
                'status' => intval($this->request['post']['status'] ?? 1),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $roleId = intval($this->request['post']['role_id'] ?? 0);
            if ($roleId === 0) {
                $roleName = trim($this->request['post']['role'] ?? '');
                if ($roleName !== '') {
                    try {
                        $roleRow = $this->db->fetchOne("SELECT id FROM roles WHERE name = ? OR display_name = ? LIMIT 1", [$roleName, $roleName]);
                        if ($roleRow && isset($roleRow['id'])) {
                            $roleId = intval($roleRow['id']);
                        }
                    } catch (Exception $e) {
                        // 忽略
                    }
                }
            }

            // 如果提供了新密码，则更新密码
            if (!empty($this->request['post']['password'])) {
                $data['password'] = password_hash($this->request['post']['password'], PASSWORD_DEFAULT);
            }

            // 验证数据
            $this->validateUserData($data, $roleId, $id);

            // 检查邮箱是否已存在（排除当前用户）
            $existing = $this->db->fetchOne("SELECT id FROM users WHERE email = ? AND id != ?", [$data['email'], $id]);
            if ($existing) {
                throw new Exception("邮箱已存在");
            }

            // 开始事务
            $this->db->beginTransaction();

            // 更新用户数据
            $this->db->update('users', $data, 'id = ?', [$id]);

            // 更新角色分配
            $this->db->delete('user_roles', 'user_id = ?', [$id]);
            if ($roleId) {
                $this->db->insert('user_roles', [
                    'user_id' => $id,
                    'role_id' => $roleId,
                    'created_at' => date('Y-m-d H:i:s')
                ]);
            }

            $this->db->commit();
            $this->redirect('index.php', '用户更新成功', 'success');

        } catch (Exception $e) {
            try {
                $this->db->rollback();
            } catch (Exception $rollbackException) {
                // 忽略回滚错误
            }
            
            $this->loadFormData();
            $user = $this->request['post'];
            $user['id'] = $id;
            
            $this->setTemplateData([
                'action' => 'edit',
                'user' => $user,
                'error' => $e->getMessage()
            ]);
            $this->render('create-template.php');
        }
    }

    /**
     * 删除用户
     */
    private function delete()
    {
        $id = intval($this->request['get']['id'] ?? 0);
        
        if (!$id) {
            $this->jsonResponse(['success' => false, 'message' => '参数错误']);
            return;
        }

        try {
            // 开始事务
            $this->db->beginTransaction();

            // 软删除用户
            $this->db->update('users', ['status' => 0, 'updated_at' => date('Y-m-d H:i:s')], 'id = ?', [$id]);

            // 删除角色关联
            $this->db->delete('user_roles', 'user_id = ?', [$id]);

            $this->db->commit();
            $this->jsonResponse(['success' => true, 'message' => '用户删除成功']);

        } catch (Exception $e) {
            try {
                $this->db->rollback();
            } catch (Exception $rollbackException) {
                // 忽略回滚错误
            }
            $this->jsonResponse(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    /**
     * 重置密码
     */
    private function resetPassword()
    {
        $id = intval($this->request['post']['id'] ?? 0);
        $newPassword = $this->request['post']['new_password'] ?? '';

        if (!$id || empty($newPassword)) {
            $this->jsonResponse(['success' => false, 'message' => '参数错误']);
            return;
        }

        try {
            $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
            $this->db->update('users', [
                'password' => $hashedPassword,
                'updated_at' => date('Y-m-d H:i:s')
            ], 'id = ?', [$id]);

            $this->jsonResponse(['success' => true, 'message' => '密码重置成功']);

        } catch (Exception $e) {
            $this->jsonResponse(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    /**
     * 更改用户状态
     */
    private function changeStatus()
    {
        $id = intval($this->request['post']['id'] ?? 0);
        $status = intval($this->request['post']['status'] ?? 1);

        if (!$id) {
            $this->jsonResponse(['success' => false, 'message' => '参数错误']);
            return;
        }

        try {
            $this->db->update('users', [
                'status' => $status,
                'updated_at' => date('Y-m-d H:i:s')
            ], 'id = ?', [$id]);

            $statusText = $status == 1 ? '启用' : '停用';
            $this->jsonResponse(['success' => true, 'message' => "用户{$statusText}成功"]);

        } catch (Exception $e) {
            $this->jsonResponse(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    /**
     * 角色管理
     */
    private function roles()
    {
        // TODO: 实现角色管理功能
        $this->setTemplateData(['page_title' => '角色管理']);
        $this->render('roles-template.php');
    }

    /**
     * 权限管理
     */
    private function permissions()
    {
        // TODO: 实现权限管理功能
        $this->setTemplateData(['page_title' => '权限管理']);
        $this->render('permissions-template.php');
    }

    /**
     * 用户登录
     */
    private function login()
    {
        if ($this->request['method'] === 'POST') {
            try {
                // 验证CSRF令牌
                $this->validateCSRF();

                $username = trim($this->request['post']['username']);
                $password = $this->request['post']['password'];

                // 检查登录尝试限制
                $this->checkLoginAttempts($username);

                // 确保用户表存在
                $this->ensureAuthTables();

                // 查找用户（支持用户名或邮箱登录）
                $user = $this->db->fetchOne("
                    SELECT u.*, r.name as role_name
                    FROM users u
                    LEFT JOIN user_roles ur ON u.id = ur.user_id
                    LEFT JOIN roles r ON ur.role_id = r.id
                    WHERE (u.name = ? OR u.email = ?) AND u.status = 1
                ", [$username, $username]);

                if (!$user) {
                    throw new Exception('用户不存在');
                }

                // 验证密码
                if (!password_verify($password, $user['password'])) {
                    // 记录登录失败并增加失败次数
                    $this->recordFailedLogin($username);
                    error_log("Failed login attempt for username: $username from IP: " . ($_SERVER['REMOTE_ADDR'] ?? 'unknown'));
                    throw new Exception('密码错误');
                }

                // 登录成功，清除失败记录
                $this->clearFailedLogins($username);

                // 设置安全会话
                SessionSecurity::setUser($user['id'], [
                    'name' => $user['name'],
                    'email' => $user['email']
                ]);

                // 会话已在SessionSecurity::setUser中设置，这里补充角色信息
                $_SESSION['user_role'] = $user['role_name'];

                // 更新最后登录时间
                $this->db->update('users', [
                    'last_login_at' => date('Y-m-d H:i:s')
                ], 'id = ?', [$user['id']]);

                // 记录登录成功
                error_log("Successful login for username: $username from IP: " . ($_SERVER['REMOTE_ADDR'] ?? 'unknown'));

                $this->redirect('../dashboard/index.php', '登录成功', 'success');

            } catch (Exception $e) {
                // 如果不是登录限制错误，也要记录失败尝试
                if (strpos($e->getMessage(), '账户已被锁定') === false && !empty($username)) {
                    $this->recordFailedLogin($username);
                }
                $this->setTemplateData(['error' => $e->getMessage()]);
            }
        }

        $this->setTemplateData(['page_title' => '用户登录']);
        $this->render('login-template.php');
    }

    /**
     * 用户退出
     */
    private function logout()
    {
        // 记录安全事件
        SessionSecurity::logSecurityEvent('user_logout', [
            'user_id' => SessionSecurity::getUserId()
        ]);

        // 安全销毁会话
        SessionSecurity::destroy();
        $this->redirect('index.php?action=login', '已退出登录', 'info');
    }

    /**
     * 检查登录尝试限制
     */
    private function checkLoginAttempts($username)
    {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        $maxAttempts = 5; // 最大尝试次数
        $lockoutTime = 900; // 锁定时间（秒）

        $key = 'login_attempts_' . $username;
        $attempts = $_SESSION[$key] ?? [];

        // 清理过期的尝试记录
        $now = time();
        $attempts = array_filter($attempts, function($timestamp) use ($now, $lockoutTime) {
            return ($now - $timestamp) < $lockoutTime;
        });

        if (count($attempts) >= $maxAttempts) {
            throw new Exception('账户已被锁定，请' . ceil($lockoutTime / 60) . '分钟后再试');
        }

        $_SESSION[$key] = $attempts;
    }

    /**
     * 记录失败的登录尝试
     */
    private function recordFailedLogin($username)
    {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        $key = 'login_attempts_' . $username;
        $attempts = $_SESSION[$key] ?? [];
        $attempts[] = time();
        $_SESSION[$key] = $attempts;
    }

    /**
     * 清除失败的登录记录
     */
    private function clearFailedLogins($username)
    {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        $key = 'login_attempts_' . $username;
        unset($_SESSION[$key]);
    }

    /**
     * 确保认证相关表存在
     */
    private function ensureAuthTables()
    {
        try {
            // 检查用户表是否存在
            $this->db->fetchOne("SELECT 1 FROM users LIMIT 1");
        } catch (Exception $e) {
            // 创建用户表
            $this->db->query("
                CREATE TABLE IF NOT EXISTS users (
                    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(100) NOT NULL,
                    real_name VARCHAR(100) DEFAULT NULL,
                    email VARCHAR(100) NOT NULL UNIQUE,
                    password VARCHAR(255) NOT NULL,
                    phone VARCHAR(20) DEFAULT NULL,
                    status TINYINT(1) NOT NULL DEFAULT 1,
                    last_login_at DATETIME DEFAULT NULL,
                    created_at DATETIME DEFAULT NULL,
                    updated_at DATETIME DEFAULT NULL
                )
            ");

            // 创建角色表
            $this->db->query("
                CREATE TABLE IF NOT EXISTS roles (
                    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(100) NOT NULL UNIQUE,
                    display_name VARCHAR(100) NOT NULL,
                    description TEXT DEFAULT NULL,
                    status TINYINT(1) NOT NULL DEFAULT 1,
                    sort_order INT NOT NULL DEFAULT 0,
                    created_at DATETIME DEFAULT NULL,
                    updated_at DATETIME DEFAULT NULL
                )
            ");

            // 创建用户角色关联表
            $this->db->query("
                CREATE TABLE IF NOT EXISTS user_roles (
                    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
                    user_id INT UNSIGNED NOT NULL,
                    role_id INT UNSIGNED NOT NULL,
                    created_at DATETIME DEFAULT NULL,
                    UNIQUE KEY unique_user_role (user_id, role_id)
                )
            ");

            // 插入默认管理员用户
            $userCount = $this->db->fetchOne("SELECT COUNT(*) as count FROM users")['count'] ?? 0;
            if ($userCount == 0) {
                // 插入默认角色
                $this->db->insert('roles', [
                    'name' => 'admin',
                    'display_name' => '系统管理员',
                    'description' => '系统管理员，拥有所有权限',
                    'status' => 1,
                    'sort_order' => 1,
                    'created_at' => date('Y-m-d H:i:s')
                ]);

                $adminRoleId = $this->db->lastInsertId();

                // 插入默认管理员用户
                $this->db->insert('users', [
                    'name' => 'admin',
                    'real_name' => '系统管理员',
                    'email' => '<EMAIL>',
                    'password' => password_hash('admin123', PASSWORD_DEFAULT),
                    'status' => 1,
                    'created_at' => date('Y-m-d H:i:s')
                ]);

                $adminUserId = $this->db->lastInsertId();

                // 分配管理员角色
                $this->db->insert('user_roles', [
                    'user_id' => $adminUserId,
                    'role_id' => $adminRoleId,
                    'created_at' => date('Y-m-d H:i:s')
                ]);
            }
        }
    }

    /**
     * 加载表单数据
     */
    private function loadFormData()
    {
        try {
            $roles = $this->db->fetchAll("
                SELECT id, name, display_name 
                FROM roles 
                WHERE status = 1 
                ORDER BY sort_order ASC, name ASC
            ");
        } catch (Exception $e) {
            $roles = $this->getMockRoles();
        }

        $this->setTemplateData([
            'roles' => $roles
        ]);
    }

    /**
     * 验证用户数据
     */
    private function validateUserData($data, $roleId = 0, $excludeId = 0)
    {
        if (empty($data['name'])) {
            throw new Exception("用户名不能为空");
        }
        if (empty($data['real_name'])) {
            throw new Exception("真实姓名不能为空");
        }
        if (empty($data['email']) || !filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            throw new Exception("邮箱格式不正确");
        }
        if ($excludeId == 0 && empty($data['password'])) {
            throw new Exception("密码不能为空");
        }
        if (!empty($data['phone']) && !preg_match('/^1[3-9]\d{9}$/', $data['phone'])) {
            throw new Exception("手机号格式不正确");
        }
    }

    /**
     * 获取用户统计信息
     */
    private function getUserStats()
    {
        try {
            $stats = $this->db->fetchOne("
                SELECT 
                    COUNT(*) as total_users,
                    SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as active_users,
                    SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as inactive_users,
                    SUM(CASE WHEN last_login_at > DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 ELSE 0 END) as recent_login_users
                FROM users
            ");

            $roleStats = $this->db->fetchAll("
                SELECT 
                    r.display_name,
                    COUNT(ur.user_id) as user_count
                FROM roles r
                LEFT JOIN user_roles ur ON r.id = ur.role_id
                LEFT JOIN users u ON ur.user_id = u.id AND u.status = 1
                WHERE r.status = 1
                GROUP BY r.id, r.display_name
                ORDER BY r.sort_order ASC
            ");

            $stats['role_stats'] = $roleStats;
            return $stats;

        } catch (Exception $e) {
            return $this->getMockStats();
        }
    }

    /**
     * 获取模拟用户数据
     */
    private function getMockUsers()
    {
        return [
            [
                'id' => 1,
                'name' => 'admin',
                'email' => '<EMAIL>',
                'real_name' => '系统管理员',
                'phone' => '13800138000',
                'role_name' => 'admin',
                'role_display_name' => '系统管理员',
                'status' => 1,
                'last_login_at' => '2024-12-01 10:30:00',
                'created_at' => '2024-01-01 10:00:00'
            ],
            [
                'id' => 2,
                'name' => 'warehouse',
                'email' => '<EMAIL>',
                'real_name' => '张仓管',
                'phone' => '13800138001',
                'role_name' => 'warehouse',
                'role_display_name' => '仓库管理员',
                'status' => 1,
                'last_login_at' => '2024-12-01 09:15:00',
                'created_at' => '2024-01-02 10:00:00'
            ]
        ];
    }

    /**
     * 获取模拟角色数据
     */
    private function getMockRoles()
    {
        return [
            ['id' => 1, 'name' => 'admin', 'display_name' => '系统管理员'],
            ['id' => 2, 'name' => 'warehouse', 'display_name' => '仓库管理员'],
            ['id' => 3, 'name' => 'chef', 'display_name' => '厨师长'],
            ['id' => 4, 'name' => 'buyer', 'display_name' => '采购员']
        ];
    }

    /**
     * 获取模拟统计数据
     */
    private function getMockStats()
    {
        return [
            'total_users' => 25,
            'active_users' => 22,
            'inactive_users' => 3,
            'recent_login_users' => 18,
            'role_stats' => [
                ['display_name' => '系统管理员', 'user_count' => 2],
                ['display_name' => '仓库管理员', 'user_count' => 3],
                ['display_name' => '厨师长', 'user_count' => 5],
                ['display_name' => '采购员', 'user_count' => 12]
            ]
        ];
    }
}
?>