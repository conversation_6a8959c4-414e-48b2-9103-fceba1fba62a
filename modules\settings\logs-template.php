<?php require_once dirname(__DIR__, 2) . '/includes/header-modular.php'; ?>

<div class="main-content">
    <?php require_once dirname(__DIR__, 2) . '/includes/sidebar-modular.php'; ?>
    
    <div class="content">
        <div class="content-header">
            <h1>
                <i class="fas fa-file-alt"></i>
                系统日志
            </h1>
            <div class="header-actions">
                <a href="index.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i>
                    返回设置
                </a>
                <button class="btn btn-primary" onclick="location.reload()">
                    <i class="fas fa-sync-alt"></i>
                    刷新日志
                </button>
            </div>
        </div>

        <div class="logs-container">
            <!-- 日志统计 -->
            <div class="logs-stats">
                <div class="stat-card">
                    <div class="stat-icon info">
                        <i class="fas fa-info-circle"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?= count(array_filter($logs ?? [], function($log) { return strpos($log, '[INFO]') !== false; })) ?></h3>
                        <p>信息日志</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon warning">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?= count(array_filter($logs ?? [], function($log) { return strpos($log, '[WARNING]') !== false; })) ?></h3>
                        <p>警告日志</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon error">
                        <i class="fas fa-times-circle"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?= count(array_filter($logs ?? [], function($log) { return strpos($log, '[ERROR]') !== false; })) ?></h3>
                        <p>错误日志</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon success">
                        <i class="fas fa-list"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?= count($logs ?? []) ?></h3>
                        <p>总计条数</p>
                    </div>
                </div>
            </div>

            <!-- 日志过滤 -->
            <div class="logs-filter">
                <div class="filter-group">
                    <label>日志级别：</label>
                    <select id="logLevel" onchange="filterLogs()">
                        <option value="">全部</option>
                        <option value="INFO">信息</option>
                        <option value="WARNING">警告</option>
                        <option value="ERROR">错误</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>搜索关键词：</label>
                    <input type="text" id="logSearch" placeholder="搜索日志内容..." oninput="filterLogs()">
                </div>
                <div class="filter-actions">
                    <button class="btn btn-secondary btn-sm" onclick="clearFilters()">
                        <i class="fas fa-times"></i>
                        清除过滤
                    </button>
                </div>
            </div>

            <!-- 日志列表 -->
            <div class="logs-list">
                <?php if (!empty($logs)): ?>
                    <?php foreach ($logs as $index => $log): ?>
                        <?php
                        $logLevel = 'INFO';
                        $logClass = 'info';
                        if (strpos($log, '[ERROR]') !== false) {
                            $logLevel = 'ERROR';
                            $logClass = 'error';
                        } elseif (strpos($log, '[WARNING]') !== false) {
                            $logLevel = 'WARNING';
                            $logClass = 'warning';
                        }
                        ?>
                        <div class="log-item <?= $logClass ?>" data-level="<?= $logLevel ?>" data-content="<?= htmlspecialchars(strtolower($log)) ?>">
                            <div class="log-level">
                                <span class="level-badge <?= $logClass ?>">
                                    <?php if ($logLevel === 'ERROR'): ?>
                                        <i class="fas fa-times-circle"></i>
                                    <?php elseif ($logLevel === 'WARNING'): ?>
                                        <i class="fas fa-exclamation-triangle"></i>
                                    <?php else: ?>
                                        <i class="fas fa-info-circle"></i>
                                    <?php endif; ?>
                                    <?= $logLevel ?>
                                </span>
                            </div>
                            <div class="log-content">
                                <pre><?= htmlspecialchars($log) ?></pre>
                            </div>
                            <div class="log-actions">
                                <button class="btn btn-sm btn-secondary" onclick="copyLog(<?= $index ?>)">
                                    <i class="fas fa-copy"></i>
                                    复制
                                </button>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="empty-logs">
                        <i class="fas fa-file-alt"></i>
                        <h3>暂无日志记录</h3>
                        <p>系统还没有生成任何日志记录</p>
                    </div>
                <?php endif; ?>
            </div>

            <!-- 分页 -->
            <?php if (!empty($logs) && count($logs) >= 50): ?>
            <div class="logs-pagination">
                <div class="pagination-info">
                    显示第 <?= (($current_page - 1) * 50) + 1 ?> - <?= $current_page * 50 ?> 条记录
                </div>
                <div class="pagination-controls">
                    <?php if ($current_page > 1): ?>
                        <a href="?action=logs&page=<?= $current_page - 1 ?>" class="btn btn-sm btn-secondary">
                            <i class="fas fa-chevron-left"></i>
                            上一页
                        </a>
                    <?php endif; ?>
                    <span class="page-current">第 <?= $current_page ?> 页</span>
                    <a href="?action=logs&page=<?= $current_page + 1 ?>" class="btn btn-sm btn-secondary">
                        下一页
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
.logs-container {
    max-width: 1200px;
}

.logs-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
}

.stat-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    display: flex;
    align-items: center;
    gap: 16px;
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
}

.stat-icon.info {
    background: #dbeafe;
    color: #2563eb;
}

.stat-icon.warning {
    background: #fef3c7;
    color: #d97706;
}

.stat-icon.error {
    background: #fee2e2;
    color: #dc2626;
}

.stat-icon.success {
    background: #dcfce7;
    color: #166534;
}

.stat-content h3 {
    margin: 0 0 4px 0;
    font-size: 24px;
    font-weight: 600;
    color: #1f2937;
}

.stat-content p {
    margin: 0;
    color: #6b7280;
    font-size: 14px;
}

.logs-filter {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.filter-group label {
    font-weight: 500;
    color: #374151;
    white-space: nowrap;
}

.filter-group select,
.filter-group input {
    padding: 6px 12px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 14px;
}

.logs-list {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    overflow: hidden;
}

.log-item {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    padding: 16px;
    border-bottom: 1px solid #e5e7eb;
    transition: background-color 0.2s ease;
}

.log-item:last-child {
    border-bottom: none;
}

.log-item:hover {
    background: #f9fafb;
}

.log-item.error {
    border-left: 4px solid #dc2626;
}

.log-item.warning {
    border-left: 4px solid #d97706;
}

.log-item.info {
    border-left: 4px solid #2563eb;
}

.log-level {
    flex-shrink: 0;
}

.level-badge {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.level-badge.error {
    background: #fee2e2;
    color: #dc2626;
}

.level-badge.warning {
    background: #fef3c7;
    color: #d97706;
}

.level-badge.info {
    background: #dbeafe;
    color: #2563eb;
}

.log-content {
    flex: 1;
    min-width: 0;
}

.log-content pre {
    margin: 0;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.4;
    color: #374151;
    white-space: pre-wrap;
    word-break: break-word;
}

.log-actions {
    flex-shrink: 0;
}

.empty-logs {
    text-align: center;
    padding: 60px 20px;
    color: #6b7280;
}

.empty-logs i {
    font-size: 48px;
    margin-bottom: 16px;
    color: #d1d5db;
}

.empty-logs h3 {
    margin: 0 0 8px 0;
    color: #374151;
}

.empty-logs p {
    margin: 0;
}

.logs-pagination {
    background: white;
    border-radius: 8px;
    padding: 16px 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    margin-top: 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.pagination-info {
    color: #6b7280;
    font-size: 14px;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 12px;
}

.page-current {
    color: #374151;
    font-weight: 500;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-secondary {
    background: #f3f4f6;
    color: #6b7280;
    border: 1px solid #d1d5db;
}

.btn-secondary:hover {
    background: #e5e7eb;
}

@media (max-width: 768px) {
    .logs-filter {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-group {
        flex-direction: column;
        align-items: stretch;
    }
    
    .log-item {
        flex-direction: column;
        gap: 12px;
    }
    
    .logs-pagination {
        flex-direction: column;
        gap: 12px;
        text-align: center;
    }
}
</style>

<script>
function filterLogs() {
    const level = document.getElementById('logLevel').value;
    const search = document.getElementById('logSearch').value.toLowerCase();
    const items = document.querySelectorAll('.log-item');
    
    items.forEach(item => {
        const itemLevel = item.getAttribute('data-level');
        const itemContent = item.getAttribute('data-content');
        
        const levelMatch = !level || itemLevel === level;
        const searchMatch = !search || itemContent.includes(search);
        
        item.style.display = (levelMatch && searchMatch) ? 'flex' : 'none';
    });
}

function clearFilters() {
    document.getElementById('logLevel').value = '';
    document.getElementById('logSearch').value = '';
    filterLogs();
}

function copyLog(index) {
    const logItem = document.querySelectorAll('.log-content pre')[index];
    if (logItem) {
        navigator.clipboard.writeText(logItem.textContent).then(() => {
            alert('日志内容已复制到剪贴板');
        }).catch(() => {
            alert('复制失败，请手动选择复制');
        });
    }
}
</script>

<?php require_once dirname(__DIR__, 2) . '/includes/footer-modular.php'; ?>
