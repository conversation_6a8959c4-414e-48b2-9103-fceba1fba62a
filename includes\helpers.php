<?php
/**
 * 全局辅助函数
 */

// 会话确保
if (session_status() === PHP_SESSION_NONE) { @session_start(); }

if (!function_exists('currentUserId')) {
    function currentUserId() { return $_SESSION['user_id'] ?? null; }
}
if (!function_exists('currentUserRole')) {
    function currentUserRole() { return $_SESSION['user_role'] ?? null; }
}

// 获取用户权限（名称数组）
if (!function_exists('getUserPermissions')) {
    function getUserPermissions($userId = null) {
        if ($userId === null) { $userId = currentUserId(); }
        if (!$userId) { return []; }
        if (!empty($_SESSION['permissions']) && is_array($_SESSION['permissions'])) {
            return $_SESSION['permissions'];
        }
        require_once __DIR__ . '/Database.php';
        $db = Database::getInstance();
        try {
            $rows = $db->fetchAll(
                "SELECT p.name FROM role_permissions rp
                 JOIN permissions p ON rp.permission_id = p.id AND p.status = 1
                 JOIN user_roles ur ON rp.role_id = ur.role_id
                 WHERE ur.user_id = ?",
                [$userId]
            );
            $names = [];
            foreach ($rows as $r) { if (isset($r['name'])) { $names[] = $r['name']; } }
            $perms = array_values(array_unique($names));
            $_SESSION['permissions'] = $perms;
            return $perms;
        } catch (Exception $e) {
            return [];
        }
    }
}

// 简易权限判断
if (!function_exists('can')) {
    function can($permissionName) {
        // 若未登录，默认允许仅查看类操作
        if (!currentUserId()) { return true; }
        $perms = getUserPermissions(currentUserId());
        if (empty($perms)) {
            // 默认放行，避免未初始化时阻塞。可按需改为严格模式
            return true;
        }
        return in_array($permissionName, $perms);
    }
}


/**
 * 格式化货币
 */
function formatCurrency($amount, $symbol = '¥') {
    return $symbol . number_format($amount, 2);
}

/**
 * 格式化百分比
 */
function formatPercent($value, $decimals = 1) {
    return number_format($value, $decimals) . '%';
}

/**
 * 安全输出HTML
 */
function e($value) {
    return htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
}

/**
 * 获取配置值
 */
function config($key, $default = null) {
    static $config = null;
    
    if ($config === null) {
        $config = require dirname(__DIR__) . '/config/app.php';
    }
    
    $keys = explode('.', $key);
    $value = $config;
    
    foreach ($keys as $k) {
        if (!isset($value[$k])) {
            return $default;
        }
        $value = $value[$k];
    }
    
    return $value;
}

/**
 * 生成URL
 */
function url($path = '') {
    $baseUrl = config('url.base', 'http://localhost:8080');
    return rtrim($baseUrl, '/') . '/' . ltrim($path, '/');
}

/**
 * 生成资源URL
 */
function asset($path) {
    $assetUrl = config('url.assets', 'http://localhost:8080/assets');
    return rtrim($assetUrl, '/') . '/' . ltrim($path, '/');
}

/**
 * 获取状态徽章信息
 */
function getStatusBadge($status, $statusMap) {
    if (!isset($statusMap[$status])) {
        return ['class' => 'badge-secondary', 'text' => '未知'];
    }
    
    $statusInfo = $statusMap[$status];
    return [
        'class' => 'badge-' . $statusInfo['class'],
        'text' => $statusInfo['name']
    ];
}

/**
 * 生成订单号
 */
function generateOrderNumber($prefix = 'PO') {
    return $prefix . date('Ymd') . sprintf('%04d', rand(1, 9999));
}

/**
 * 验证日期格式
 */
function isValidDate($date, $format = 'Y-m-d') {
    $d = DateTime::createFromFormat($format, $date);
    return $d && $d->format($format) === $date;
}

/**
 * 获取文件大小的人类可读格式
 */
function formatFileSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, 2) . ' ' . $units[$i];
}

/**
 * 生成随机字符串
 */
function generateRandomString($length = 10) {
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $charactersLength = strlen($characters);
    $randomString = '';
    
    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }
    
    return $randomString;
}

/**
 * 检查是否为AJAX请求
 */
function isAjax() {
    return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
           strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
}

/**
 * 获取客户端IP地址
 */
function getClientIp() {
    $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
    
    foreach ($ipKeys as $key) {
        if (!empty($_SERVER[$key])) {
            $ips = explode(',', $_SERVER[$key]);
            return trim($ips[0]);
        }
    }
    
    return '0.0.0.0';
}

/**
 * 数组转换为HTML属性字符串
 */
function arrayToAttributes($attributes) {
    $html = [];
    
    foreach ($attributes as $key => $value) {
        if (is_bool($value)) {
            if ($value) {
                $html[] = $key;
            }
        } else {
            $html[] = $key . '="' . e($value) . '"';
        }
    }
    
    return implode(' ', $html);
}

/**
 * 截断文本（兼容无mbstring扩展的环境）
 */
function truncate($text, $length = 100, $suffix = '...') {
    // 检查是否有mbstring扩展
    if (function_exists('mb_strlen')) {
        if (mb_strlen($text) <= $length) {
            return $text;
        }
        return mb_substr($text, 0, $length) . $suffix;
    } else {
        // 使用普通字符串函数作为后备
        if (strlen($text) <= $length) {
            return $text;
        }
        return substr($text, 0, $length) . $suffix;
    }
}

/**
 * 时间差的人类可读格式
 */
function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) {
        return '刚刚';
    } elseif ($time < 3600) {
        return floor($time / 60) . '分钟前';
    } elseif ($time < 86400) {
        return floor($time / 3600) . '小时前';
    } elseif ($time < 2592000) {
        return floor($time / 86400) . '天前';
    } elseif ($time < 31536000) {
        return floor($time / 2592000) . '个月前';
    } else {
        return floor($time / 31536000) . '年前';
    }
}

/**
 * 验证邮箱格式
 */
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * 验证手机号格式
 */
function isValidPhone($phone) {
    return preg_match('/^1[3-9]\d{9}$/', $phone);
}

/**
 * 生成分页HTML
 */
function generatePagination($currentPage, $totalPages, $baseUrl) {
    if ($totalPages <= 1) {
        return '';
    }
    
    $html = '<nav aria-label="分页导航"><ul class="pagination">';
    
    // 上一页
    if ($currentPage > 1) {
        $prevUrl = $baseUrl . '?page=' . ($currentPage - 1);
        $html .= '<li class="page-item"><a class="page-link" href="' . $prevUrl . '">上一页</a></li>';
    }
    
    // 页码
    $start = max(1, $currentPage - 2);
    $end = min($totalPages, $currentPage + 2);
    
    for ($i = $start; $i <= $end; $i++) {
        $active = $i == $currentPage ? ' active' : '';
        $pageUrl = $baseUrl . '?page=' . $i;
        $html .= '<li class="page-item' . $active . '"><a class="page-link" href="' . $pageUrl . '">' . $i . '</a></li>';
    }
    
    // 下一页
    if ($currentPage < $totalPages) {
        $nextUrl = $baseUrl . '?page=' . ($currentPage + 1);
        $html .= '<li class="page-item"><a class="page-link" href="' . $nextUrl . '">下一页</a></li>';
    }
    
    $html .= '</ul></nav>';
    
    return $html;
}

/**
 * 调试输出
 */
function dd($data) {
    echo '<pre>';
    var_dump($data);
    echo '</pre>';
    die();
}

/**
 * 记录调试信息
 */
function debug($data, $label = '') {
    if (config('debug', false)) {
        $logFile = config('paths.root') . '/logs/debug.log';
        $logDir = dirname($logFile);
        
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        $timestamp = date('Y-m-d H:i:s');
        $message = $label ? "[$label] " : '';
        $message .= is_string($data) ? $data : json_encode($data, JSON_UNESCAPED_UNICODE);
        $logMessage = "[{$timestamp}] {$message}" . PHP_EOL;
        
        file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
    }
}
?>
