<?php
/**
 * 创建用户表
 */

require_once 'includes/Database.php';

echo "<h2>创建用户表</h2>";

try {
    $db = Database::getInstance();
    echo "<p>✅ 数据库连接成功</p>";
    
    // 检查表是否已存在
    $tableExists = $db->fetchOne("SHOW TABLES LIKE 'users'");
    
    if ($tableExists) {
        echo "<p style='color: orange;'>⚠️ users表已存在</p>";
        
        // 显示现有表结构
        $columns = $db->fetchAll("DESCRIBE users");
        echo "<h3>现有表结构：</h3>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr style='background: #f0f0f0;'><th>字段</th><th>类型</th><th>空值</th><th>键</th><th>默认值</th><th>额外</th></tr>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Default'] ?? 'NULL') . "</td>";
            echo "<td>" . htmlspecialchars($column['Extra']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
    } else {
        echo "<p>📝 创建users表...</p>";
        
        $createTableSQL = "
        CREATE TABLE users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
            name VARCHAR(100) NOT NULL COMMENT '姓名',
            phone VARCHAR(20) COMMENT '手机号码',
            email VARCHAR(100) COMMENT '邮箱地址',
            role ENUM('admin', 'chef', 'buyer', 'staff') NOT NULL DEFAULT 'staff' COMMENT '角色',
            department VARCHAR(100) COMMENT '部门',
            status TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态：1=在职，0=离职',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户表'
        ";
        
        $db->query($createTableSQL);
        echo "<p style='color: green;'>✅ users表创建成功</p>";
        
        // 插入示例数据
        echo "<p>📝 插入示例数据...</p>";
        
        $sampleUsers = [
            [
                'username' => 'admin',
                'name' => '系统管理员',
                'phone' => '13800138000',
                'email' => '<EMAIL>',
                'role' => 'admin',
                'department' => '管理部'
            ],
            [
                'username' => 'chef01',
                'name' => '张师傅',
                'phone' => '13800138001',
                'email' => '<EMAIL>',
                'role' => 'chef',
                'department' => '厨房部'
            ],
            [
                'username' => 'chef02',
                'name' => '李师傅',
                'phone' => '13800138002',
                'email' => '<EMAIL>',
                'role' => 'chef',
                'department' => '厨房部'
            ],
            [
                'username' => 'buyer01',
                'name' => '王采购',
                'phone' => '13800138003',
                'email' => '<EMAIL>',
                'role' => 'buyer',
                'department' => '采购部'
            ],
            [
                'username' => 'staff01',
                'name' => '赵员工',
                'phone' => '13800138004',
                'email' => '<EMAIL>',
                'role' => 'staff',
                'department' => '后勤部'
            ]
        ];
        
        foreach ($sampleUsers as $user) {
            $db->insert('users', $user);
            echo "<p style='color: green;'>✅ 添加用户：{$user['name']} ({$user['username']})</p>";
        }
        
        echo "<p style='color: green; font-weight: bold;'>🎉 示例数据插入完成</p>";
    }
    
    // 显示当前用户数据
    echo "<h3>当前用户数据：</h3>";
    $users = $db->fetchAll("SELECT * FROM users ORDER BY created_at DESC");
    
    if (!empty($users)) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th>ID</th><th>用户名</th><th>姓名</th><th>角色</th><th>部门</th><th>手机</th><th>状态</th><th>创建时间</th>";
        echo "</tr>";
        
        foreach ($users as $user) {
            $roleNames = [
                'admin' => '管理员',
                'chef' => '厨师',
                'buyer' => '采购员',
                'staff' => '员工'
            ];
            
            $statusText = $user['status'] ? '在职' : '离职';
            $statusColor = $user['status'] ? 'green' : 'red';
            
            echo "<tr>";
            echo "<td>" . $user['id'] . "</td>";
            echo "<td><strong>" . htmlspecialchars($user['username']) . "</strong></td>";
            echo "<td>" . htmlspecialchars($user['name']) . "</td>";
            echo "<td>" . ($roleNames[$user['role']] ?? $user['role']) . "</td>";
            echo "<td>" . htmlspecialchars($user['department'] ?? '') . "</td>";
            echo "<td>" . htmlspecialchars($user['phone'] ?? '') . "</td>";
            echo "<td style='color: $statusColor;'>" . $statusText . "</td>";
            echo "<td>" . $user['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<p>📊 总计：" . count($users) . " 个用户</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ 暂无用户数据</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red; font-weight: bold;'>❌ 错误: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='modules/users/index.php'>访问人员管理</a> | <a href='index.php'>返回首页</a></p>";
?>
