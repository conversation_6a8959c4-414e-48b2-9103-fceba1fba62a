<?php
/**
 * 测试修复后的分类查询
 */

require_once '../includes/Database.php';

echo "<h2>测试修复后的分类查询</h2>";

try {
    $db = Database::getInstance();
    echo "<p>✅ 数据库连接成功</p>";
    
    // 测试修复后的查询
    echo "<h3>修复后的查询测试：</h3>";
    
    $fixedQuery = "
        SELECT c.*, 
               COUNT(i.id) as ingredient_count,
               COALESCE(SUM(CASE WHEN i.unit_price IS NOT NULL AND i.current_stock IS NOT NULL 
                                 THEN i.current_stock * i.unit_price 
                                 ELSE 0 END), 0) as total_value
        FROM ingredient_categories c
        LEFT JOIN ingredients i ON c.id = i.category_id AND i.status = 1
        WHERE c.status = 1
        GROUP BY c.id
        ORDER BY c.sort_order ASC, c.name ASC
    ";
    
    echo "<p>📝 执行的查询：</p>";
    echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>" . htmlspecialchars($fixedQuery) . "</pre>";
    
    try {
        $categories = $db->fetchAll($fixedQuery);
        echo "<p style='color: green;'>✅ 查询执行成功</p>";
        echo "<p>📊 返回结果：" . count($categories) . " 个分类</p>";
        
        if (!empty($categories)) {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
            echo "<tr style='background: #f0f0f0;'>";
            echo "<th>ID</th><th>名称</th><th>代码</th><th>排序</th><th>食材数量</th><th>总价值</th><th>状态</th>";
            echo "</tr>";
            
            foreach ($categories as $category) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($category['id']) . "</td>";
                echo "<td><strong>" . htmlspecialchars($category['name']) . "</strong></td>";
                echo "<td>" . htmlspecialchars($category['code'] ?? '') . "</td>";
                echo "<td>" . htmlspecialchars($category['sort_order'] ?? '0') . "</td>";
                echo "<td>" . intval($category['ingredient_count']) . "</td>";
                echo "<td>¥" . number_format(floatval($category['total_value']), 2) . "</td>";
                echo "<td>" . ($category['status'] ? '启用' : '禁用') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p style='color: orange;'>⚠️ 查询结果为空</p>";
            echo "<p>可能的原因：</p>";
            echo "<ul>";
            echo "<li>没有状态为1的分类</li>";
            echo "<li>分类表为空</li>";
            echo "<li>数据库连接问题</li>";
            echo "</ul>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ 查询执行失败: " . $e->getMessage() . "</p>";
    }
    
    // 对比原查询（如果有问题的话）
    echo "<h3>原查询对比测试：</h3>";
    
    $originalQuery = "
        SELECT c.*, 
               COUNT(i.id) as ingredient_count,
               SUM(i.current_stock * i.unit_price) as total_value
        FROM ingredient_categories c
        LEFT JOIN ingredients i ON c.id = i.category_id AND i.status = 1
        WHERE status = 1
        GROUP BY c.id
        ORDER BY c.name ASC
    ";
    
    echo "<p>📝 原查询（可能有歧义）：</p>";
    echo "<pre style='background: #fff3cd; padding: 10px; border-radius: 5px;'>" . htmlspecialchars($originalQuery) . "</pre>";
    
    try {
        $originalCategories = $db->fetchAll($originalQuery);
        echo "<p style='color: blue;'>ℹ️ 原查询执行成功，返回 " . count($originalCategories) . " 个分类</p>";
        
        if (count($originalCategories) != count($categories)) {
            echo "<p style='color: orange;'>⚠️ 查询结果数量不同，说明确实存在歧义问题</p>";
        } else {
            echo "<p style='color: green;'>✅ 查询结果数量相同</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ 原查询执行失败: " . $e->getMessage() . "</p>";
        echo "<p>这证明了原查询确实有问题</p>";
    }
    
    // 检查分类状态
    echo "<h3>分类状态检查：</h3>";
    
    $statusCheck = $db->fetchAll("
        SELECT status, COUNT(*) as count 
        FROM ingredient_categories 
        GROUP BY status 
        ORDER BY status
    ");
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr style='background: #f0f0f0;'><th>状态值</th><th>数量</th><th>说明</th></tr>";
    
    foreach ($statusCheck as $status) {
        $statusValue = $status['status'];
        $statusText = '';
        $statusColor = '';
        
        if ($statusValue === null || $statusValue === '') {
            $statusText = 'NULL/空值';
            $statusColor = 'orange';
        } elseif ($statusValue == 1) {
            $statusText = '启用';
            $statusColor = 'green';
        } elseif ($statusValue == 0) {
            $statusText = '禁用';
            $statusColor = 'red';
        } else {
            $statusText = '其他值';
            $statusColor = 'purple';
        }
        
        echo "<tr>";
        echo "<td>" . htmlspecialchars($statusValue ?? 'NULL') . "</td>";
        echo "<td>" . $status['count'] . "</td>";
        echo "<td style='color: $statusColor;'>" . $statusText . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 性能测试
    echo "<h3>查询性能测试：</h3>";
    
    $iterations = 10;
    $times = [];
    
    for ($i = 0; $i < $iterations; $i++) {
        $start = microtime(true);
        $db->fetchAll($fixedQuery);
        $end = microtime(true);
        $times[] = ($end - $start) * 1000; // 转换为毫秒
    }
    
    $avgTime = array_sum($times) / count($times);
    $minTime = min($times);
    $maxTime = max($times);
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr style='background: #f0f0f0;'><th>指标</th><th>值</th></tr>";
    echo "<tr><td>测试次数</td><td>$iterations</td></tr>";
    echo "<tr><td>平均耗时</td><td>" . number_format($avgTime, 2) . " ms</td></tr>";
    echo "<tr><td>最短耗时</td><td>" . number_format($minTime, 2) . " ms</td></tr>";
    echo "<tr><td>最长耗时</td><td>" . number_format($maxTime, 2) . " ms</td></tr>";
    echo "</table>";
    
} catch (Exception $e) {
    echo "<p style='color: red; font-weight: bold;'>❌ 数据库连接失败: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='../modules/categories/index.php'>查看分类列表</a> | <a href='debug-categories-list.php'>调试分类列表</a> | <a href='../index.php'>返回首页</a></p>";
?>
