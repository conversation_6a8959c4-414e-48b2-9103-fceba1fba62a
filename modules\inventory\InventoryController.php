<?php
/**
 * 库存管理控制器
 */

class InventoryController extends BaseController
{
    public function __construct()
    {
        parent::__construct();
        $this->setTemplateData('page_title', '库存管理');
    }

    public function handleRequest()
    {
        switch ($this->request['action']) {
            case 'inbound':
                return $this->inbound();
            case 'outbound':
                return $this->outbound();
            case 'stocktaking':
                return $this->stocktaking();
            case 'damage':
                return $this->damage();
            case 'stock':
                return $this->stockDetail();
            case 'query':
            default:
                return $this->query();
        }
    }

    /**
     * 库存查询
     */
    private function query()
    {
        // 获取搜索参数
        $search = trim($this->request['get']['search'] ?? '');
        $category = trim($this->request['get']['category'] ?? '');
        $status = trim($this->request['get']['status'] ?? '');

        // 获取库存数据
        try {
            $inventory = $this->getInventoryData($search, $category, $status);
        } catch (Exception $e) {
            // 后备为模拟数据，避免空白页
            $inventory = $this->getMockInventoryData();
        }

        // 分类与统计独立获取，容错
        try {
            $categories = $this->db->fetchAll("SELECT * FROM ingredient_categories WHERE status = 1 ORDER BY name");
        } catch (Exception $e) {
            $categories = $this->getMockCategories();
        }
        try {
            $stats = $this->getInventoryStats();
        } catch (Exception $e) {
            $stats = $this->getMockStats();
        }

        $this->setTemplateData([
            'inventory' => $inventory,
            'categories' => $categories,
            'stats' => $stats,
            'search' => $search,
            'category' => $category,
            'status' => $status,
            // 兼容模板中的 $data[...] 访问
            'data' => [
                'inventory' => $inventory,
                'categories' => $categories,
                'stats' => $stats,
                'search' => $search,
                'category' => $category,
                'status' => $status,
            ]
        ]);

        $this->render('template.php');
    }

    /**
     * 食材入库
     */
    private function inbound()
    {
        header('Location: ../inbound/index.php');
        exit;
    }

    /**
     * 食材出库
     */
    private function outbound()
    {
        $this->setTemplateData('current_action', 'outbound');
        $this->render('outbound-template.php');
    }

    /**
     * 库存盘点
     */
    private function stocktaking()
    {
        $this->setTemplateData('current_action', 'stocktaking');
        $this->render('stocktaking-template.php');
    }

    /**
     * 食材报损
     */
    private function damage()
    {
        // 列表可查看
        if (($this->request['get']['operation'] ?? '') === 'create' || $this->request['get']['operation'] ?? '' === 'save') {
            $this->requirePermission('damage.create');
        }
        $operation = trim($this->request['get']['operation'] ?? '');
        // 确保表存在（无侵入性，若已存在则忽略）
        $this->ensureDamageTable();
        
        switch ($operation) {
            case 'create':
                return $this->damageCreate();
            case 'view':
                return $this->damageView();
            default:
                return $this->damageList();
        }
    }

    /**
     * 报损列表
     */
    private function damageList()
    {
        // 获取搜索参数
        $search = trim($this->request['get']['search'] ?? '');
        $damageType = trim($this->request['get']['damage_type'] ?? '');
        $dateFrom = trim($this->request['get']['date_from'] ?? '');
        $dateTo = trim($this->request['get']['date_to'] ?? '');

        // 获取报损数据
        try {
            $damageRecords = $this->getDamageRecords($search, $damageType, $dateFrom, $dateTo);
            $stats = $this->getDamageStats();
            $summary = $this->getDamageSummary();
        } catch (Exception $e) {
            $damageRecords = $this->getMockDamageRecords();
            $stats = $this->getMockDamageStats();
            $summary = $this->getMockDamageSummary();
        }

        $this->setTemplateData([
            'current_action' => 'damage',
            'damage_records' => $damageRecords,
            'stats' => $stats,
            'summary' => $summary,
            'search' => $search,
            'damage_type' => $damageType,
            'date_from' => $dateFrom,
            'date_to' => $dateTo,
            // 兼容模板中的 $data[...] 结构
            'data' => [
                'damage_records' => $damageRecords,
                'stats' => $stats,
                'summary' => $summary,
                'search' => $search,
                'damage_type' => $damageType,
                'date_from' => $dateFrom,
                'date_to' => $dateTo,
            ],
        ]);

        $this->render('damage-template.php');
    }

    /**
     * 报损详情
     */
    private function damageView()
    {
        try {
            $id = intval($this->request['get']['id'] ?? 0);
            if ($id <= 0) {
                throw new Exception('无效的报损记录ID');
            }

            $record = $this->db->fetchOne(
                "SELECT dr.*, i.name AS ingredient_name, i.unit, u.name AS operator_name
                 FROM damage_records dr
                 LEFT JOIN ingredients i ON dr.ingredient_id = i.id
                 LEFT JOIN users u ON dr.operator_id = u.id
                 WHERE dr.id = ?",
                [$id]
            );

            if (!$record) {
                throw new Exception('未找到报损记录');
            }

            $this->setTemplateData([
                'current_action' => 'damage',
                'record' => $record,
            ]);

            $this->render('damage-view-template.php');
        } catch (Exception $e) {
            $this->setTemplateData([
                'current_action' => 'damage',
                'error_message' => $e->getMessage(),
            ]);
            $this->render('damage-template.php');
        }
    }

    /**
     * 新增报损
     */
    private function damageCreate()
    {
        // 处理POST请求 - 保存报损记录
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            return $this->saveDamageRecord();
        }

        // 获取食材列表用于下拉选择
        try {
            $ingredients = $this->getIngredientsForSelect();
        } catch (Exception $e) {
            $ingredients = $this->getMockIngredientsForSelect();
        }

        $this->setTemplateData([
            'page_title' => '新增报损',
            'current_action' => 'damage',
            'operation' => 'create',
            'ingredients' => $ingredients
        ]);

        $this->render('damage-create-template.php');
    }

    /**
     * 保存报损记录
     */
    private function saveDamageRecord()
    {
        $this->requirePermission('damage.create');
        try {
            // 确保表存在
            $this->ensureDamageTable();
            // 获取表单数据
            $ingredientId = intval($_POST['ingredient_id'] ?? 0);
            $damageQuantity = floatval($_POST['damage_quantity'] ?? 0);
            $damageType = trim($_POST['damage_type'] ?? '');
            $damageReason = trim($_POST['damage_reason'] ?? '');
            $batchNumber = trim($_POST['batch_number'] ?? '');
            
            // 验证必填字段
            if ($ingredientId <= 0) {
                throw new Exception('请选择要报损的食材');
            }
            if ($damageQuantity <= 0) {
                throw new Exception('报损数量必须大于0');
            }
            if (empty($damageType)) {
                throw new Exception('请选择报损类型');
            }
            if (empty($damageReason)) {
                throw new Exception('请填写报损原因');
            }

            // 获取食材信息
            $ingredient = $this->db->fetchOne(
                "SELECT id, name, unit, unit_price, current_stock FROM ingredients WHERE id = ? AND status = 1",
                [$ingredientId]
            );

            if (!$ingredient) {
                throw new Exception('食材不存在或已停用');
            }

            // 检查库存是否足够
            if ($damageQuantity > $ingredient['current_stock']) {
                throw new Exception('报损数量不能超过当前库存(' . $ingredient['current_stock'] . $ingredient['unit'] . ')');
            }

            // 计算报损价值
            $damageValue = $damageQuantity * $ingredient['unit_price'];

            // 开启事务
            $this->db->beginTransaction();

            // 插入报损记录
            $damageRecordSql = "
                INSERT INTO damage_records (
                    ingredient_id, damage_quantity, unit_price, damage_value, 
                    damage_type, damage_reason, batch_number, damage_date, 
                    operator_id, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), ?, NOW(), NOW())
            ";

            $operatorId = $_SESSION['user_id'] ?? 1; // 默认操作员ID
            
            // 使用通用 query() 执行带占位符的 SQL
            $this->db->query($damageRecordSql, [
                $ingredientId,
                $damageQuantity,
                $ingredient['unit_price'],
                $damageValue,
                $damageType,
                $damageReason,
                $batchNumber,
                $operatorId
            ]);

            // 更新库存
            $newStock = $ingredient['current_stock'] - $damageQuantity;
            $this->db->query(
                "UPDATE ingredients SET current_stock = ?, updated_at = NOW() WHERE id = ?",
                [$newStock, $ingredientId]
            );

            // 提交事务
            $this->db->commit();

            // 记录日志
            $this->log("报损记录创建成功 - 食材: {$ingredient['name']}, 数量: {$damageQuantity}{$ingredient['unit']}, 价值: ¥{$damageValue}");

            // 返回成功页面或重定向
            header('Location: index.php?action=damage&success=1');
            exit;

        } catch (Exception $e) {
            // 回滚事务
            if ($this->db->inTransaction()) {
                $this->db->rollback();
            }

            // 记录错误
            $this->log("报损记录创建失败: " . $e->getMessage(), 'error');

            // 重新显示表单，包含错误信息
            try {
                $ingredients = $this->getIngredientsForSelect();
            } catch (Exception $e2) {
                $ingredients = $this->getMockIngredientsForSelect();
            }

            $this->setTemplateData([
                'page_title' => '新增报损',
                'current_action' => 'damage',
                'operation' => 'create',
                'ingredients' => $ingredients,
                'error_message' => $e->getMessage(),
                'form_data' => $_POST
            ]);

            $this->render('damage-create-template.php');
        }
    }

    /**
     * 确保 damage_records 表存在（若不存在则自动创建）
     */
    private function ensureDamageTable()
    {
        try {
            $this->db->query("SELECT 1 FROM damage_records LIMIT 1");
            return; // 表存在
        } catch (Exception $e) {
            // 尝试创建表
            $createSql = "
                CREATE TABLE IF NOT EXISTS damage_records (
                    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
                    ingredient_id INT UNSIGNED NOT NULL,
                    damage_quantity DECIMAL(10,2) NOT NULL DEFAULT 0,
                    unit_price DECIMAL(10,2) NOT NULL DEFAULT 0,
                    damage_value DECIMAL(12,2) NOT NULL DEFAULT 0,
                    damage_type VARCHAR(32) NOT NULL,
                    damage_reason VARCHAR(255) DEFAULT NULL,
                    batch_number VARCHAR(64) DEFAULT NULL,
                    damage_date DATETIME NOT NULL,
                    operator_id INT UNSIGNED DEFAULT NULL,
                    created_at DATETIME DEFAULT NULL,
                    updated_at DATETIME DEFAULT NULL,
                    INDEX idx_ingredient (ingredient_id),
                    INDEX idx_damage_date (damage_date),
                    INDEX idx_damage_type (damage_type)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            ";
            try {
                $this->db->query($createSql);
            } catch (Exception $e2) {
                // 如果创建失败，向上抛出原错误
                throw $e;
            }
        }
    }

    /**
     * 获取库存数据
     */
    private function getInventoryData($search = '', $category = '', $status = '')
    {
        $sql = "
            SELECT 
                i.id,
                i.name,
                i.unit,
                i.current_stock,
                i.min_stock,
                i.unit_price,
                ic.name as category_name,
                CASE 
                    WHEN i.current_stock <= 0 THEN 'out_of_stock'
                    WHEN i.current_stock <= i.min_stock THEN 'low_stock'
                    WHEN i.current_stock <= i.min_stock * 1.5 THEN 'warning'
                    ELSE 'normal'
                END as stock_status
            FROM ingredients i
            LEFT JOIN ingredient_categories ic ON i.category_id = ic.id
            WHERE i.status = 1
        ";

        $params = [];

        if (!empty($search)) {
            $sql .= " AND i.name LIKE ?";
            $params[] = "%{$search}%";
        }

        if (!empty($category)) {
            $sql .= " AND i.category_id = ?";
            $params[] = $category;
        }

        if (!empty($status)) {
            switch ($status) {
                case 'out_of_stock':
                    $sql .= " AND i.current_stock <= 0";
                    break;
                case 'low_stock':
                    $sql .= " AND i.current_stock > 0 AND i.current_stock <= i.min_stock";
                    break;
                case 'warning':
                    $sql .= " AND i.current_stock > i.min_stock AND i.current_stock <= i.min_stock * 1.5";
                    break;
                case 'normal':
                    $sql .= " AND i.current_stock > i.min_stock * 1.5";
                    break;
            }
        }

        $sql .= " ORDER BY i.name ASC";

        return $this->db->fetchAll($sql, $params);
    }

    /**
     * 获取库存统计
     */
    private function getInventoryStats()
    {
        $stats = [
            'total_items' => 0,
            'out_of_stock' => 0,
            'low_stock' => 0,
            'warning_stock' => 0,
            'normal_stock' => 0,
            'total_value' => 0
        ];

        $sql = "
            SELECT 
                COUNT(*) as total_items,
                SUM(CASE WHEN current_stock <= 0 THEN 1 ELSE 0 END) as out_of_stock,
                SUM(CASE WHEN current_stock > 0 AND current_stock <= min_stock THEN 1 ELSE 0 END) as low_stock,
                SUM(CASE WHEN current_stock > min_stock AND current_stock <= min_stock * 1.5 THEN 1 ELSE 0 END) as warning_stock,
                SUM(CASE WHEN current_stock > min_stock * 1.5 THEN 1 ELSE 0 END) as normal_stock,
                SUM(current_stock * unit_price) as total_value
            FROM ingredients 
            WHERE status = 1
        ";

        $result = $this->db->fetchOne($sql);
        if ($result) {
            $stats = array_merge($stats, $result);
        }

        return $stats;
    }

    /**
     * 获取模拟库存数据
     */
    private function getMockInventoryData()
    {
        return [
            [
                'id' => 1,
                'name' => '白菜',
                'unit' => '斤',
                'current_stock' => 25.5,
                'min_stock' => 20.0,
                'unit_price' => 2.50,
                'category_name' => '蔬菜类',
                'stock_status' => 'normal'
            ],
            [
                'id' => 2,
                'name' => '土豆',
                'unit' => '斤',
                'current_stock' => 8.0,
                'min_stock' => 15.0,
                'unit_price' => 3.00,
                'category_name' => '蔬菜类',
                'stock_status' => 'low_stock'
            ],
            [
                'id' => 3,
                'name' => '猪肉',
                'unit' => '斤',
                'current_stock' => 0,
                'min_stock' => 10.0,
                'unit_price' => 28.00,
                'category_name' => '肉类',
                'stock_status' => 'out_of_stock'
            ]
        ];
    }

    /**
     * 获取模拟分类数据
     */
    private function getMockCategories()
    {
        return [
            ['id' => 1, 'name' => '蔬菜类'],
            ['id' => 2, 'name' => '肉类'],
            ['id' => 3, 'name' => '粮油类']
        ];
    }

    /**
     * 获取模拟统计数据
     */
    private function getMockStats()
    {
        return [
            'total_items' => 25,
            'out_of_stock' => 3,
            'low_stock' => 5,
            'warning_stock' => 7,
            'normal_stock' => 10,
            'total_value' => 15680.50
        ];
    }

    /**
     * 获取报损记录数据
     */
    private function getDamageRecords($search = '', $damageType = '', $dateFrom = '', $dateTo = '')
    {
        $sql = "
            SELECT 
                dr.id,
                dr.ingredient_id,
                i.name as ingredient_name,
                i.unit,
                dr.damage_quantity,
                dr.unit_price,
                dr.damage_value,
                dr.damage_type,
                dr.damage_reason,
                dr.batch_number,
                dr.damage_date,
                dr.operator_id,
                u.name as operator_name,
                dr.created_at,
                1 as can_edit
            FROM damage_records dr
            LEFT JOIN ingredients i ON dr.ingredient_id = i.id
            LEFT JOIN users u ON dr.operator_id = u.id
            WHERE 1=1
        ";

        $params = [];

        if (!empty($search)) {
            $sql .= " AND i.name LIKE ?";
            $params[] = "%{$search}%";
        }

        if (!empty($damageType)) {
            $sql .= " AND dr.damage_type = ?";
            $params[] = $damageType;
        }

        if (!empty($dateFrom)) {
            $sql .= " AND DATE(dr.damage_date) >= ?";
            $params[] = $dateFrom;
        }

        if (!empty($dateTo)) {
            $sql .= " AND DATE(dr.damage_date) <= ?";
            $params[] = $dateTo;
        }

        $sql .= " ORDER BY dr.damage_date DESC";

        return $this->db->fetchAll($sql, $params);
    }

    /**
     * 获取报损统计数据
     */
    private function getDamageStats()
    {
        $stats = [
            'total_damage_records' => 0,
            'total_damage_quantity' => 0,
            'total_damage_value' => 0,
            'this_month_records' => 0
        ];

        $sql = "
            SELECT 
                COUNT(*) as total_damage_records,
                SUM(damage_quantity) as total_damage_quantity,
                SUM(damage_value) as total_damage_value,
                SUM(CASE WHEN YEAR(damage_date) = YEAR(NOW()) AND MONTH(damage_date) = MONTH(NOW()) THEN 1 ELSE 0 END) as this_month_records
            FROM damage_records 
            WHERE 1=1
        ";

        $result = $this->db->fetchOne($sql);
        if ($result) {
            $stats = array_merge($stats, $result);
        }

        return $stats;
    }

    /**
     * 获取报损汇总数据
     */
    private function getDamageSummary()
    {
        $summary = [
            'expired_value' => 0,
            'damaged_value' => 0,
            'quality_value' => 0,
            'other_value' => 0
        ];

        $sql = "
            SELECT 
                damage_type,
                SUM(damage_value) as total_value
            FROM damage_records 
            GROUP BY damage_type
        ";

        $results = $this->db->fetchAll($sql);
        foreach ($results as $result) {
            $key = $result['damage_type'] . '_value';
            if (isset($summary[$key])) {
                $summary[$key] = $result['total_value'];
            }
        }

        return $summary;
    }

    /**
     * 获取模拟报损记录数据
     */
    private function getMockDamageRecords()
    {
        return [
            [
                'id' => 1,
                'ingredient_name' => '白菜',
                'unit' => '斤',
                'damage_quantity' => 5.0,
                'unit_price' => 2.50,
                'damage_value' => 12.50,
                'damage_type' => 'expired',
                'damage_reason' => '超过保质期',
                'batch_number' => 'BC20240125',
                'operator_name' => '张三',
                'damage_date' => date('Y-m-d H:i:s'),
                'can_edit' => true
            ],
            [
                'id' => 2,
                'ingredient_name' => '土豆',
                'unit' => '斤',
                'damage_quantity' => 3.0,
                'unit_price' => 3.00,
                'damage_value' => 9.00,
                'damage_type' => 'damaged',
                'damage_reason' => '运输过程中破损',
                'batch_number' => 'TD20240124',
                'operator_name' => '李四',
                'damage_date' => date('Y-m-d H:i:s', strtotime('-1 day')),
                'can_edit' => true
            ],
            [
                'id' => 3,
                'ingredient_name' => '猪肉',
                'unit' => '斤',
                'damage_quantity' => 2.0,
                'unit_price' => 28.00,
                'damage_value' => 56.00,
                'damage_type' => 'quality',
                'damage_reason' => '质量不符合标准',
                'batch_number' => 'ZR20240123',
                'operator_name' => '王五',
                'damage_date' => date('Y-m-d H:i:s', strtotime('-2 days')),
                'can_edit' => false
            ]
        ];
    }

    /**
     * 获取模拟报损统计数据
     */
    private function getMockDamageStats()
    {
        return [
            'total_damage_records' => 15,
            'total_damage_quantity' => 85.5,
            'total_damage_value' => 1256.80,
            'this_month_records' => 8
        ];
    }

    /**
     * 获取食材列表用于下拉选择
     */
    private function getIngredientsForSelect()
    {
        $sql = "
            SELECT 
                i.id,
                i.name,
                i.unit,
                i.current_stock,
                i.unit_price,
                ic.name as category_name
            FROM ingredients i
            LEFT JOIN ingredient_categories ic ON i.category_id = ic.id
            WHERE i.status = 1 AND i.current_stock > 0
            ORDER BY ic.name ASC, i.name ASC
        ";

        return $this->db->fetchAll($sql);
    }

    /**
     * 获取模拟食材数据用于下拉选择
     */
    private function getMockIngredientsForSelect()
    {
        return [
            [
                'id' => 1,
                'name' => '白菜',
                'unit' => '斤',
                'current_stock' => 25.5,
                'unit_price' => 2.50,
                'category_name' => '蔬菜类'
            ],
            [
                'id' => 2,
                'name' => '土豆',
                'unit' => '斤',
                'current_stock' => 8.0,
                'unit_price' => 3.00,
                'category_name' => '蔬菜类'
            ],
            [
                'id' => 4,
                'name' => '牛肉',
                'unit' => '斤',
                'current_stock' => 15.0,
                'unit_price' => 35.00,
                'category_name' => '肉类'
            ],
            [
                'id' => 5,
                'name' => '大米',
                'unit' => '斤',
                'current_stock' => 100.0,
                'unit_price' => 4.50,
                'category_name' => '粮油类'
            ]
        ];
    }

    /**
     * 获取模拟报损汇总数据
     */
    private function getMockDamageSummary()
    {
        return [
            'expired_value' => 456.30,
            'damaged_value' => 234.50,
            'quality_value' => 566.00,
            'other_value' => 0
        ];
    }

    /**
     * 单个食材库存情况（入库/出库/报损）
     */
    private function stockDetail()
    {
        $ingredientId = intval($this->request['get']['ingredient_id'] ?? 0);
        if ($ingredientId <= 0) {
            $this->redirect('index.php?action=query', '未指定食材', 'error');
            return;
        }

        // 基本信息
        try {
            $ingredient = $this->db->fetchOne(
                "SELECT i.*, ic.name AS category_name FROM ingredients i LEFT JOIN ingredient_categories ic ON i.category_id = ic.id WHERE i.id = ?",
                [$ingredientId]
            );
        } catch (Exception $e) {
            $ingredient = null;
        }

        // 入库记录
        try {
            $inbound = $this->db->fetchAll(
                "SELECT ir.id, ir.quantity, ir.unit_price, ir.batch_number, ir.created_at, s.name AS supplier_name
                 FROM inbound_records ir
                 LEFT JOIN suppliers s ON ir.supplier_id = s.id
                 WHERE ir.ingredient_id = ?
                 ORDER BY ir.created_at DESC LIMIT 100",
                [$ingredientId]
            );
        } catch (Exception $e) {
            $inbound = $this->getMockInboundByIngredient();
        }

        // 出库记录
        try {
            $outbound = $this->db->fetchAll(
                "SELECT orr.id, orr.quantity, orr.unit_price, orr.batch_number, orr.created_at, u.name AS operator_name
                 FROM outbound_records orr
                 LEFT JOIN users u ON orr.operator_id = u.id
                 WHERE orr.ingredient_id = ?
                 ORDER BY orr.created_at DESC LIMIT 100",
                [$ingredientId]
            );
        } catch (Exception $e) {
            $outbound = $this->getMockOutboundByIngredient();
        }

        // 报损记录
        try {
            // 表可能不存在，尝试创建
            $this->ensureDamageTable();
            $damages = $this->db->fetchAll(
                "SELECT dr.id, dr.damage_quantity, dr.unit_price, dr.damage_value, dr.damage_type, dr.damage_reason, dr.batch_number, dr.damage_date, u.name AS operator_name
                 FROM damage_records dr
                 LEFT JOIN users u ON dr.operator_id = u.id
                 WHERE dr.ingredient_id = ?
                 ORDER BY dr.damage_date DESC LIMIT 100",
                [$ingredientId]
            );
        } catch (Exception $e) {
            $damages = $this->getMockDamagesByIngredient();
        }

        // 汇总
        $summary = [
            'inbound_qty' => 0.0,
            'inbound_amount' => 0.0,
            'outbound_qty' => 0.0,
            'outbound_amount' => 0.0,
            'damage_qty' => 0.0,
            'damage_amount' => 0.0,
        ];
        foreach ($inbound as $row) {
            $summary['inbound_qty'] += floatval($row['quantity'] ?? 0);
            $summary['inbound_amount'] += floatval(($row['quantity'] ?? 0) * ($row['unit_price'] ?? 0));
        }
        foreach ($outbound as $row) {
            $summary['outbound_qty'] += floatval($row['quantity'] ?? 0);
            $summary['outbound_amount'] += floatval(($row['quantity'] ?? 0) * ($row['unit_price'] ?? 0));
        }
        foreach ($damages as $row) {
            $summary['damage_qty'] += floatval($row['damage_quantity'] ?? 0);
            $summary['damage_amount'] += floatval($row['damage_value'] ?? 0);
        }

        $this->setTemplateData([
            'page_title' => '库存情况',
            'current_action' => 'query',
            'ingredient' => $ingredient,
            'inbound' => $inbound,
            'outbound' => $outbound,
            'damages' => $damages,
            'summary' => $summary,
            // 兼容 $data 结构
            'data' => [
                'ingredient' => $ingredient,
                'inbound' => $inbound,
                'outbound' => $outbound,
                'damages' => $damages,
                'summary' => $summary,
            ],
        ]);

        $this->render('stock-detail-template.php');
    }

    private function getMockInboundByIngredient()
    {
        return [
            ['id' => 1, 'quantity' => 10, 'unit_price' => 3.2, 'batch_number' => 'MOCK-IN-001', 'created_at' => date('Y-m-d H:i:s'), 'supplier_name' => '示例供应商A'],
            ['id' => 2, 'quantity' => 5.5, 'unit_price' => 3.0, 'batch_number' => 'MOCK-IN-002', 'created_at' => date('Y-m-d H:i:s', strtotime('-1 day')), 'supplier_name' => '示例供应商B'],
        ];
    }

    private function getMockOutboundByIngredient()
    {
        return [
            ['id' => 11, 'quantity' => 4.0, 'unit_price' => 3.5, 'batch_number' => 'MOCK-OUT-001', 'created_at' => date('Y-m-d H:i:s'), 'operator_name' => '张三'],
            ['id' => 12, 'quantity' => 2.0, 'unit_price' => 3.4, 'batch_number' => 'MOCK-OUT-002', 'created_at' => date('Y-m-d H:i:s', strtotime('-2 days')), 'operator_name' => '李四'],
        ];
    }

    private function getMockDamagesByIngredient()
    {
        return [
            ['id' => 21, 'damage_quantity' => 1.0, 'unit_price' => 3.5, 'damage_value' => 3.5, 'damage_type' => 'expired', 'damage_reason' => '超过保质期', 'batch_number' => 'MOCK-DMG-001', 'damage_date' => date('Y-m-d H:i:s'), 'operator_name' => '王五'],
        ];
    }
}
?>
