<?php
/**
 * 添加食品留样记录表
 */

// 数据库配置 - 请根据实际情况修改
$config = require dirname(__DIR__) . '/config/database.php';
$host = $config['host'];
$port = $config['port'];
$dbname = $config['database']; // 修复配置项名称
$username = $config['username'];
$password = $config['password'];

echo "<h2>添加食品留样记录表</h2>";

try {
    // 使用PDO连接数据库
    $dsn = "mysql:host=$host;port=$port;dbname=$dbname;charset=utf8";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    
    echo "<p style='color: green;'>数据库连接成功！</p>";
    
    // 创建食品留样记录表
    $sql = "
    CREATE TABLE IF NOT EXISTS `food_samples` (
        `id` bigint(20) NOT NULL AUTO_INCREMENT,
        `ingredient_id` bigint(20) NOT NULL COMMENT '食材ID',
        `batch_number` varchar(64) NOT NULL COMMENT '批次号',
        `sample_code` varchar(50) NOT NULL COMMENT '留样编号',
        `sample_quantity` decimal(10,2) NOT NULL COMMENT '留样数量',
        `sample_unit` varchar(20) NOT NULL COMMENT '留样单位',
        `meal_type` enum('breakfast','lunch','dinner','other') NOT NULL DEFAULT 'other' COMMENT '餐次类型',
        `meal_date` date NOT NULL COMMENT '用餐日期',
        `sample_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '留样时间',
        `storage_location` varchar(100) DEFAULT NULL COMMENT '存储位置',
        `storage_temperature` varchar(20) DEFAULT NULL COMMENT '存储温度',
        `expiration_date` date NOT NULL COMMENT '过期日期',
        `responsible_person` varchar(50) NOT NULL COMMENT '责任人',
        `sample_status` tinyint(1) DEFAULT 1 COMMENT '留样状态: 1-正常, 0-已处理',
        `notes` text DEFAULT NULL COMMENT '备注',
        `created_by` bigint(20) NOT NULL COMMENT '创建人',
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `sample_code` (`sample_code`),
        KEY `ingredient_id` (`ingredient_id`),
        KEY `batch_number` (`batch_number`),
        KEY `meal_date` (`meal_date`),
        KEY `expiration_date` (`expiration_date`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='食品留样记录表'";

    $pdo->exec($sql);
    echo "<p style='color: green;'>✅ 食品留样记录表(food_samples) 创建成功</p>";
    
    // 添加外键约束
    try {
        $pdo->exec("ALTER TABLE food_samples ADD CONSTRAINT fk_samples_ingredient FOREIGN KEY (ingredient_id) REFERENCES ingredients (id) ON DELETE CASCADE");
        echo "<p style='color: green;'>✅ 外键约束添加成功</p>";
    } catch (Exception $e) {
        echo "<p style='color: orange;'>⚠️ 外键约束可能已存在或添加失败: " . $e->getMessage() . "</p>";
    }
    
    echo "<hr>";
    echo "<h3>操作完成</h3>";
    echo "<p>食品留样记录表已成功创建，可以开始使用食品留样管理功能。</p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ 数据库操作失败: " . $e->getMessage() . "</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ 操作失败: " . $e->getMessage() . "</p>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    line-height: 1.6;
}
h2, h3 {
    color: #333;
}
hr {
    margin: 20px 0;
}
</style>