Import Debug Log - Created at 2025-06-29 06:36:37
[2025-06-29 06:36:37] === 日志系统初始化 ===
[2025-06-29 06:36:37] 日志文件创建成功
[2025-06-29 06:39:07] === 开始处理明细数据 ===
[2025-06-29 06:39:07] 数据总行数: 100
[2025-06-29 06:39:07] 检查第6行第1列: '商品明细'
[2025-06-29 06:39:07] 找到明细标题行: 第6行
[2025-06-29 06:39:07] 明细数据从第7行开始处理
[2025-06-29 06:39:07] --- 处理第7行 ---
[2025-06-29 06:39:07] 第7行字段: 编码='商品编码', 名称='商品名称', 数量='金额小计(元)'
[2025-06-29 06:39:07] 第7行: 开始导入明细
[2025-06-29 06:39:07] 第7行: 导入失败 - 第7行: 数量必须大于0（第10列）
[2025-06-29 06:39:07] --- 处理第8行 ---
[2025-06-29 06:39:07] 第8行字段: 编码='DBC1685', 名称='大白菜', 数量='44.4'
[2025-06-29 06:39:07] 第8行: 开始导入明细
[2025-06-29 06:39:07] 第8行: 导入失败 - 第8行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:39:07] --- 处理第9行 ---
[2025-06-29 06:39:07] 第9行字段: 编码='LHB1033', 名称='莲花白（平顶）', 数量='42.8'
[2025-06-29 06:39:07] 第9行: 开始导入明细
[2025-06-29 06:39:07] 第9行: 导入失败 - 第9行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:39:07] --- 处理第10行 ---
[2025-06-29 06:39:07] 第10行字段: 编码='XLH1581', 名称='西兰花', 数量='162'
[2025-06-29 06:39:07] 第10行: 开始导入明细
[2025-06-29 06:39:07] 第10行: 导入失败 - 第10行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:39:07] --- 处理第11行 ---
[2025-06-29 06:39:07] 第11行字段: 编码='XHS1526', 名称='西红柿', 数量='127.8'
[2025-06-29 06:39:07] 第11行: 开始导入明细
[2025-06-29 06:39:08] 第11行: 导入失败 - 第11行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:39:08] --- 处理第12行 ---
[2025-06-29 06:39:08] 第12行字段: 编码='WDHG7779', 名称='外地黄瓜', 数量='142.2'
[2025-06-29 06:39:08] 第12行: 开始导入明细
[2025-06-29 06:39:08] 第12行: 导入失败 - 第12行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:39:08] --- 处理第13行 ---
[2025-06-29 06:39:08] 第13行字段: 编码='XNGC2635', 名称='小南瓜（长）', 数量='90'
[2025-06-29 06:39:08] 第13行: 开始导入明细
[2025-06-29 06:39:08] 第13行: 导入失败 - 第13行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:39:08] --- 处理第14行 ---
[2025-06-29 06:39:08] 第14行字段: 编码='dasd12', 名称='黄玉米棒', 数量='169'
[2025-06-29 06:39:08] 第14行: 开始导入明细
[2025-06-29 06:39:08] 第14行: 导入失败 - 第14行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:39:08] --- 处理第15行 ---
[2025-06-29 06:39:08] 第15行字段: 编码='SDCK1461', 名称='莴笋', 数量='113.5'
[2025-06-29 06:39:08] 第15行: 开始导入明细
[2025-06-29 06:39:08] 第15行: 导入失败 - 第15行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:39:08] --- 处理第16行 ---
[2025-06-29 06:39:08] 第16行字段: 编码='HLBWD2265', 名称='胡萝卜（外地）', 数量='72'
[2025-06-29 06:39:08] 第16行: 开始导入明细
[2025-06-29 06:39:08] 第16行: 导入失败 - 第16行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:39:08] --- 处理第17行 ---
[2025-06-29 06:39:08] 第17行字段: 编码='BLBG9279', 名称='白萝卜', 数量='32.7'
[2025-06-29 06:39:08] 第17行: 开始导入明细
[2025-06-29 06:39:08] 第17行: 导入失败 - 第17行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:39:08] --- 处理第18行 ---
[2025-06-29 06:39:08] 第18行字段: 编码='QCLBDG1821', 名称='芹菜（绿）', 数量='79'
[2025-06-29 06:39:08] 第18行: 开始导入明细
[2025-06-29 06:39:08] 第18行: 导入失败 - 第18行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:39:08] --- 处理第19行 ---
[2025-06-29 06:39:08] 第19行字段: 编码='XC1624', 名称='香菜', 数量='31.32'
[2025-06-29 06:39:08] 第19行: 开始导入明细
[2025-06-29 06:39:08] 第19行: 导入失败 - 第19行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:39:08] --- 处理第20行 ---
[2025-06-29 06:39:08] 第20行字段: 编码='SMBT0731', 名称='蒜苗', 数量='76.5'
[2025-06-29 06:39:08] 第20行: 开始导入明细
[2025-06-29 06:39:08] 第20行: 导入失败 - 第20行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:39:08] --- 处理第21行 ---
[2025-06-29 06:39:08] 第21行字段: 编码='XCBDDFC7870', 名称='小葱', 数量='42'
[2025-06-29 06:39:08] 第21行: 开始导入明细
[2025-06-29 06:39:08] 第21行: 导入失败 - 第21行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:39:08] --- 处理第22行 ---
[2025-06-29 06:39:08] 第22行字段: 编码='XJJ0650', 名称='小米椒（小尖椒）', 数量='17.34'
[2025-06-29 06:39:08] 第22行: 开始导入明细
[2025-06-29 06:39:08] 第22行: 导入失败 - 第22行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:39:08] --- 处理第23行 ---
[2025-06-29 06:39:08] 第23行字段: 编码='XJ0544', 名称='青线椒', 数量='129'
[2025-06-29 06:39:08] 第23行: 开始导入明细
[2025-06-29 06:39:08] 第23行: 导入失败 - 第23行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:39:08] --- 处理第24行 ---
[2025-06-29 06:39:08] 第24行字段: 编码='DQJ0496', 名称='大青椒', 数量='84'
[2025-06-29 06:39:08] 第24行: 开始导入明细
[2025-06-29 06:39:08] 第24行: 导入失败 - 第24行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:39:08] --- 处理第25行 ---
[2025-06-29 06:39:08] 第25行字段: 编码='DHJ6400', 名称='大红椒', 数量='99.4'
[2025-06-29 06:39:08] 第25行: 开始导入明细
[2025-06-29 06:39:08] 第25行: 导入失败 - 第25行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:39:08] --- 处理第26行 ---
[2025-06-29 06:39:08] 第26行字段: 编码='ZG0206', 名称='猪肝', 数量='232.5'
[2025-06-29 06:39:08] 第26行: 开始导入明细
[2025-06-29 06:39:08] 第26行: 导入失败 - 第26行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:39:08] --- 处理第27行 ---
[2025-06-29 06:39:08] 第27行字段: 编码='WHR4545', 名称='猪五花肉', 数量='408.9'
[2025-06-29 06:39:08] 第27行: 开始导入明细
[2025-06-29 06:39:08] 第27行: 导入失败 - 第27行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:39:08] --- 处理第28行 ---
[2025-06-29 06:39:08] 第28行字段: 编码='ZQTRQG4200', 名称='猪前后腿肉去骨', 数量='1712.2'
[2025-06-29 06:39:08] 第28行: 开始导入明细
[2025-06-29 06:39:08] 第28行: 导入成功
[2025-06-29 06:39:08] --- 处理第29行 ---
[2025-06-29 06:39:08] 第29行字段: 编码='ZPGZP8811', 名称='猪排骨（杂排）', 数量='110'
[2025-06-29 06:39:08] 第29行: 开始导入明细
[2025-06-29 06:39:08] 第29行: 导入失败 - 第29行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:39:08] --- 处理第30行 ---
[2025-06-29 06:39:08] 第30行字段: 编码='SPBM00003294', 名称='牛腩', 数量='930'
[2025-06-29 06:39:08] 第30行: 开始导入明细
[2025-06-29 06:39:08] 第30行: 导入失败 - 第30行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:39:08] --- 处理第31行 ---
[2025-06-29 06:39:08] 第31行字段: 编码='WZSNR3317', 名称='牛肉', 数量='679.4'
[2025-06-29 06:39:08] 第31行: 开始导入明细
[2025-06-29 06:39:08] 第31行: 导入失败 - 第31行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:39:08] --- 处理第32行 ---
[2025-06-29 06:39:08] 第32行字段: 编码='SHJ8439', 名称='三黄鸡（白条、无内脏）', 数量='1022'
[2025-06-29 06:39:08] 第32行: 开始导入明细
[2025-06-29 06:39:08] 第32行: 导入失败 - 第32行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:39:08] --- 处理第33行 ---
[2025-06-29 06:39:08] 第33行字段: 编码='HDY6675', 名称='黄豆芽', 数量='56.4'
[2025-06-29 06:39:08] 第33行: 开始导入明细
[2025-06-29 06:39:08] 第33行: 导入失败 - 第33行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:39:08] --- 处理第34行 ---
[2025-06-29 06:39:08] 第34行字段: 编码='SDF2528', 名称='水豆腐', 数量='125'
[2025-06-29 06:39:08] 第34行: 开始导入明细
[2025-06-29 06:39:08] 第34行: 导入失败 - 第34行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:39:08] --- 处理第35行 ---
[2025-06-29 06:39:08] 第35行字段: 编码='GDFP4973', 名称='干豆腐片（薄）', 数量='52.5'
[2025-06-29 06:39:08] 第35行: 开始导入明细
[2025-06-29 06:39:08] 第35行: 导入失败 - 第35行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:39:08] --- 处理第36行 ---
[2025-06-29 06:39:08] 第36行字段: 编码='GDFHP1223', 名称='干豆腐片（厚）', 数量='160'
[2025-06-29 06:39:08] 第36行: 开始导入明细
[2025-06-29 06:39:08] 第36行: 导入失败 - 第36行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:39:08] --- 处理第37行 ---
[2025-06-29 06:39:08] 第37行字段: 编码='YF4729', 名称='圆粉', 数量='330'
[2025-06-29 06:39:08] 第37行: 开始导入明细
[2025-06-29 06:39:08] 第37行: 导入失败 - 第37行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:39:08] --- 处理第38行 ---
[2025-06-29 06:39:08] 第38行字段: 编码='BF4586', 名称='扁粉', 数量='577.5'
[2025-06-29 06:39:08] 第38行: 开始导入明细
[2025-06-29 06:39:08] 第38行: 导入失败 - 第38行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:39:08] --- 处理第39行 ---
[2025-06-29 06:39:08] 第39行字段: 编码='YF5478', 名称='油粉', 数量='49.5'
[2025-06-29 06:39:08] 第39行: 开始导入明细
[2025-06-29 06:39:08] 第39行: 导入失败 - 第39行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:39:08] --- 处理第40行 ---
[2025-06-29 06:39:08] 第40行字段: 编码='BF5568', 名称='薄粉（卷粉皮）', 数量='24'
[2025-06-29 06:39:08] 第40行: 开始导入明细
[2025-06-29 06:39:08] 第40行: 导入失败 - 第40行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:39:08] --- 处理第41行 ---
[2025-06-29 06:39:08] 第41行字段: 编码='QTXW2925', 名称='秋田小町25KG', 数量='1240'
[2025-06-29 06:39:08] 第41行: 开始导入明细
[2025-06-29 06:39:08] 第41行: 导入失败 - 第41行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:39:08] --- 处理第42行 ---
[2025-06-29 06:39:08] 第42行字段: 编码='GLHLJM1006', 名称='冠霖糊辣椒面', 数量='212'
[2025-06-29 06:39:08] 第42行: 开始导入明细
[2025-06-29 06:39:08] 第42行: 导入失败 - 第42行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:39:08] --- 处理第43行 ---
[2025-06-29 06:39:08] 第43行字段: 编码='DFP8053', 名称='干豆腐皮', 数量='75'
[2025-06-29 06:39:08] 第43行: 开始导入明细
[2025-06-29 06:39:08] 第43行: 导入失败 - 第43行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:39:08] --- 处理第44行 ---
[2025-06-29 06:39:08] 第44行字段: 编码='CS7213', 名称='脆哨', 数量='340'
[2025-06-29 06:39:08] 第44行: 开始导入明细
[2025-06-29 06:39:09] 第44行: 导入失败 - 第44行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:39:09] --- 处理第45行 ---
[2025-06-29 06:39:09] 第45行字段: 编码='XJ1638', 名称='香蕉', 数量='129.9'
[2025-06-29 06:39:09] 第45行: 开始导入明细
[2025-06-29 06:39:09] 第45行: 导入失败 - 第45行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:39:09] --- 处理第46行 ---
[2025-06-29 06:39:09] 第46行字段: 编码='HWZTX7875', 名称='茉莉香提', 数量='153.2'
[2025-06-29 06:39:09] 第46行: 开始导入明细
[2025-06-29 06:39:09] 第46行: 导入失败 - 第46行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:39:09] --- 处理第47行 ---
[2025-06-29 06:39:09] 第47行字段: 编码='HMGXMH7349', 名称='哈密瓜（小蜜25号）大果', 数量='275'
[2025-06-29 06:39:09] 第47行: 开始导入明细
[2025-06-29 06:39:09] 第47行: 导入失败 - 第47行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:39:09] --- 处理第48行 ---
[2025-06-29 06:39:09] 第48行字段: 编码='XD6079', 名称='豇豆（绿）', 数量='126'
[2025-06-29 06:39:09] 第48行: 开始导入明细
[2025-06-29 06:39:09] 第48行: 导入失败 - 第48行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:39:09] --- 处理第49行 ---
[2025-06-29 06:39:09] 第49行字段: 编码='XTD9873', 名称='土豆', 数量='380'
[2025-06-29 06:39:09] 第49行: 开始导入明细
[2025-06-29 06:39:09] 第49行: 导入失败 - 第49行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:39:09] --- 处理第50行 ---
[2025-06-29 06:39:09] 第50行字段: 编码='LJ0368', 名称='老姜（带土）', 数量='108'
[2025-06-29 06:39:09] 第50行: 开始导入明细
[2025-06-29 06:39:09] 第50行: 导入失败 - 第50行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:39:09] --- 处理第51行 ---
[2025-06-29 06:39:09] 第51行字段: 编码='SM0321', 名称='蒜米', 数量='42.3'
[2025-06-29 06:39:09] 第51行: 开始导入明细
[2025-06-29 06:39:09] 第51行: 导入失败 - 第51行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:39:09] --- 处理第52行 ---
[2025-06-29 06:39:09] 第52行字段: 编码='SPBM00003264', 名称='猪里脊肉', 数量='378.9'
[2025-06-29 06:39:09] 第52行: 开始导入明细
[2025-06-29 06:39:09] 第52行: 导入失败 - 第52行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:39:09] --- 处理第53行 ---
[2025-06-29 06:39:09] 第53行字段: 编码='HYML4819', 名称='黄玉米粒（新鲜）', 数量='175.5'
[2025-06-29 06:39:09] 第53行: 开始导入明细
[2025-06-29 06:39:09] 第53行: 导入失败 - 第53行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:39:09] --- 处理第54行 ---
[2025-06-29 06:39:09] 第54行字段: 编码='', 名称='', 数量='11660.66'
[2025-06-29 06:39:09] 第54行: 关键字段为空，跳过
[2025-06-29 06:39:09] --- 处理第55行 ---
[2025-06-29 06:39:09] 第55行: 空行，跳过
[2025-06-29 06:39:09] --- 处理第56行 ---
[2025-06-29 06:39:09] 第56行: 空行，跳过
[2025-06-29 06:39:09] --- 处理第57行 ---
[2025-06-29 06:39:09] 第57行: 空行，跳过
[2025-06-29 06:39:09] --- 处理第58行 ---
[2025-06-29 06:39:09] 第58行: 空行，跳过
[2025-06-29 06:39:09] --- 处理第59行 ---
[2025-06-29 06:39:09] 第59行: 空行，跳过
[2025-06-29 06:39:09] --- 处理第60行 ---
[2025-06-29 06:39:09] 第60行: 空行，跳过
[2025-06-29 06:39:09] --- 处理第61行 ---
[2025-06-29 06:39:09] 第61行: 空行，跳过
[2025-06-29 06:39:09] --- 处理第62行 ---
[2025-06-29 06:39:09] 第62行: 空行，跳过
[2025-06-29 06:39:09] --- 处理第63行 ---
[2025-06-29 06:39:09] 第63行: 空行，跳过
[2025-06-29 06:39:09] --- 处理第64行 ---
[2025-06-29 06:39:09] 第64行: 空行，跳过
[2025-06-29 06:39:09] --- 处理第65行 ---
[2025-06-29 06:39:09] 第65行: 空行，跳过
[2025-06-29 06:39:09] --- 处理第66行 ---
[2025-06-29 06:39:09] 第66行: 空行，跳过
[2025-06-29 06:39:09] --- 处理第67行 ---
[2025-06-29 06:39:09] 第67行: 空行，跳过
[2025-06-29 06:39:09] --- 处理第68行 ---
[2025-06-29 06:39:09] 第68行: 空行，跳过
[2025-06-29 06:39:09] --- 处理第69行 ---
[2025-06-29 06:39:09] 第69行: 空行，跳过
[2025-06-29 06:39:09] --- 处理第70行 ---
[2025-06-29 06:39:09] 第70行: 空行，跳过
[2025-06-29 06:39:09] --- 处理第71行 ---
[2025-06-29 06:39:09] 第71行: 空行，跳过
[2025-06-29 06:39:09] --- 处理第72行 ---
[2025-06-29 06:39:09] 第72行: 空行，跳过
[2025-06-29 06:39:09] --- 处理第73行 ---
[2025-06-29 06:39:09] 第73行: 空行，跳过
[2025-06-29 06:39:09] --- 处理第74行 ---
[2025-06-29 06:39:09] 第74行: 空行，跳过
[2025-06-29 06:39:09] --- 处理第75行 ---
[2025-06-29 06:39:09] 第75行: 空行，跳过
[2025-06-29 06:39:09] --- 处理第76行 ---
[2025-06-29 06:39:09] 第76行: 空行，跳过
[2025-06-29 06:39:09] --- 处理第77行 ---
[2025-06-29 06:39:09] 第77行: 空行，跳过
[2025-06-29 06:39:09] --- 处理第78行 ---
[2025-06-29 06:39:09] 第78行: 空行，跳过
[2025-06-29 06:39:09] --- 处理第79行 ---
[2025-06-29 06:39:09] 第79行: 空行，跳过
[2025-06-29 06:39:09] --- 处理第80行 ---
[2025-06-29 06:39:09] 第80行: 空行，跳过
[2025-06-29 06:39:09] --- 处理第81行 ---
[2025-06-29 06:39:09] 第81行: 空行，跳过
[2025-06-29 06:39:09] --- 处理第82行 ---
[2025-06-29 06:39:09] 第82行: 空行，跳过
[2025-06-29 06:39:09] --- 处理第83行 ---
[2025-06-29 06:39:09] 第83行: 空行，跳过
[2025-06-29 06:39:09] --- 处理第84行 ---
[2025-06-29 06:39:09] 第84行: 空行，跳过
[2025-06-29 06:39:09] --- 处理第85行 ---
[2025-06-29 06:39:09] 第85行: 空行，跳过
[2025-06-29 06:39:09] --- 处理第86行 ---
[2025-06-29 06:39:09] 第86行: 空行，跳过
[2025-06-29 06:39:09] --- 处理第87行 ---
[2025-06-29 06:39:09] 第87行: 空行，跳过
[2025-06-29 06:39:09] --- 处理第88行 ---
[2025-06-29 06:39:09] 第88行: 空行，跳过
[2025-06-29 06:39:09] --- 处理第89行 ---
[2025-06-29 06:39:09] 第89行: 空行，跳过
[2025-06-29 06:39:09] --- 处理第90行 ---
[2025-06-29 06:39:09] 第90行: 空行，跳过
[2025-06-29 06:39:09] --- 处理第91行 ---
[2025-06-29 06:39:09] 第91行: 空行，跳过
[2025-06-29 06:39:09] --- 处理第92行 ---
[2025-06-29 06:39:09] 第92行: 空行，跳过
[2025-06-29 06:39:09] --- 处理第93行 ---
[2025-06-29 06:39:09] 第93行: 空行，跳过
[2025-06-29 06:39:09] --- 处理第94行 ---
[2025-06-29 06:39:09] 第94行: 空行，跳过
[2025-06-29 06:39:09] --- 处理第95行 ---
[2025-06-29 06:39:09] 第95行: 空行，跳过
[2025-06-29 06:39:09] --- 处理第96行 ---
[2025-06-29 06:39:09] 第96行: 空行，跳过
[2025-06-29 06:39:09] --- 处理第97行 ---
[2025-06-29 06:39:09] 第97行: 空行，跳过
[2025-06-29 06:39:09] --- 处理第98行 ---
[2025-06-29 06:39:09] 第98行: 空行，跳过
[2025-06-29 06:39:09] --- 处理第99行 ---
[2025-06-29 06:39:09] 第99行: 空行，跳过
[2025-06-29 06:39:09] --- 处理第100行 ---
[2025-06-29 06:39:09] 第100行: 空行，跳过
[2025-06-29 06:39:09] === 明细数据处理完成 ===
[2025-06-29 06:39:09] 成功: 1, 失败: 46
[2025-06-29 06:46:38] === 开始处理明细数据 ===
[2025-06-29 06:46:38] 数据总行数: 100
[2025-06-29 06:46:38] 检查第6行第1列: '商品明细'
[2025-06-29 06:46:38] 找到明细标题行: 第6行
[2025-06-29 06:46:38] 明细数据从第7行开始处理
[2025-06-29 06:46:38] --- 处理第7行 ---
[2025-06-29 06:46:38] 第7行字段: 编码='商品编码', 名称='商品名称', 数量='金额小计(元)'
[2025-06-29 06:46:38] 第7行: 开始导入明细
[2025-06-29 06:46:38] 第7行: 导入失败 - 第7行: 数量必须大于0（第10列）
[2025-06-29 06:46:38] --- 处理第8行 ---
[2025-06-29 06:46:38] 第8行字段: 编码='DBC1685', 名称='大白菜', 数量='44.4'
[2025-06-29 06:46:38] 第8行: 开始导入明细
[2025-06-29 06:46:38] 第8行: 导入失败 - 第8行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:46:38] --- 处理第9行 ---
[2025-06-29 06:46:38] 第9行字段: 编码='LHB1033', 名称='莲花白（平顶）', 数量='42.8'
[2025-06-29 06:46:38] 第9行: 开始导入明细
[2025-06-29 06:46:38] 第9行: 导入失败 - 第9行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:46:38] --- 处理第10行 ---
[2025-06-29 06:46:38] 第10行字段: 编码='XLH1581', 名称='西兰花', 数量='162'
[2025-06-29 06:46:38] 第10行: 开始导入明细
[2025-06-29 06:46:38] 第10行: 导入失败 - 第10行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:46:38] --- 处理第11行 ---
[2025-06-29 06:46:38] 第11行字段: 编码='XHS1526', 名称='西红柿', 数量='127.8'
[2025-06-29 06:46:38] 第11行: 开始导入明细
[2025-06-29 06:46:38] 第11行: 导入失败 - 第11行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:46:38] --- 处理第12行 ---
[2025-06-29 06:46:38] 第12行字段: 编码='WDHG7779', 名称='外地黄瓜', 数量='142.2'
[2025-06-29 06:46:38] 第12行: 开始导入明细
[2025-06-29 06:46:38] 第12行: 导入失败 - 第12行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:46:38] --- 处理第13行 ---
[2025-06-29 06:46:38] 第13行字段: 编码='XNGC2635', 名称='小南瓜（长）', 数量='90'
[2025-06-29 06:46:38] 第13行: 开始导入明细
[2025-06-29 06:46:38] 第13行: 导入失败 - 第13行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:46:38] --- 处理第14行 ---
[2025-06-29 06:46:38] 第14行字段: 编码='dasd12', 名称='黄玉米棒', 数量='169'
[2025-06-29 06:46:38] 第14行: 开始导入明细
[2025-06-29 06:46:38] 第14行: 导入失败 - 第14行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:46:38] --- 处理第15行 ---
[2025-06-29 06:46:38] 第15行字段: 编码='SDCK1461', 名称='莴笋', 数量='113.5'
[2025-06-29 06:46:38] 第15行: 开始导入明细
[2025-06-29 06:46:38] 第15行: 导入失败 - 第15行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:46:38] --- 处理第16行 ---
[2025-06-29 06:46:38] 第16行字段: 编码='HLBWD2265', 名称='胡萝卜（外地）', 数量='72'
[2025-06-29 06:46:38] 第16行: 开始导入明细
[2025-06-29 06:46:38] 第16行: 导入失败 - 第16行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:46:38] --- 处理第17行 ---
[2025-06-29 06:46:38] 第17行字段: 编码='BLBG9279', 名称='白萝卜', 数量='32.7'
[2025-06-29 06:46:38] 第17行: 开始导入明细
[2025-06-29 06:46:38] 第17行: 导入失败 - 第17行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:46:38] --- 处理第18行 ---
[2025-06-29 06:46:38] 第18行字段: 编码='QCLBDG1821', 名称='芹菜（绿）', 数量='79'
[2025-06-29 06:46:38] 第18行: 开始导入明细
[2025-06-29 06:46:38] 第18行: 导入失败 - 第18行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:46:38] --- 处理第19行 ---
[2025-06-29 06:46:38] 第19行字段: 编码='XC1624', 名称='香菜', 数量='31.32'
[2025-06-29 06:46:38] 第19行: 开始导入明细
[2025-06-29 06:46:38] 第19行: 导入失败 - 第19行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:46:38] --- 处理第20行 ---
[2025-06-29 06:46:38] 第20行字段: 编码='SMBT0731', 名称='蒜苗', 数量='76.5'
[2025-06-29 06:46:38] 第20行: 开始导入明细
[2025-06-29 06:46:38] 第20行: 导入失败 - 第20行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:46:38] --- 处理第21行 ---
[2025-06-29 06:46:38] 第21行字段: 编码='XCBDDFC7870', 名称='小葱', 数量='42'
[2025-06-29 06:46:38] 第21行: 开始导入明细
[2025-06-29 06:46:38] 第21行: 导入失败 - 第21行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:46:38] --- 处理第22行 ---
[2025-06-29 06:46:38] 第22行字段: 编码='XJJ0650', 名称='小米椒（小尖椒）', 数量='17.34'
[2025-06-29 06:46:38] 第22行: 开始导入明细
[2025-06-29 06:46:38] 第22行: 导入失败 - 第22行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:46:38] --- 处理第23行 ---
[2025-06-29 06:46:38] 第23行字段: 编码='XJ0544', 名称='青线椒', 数量='129'
[2025-06-29 06:46:38] 第23行: 开始导入明细
[2025-06-29 06:46:38] 第23行: 导入失败 - 第23行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:46:38] --- 处理第24行 ---
[2025-06-29 06:46:38] 第24行字段: 编码='DQJ0496', 名称='大青椒', 数量='84'
[2025-06-29 06:46:38] 第24行: 开始导入明细
[2025-06-29 06:46:38] 第24行: 导入失败 - 第24行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:46:38] --- 处理第25行 ---
[2025-06-29 06:46:38] 第25行字段: 编码='DHJ6400', 名称='大红椒', 数量='99.4'
[2025-06-29 06:46:38] 第25行: 开始导入明细
[2025-06-29 06:46:38] 第25行: 导入失败 - 第25行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:46:38] --- 处理第26行 ---
[2025-06-29 06:46:38] 第26行字段: 编码='ZG0206', 名称='猪肝', 数量='232.5'
[2025-06-29 06:46:38] 第26行: 开始导入明细
[2025-06-29 06:46:38] 第26行: 导入失败 - 第26行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:46:38] --- 处理第27行 ---
[2025-06-29 06:46:38] 第27行字段: 编码='WHR4545', 名称='猪五花肉', 数量='408.9'
[2025-06-29 06:46:38] 第27行: 开始导入明细
[2025-06-29 06:46:38] 第27行: 导入失败 - 第27行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:46:38] --- 处理第28行 ---
[2025-06-29 06:46:38] 第28行字段: 编码='ZQTRQG4200', 名称='猪前后腿肉去骨', 数量='1712.2'
[2025-06-29 06:46:38] 第28行: 开始导入明细
[2025-06-29 06:46:39] 第28行: 导入成功
[2025-06-29 06:46:39] --- 处理第29行 ---
[2025-06-29 06:46:39] 第29行字段: 编码='ZPGZP8811', 名称='猪排骨（杂排）', 数量='110'
[2025-06-29 06:46:39] 第29行: 开始导入明细
[2025-06-29 06:46:39] 第29行: 导入失败 - 第29行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:46:39] --- 处理第30行 ---
[2025-06-29 06:46:39] 第30行字段: 编码='SPBM00003294', 名称='牛腩', 数量='930'
[2025-06-29 06:46:39] 第30行: 开始导入明细
[2025-06-29 06:46:39] 第30行: 导入失败 - 第30行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:46:39] --- 处理第31行 ---
[2025-06-29 06:46:39] 第31行字段: 编码='WZSNR3317', 名称='牛肉', 数量='679.4'
[2025-06-29 06:46:39] 第31行: 开始导入明细
[2025-06-29 06:46:39] 第31行: 导入失败 - 第31行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:46:39] --- 处理第32行 ---
[2025-06-29 06:46:39] 第32行字段: 编码='SHJ8439', 名称='三黄鸡（白条、无内脏）', 数量='1022'
[2025-06-29 06:46:39] 第32行: 开始导入明细
[2025-06-29 06:46:39] 第32行: 导入失败 - 第32行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:46:39] --- 处理第33行 ---
[2025-06-29 06:46:39] 第33行字段: 编码='HDY6675', 名称='黄豆芽', 数量='56.4'
[2025-06-29 06:46:39] 第33行: 开始导入明细
[2025-06-29 06:46:39] 第33行: 导入失败 - 第33行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:46:39] --- 处理第34行 ---
[2025-06-29 06:46:39] 第34行字段: 编码='SDF2528', 名称='水豆腐', 数量='125'
[2025-06-29 06:46:39] 第34行: 开始导入明细
[2025-06-29 06:46:39] 第34行: 导入失败 - 第34行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:46:39] --- 处理第35行 ---
[2025-06-29 06:46:39] 第35行字段: 编码='GDFP4973', 名称='干豆腐片（薄）', 数量='52.5'
[2025-06-29 06:46:39] 第35行: 开始导入明细
[2025-06-29 06:46:39] 第35行: 导入失败 - 第35行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:46:39] --- 处理第36行 ---
[2025-06-29 06:46:39] 第36行字段: 编码='GDFHP1223', 名称='干豆腐片（厚）', 数量='160'
[2025-06-29 06:46:39] 第36行: 开始导入明细
[2025-06-29 06:46:39] 第36行: 导入失败 - 第36行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:46:39] --- 处理第37行 ---
[2025-06-29 06:46:39] 第37行字段: 编码='YF4729', 名称='圆粉', 数量='330'
[2025-06-29 06:46:39] 第37行: 开始导入明细
[2025-06-29 06:46:39] 第37行: 导入失败 - 第37行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:46:39] --- 处理第38行 ---
[2025-06-29 06:46:39] 第38行字段: 编码='BF4586', 名称='扁粉', 数量='577.5'
[2025-06-29 06:46:39] 第38行: 开始导入明细
[2025-06-29 06:46:39] 第38行: 导入失败 - 第38行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:46:39] --- 处理第39行 ---
[2025-06-29 06:46:39] 第39行字段: 编码='YF5478', 名称='油粉', 数量='49.5'
[2025-06-29 06:46:39] 第39行: 开始导入明细
[2025-06-29 06:46:39] 第39行: 导入失败 - 第39行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:46:39] --- 处理第40行 ---
[2025-06-29 06:46:39] 第40行字段: 编码='BF5568', 名称='薄粉（卷粉皮）', 数量='24'
[2025-06-29 06:46:39] 第40行: 开始导入明细
[2025-06-29 06:46:39] 第40行: 导入失败 - 第40行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:46:39] --- 处理第41行 ---
[2025-06-29 06:46:39] 第41行字段: 编码='QTXW2925', 名称='秋田小町25KG', 数量='1240'
[2025-06-29 06:46:39] 第41行: 开始导入明细
[2025-06-29 06:46:39] 第41行: 导入失败 - 第41行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:46:39] --- 处理第42行 ---
[2025-06-29 06:46:39] 第42行字段: 编码='GLHLJM1006', 名称='冠霖糊辣椒面', 数量='212'
[2025-06-29 06:46:39] 第42行: 开始导入明细
[2025-06-29 06:46:39] 第42行: 导入失败 - 第42行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:46:39] --- 处理第43行 ---
[2025-06-29 06:46:39] 第43行字段: 编码='DFP8053', 名称='干豆腐皮', 数量='75'
[2025-06-29 06:46:39] 第43行: 开始导入明细
[2025-06-29 06:46:39] 第43行: 导入失败 - 第43行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:46:39] --- 处理第44行 ---
[2025-06-29 06:46:39] 第44行字段: 编码='CS7213', 名称='脆哨', 数量='340'
[2025-06-29 06:46:39] 第44行: 开始导入明细
[2025-06-29 06:46:39] 第44行: 导入失败 - 第44行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:46:39] --- 处理第45行 ---
[2025-06-29 06:46:39] 第45行字段: 编码='XJ1638', 名称='香蕉', 数量='129.9'
[2025-06-29 06:46:39] 第45行: 开始导入明细
[2025-06-29 06:46:39] 第45行: 导入失败 - 第45行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:46:39] --- 处理第46行 ---
[2025-06-29 06:46:39] 第46行字段: 编码='HWZTX7875', 名称='茉莉香提', 数量='153.2'
[2025-06-29 06:46:39] 第46行: 开始导入明细
[2025-06-29 06:46:39] 第46行: 导入失败 - 第46行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:46:39] --- 处理第47行 ---
[2025-06-29 06:46:39] 第47行字段: 编码='HMGXMH7349', 名称='哈密瓜（小蜜25号）大果', 数量='275'
[2025-06-29 06:46:39] 第47行: 开始导入明细
[2025-06-29 06:46:39] 第47行: 导入失败 - 第47行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:46:39] --- 处理第48行 ---
[2025-06-29 06:46:39] 第48行字段: 编码='XD6079', 名称='豇豆（绿）', 数量='126'
[2025-06-29 06:46:39] 第48行: 开始导入明细
[2025-06-29 06:46:39] 第48行: 导入失败 - 第48行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:46:39] --- 处理第49行 ---
[2025-06-29 06:46:39] 第49行字段: 编码='XTD9873', 名称='土豆', 数量='380'
[2025-06-29 06:46:39] 第49行: 开始导入明细
[2025-06-29 06:46:39] 第49行: 导入失败 - 第49行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:46:39] --- 处理第50行 ---
[2025-06-29 06:46:39] 第50行字段: 编码='LJ0368', 名称='老姜（带土）', 数量='108'
[2025-06-29 06:46:39] 第50行: 开始导入明细
[2025-06-29 06:46:39] 第50行: 导入失败 - 第50行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:46:39] --- 处理第51行 ---
[2025-06-29 06:46:39] 第51行字段: 编码='SM0321', 名称='蒜米', 数量='42.3'
[2025-06-29 06:46:39] 第51行: 开始导入明细
[2025-06-29 06:46:39] 第51行: 导入失败 - 第51行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:46:39] --- 处理第52行 ---
[2025-06-29 06:46:39] 第52行字段: 编码='SPBM00003264', 名称='猪里脊肉', 数量='378.9'
[2025-06-29 06:46:39] 第52行: 开始导入明细
[2025-06-29 06:46:39] 第52行: 导入失败 - 第52行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:46:39] --- 处理第53行 ---
[2025-06-29 06:46:39] 第53行字段: 编码='HYML4819', 名称='黄玉米粒（新鲜）', 数量='175.5'
[2025-06-29 06:46:39] 第53行: 开始导入明细
[2025-06-29 06:46:39] 第53行: 导入失败 - 第53行: 处理食材失败: 查询执行失败: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'brand' in 'field list'
[2025-06-29 06:46:39] --- 处理第54行 ---
[2025-06-29 06:46:39] 第54行字段: 编码='', 名称='', 数量='11660.66'
[2025-06-29 06:46:39] 第54行: 关键字段为空，跳过
[2025-06-29 06:46:39] --- 处理第55行 ---
[2025-06-29 06:46:39] 第55行: 空行，跳过
[2025-06-29 06:46:39] --- 处理第56行 ---
[2025-06-29 06:46:39] 第56行: 空行，跳过
[2025-06-29 06:46:39] --- 处理第57行 ---
[2025-06-29 06:46:39] 第57行: 空行，跳过
[2025-06-29 06:46:39] --- 处理第58行 ---
[2025-06-29 06:46:39] 第58行: 空行，跳过
[2025-06-29 06:46:39] --- 处理第59行 ---
[2025-06-29 06:46:39] 第59行: 空行，跳过
[2025-06-29 06:46:39] --- 处理第60行 ---
[2025-06-29 06:46:39] 第60行: 空行，跳过
[2025-06-29 06:46:39] --- 处理第61行 ---
[2025-06-29 06:46:39] 第61行: 空行，跳过
[2025-06-29 06:46:39] --- 处理第62行 ---
[2025-06-29 06:46:39] 第62行: 空行，跳过
[2025-06-29 06:46:39] --- 处理第63行 ---
[2025-06-29 06:46:39] 第63行: 空行，跳过
[2025-06-29 06:46:39] --- 处理第64行 ---
[2025-06-29 06:46:39] 第64行: 空行，跳过
[2025-06-29 06:46:39] --- 处理第65行 ---
[2025-06-29 06:46:39] 第65行: 空行，跳过
[2025-06-29 06:46:39] --- 处理第66行 ---
[2025-06-29 06:46:39] 第66行: 空行，跳过
[2025-06-29 06:46:39] --- 处理第67行 ---
[2025-06-29 06:46:39] 第67行: 空行，跳过
[2025-06-29 06:46:39] --- 处理第68行 ---
[2025-06-29 06:46:39] 第68行: 空行，跳过
[2025-06-29 06:46:39] --- 处理第69行 ---
[2025-06-29 06:46:39] 第69行: 空行，跳过
[2025-06-29 06:46:39] --- 处理第70行 ---
[2025-06-29 06:46:39] 第70行: 空行，跳过
[2025-06-29 06:46:39] --- 处理第71行 ---
[2025-06-29 06:46:39] 第71行: 空行，跳过
[2025-06-29 06:46:39] --- 处理第72行 ---
[2025-06-29 06:46:39] 第72行: 空行，跳过
[2025-06-29 06:46:39] --- 处理第73行 ---
[2025-06-29 06:46:39] 第73行: 空行，跳过
[2025-06-29 06:46:39] --- 处理第74行 ---
[2025-06-29 06:46:39] 第74行: 空行，跳过
[2025-06-29 06:46:39] --- 处理第75行 ---
[2025-06-29 06:46:39] 第75行: 空行，跳过
[2025-06-29 06:46:39] --- 处理第76行 ---
[2025-06-29 06:46:39] 第76行: 空行，跳过
[2025-06-29 06:46:39] --- 处理第77行 ---
[2025-06-29 06:46:39] 第77行: 空行，跳过
[2025-06-29 06:46:39] --- 处理第78行 ---
[2025-06-29 06:46:39] 第78行: 空行，跳过
[2025-06-29 06:46:39] --- 处理第79行 ---
[2025-06-29 06:46:39] 第79行: 空行，跳过
[2025-06-29 06:46:39] --- 处理第80行 ---
[2025-06-29 06:46:39] 第80行: 空行，跳过
[2025-06-29 06:46:39] --- 处理第81行 ---
[2025-06-29 06:46:39] 第81行: 空行，跳过
[2025-06-29 06:46:39] --- 处理第82行 ---
[2025-06-29 06:46:39] 第82行: 空行，跳过
[2025-06-29 06:46:39] --- 处理第83行 ---
[2025-06-29 06:46:39] 第83行: 空行，跳过
[2025-06-29 06:46:39] --- 处理第84行 ---
[2025-06-29 06:46:39] 第84行: 空行，跳过
[2025-06-29 06:46:39] --- 处理第85行 ---
[2025-06-29 06:46:39] 第85行: 空行，跳过
[2025-06-29 06:46:39] --- 处理第86行 ---
[2025-06-29 06:46:39] 第86行: 空行，跳过
[2025-06-29 06:46:39] --- 处理第87行 ---
[2025-06-29 06:46:39] 第87行: 空行，跳过
[2025-06-29 06:46:39] --- 处理第88行 ---
[2025-06-29 06:46:39] 第88行: 空行，跳过
[2025-06-29 06:46:39] --- 处理第89行 ---
[2025-06-29 06:46:39] 第89行: 空行，跳过
[2025-06-29 06:46:39] --- 处理第90行 ---
[2025-06-29 06:46:39] 第90行: 空行，跳过
[2025-06-29 06:46:39] --- 处理第91行 ---
[2025-06-29 06:46:39] 第91行: 空行，跳过
[2025-06-29 06:46:39] --- 处理第92行 ---
[2025-06-29 06:46:39] 第92行: 空行，跳过
[2025-06-29 06:46:39] --- 处理第93行 ---
[2025-06-29 06:46:39] 第93行: 空行，跳过
[2025-06-29 06:46:39] --- 处理第94行 ---
[2025-06-29 06:46:39] 第94行: 空行，跳过
[2025-06-29 06:46:39] --- 处理第95行 ---
[2025-06-29 06:46:39] 第95行: 空行，跳过
[2025-06-29 06:46:39] --- 处理第96行 ---
[2025-06-29 06:46:39] 第96行: 空行，跳过
[2025-06-29 06:46:39] --- 处理第97行 ---
[2025-06-29 06:46:39] 第97行: 空行，跳过
[2025-06-29 06:46:39] --- 处理第98行 ---
[2025-06-29 06:46:39] 第98行: 空行，跳过
[2025-06-29 06:46:39] --- 处理第99行 ---
[2025-06-29 06:46:39] 第99行: 空行，跳过
[2025-06-29 06:46:39] --- 处理第100行 ---
[2025-06-29 06:46:39] 第100行: 空行，跳过
[2025-06-29 06:46:39] === 明细数据处理完成 ===
[2025-06-29 06:46:39] 成功: 1, 失败: 46
[2025-06-29 06:51:04] === 开始处理明细数据 ===
[2025-06-29 06:51:04] 数据总行数: 100
[2025-06-29 06:51:04] 检查第6行第1列: '商品明细'
[2025-06-29 06:51:04] 找到明细标题行: 第6行
[2025-06-29 06:51:04] 明细数据从第7行开始处理
[2025-06-29 06:51:04] --- 处理第7行 ---
[2025-06-29 06:51:04] 第7行字段: 编码='商品编码', 名称='商品名称', 数量='金额小计(元)'
[2025-06-29 06:51:04] 第7行: 开始导入明细
[2025-06-29 06:51:04] 第7行: 导入失败 - 第7行: 数量必须大于0（第10列）
[2025-06-29 06:51:04] --- 处理第8行 ---
[2025-06-29 06:51:04] 第8行字段: 编码='DBC1685', 名称='大白菜', 数量='44.4'
[2025-06-29 06:51:04] 第8行: 开始导入明细
[2025-06-29 06:51:04] 第8行: 导入失败 - 第8行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:51:04] --- 处理第9行 ---
[2025-06-29 06:51:04] 第9行字段: 编码='LHB1033', 名称='莲花白（平顶）', 数量='42.8'
[2025-06-29 06:51:04] 第9行: 开始导入明细
[2025-06-29 06:51:04] 第9行: 导入失败 - 第9行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:51:04] --- 处理第10行 ---
[2025-06-29 06:51:04] 第10行字段: 编码='XLH1581', 名称='西兰花', 数量='162'
[2025-06-29 06:51:04] 第10行: 开始导入明细
[2025-06-29 06:51:04] 第10行: 导入失败 - 第10行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:51:04] --- 处理第11行 ---
[2025-06-29 06:51:04] 第11行字段: 编码='XHS1526', 名称='西红柿', 数量='127.8'
[2025-06-29 06:51:04] 第11行: 开始导入明细
[2025-06-29 06:51:04] 第11行: 导入失败 - 第11行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:51:04] --- 处理第12行 ---
[2025-06-29 06:51:04] 第12行字段: 编码='WDHG7779', 名称='外地黄瓜', 数量='142.2'
[2025-06-29 06:51:04] 第12行: 开始导入明细
[2025-06-29 06:51:05] 第12行: 导入失败 - 第12行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:51:05] --- 处理第13行 ---
[2025-06-29 06:51:05] 第13行字段: 编码='XNGC2635', 名称='小南瓜（长）', 数量='90'
[2025-06-29 06:51:05] 第13行: 开始导入明细
[2025-06-29 06:51:05] 第13行: 导入失败 - 第13行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:51:05] --- 处理第14行 ---
[2025-06-29 06:51:05] 第14行字段: 编码='dasd12', 名称='黄玉米棒', 数量='169'
[2025-06-29 06:51:05] 第14行: 开始导入明细
[2025-06-29 06:51:05] 第14行: 导入失败 - 第14行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:51:05] --- 处理第15行 ---
[2025-06-29 06:51:05] 第15行字段: 编码='SDCK1461', 名称='莴笋', 数量='113.5'
[2025-06-29 06:51:05] 第15行: 开始导入明细
[2025-06-29 06:51:05] 第15行: 导入失败 - 第15行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:51:05] --- 处理第16行 ---
[2025-06-29 06:51:05] 第16行字段: 编码='HLBWD2265', 名称='胡萝卜（外地）', 数量='72'
[2025-06-29 06:51:05] 第16行: 开始导入明细
[2025-06-29 06:51:05] 第16行: 导入失败 - 第16行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:51:05] --- 处理第17行 ---
[2025-06-29 06:51:05] 第17行字段: 编码='BLBG9279', 名称='白萝卜', 数量='32.7'
[2025-06-29 06:51:05] 第17行: 开始导入明细
[2025-06-29 06:51:05] 第17行: 导入失败 - 第17行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:51:05] --- 处理第18行 ---
[2025-06-29 06:51:05] 第18行字段: 编码='QCLBDG1821', 名称='芹菜（绿）', 数量='79'
[2025-06-29 06:51:05] 第18行: 开始导入明细
[2025-06-29 06:51:05] 第18行: 导入失败 - 第18行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:51:05] --- 处理第19行 ---
[2025-06-29 06:51:05] 第19行字段: 编码='XC1624', 名称='香菜', 数量='31.32'
[2025-06-29 06:51:05] 第19行: 开始导入明细
[2025-06-29 06:51:05] 第19行: 导入失败 - 第19行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:51:05] --- 处理第20行 ---
[2025-06-29 06:51:05] 第20行字段: 编码='SMBT0731', 名称='蒜苗', 数量='76.5'
[2025-06-29 06:51:05] 第20行: 开始导入明细
[2025-06-29 06:51:05] 第20行: 导入失败 - 第20行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:51:05] --- 处理第21行 ---
[2025-06-29 06:51:05] 第21行字段: 编码='XCBDDFC7870', 名称='小葱', 数量='42'
[2025-06-29 06:51:05] 第21行: 开始导入明细
[2025-06-29 06:51:05] 第21行: 导入失败 - 第21行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:51:05] --- 处理第22行 ---
[2025-06-29 06:51:05] 第22行字段: 编码='XJJ0650', 名称='小米椒（小尖椒）', 数量='17.34'
[2025-06-29 06:51:05] 第22行: 开始导入明细
[2025-06-29 06:51:05] 第22行: 导入失败 - 第22行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:51:05] --- 处理第23行 ---
[2025-06-29 06:51:05] 第23行字段: 编码='XJ0544', 名称='青线椒', 数量='129'
[2025-06-29 06:51:05] 第23行: 开始导入明细
[2025-06-29 06:51:05] 第23行: 导入失败 - 第23行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:51:05] --- 处理第24行 ---
[2025-06-29 06:51:05] 第24行字段: 编码='DQJ0496', 名称='大青椒', 数量='84'
[2025-06-29 06:51:05] 第24行: 开始导入明细
[2025-06-29 06:51:05] 第24行: 导入失败 - 第24行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:51:05] --- 处理第25行 ---
[2025-06-29 06:51:05] 第25行字段: 编码='DHJ6400', 名称='大红椒', 数量='99.4'
[2025-06-29 06:51:05] 第25行: 开始导入明细
[2025-06-29 06:51:05] 第25行: 导入失败 - 第25行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:51:05] --- 处理第26行 ---
[2025-06-29 06:51:05] 第26行字段: 编码='ZG0206', 名称='猪肝', 数量='232.5'
[2025-06-29 06:51:05] 第26行: 开始导入明细
[2025-06-29 06:51:05] 第26行: 导入失败 - 第26行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:51:05] --- 处理第27行 ---
[2025-06-29 06:51:05] 第27行字段: 编码='WHR4545', 名称='猪五花肉', 数量='408.9'
[2025-06-29 06:51:05] 第27行: 开始导入明细
[2025-06-29 06:51:05] 第27行: 导入失败 - 第27行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:51:05] --- 处理第28行 ---
[2025-06-29 06:51:05] 第28行字段: 编码='ZQTRQG4200', 名称='猪前后腿肉去骨', 数量='1712.2'
[2025-06-29 06:51:05] 第28行: 开始导入明细
[2025-06-29 06:51:05] 第28行: 导入成功
[2025-06-29 06:51:05] --- 处理第29行 ---
[2025-06-29 06:51:05] 第29行字段: 编码='ZPGZP8811', 名称='猪排骨（杂排）', 数量='110'
[2025-06-29 06:51:05] 第29行: 开始导入明细
[2025-06-29 06:51:05] 第29行: 导入失败 - 第29行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:51:05] --- 处理第30行 ---
[2025-06-29 06:51:05] 第30行字段: 编码='SPBM00003294', 名称='牛腩', 数量='930'
[2025-06-29 06:51:05] 第30行: 开始导入明细
[2025-06-29 06:51:05] 第30行: 导入失败 - 第30行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:51:05] --- 处理第31行 ---
[2025-06-29 06:51:05] 第31行字段: 编码='WZSNR3317', 名称='牛肉', 数量='679.4'
[2025-06-29 06:51:05] 第31行: 开始导入明细
[2025-06-29 06:51:05] 第31行: 导入失败 - 第31行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:51:05] --- 处理第32行 ---
[2025-06-29 06:51:05] 第32行字段: 编码='SHJ8439', 名称='三黄鸡（白条、无内脏）', 数量='1022'
[2025-06-29 06:51:05] 第32行: 开始导入明细
[2025-06-29 06:51:05] 第32行: 导入失败 - 第32行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:51:05] --- 处理第33行 ---
[2025-06-29 06:51:05] 第33行字段: 编码='HDY6675', 名称='黄豆芽', 数量='56.4'
[2025-06-29 06:51:05] 第33行: 开始导入明细
[2025-06-29 06:51:05] 第33行: 导入失败 - 第33行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:51:05] --- 处理第34行 ---
[2025-06-29 06:51:05] 第34行字段: 编码='SDF2528', 名称='水豆腐', 数量='125'
[2025-06-29 06:51:05] 第34行: 开始导入明细
[2025-06-29 06:51:05] 第34行: 导入失败 - 第34行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:51:05] --- 处理第35行 ---
[2025-06-29 06:51:05] 第35行字段: 编码='GDFP4973', 名称='干豆腐片（薄）', 数量='52.5'
[2025-06-29 06:51:05] 第35行: 开始导入明细
[2025-06-29 06:51:06] 第35行: 导入失败 - 第35行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:51:06] --- 处理第36行 ---
[2025-06-29 06:51:06] 第36行字段: 编码='GDFHP1223', 名称='干豆腐片（厚）', 数量='160'
[2025-06-29 06:51:06] 第36行: 开始导入明细
[2025-06-29 06:51:06] 第36行: 导入失败 - 第36行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:51:06] --- 处理第37行 ---
[2025-06-29 06:51:06] 第37行字段: 编码='YF4729', 名称='圆粉', 数量='330'
[2025-06-29 06:51:06] 第37行: 开始导入明细
[2025-06-29 06:51:06] 第37行: 导入失败 - 第37行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:51:06] --- 处理第38行 ---
[2025-06-29 06:51:06] 第38行字段: 编码='BF4586', 名称='扁粉', 数量='577.5'
[2025-06-29 06:51:06] 第38行: 开始导入明细
[2025-06-29 06:51:06] 第38行: 导入失败 - 第38行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:51:06] --- 处理第39行 ---
[2025-06-29 06:51:06] 第39行字段: 编码='YF5478', 名称='油粉', 数量='49.5'
[2025-06-29 06:51:06] 第39行: 开始导入明细
[2025-06-29 06:51:06] 第39行: 导入失败 - 第39行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:51:06] --- 处理第40行 ---
[2025-06-29 06:51:06] 第40行字段: 编码='BF5568', 名称='薄粉（卷粉皮）', 数量='24'
[2025-06-29 06:51:06] 第40行: 开始导入明细
[2025-06-29 06:51:06] 第40行: 导入失败 - 第40行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:51:06] --- 处理第41行 ---
[2025-06-29 06:51:06] 第41行字段: 编码='QTXW2925', 名称='秋田小町25KG', 数量='1240'
[2025-06-29 06:51:06] 第41行: 开始导入明细
[2025-06-29 06:51:06] 第41行: 导入失败 - 第41行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:51:06] --- 处理第42行 ---
[2025-06-29 06:51:06] 第42行字段: 编码='GLHLJM1006', 名称='冠霖糊辣椒面', 数量='212'
[2025-06-29 06:51:06] 第42行: 开始导入明细
[2025-06-29 06:51:06] 第42行: 导入失败 - 第42行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:51:06] --- 处理第43行 ---
[2025-06-29 06:51:06] 第43行字段: 编码='DFP8053', 名称='干豆腐皮', 数量='75'
[2025-06-29 06:51:06] 第43行: 开始导入明细
[2025-06-29 06:51:06] 第43行: 导入失败 - 第43行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:51:06] --- 处理第44行 ---
[2025-06-29 06:51:06] 第44行字段: 编码='CS7213', 名称='脆哨', 数量='340'
[2025-06-29 06:51:06] 第44行: 开始导入明细
[2025-06-29 06:51:06] 第44行: 导入失败 - 第44行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:51:06] --- 处理第45行 ---
[2025-06-29 06:51:06] 第45行字段: 编码='XJ1638', 名称='香蕉', 数量='129.9'
[2025-06-29 06:51:06] 第45行: 开始导入明细
[2025-06-29 06:51:06] 第45行: 导入失败 - 第45行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:51:06] --- 处理第46行 ---
[2025-06-29 06:51:06] 第46行字段: 编码='HWZTX7875', 名称='茉莉香提', 数量='153.2'
[2025-06-29 06:51:06] 第46行: 开始导入明细
[2025-06-29 06:51:06] 第46行: 导入失败 - 第46行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:51:06] --- 处理第47行 ---
[2025-06-29 06:51:06] 第47行字段: 编码='HMGXMH7349', 名称='哈密瓜（小蜜25号）大果', 数量='275'
[2025-06-29 06:51:06] 第47行: 开始导入明细
[2025-06-29 06:51:06] 第47行: 导入失败 - 第47行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:51:06] --- 处理第48行 ---
[2025-06-29 06:51:06] 第48行字段: 编码='XD6079', 名称='豇豆（绿）', 数量='126'
[2025-06-29 06:51:06] 第48行: 开始导入明细
[2025-06-29 06:51:06] 第48行: 导入失败 - 第48行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:51:06] --- 处理第49行 ---
[2025-06-29 06:51:06] 第49行字段: 编码='XTD9873', 名称='土豆', 数量='380'
[2025-06-29 06:51:06] 第49行: 开始导入明细
[2025-06-29 06:51:06] 第49行: 导入失败 - 第49行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:51:06] --- 处理第50行 ---
[2025-06-29 06:51:06] 第50行字段: 编码='LJ0368', 名称='老姜（带土）', 数量='108'
[2025-06-29 06:51:06] 第50行: 开始导入明细
[2025-06-29 06:51:06] 第50行: 导入失败 - 第50行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:51:06] --- 处理第51行 ---
[2025-06-29 06:51:06] 第51行字段: 编码='SM0321', 名称='蒜米', 数量='42.3'
[2025-06-29 06:51:06] 第51行: 开始导入明细
[2025-06-29 06:51:06] 第51行: 导入失败 - 第51行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:51:06] --- 处理第52行 ---
[2025-06-29 06:51:06] 第52行字段: 编码='SPBM00003264', 名称='猪里脊肉', 数量='378.9'
[2025-06-29 06:51:06] 第52行: 开始导入明细
[2025-06-29 06:51:06] 第52行: 导入失败 - 第52行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:51:06] --- 处理第53行 ---
[2025-06-29 06:51:06] 第53行字段: 编码='HYML4819', 名称='黄玉米粒（新鲜）', 数量='175.5'
[2025-06-29 06:51:06] 第53行: 开始导入明细
[2025-06-29 06:51:06] 第53行: 导入失败 - 第53行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:51:06] --- 处理第54行 ---
[2025-06-29 06:51:06] 第54行字段: 编码='', 名称='', 数量='11660.66'
[2025-06-29 06:51:06] 第54行: 关键字段为空，跳过
[2025-06-29 06:51:06] --- 处理第55行 ---
[2025-06-29 06:51:06] 第55行: 空行，跳过
[2025-06-29 06:51:06] --- 处理第56行 ---
[2025-06-29 06:51:06] 第56行: 空行，跳过
[2025-06-29 06:51:06] --- 处理第57行 ---
[2025-06-29 06:51:06] 第57行: 空行，跳过
[2025-06-29 06:51:06] --- 处理第58行 ---
[2025-06-29 06:51:06] 第58行: 空行，跳过
[2025-06-29 06:51:06] --- 处理第59行 ---
[2025-06-29 06:51:06] 第59行: 空行，跳过
[2025-06-29 06:51:06] --- 处理第60行 ---
[2025-06-29 06:51:06] 第60行: 空行，跳过
[2025-06-29 06:51:06] --- 处理第61行 ---
[2025-06-29 06:51:06] 第61行: 空行，跳过
[2025-06-29 06:51:06] --- 处理第62行 ---
[2025-06-29 06:51:06] 第62行: 空行，跳过
[2025-06-29 06:51:06] --- 处理第63行 ---
[2025-06-29 06:51:06] 第63行: 空行，跳过
[2025-06-29 06:51:06] --- 处理第64行 ---
[2025-06-29 06:51:06] 第64行: 空行，跳过
[2025-06-29 06:51:06] --- 处理第65行 ---
[2025-06-29 06:51:06] 第65行: 空行，跳过
[2025-06-29 06:51:06] --- 处理第66行 ---
[2025-06-29 06:51:06] 第66行: 空行，跳过
[2025-06-29 06:51:06] --- 处理第67行 ---
[2025-06-29 06:51:06] 第67行: 空行，跳过
[2025-06-29 06:51:06] --- 处理第68行 ---
[2025-06-29 06:51:06] 第68行: 空行，跳过
[2025-06-29 06:51:06] --- 处理第69行 ---
[2025-06-29 06:51:06] 第69行: 空行，跳过
[2025-06-29 06:51:06] --- 处理第70行 ---
[2025-06-29 06:51:06] 第70行: 空行，跳过
[2025-06-29 06:51:06] --- 处理第71行 ---
[2025-06-29 06:51:06] 第71行: 空行，跳过
[2025-06-29 06:51:06] --- 处理第72行 ---
[2025-06-29 06:51:06] 第72行: 空行，跳过
[2025-06-29 06:51:06] --- 处理第73行 ---
[2025-06-29 06:51:06] 第73行: 空行，跳过
[2025-06-29 06:51:06] --- 处理第74行 ---
[2025-06-29 06:51:06] 第74行: 空行，跳过
[2025-06-29 06:51:06] --- 处理第75行 ---
[2025-06-29 06:51:06] 第75行: 空行，跳过
[2025-06-29 06:51:06] --- 处理第76行 ---
[2025-06-29 06:51:06] 第76行: 空行，跳过
[2025-06-29 06:51:06] --- 处理第77行 ---
[2025-06-29 06:51:06] 第77行: 空行，跳过
[2025-06-29 06:51:06] --- 处理第78行 ---
[2025-06-29 06:51:06] 第78行: 空行，跳过
[2025-06-29 06:51:06] --- 处理第79行 ---
[2025-06-29 06:51:06] 第79行: 空行，跳过
[2025-06-29 06:51:06] --- 处理第80行 ---
[2025-06-29 06:51:06] 第80行: 空行，跳过
[2025-06-29 06:51:06] --- 处理第81行 ---
[2025-06-29 06:51:06] 第81行: 空行，跳过
[2025-06-29 06:51:06] --- 处理第82行 ---
[2025-06-29 06:51:06] 第82行: 空行，跳过
[2025-06-29 06:51:06] --- 处理第83行 ---
[2025-06-29 06:51:06] 第83行: 空行，跳过
[2025-06-29 06:51:06] --- 处理第84行 ---
[2025-06-29 06:51:06] 第84行: 空行，跳过
[2025-06-29 06:51:06] --- 处理第85行 ---
[2025-06-29 06:51:06] 第85行: 空行，跳过
[2025-06-29 06:51:06] --- 处理第86行 ---
[2025-06-29 06:51:06] 第86行: 空行，跳过
[2025-06-29 06:51:06] --- 处理第87行 ---
[2025-06-29 06:51:06] 第87行: 空行，跳过
[2025-06-29 06:51:06] --- 处理第88行 ---
[2025-06-29 06:51:06] 第88行: 空行，跳过
[2025-06-29 06:51:06] --- 处理第89行 ---
[2025-06-29 06:51:06] 第89行: 空行，跳过
[2025-06-29 06:51:06] --- 处理第90行 ---
[2025-06-29 06:51:06] 第90行: 空行，跳过
[2025-06-29 06:51:06] --- 处理第91行 ---
[2025-06-29 06:51:06] 第91行: 空行，跳过
[2025-06-29 06:51:06] --- 处理第92行 ---
[2025-06-29 06:51:06] 第92行: 空行，跳过
[2025-06-29 06:51:06] --- 处理第93行 ---
[2025-06-29 06:51:06] 第93行: 空行，跳过
[2025-06-29 06:51:06] --- 处理第94行 ---
[2025-06-29 06:51:06] 第94行: 空行，跳过
[2025-06-29 06:51:06] --- 处理第95行 ---
[2025-06-29 06:51:06] 第95行: 空行，跳过
[2025-06-29 06:51:06] --- 处理第96行 ---
[2025-06-29 06:51:06] 第96行: 空行，跳过
[2025-06-29 06:51:06] --- 处理第97行 ---
[2025-06-29 06:51:06] 第97行: 空行，跳过
[2025-06-29 06:51:06] --- 处理第98行 ---
[2025-06-29 06:51:06] 第98行: 空行，跳过
[2025-06-29 06:51:06] --- 处理第99行 ---
[2025-06-29 06:51:06] 第99行: 空行，跳过
[2025-06-29 06:51:06] --- 处理第100行 ---
[2025-06-29 06:51:06] 第100行: 空行，跳过
[2025-06-29 06:51:06] === 明细数据处理完成 ===
[2025-06-29 06:51:06] 成功: 1, 失败: 46
[2025-06-29 06:53:12] === 日志系统初始化 ===
[2025-06-29 06:53:12] 日志文件创建成功
[2025-06-29 06:56:26] === 开始处理明细数据 ===
[2025-06-29 06:56:26] 数据总行数: 100
[2025-06-29 06:56:26] 检查第6行第1列: '商品明细'
[2025-06-29 06:56:26] 找到明细标题行: 第6行
[2025-06-29 06:56:26] 明细数据从第7行开始处理
[2025-06-29 06:56:26] --- 处理第7行 ---
[2025-06-29 06:56:26] 第7行字段: 编码='商品编码', 名称='商品名称', 数量='金额小计(元)'
[2025-06-29 06:56:26] 第7行: 开始导入明细
[2025-06-29 06:56:26] 第7行: 导入失败 - 第7行: 数量必须大于0（第10列）
[2025-06-29 06:56:26] --- 处理第8行 ---
[2025-06-29 06:56:26] 第8行字段: 编码='DBC1685', 名称='大白菜', 数量='44.4'
[2025-06-29 06:56:26] 第8行: 开始导入明细
[2025-06-29 06:56:26] 第8行: 导入失败 - 第8行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:56:26] --- 处理第9行 ---
[2025-06-29 06:56:26] 第9行字段: 编码='LHB1033', 名称='莲花白（平顶）', 数量='42.8'
[2025-06-29 06:56:26] 第9行: 开始导入明细
[2025-06-29 06:56:26] 第9行: 导入失败 - 第9行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:56:26] --- 处理第10行 ---
[2025-06-29 06:56:26] 第10行字段: 编码='XLH1581', 名称='西兰花', 数量='162'
[2025-06-29 06:56:26] 第10行: 开始导入明细
[2025-06-29 06:56:26] 第10行: 导入失败 - 第10行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:56:26] --- 处理第11行 ---
[2025-06-29 06:56:26] 第11行字段: 编码='XHS1526', 名称='西红柿', 数量='127.8'
[2025-06-29 06:56:26] 第11行: 开始导入明细
[2025-06-29 06:56:26] 第11行: 导入失败 - 第11行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:56:26] --- 处理第12行 ---
[2025-06-29 06:56:26] 第12行字段: 编码='WDHG7779', 名称='外地黄瓜', 数量='142.2'
[2025-06-29 06:56:26] 第12行: 开始导入明细
[2025-06-29 06:56:26] 第12行: 导入失败 - 第12行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:56:26] --- 处理第13行 ---
[2025-06-29 06:56:26] 第13行字段: 编码='XNGC2635', 名称='小南瓜（长）', 数量='90'
[2025-06-29 06:56:26] 第13行: 开始导入明细
[2025-06-29 06:56:26] 第13行: 导入失败 - 第13行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:56:26] --- 处理第14行 ---
[2025-06-29 06:56:26] 第14行字段: 编码='dasd12', 名称='黄玉米棒', 数量='169'
[2025-06-29 06:56:26] 第14行: 开始导入明细
[2025-06-29 06:56:26] 第14行: 导入失败 - 第14行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:56:26] --- 处理第15行 ---
[2025-06-29 06:56:26] 第15行字段: 编码='SDCK1461', 名称='莴笋', 数量='113.5'
[2025-06-29 06:56:26] 第15行: 开始导入明细
[2025-06-29 06:56:26] 第15行: 导入失败 - 第15行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:56:26] --- 处理第16行 ---
[2025-06-29 06:56:26] 第16行字段: 编码='HLBWD2265', 名称='胡萝卜（外地）', 数量='72'
[2025-06-29 06:56:26] 第16行: 开始导入明细
[2025-06-29 06:56:27] 第16行: 导入失败 - 第16行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:56:27] --- 处理第17行 ---
[2025-06-29 06:56:27] 第17行字段: 编码='BLBG9279', 名称='白萝卜', 数量='32.7'
[2025-06-29 06:56:27] 第17行: 开始导入明细
[2025-06-29 06:56:27] 第17行: 导入失败 - 第17行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:56:27] --- 处理第18行 ---
[2025-06-29 06:56:27] 第18行字段: 编码='QCLBDG1821', 名称='芹菜（绿）', 数量='79'
[2025-06-29 06:56:27] 第18行: 开始导入明细
[2025-06-29 06:56:27] 第18行: 导入失败 - 第18行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:56:27] --- 处理第19行 ---
[2025-06-29 06:56:27] 第19行字段: 编码='XC1624', 名称='香菜', 数量='31.32'
[2025-06-29 06:56:27] 第19行: 开始导入明细
[2025-06-29 06:56:27] 第19行: 导入失败 - 第19行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:56:27] --- 处理第20行 ---
[2025-06-29 06:56:27] 第20行字段: 编码='SMBT0731', 名称='蒜苗', 数量='76.5'
[2025-06-29 06:56:27] 第20行: 开始导入明细
[2025-06-29 06:56:27] 第20行: 导入失败 - 第20行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:56:27] --- 处理第21行 ---
[2025-06-29 06:56:27] 第21行字段: 编码='XCBDDFC7870', 名称='小葱', 数量='42'
[2025-06-29 06:56:27] 第21行: 开始导入明细
[2025-06-29 06:56:27] 第21行: 导入失败 - 第21行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:56:27] --- 处理第22行 ---
[2025-06-29 06:56:27] 第22行字段: 编码='XJJ0650', 名称='小米椒（小尖椒）', 数量='17.34'
[2025-06-29 06:56:27] 第22行: 开始导入明细
[2025-06-29 06:56:27] 第22行: 导入失败 - 第22行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:56:27] --- 处理第23行 ---
[2025-06-29 06:56:27] 第23行字段: 编码='XJ0544', 名称='青线椒', 数量='129'
[2025-06-29 06:56:27] 第23行: 开始导入明细
[2025-06-29 06:56:27] 第23行: 导入失败 - 第23行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:56:27] --- 处理第24行 ---
[2025-06-29 06:56:27] 第24行字段: 编码='DQJ0496', 名称='大青椒', 数量='84'
[2025-06-29 06:56:27] 第24行: 开始导入明细
[2025-06-29 06:56:27] 第24行: 导入失败 - 第24行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:56:27] --- 处理第25行 ---
[2025-06-29 06:56:27] 第25行字段: 编码='DHJ6400', 名称='大红椒', 数量='99.4'
[2025-06-29 06:56:27] 第25行: 开始导入明细
[2025-06-29 06:56:27] 第25行: 导入失败 - 第25行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:56:27] --- 处理第26行 ---
[2025-06-29 06:56:27] 第26行字段: 编码='ZG0206', 名称='猪肝', 数量='232.5'
[2025-06-29 06:56:27] 第26行: 开始导入明细
[2025-06-29 06:56:27] 第26行: 导入失败 - 第26行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:56:27] --- 处理第27行 ---
[2025-06-29 06:56:27] 第27行字段: 编码='WHR4545', 名称='猪五花肉', 数量='408.9'
[2025-06-29 06:56:27] 第27行: 开始导入明细
[2025-06-29 06:56:27] 第27行: 导入成功
[2025-06-29 06:56:27] --- 处理第28行 ---
[2025-06-29 06:56:27] 第28行字段: 编码='ZQTRQG4200', 名称='猪前后腿肉去骨', 数量='1712.2'
[2025-06-29 06:56:27] 第28行: 开始导入明细
[2025-06-29 06:56:27] 第28行: 导入成功
[2025-06-29 06:56:27] --- 处理第29行 ---
[2025-06-29 06:56:27] 第29行字段: 编码='ZPGZP8811', 名称='猪排骨（杂排）', 数量='110'
[2025-06-29 06:56:27] 第29行: 开始导入明细
[2025-06-29 06:56:27] 第29行: 导入失败 - 第29行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:56:27] --- 处理第30行 ---
[2025-06-29 06:56:27] 第30行字段: 编码='SPBM00003294', 名称='牛腩', 数量='930'
[2025-06-29 06:56:27] 第30行: 开始导入明细
[2025-06-29 06:56:27] 第30行: 导入失败 - 第30行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:56:27] --- 处理第31行 ---
[2025-06-29 06:56:27] 第31行字段: 编码='WZSNR3317', 名称='牛肉', 数量='679.4'
[2025-06-29 06:56:27] 第31行: 开始导入明细
[2025-06-29 06:56:27] 第31行: 导入失败 - 第31行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:56:27] --- 处理第32行 ---
[2025-06-29 06:56:27] 第32行字段: 编码='SHJ8439', 名称='三黄鸡（白条、无内脏）', 数量='1022'
[2025-06-29 06:56:27] 第32行: 开始导入明细
[2025-06-29 06:56:27] 第32行: 导入失败 - 第32行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:56:27] --- 处理第33行 ---
[2025-06-29 06:56:27] 第33行字段: 编码='HDY6675', 名称='黄豆芽', 数量='56.4'
[2025-06-29 06:56:27] 第33行: 开始导入明细
[2025-06-29 06:56:27] 第33行: 导入失败 - 第33行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:56:27] --- 处理第34行 ---
[2025-06-29 06:56:27] 第34行字段: 编码='SDF2528', 名称='水豆腐', 数量='125'
[2025-06-29 06:56:27] 第34行: 开始导入明细
[2025-06-29 06:56:27] 第34行: 导入失败 - 第34行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:56:27] --- 处理第35行 ---
[2025-06-29 06:56:27] 第35行字段: 编码='GDFP4973', 名称='干豆腐片（薄）', 数量='52.5'
[2025-06-29 06:56:27] 第35行: 开始导入明细
[2025-06-29 06:56:27] 第35行: 导入失败 - 第35行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:56:27] --- 处理第36行 ---
[2025-06-29 06:56:27] 第36行字段: 编码='GDFHP1223', 名称='干豆腐片（厚）', 数量='160'
[2025-06-29 06:56:27] 第36行: 开始导入明细
[2025-06-29 06:56:27] 第36行: 导入失败 - 第36行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:56:27] --- 处理第37行 ---
[2025-06-29 06:56:27] 第37行字段: 编码='YF4729', 名称='圆粉', 数量='330'
[2025-06-29 06:56:27] 第37行: 开始导入明细
[2025-06-29 06:56:27] 第37行: 导入失败 - 第37行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:56:27] --- 处理第38行 ---
[2025-06-29 06:56:27] 第38行字段: 编码='BF4586', 名称='扁粉', 数量='577.5'
[2025-06-29 06:56:27] 第38行: 开始导入明细
[2025-06-29 06:56:27] 第38行: 导入失败 - 第38行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:56:27] --- 处理第39行 ---
[2025-06-29 06:56:27] 第39行字段: 编码='YF5478', 名称='油粉', 数量='49.5'
[2025-06-29 06:56:27] 第39行: 开始导入明细
[2025-06-29 06:56:27] 第39行: 导入失败 - 第39行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:56:27] --- 处理第40行 ---
[2025-06-29 06:56:27] 第40行字段: 编码='BF5568', 名称='薄粉（卷粉皮）', 数量='24'
[2025-06-29 06:56:27] 第40行: 开始导入明细
[2025-06-29 06:56:27] 第40行: 导入失败 - 第40行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:56:27] --- 处理第41行 ---
[2025-06-29 06:56:27] 第41行字段: 编码='QTXW2925', 名称='秋田小町25KG', 数量='1240'
[2025-06-29 06:56:27] 第41行: 开始导入明细
[2025-06-29 06:56:28] 第41行: 导入失败 - 第41行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:56:28] --- 处理第42行 ---
[2025-06-29 06:56:28] 第42行字段: 编码='GLHLJM1006', 名称='冠霖糊辣椒面', 数量='212'
[2025-06-29 06:56:28] 第42行: 开始导入明细
[2025-06-29 06:56:28] 第42行: 导入失败 - 第42行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:56:28] --- 处理第43行 ---
[2025-06-29 06:56:28] 第43行字段: 编码='DFP8053', 名称='干豆腐皮', 数量='75'
[2025-06-29 06:56:28] 第43行: 开始导入明细
[2025-06-29 06:56:28] 第43行: 导入失败 - 第43行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:56:28] --- 处理第44行 ---
[2025-06-29 06:56:28] 第44行字段: 编码='CS7213', 名称='脆哨', 数量='340'
[2025-06-29 06:56:28] 第44行: 开始导入明细
[2025-06-29 06:56:28] 第44行: 导入失败 - 第44行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:56:28] --- 处理第45行 ---
[2025-06-29 06:56:28] 第45行字段: 编码='XJ1638', 名称='香蕉', 数量='129.9'
[2025-06-29 06:56:28] 第45行: 开始导入明细
[2025-06-29 06:56:28] 第45行: 导入失败 - 第45行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:56:28] --- 处理第46行 ---
[2025-06-29 06:56:28] 第46行字段: 编码='HWZTX7875', 名称='茉莉香提', 数量='153.2'
[2025-06-29 06:56:28] 第46行: 开始导入明细
[2025-06-29 06:56:28] 第46行: 导入失败 - 第46行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:56:28] --- 处理第47行 ---
[2025-06-29 06:56:28] 第47行字段: 编码='HMGXMH7349', 名称='哈密瓜（小蜜25号）大果', 数量='275'
[2025-06-29 06:56:28] 第47行: 开始导入明细
[2025-06-29 06:56:28] 第47行: 导入失败 - 第47行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:56:28] --- 处理第48行 ---
[2025-06-29 06:56:28] 第48行字段: 编码='XD6079', 名称='豇豆（绿）', 数量='126'
[2025-06-29 06:56:28] 第48行: 开始导入明细
[2025-06-29 06:56:28] 第48行: 导入失败 - 第48行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:56:28] --- 处理第49行 ---
[2025-06-29 06:56:28] 第49行字段: 编码='XTD9873', 名称='土豆', 数量='380'
[2025-06-29 06:56:28] 第49行: 开始导入明细
[2025-06-29 06:56:28] 第49行: 导入失败 - 第49行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:56:28] --- 处理第50行 ---
[2025-06-29 06:56:28] 第50行字段: 编码='LJ0368', 名称='老姜（带土）', 数量='108'
[2025-06-29 06:56:28] 第50行: 开始导入明细
[2025-06-29 06:56:28] 第50行: 导入失败 - 第50行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:56:28] --- 处理第51行 ---
[2025-06-29 06:56:28] 第51行字段: 编码='SM0321', 名称='蒜米', 数量='42.3'
[2025-06-29 06:56:28] 第51行: 开始导入明细
[2025-06-29 06:56:28] 第51行: 导入失败 - 第51行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:56:28] --- 处理第52行 ---
[2025-06-29 06:56:28] 第52行字段: 编码='SPBM00003264', 名称='猪里脊肉', 数量='378.9'
[2025-06-29 06:56:28] 第52行: 开始导入明细
[2025-06-29 06:56:28] 第52行: 导入失败 - 第52行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:56:28] --- 处理第53行 ---
[2025-06-29 06:56:28] 第53行字段: 编码='HYML4819', 名称='黄玉米粒（新鲜）', 数量='175.5'
[2025-06-29 06:56:28] 第53行: 开始导入明细
[2025-06-29 06:56:28] 第53行: 导入失败 - 第53行: 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:56:28] --- 处理第54行 ---
[2025-06-29 06:56:28] 第54行字段: 编码='', 名称='', 数量='11660.66'
[2025-06-29 06:56:28] 第54行: 关键字段为空，跳过
[2025-06-29 06:56:28] --- 处理第55行 ---
[2025-06-29 06:56:28] 第55行: 空行，跳过
[2025-06-29 06:56:28] --- 处理第56行 ---
[2025-06-29 06:56:28] 第56行: 空行，跳过
[2025-06-29 06:56:28] --- 处理第57行 ---
[2025-06-29 06:56:28] 第57行: 空行，跳过
[2025-06-29 06:56:28] --- 处理第58行 ---
[2025-06-29 06:56:28] 第58行: 空行，跳过
[2025-06-29 06:56:28] --- 处理第59行 ---
[2025-06-29 06:56:28] 第59行: 空行，跳过
[2025-06-29 06:56:28] --- 处理第60行 ---
[2025-06-29 06:56:28] 第60行: 空行，跳过
[2025-06-29 06:56:28] --- 处理第61行 ---
[2025-06-29 06:56:28] 第61行: 空行，跳过
[2025-06-29 06:56:28] --- 处理第62行 ---
[2025-06-29 06:56:28] 第62行: 空行，跳过
[2025-06-29 06:56:28] --- 处理第63行 ---
[2025-06-29 06:56:28] 第63行: 空行，跳过
[2025-06-29 06:56:28] --- 处理第64行 ---
[2025-06-29 06:56:28] 第64行: 空行，跳过
[2025-06-29 06:56:28] --- 处理第65行 ---
[2025-06-29 06:56:28] 第65行: 空行，跳过
[2025-06-29 06:56:28] --- 处理第66行 ---
[2025-06-29 06:56:28] 第66行: 空行，跳过
[2025-06-29 06:56:28] --- 处理第67行 ---
[2025-06-29 06:56:28] 第67行: 空行，跳过
[2025-06-29 06:56:28] --- 处理第68行 ---
[2025-06-29 06:56:28] 第68行: 空行，跳过
[2025-06-29 06:56:28] --- 处理第69行 ---
[2025-06-29 06:56:28] 第69行: 空行，跳过
[2025-06-29 06:56:28] --- 处理第70行 ---
[2025-06-29 06:56:28] 第70行: 空行，跳过
[2025-06-29 06:56:28] --- 处理第71行 ---
[2025-06-29 06:56:28] 第71行: 空行，跳过
[2025-06-29 06:56:28] --- 处理第72行 ---
[2025-06-29 06:56:28] 第72行: 空行，跳过
[2025-06-29 06:56:28] --- 处理第73行 ---
[2025-06-29 06:56:28] 第73行: 空行，跳过
[2025-06-29 06:56:28] --- 处理第74行 ---
[2025-06-29 06:56:28] 第74行: 空行，跳过
[2025-06-29 06:56:28] --- 处理第75行 ---
[2025-06-29 06:56:28] 第75行: 空行，跳过
[2025-06-29 06:56:28] --- 处理第76行 ---
[2025-06-29 06:56:28] 第76行: 空行，跳过
[2025-06-29 06:56:28] --- 处理第77行 ---
[2025-06-29 06:56:28] 第77行: 空行，跳过
[2025-06-29 06:56:28] --- 处理第78行 ---
[2025-06-29 06:56:28] 第78行: 空行，跳过
[2025-06-29 06:56:28] --- 处理第79行 ---
[2025-06-29 06:56:28] 第79行: 空行，跳过
[2025-06-29 06:56:28] --- 处理第80行 ---
[2025-06-29 06:56:28] 第80行: 空行，跳过
[2025-06-29 06:56:28] --- 处理第81行 ---
[2025-06-29 06:56:28] 第81行: 空行，跳过
[2025-06-29 06:56:28] --- 处理第82行 ---
[2025-06-29 06:56:28] 第82行: 空行，跳过
[2025-06-29 06:56:28] --- 处理第83行 ---
[2025-06-29 06:56:28] 第83行: 空行，跳过
[2025-06-29 06:56:28] --- 处理第84行 ---
[2025-06-29 06:56:28] 第84行: 空行，跳过
[2025-06-29 06:56:28] --- 处理第85行 ---
[2025-06-29 06:56:28] 第85行: 空行，跳过
[2025-06-29 06:56:28] --- 处理第86行 ---
[2025-06-29 06:56:28] 第86行: 空行，跳过
[2025-06-29 06:56:28] --- 处理第87行 ---
[2025-06-29 06:56:28] 第87行: 空行，跳过
[2025-06-29 06:56:28] --- 处理第88行 ---
[2025-06-29 06:56:28] 第88行: 空行，跳过
[2025-06-29 06:56:28] --- 处理第89行 ---
[2025-06-29 06:56:28] 第89行: 空行，跳过
[2025-06-29 06:56:28] --- 处理第90行 ---
[2025-06-29 06:56:28] 第90行: 空行，跳过
[2025-06-29 06:56:28] --- 处理第91行 ---
[2025-06-29 06:56:28] 第91行: 空行，跳过
[2025-06-29 06:56:28] --- 处理第92行 ---
[2025-06-29 06:56:28] 第92行: 空行，跳过
[2025-06-29 06:56:28] --- 处理第93行 ---
[2025-06-29 06:56:28] 第93行: 空行，跳过
[2025-06-29 06:56:28] --- 处理第94行 ---
[2025-06-29 06:56:28] 第94行: 空行，跳过
[2025-06-29 06:56:28] --- 处理第95行 ---
[2025-06-29 06:56:28] 第95行: 空行，跳过
[2025-06-29 06:56:28] --- 处理第96行 ---
[2025-06-29 06:56:28] 第96行: 空行，跳过
[2025-06-29 06:56:28] --- 处理第97行 ---
[2025-06-29 06:56:28] 第97行: 空行，跳过
[2025-06-29 06:56:28] --- 处理第98行 ---
[2025-06-29 06:56:28] 第98行: 空行，跳过
[2025-06-29 06:56:28] --- 处理第99行 ---
[2025-06-29 06:56:28] 第99行: 空行，跳过
[2025-06-29 06:56:28] --- 处理第100行 ---
[2025-06-29 06:56:28] 第100行: 空行，跳过
[2025-06-29 06:56:28] === 明细数据处理完成 ===
[2025-06-29 06:56:28] 成功: 2, 失败: 45
[2025-06-29 06:59:54] 查找食材: 编码='TEST001', 名称='测试食材1'
[2025-06-29 06:59:54] ⚠️ 食材不存在，开始自动创建
[2025-06-29 06:59:54] 准备创建食材: {"code":"TEST001","name":"\u6d4b\u8bd5\u98df\u67501","specification":"500g","unit":"\u5305","category_id":1,"brand":"\u6d4b\u8bd5\u54c1\u724c","origin":"\u6d4b\u8bd5\u4ea7\u5730","status":1,"created_at":"2025-06-29 06:59:54"}
[2025-06-29 06:59:54] ❌ 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:59:54] 查找食材: 编码='TEST002', 名称='测试食材2'
[2025-06-29 06:59:54] ⚠️ 食材不存在，开始自动创建
[2025-06-29 06:59:54] 准备创建食材: {"code":"TEST002","name":"\u6d4b\u8bd5\u98df\u67502","specification":"1kg","unit":"\u888b","category_id":1,"brand":"","origin":"","status":1,"created_at":"2025-06-29 06:59:54"}
[2025-06-29 06:59:54] ❌ 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 06:59:54] 查找食材: 编码='', 名称='无编码测试食材'
[2025-06-29 06:59:54] ⚠️ 食材不存在，开始自动创建
[2025-06-29 06:59:54] 准备创建食材: {"code":"AUTO_1751180394_8135","name":"\u65e0\u7f16\u7801\u6d4b\u8bd5\u98df\u6750","specification":"","unit":"\u4e2a","category_id":1,"brand":"","origin":"","status":1,"created_at":"2025-06-29 06:59:54"}
[2025-06-29 06:59:54] ❌ 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'shelf_life_days' doesn't have a default value
[2025-06-29 07:02:05] 查找食材: 编码='TEST001', 名称='测试食材1'
[2025-06-29 07:02:05] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:02:05] 准备创建食材: {"code":"TEST001","name":"\u6d4b\u8bd5\u98df\u67501","specification":"500g","unit":"\u5305","category_id":1,"brand":"\u6d4b\u8bd5\u54c1\u724c","origin":"\u6d4b\u8bd5\u4ea7\u5730","shelf_life":"","shelf_life_days":0,"status":1,"created_at":"2025-06-29 07:02:05"}
[2025-06-29 07:02:05] ❌ 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'created_by' doesn't have a default value
[2025-06-29 07:02:05] 查找食材: 编码='TEST002', 名称='测试食材2'
[2025-06-29 07:02:05] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:02:05] 准备创建食材: {"code":"TEST002","name":"\u6d4b\u8bd5\u98df\u67502","specification":"1kg","unit":"\u888b","category_id":1,"brand":"","origin":"","shelf_life":"","shelf_life_days":0,"status":1,"created_at":"2025-06-29 07:02:05"}
[2025-06-29 07:02:05] ❌ 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'created_by' doesn't have a default value
[2025-06-29 07:02:05] 查找食材: 编码='', 名称='无编码测试食材'
[2025-06-29 07:02:05] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:02:05] 准备创建食材: {"code":"AUTO_1751180525_5539","name":"\u65e0\u7f16\u7801\u6d4b\u8bd5\u98df\u6750","specification":"","unit":"\u4e2a","category_id":1,"brand":"","origin":"","shelf_life":"","shelf_life_days":0,"status":1,"created_at":"2025-06-29 07:02:05"}
[2025-06-29 07:02:05] ❌ 处理食材失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'created_by' doesn't have a default value
[2025-06-29 07:06:24] 查找食材: 编码='TEST001', 名称='测试食材1'
[2025-06-29 07:06:24] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:06:24] 准备创建食材: {"code":"TEST001","name":"\u6d4b\u8bd5\u98df\u67501","specification":"500g","unit":"\u5305","category_id":1,"brand":"\u6d4b\u8bd5\u54c1\u724c","origin":"\u6d4b\u8bd5\u4ea7\u5730","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:06:24"}
[2025-06-29 07:06:24] ✅ 食材创建成功: ID=6
[2025-06-29 07:06:24] 查找食材: 编码='TEST002', 名称='测试食材2'
[2025-06-29 07:06:24] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:06:24] 准备创建食材: {"code":"TEST002","name":"\u6d4b\u8bd5\u98df\u67502","specification":"1kg","unit":"\u888b","category_id":1,"brand":"","origin":"","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:06:24"}
[2025-06-29 07:06:24] ✅ 食材创建成功: ID=7
[2025-06-29 07:06:24] 查找食材: 编码='', 名称='无编码测试食材'
[2025-06-29 07:06:24] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:06:24] 准备创建食材: {"code":"AUTO_1751180784_2630","name":"\u65e0\u7f16\u7801\u6d4b\u8bd5\u98df\u6750","specification":"","unit":"\u4e2a","category_id":1,"brand":"","origin":"","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:06:24"}
[2025-06-29 07:06:24] ✅ 食材创建成功: ID=8
[2025-06-29 07:08:41] === 开始处理明细数据 ===
[2025-06-29 07:08:41] 数据总行数: 100
[2025-06-29 07:08:41] 检查第6行第1列: '商品明细'
[2025-06-29 07:08:41] 找到明细标题行: 第6行
[2025-06-29 07:08:41] 明细数据从第7行开始处理
[2025-06-29 07:08:41] --- 处理第7行 ---
[2025-06-29 07:08:41] 第7行字段: 编码='商品编码', 名称='商品名称', 数量='金额小计(元)'
[2025-06-29 07:08:41] 第7行: 开始导入明细
[2025-06-29 07:08:41] 第7行: 导入失败 - 第7行: 数量必须大于0（第10列）
[2025-06-29 07:08:41] --- 处理第8行 ---
[2025-06-29 07:08:41] 第8行字段: 编码='DBC1685', 名称='大白菜', 数量='44.4'
[2025-06-29 07:08:41] 第8行: 开始导入明细
[2025-06-29 07:08:41] 查找食材: 编码='DBC1685', 名称='大白菜'
[2025-06-29 07:08:41] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:08:41] 准备创建食材: {"code":"DBC1685","name":"\u5927\u767d\u83dc","specification":"","unit":"\u65b0\u9c9c\u852c\u83dc","category_id":1,"brand":"\u53f6\u83dc\u7c7b","origin":"\u65a4","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:08:41"}
[2025-06-29 07:08:41] ✅ 食材创建成功: ID=9
[2025-06-29 07:08:41] 第8行: 导入成功
[2025-06-29 07:08:41] --- 处理第9行 ---
[2025-06-29 07:08:41] 第9行字段: 编码='LHB1033', 名称='莲花白（平顶）', 数量='42.8'
[2025-06-29 07:08:41] 第9行: 开始导入明细
[2025-06-29 07:08:41] 查找食材: 编码='LHB1033', 名称='莲花白（平顶）'
[2025-06-29 07:08:41] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:08:41] 准备创建食材: {"code":"LHB1033","name":"\u83b2\u82b1\u767d\uff08\u5e73\u9876\uff09","specification":"","unit":"\u65b0\u9c9c\u852c\u83dc","category_id":1,"brand":"\u53f6\u83dc\u7c7b","origin":"\u65a4","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:08:41"}
[2025-06-29 07:08:41] ✅ 食材创建成功: ID=10
[2025-06-29 07:08:41] 第9行: 导入成功
[2025-06-29 07:08:41] --- 处理第10行 ---
[2025-06-29 07:08:41] 第10行字段: 编码='XLH1581', 名称='西兰花', 数量='162'
[2025-06-29 07:08:41] 第10行: 开始导入明细
[2025-06-29 07:08:41] 查找食材: 编码='XLH1581', 名称='西兰花'
[2025-06-29 07:08:41] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:08:41] 准备创建食材: {"code":"XLH1581","name":"\u897f\u5170\u82b1","specification":"","unit":"\u65b0\u9c9c\u852c\u83dc","category_id":1,"brand":"\u8304\u679c\u7c7b","origin":"\u65a4","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:08:41"}
[2025-06-29 07:08:41] ✅ 食材创建成功: ID=11
[2025-06-29 07:08:41] 第10行: 导入成功
[2025-06-29 07:08:41] --- 处理第11行 ---
[2025-06-29 07:08:41] 第11行字段: 编码='XHS1526', 名称='西红柿', 数量='127.8'
[2025-06-29 07:08:41] 第11行: 开始导入明细
[2025-06-29 07:08:41] 查找食材: 编码='XHS1526', 名称='西红柿'
[2025-06-29 07:08:41] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:08:41] 准备创建食材: {"code":"XHS1526","name":"\u897f\u7ea2\u67ff","specification":"","unit":"\u65b0\u9c9c\u852c\u83dc","category_id":1,"brand":"\u8304\u679c\u7c7b","origin":"\u65a4","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:08:41"}
[2025-06-29 07:08:41] ✅ 食材创建成功: ID=12
[2025-06-29 07:08:41] 第11行: 导入成功
[2025-06-29 07:08:41] --- 处理第12行 ---
[2025-06-29 07:08:41] 第12行字段: 编码='WDHG7779', 名称='外地黄瓜', 数量='142.2'
[2025-06-29 07:08:41] 第12行: 开始导入明细
[2025-06-29 07:08:41] 查找食材: 编码='WDHG7779', 名称='外地黄瓜'
[2025-06-29 07:08:41] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:08:41] 准备创建食材: {"code":"WDHG7779","name":"\u5916\u5730\u9ec4\u74dc","specification":"","unit":"\u65b0\u9c9c\u852c\u83dc","category_id":1,"brand":"\u8304\u679c\u7c7b","origin":"\u65a4","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:08:41"}
[2025-06-29 07:08:41] ✅ 食材创建成功: ID=13
[2025-06-29 07:08:41] 第12行: 导入成功
[2025-06-29 07:08:41] --- 处理第13行 ---
[2025-06-29 07:08:41] 第13行字段: 编码='XNGC2635', 名称='小南瓜（长）', 数量='90'
[2025-06-29 07:08:41] 第13行: 开始导入明细
[2025-06-29 07:08:41] 查找食材: 编码='XNGC2635', 名称='小南瓜（长）'
[2025-06-29 07:08:41] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:08:41] 准备创建食材: {"code":"XNGC2635","name":"\u5c0f\u5357\u74dc\uff08\u957f\uff09","specification":"","unit":"\u65b0\u9c9c\u852c\u83dc","category_id":1,"brand":"\u8304\u679c\u7c7b","origin":"\u65a4","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:08:41"}
[2025-06-29 07:08:41] ✅ 食材创建成功: ID=14
[2025-06-29 07:08:42] 第13行: 导入成功
[2025-06-29 07:08:42] --- 处理第14行 ---
[2025-06-29 07:08:42] 第14行字段: 编码='dasd12', 名称='黄玉米棒', 数量='169'
[2025-06-29 07:08:42] 第14行: 开始导入明细
[2025-06-29 07:08:42] 查找食材: 编码='dasd12', 名称='黄玉米棒'
[2025-06-29 07:08:42] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:08:42] 准备创建食材: {"code":"dasd12","name":"\u9ec4\u7389\u7c73\u68d2","specification":"","unit":"\u65b0\u9c9c\u852c\u83dc","category_id":1,"brand":"\u8304\u679c\u7c7b","origin":"\u65a4","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:08:42"}
[2025-06-29 07:08:42] ✅ 食材创建成功: ID=15
[2025-06-29 07:08:42] 第14行: 导入成功
[2025-06-29 07:08:42] --- 处理第15行 ---
[2025-06-29 07:08:42] 第15行字段: 编码='SDCK1461', 名称='莴笋', 数量='113.5'
[2025-06-29 07:08:42] 第15行: 开始导入明细
[2025-06-29 07:08:42] 查找食材: 编码='SDCK1461', 名称='莴笋'
[2025-06-29 07:08:42] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:08:42] 准备创建食材: {"code":"SDCK1461","name":"\u83b4\u7b0b","specification":"","unit":"\u65b0\u9c9c\u852c\u83dc","category_id":1,"brand":"\u6839\u830e\u7c7b","origin":"\u65a4","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:08:42"}
[2025-06-29 07:08:42] ✅ 食材创建成功: ID=16
[2025-06-29 07:08:42] 第15行: 导入成功
[2025-06-29 07:08:42] --- 处理第16行 ---
[2025-06-29 07:08:42] 第16行字段: 编码='HLBWD2265', 名称='胡萝卜（外地）', 数量='72'
[2025-06-29 07:08:42] 第16行: 开始导入明细
[2025-06-29 07:08:42] 查找食材: 编码='HLBWD2265', 名称='胡萝卜（外地）'
[2025-06-29 07:08:42] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:08:42] 准备创建食材: {"code":"HLBWD2265","name":"\u80e1\u841d\u535c\uff08\u5916\u5730\uff09","specification":"","unit":"\u65b0\u9c9c\u852c\u83dc","category_id":1,"brand":"\u6839\u830e\u7c7b","origin":"\u65a4","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:08:42"}
[2025-06-29 07:08:42] ✅ 食材创建成功: ID=17
[2025-06-29 07:08:42] 第16行: 导入成功
[2025-06-29 07:08:42] --- 处理第17行 ---
[2025-06-29 07:08:42] 第17行字段: 编码='BLBG9279', 名称='白萝卜', 数量='32.7'
[2025-06-29 07:08:42] 第17行: 开始导入明细
[2025-06-29 07:08:42] 查找食材: 编码='BLBG9279', 名称='白萝卜'
[2025-06-29 07:08:42] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:08:42] 准备创建食材: {"code":"BLBG9279","name":"\u767d\u841d\u535c","specification":"","unit":"\u65b0\u9c9c\u852c\u83dc","category_id":1,"brand":"\u6839\u830e\u7c7b","origin":"\u65a4","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:08:42"}
[2025-06-29 07:08:42] ✅ 食材创建成功: ID=18
[2025-06-29 07:08:42] 第17行: 导入成功
[2025-06-29 07:08:42] --- 处理第18行 ---
[2025-06-29 07:08:42] 第18行字段: 编码='QCLBDG1821', 名称='芹菜（绿）', 数量='79'
[2025-06-29 07:08:42] 第18行: 开始导入明细
[2025-06-29 07:08:42] 查找食材: 编码='QCLBDG1821', 名称='芹菜（绿）'
[2025-06-29 07:08:42] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:08:42] 准备创建食材: {"code":"QCLBDG1821","name":"\u82b9\u83dc\uff08\u7eff\uff09","specification":"","unit":"\u65b0\u9c9c\u852c\u83dc","category_id":1,"brand":"\u6839\u830e\u7c7b","origin":"\u65a4","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:08:42"}
[2025-06-29 07:08:42] ✅ 食材创建成功: ID=19
[2025-06-29 07:08:42] 第18行: 导入成功
[2025-06-29 07:08:42] --- 处理第19行 ---
[2025-06-29 07:08:42] 第19行字段: 编码='XC1624', 名称='香菜', 数量='31.32'
[2025-06-29 07:08:42] 第19行: 开始导入明细
[2025-06-29 07:08:42] 查找食材: 编码='XC1624', 名称='香菜'
[2025-06-29 07:08:42] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:08:42] 准备创建食材: {"code":"XC1624","name":"\u9999\u83dc","specification":"","unit":"\u65b0\u9c9c\u852c\u83dc","category_id":1,"brand":"\u8471\u59dc\u849c\u7c7b","origin":"\u65a4","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:08:42"}
[2025-06-29 07:08:42] ✅ 食材创建成功: ID=20
[2025-06-29 07:08:42] 第19行: 导入成功
[2025-06-29 07:08:42] --- 处理第20行 ---
[2025-06-29 07:08:42] 第20行字段: 编码='SMBT0731', 名称='蒜苗', 数量='76.5'
[2025-06-29 07:08:42] 第20行: 开始导入明细
[2025-06-29 07:08:42] 查找食材: 编码='SMBT0731', 名称='蒜苗'
[2025-06-29 07:08:42] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:08:42] 准备创建食材: {"code":"SMBT0731","name":"\u849c\u82d7","specification":"","unit":"\u65b0\u9c9c\u852c\u83dc","category_id":1,"brand":"\u8471\u59dc\u849c\u7c7b","origin":"\u65a4","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:08:42"}
[2025-06-29 07:08:42] ✅ 食材创建成功: ID=21
[2025-06-29 07:08:42] 第20行: 导入成功
[2025-06-29 07:08:42] --- 处理第21行 ---
[2025-06-29 07:08:42] 第21行字段: 编码='XCBDDFC7870', 名称='小葱', 数量='42'
[2025-06-29 07:08:42] 第21行: 开始导入明细
[2025-06-29 07:08:42] 查找食材: 编码='XCBDDFC7870', 名称='小葱'
[2025-06-29 07:08:42] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:08:42] 准备创建食材: {"code":"XCBDDFC7870","name":"\u5c0f\u8471","specification":"","unit":"\u65b0\u9c9c\u852c\u83dc","category_id":1,"brand":"\u8471\u59dc\u849c\u7c7b","origin":"\u65a4","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:08:42"}
[2025-06-29 07:08:42] ✅ 食材创建成功: ID=22
[2025-06-29 07:08:42] 第21行: 导入成功
[2025-06-29 07:08:42] --- 处理第22行 ---
[2025-06-29 07:08:42] 第22行字段: 编码='XJJ0650', 名称='小米椒（小尖椒）', 数量='17.34'
[2025-06-29 07:08:42] 第22行: 开始导入明细
[2025-06-29 07:08:42] 查找食材: 编码='XJJ0650', 名称='小米椒（小尖椒）'
[2025-06-29 07:08:42] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:08:42] 准备创建食材: {"code":"XJJ0650","name":"\u5c0f\u7c73\u6912\uff08\u5c0f\u5c16\u6912\uff09","specification":"","unit":"\u65b0\u9c9c\u852c\u83dc","category_id":1,"brand":"\u8fa3\u6912\u7c7b","origin":"\u65a4","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:08:42"}
[2025-06-29 07:08:42] ✅ 食材创建成功: ID=23
[2025-06-29 07:08:42] 第22行: 导入成功
[2025-06-29 07:08:42] --- 处理第23行 ---
[2025-06-29 07:08:42] 第23行字段: 编码='XJ0544', 名称='青线椒', 数量='129'
[2025-06-29 07:08:42] 第23行: 开始导入明细
[2025-06-29 07:08:42] 查找食材: 编码='XJ0544', 名称='青线椒'
[2025-06-29 07:08:42] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:08:42] 准备创建食材: {"code":"XJ0544","name":"\u9752\u7ebf\u6912","specification":"","unit":"\u65b0\u9c9c\u852c\u83dc","category_id":1,"brand":"\u8fa3\u6912\u7c7b","origin":"\u65a4","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:08:42"}
[2025-06-29 07:08:42] ✅ 食材创建成功: ID=24
[2025-06-29 07:08:43] 第23行: 导入成功
[2025-06-29 07:08:43] --- 处理第24行 ---
[2025-06-29 07:08:43] 第24行字段: 编码='DQJ0496', 名称='大青椒', 数量='84'
[2025-06-29 07:08:43] 第24行: 开始导入明细
[2025-06-29 07:08:43] 查找食材: 编码='DQJ0496', 名称='大青椒'
[2025-06-29 07:08:43] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:08:43] 准备创建食材: {"code":"DQJ0496","name":"\u5927\u9752\u6912","specification":"","unit":"\u65b0\u9c9c\u852c\u83dc","category_id":1,"brand":"\u8fa3\u6912\u7c7b","origin":"\u65a4","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:08:43"}
[2025-06-29 07:08:43] ✅ 食材创建成功: ID=25
[2025-06-29 07:08:43] 第24行: 导入成功
[2025-06-29 07:08:43] --- 处理第25行 ---
[2025-06-29 07:08:43] 第25行字段: 编码='DHJ6400', 名称='大红椒', 数量='99.4'
[2025-06-29 07:08:43] 第25行: 开始导入明细
[2025-06-29 07:08:43] 查找食材: 编码='DHJ6400', 名称='大红椒'
[2025-06-29 07:08:43] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:08:43] 准备创建食材: {"code":"DHJ6400","name":"\u5927\u7ea2\u6912","specification":"","unit":"\u65b0\u9c9c\u852c\u83dc","category_id":1,"brand":"\u8fa3\u6912\u7c7b","origin":"\u65a4","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:08:43"}
[2025-06-29 07:08:43] ✅ 食材创建成功: ID=26
[2025-06-29 07:08:43] 第25行: 导入成功
[2025-06-29 07:08:43] --- 处理第26行 ---
[2025-06-29 07:08:43] 第26行字段: 编码='ZG0206', 名称='猪肝', 数量='232.5'
[2025-06-29 07:08:43] 第26行: 开始导入明细
[2025-06-29 07:08:43] 查找食材: 编码='ZG0206', 名称='猪肝'
[2025-06-29 07:08:43] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:08:43] 准备创建食材: {"code":"ZG0206","name":"\u732a\u809d","specification":"","unit":"\u8089\u79bd\u7c7b","category_id":1,"brand":"\u732a\u8089\u7c7b","origin":"\u65a4","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:08:43"}
[2025-06-29 07:08:43] ✅ 食材创建成功: ID=27
[2025-06-29 07:08:43] 第26行: 导入成功
[2025-06-29 07:08:43] --- 处理第27行 ---
[2025-06-29 07:08:43] 第27行字段: 编码='WHR4545', 名称='猪五花肉', 数量='408.9'
[2025-06-29 07:08:43] 第27行: 开始导入明细
[2025-06-29 07:08:43] 查找食材: 编码='WHR4545', 名称='猪五花肉'
[2025-06-29 07:08:43] ✅ 通过编码找到食材: ID=4, 名称=猪五花肉
[2025-06-29 07:08:43] 第27行: 导入成功
[2025-06-29 07:08:43] --- 处理第28行 ---
[2025-06-29 07:08:43] 第28行字段: 编码='ZQTRQG4200', 名称='猪前后腿肉去骨', 数量='1712.2'
[2025-06-29 07:08:43] 第28行: 开始导入明细
[2025-06-29 07:08:43] 查找食材: 编码='ZQTRQG4200', 名称='猪前后腿肉去骨'
[2025-06-29 07:08:43] ✅ 通过编码找到食材: ID=3, 名称=猪前后腿肉去骨
[2025-06-29 07:08:43] 第28行: 导入成功
[2025-06-29 07:08:43] --- 处理第29行 ---
[2025-06-29 07:08:43] 第29行字段: 编码='ZPGZP8811', 名称='猪排骨（杂排）', 数量='110'
[2025-06-29 07:08:43] 第29行: 开始导入明细
[2025-06-29 07:08:43] 查找食材: 编码='ZPGZP8811', 名称='猪排骨（杂排）'
[2025-06-29 07:08:43] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:08:43] 准备创建食材: {"code":"ZPGZP8811","name":"\u732a\u6392\u9aa8\uff08\u6742\u6392\uff09","specification":"","unit":"\u8089\u79bd\u7c7b","category_id":1,"brand":"\u732a\u8089\u7c7b","origin":"\u65a4","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:08:43"}
[2025-06-29 07:08:43] ✅ 食材创建成功: ID=28
[2025-06-29 07:08:43] 第29行: 导入成功
[2025-06-29 07:08:43] --- 处理第30行 ---
[2025-06-29 07:08:43] 第30行字段: 编码='SPBM00003294', 名称='牛腩', 数量='930'
[2025-06-29 07:08:43] 第30行: 开始导入明细
[2025-06-29 07:08:43] 查找食材: 编码='SPBM00003294', 名称='牛腩'
[2025-06-29 07:08:43] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:08:43] 准备创建食材: {"code":"SPBM00003294","name":"\u725b\u8169","specification":"","unit":"\u8089\u79bd\u7c7b","category_id":1,"brand":"\u725b\u7f8a\u8089\u7c7b","origin":"\u65a4","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:08:43"}
[2025-06-29 07:08:43] ✅ 食材创建成功: ID=29
[2025-06-29 07:08:43] 第30行: 导入成功
[2025-06-29 07:08:43] --- 处理第31行 ---
[2025-06-29 07:08:43] 第31行字段: 编码='WZSNR3317', 名称='牛肉', 数量='679.4'
[2025-06-29 07:08:43] 第31行: 开始导入明细
[2025-06-29 07:08:43] 查找食材: 编码='WZSNR3317', 名称='牛肉'
[2025-06-29 07:08:43] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:08:43] 准备创建食材: {"code":"WZSNR3317","name":"\u725b\u8089","specification":"","unit":"\u8089\u79bd\u7c7b","category_id":1,"brand":"\u725b\u7f8a\u8089\u7c7b","origin":"\u65a4","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:08:43"}
[2025-06-29 07:08:43] ✅ 食材创建成功: ID=30
[2025-06-29 07:08:43] 第31行: 导入成功
[2025-06-29 07:08:43] --- 处理第32行 ---
[2025-06-29 07:08:43] 第32行字段: 编码='SHJ8439', 名称='三黄鸡（白条、无内脏）', 数量='1022'
[2025-06-29 07:08:43] 第32行: 开始导入明细
[2025-06-29 07:08:43] 查找食材: 编码='SHJ8439', 名称='三黄鸡（白条、无内脏）'
[2025-06-29 07:08:43] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:08:43] 准备创建食材: {"code":"SHJ8439","name":"\u4e09\u9ec4\u9e21\uff08\u767d\u6761\u3001\u65e0\u5185\u810f\uff09","specification":"","unit":"\u8089\u79bd\u7c7b","category_id":1,"brand":"\u9e21\u9e2d\u9e45\u5154\u7c7b","origin":"\u65a4","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:08:43"}
[2025-06-29 07:08:43] ✅ 食材创建成功: ID=31
[2025-06-29 07:08:43] 第32行: 导入成功
[2025-06-29 07:08:43] --- 处理第33行 ---
[2025-06-29 07:08:43] 第33行字段: 编码='HDY6675', 名称='黄豆芽', 数量='56.4'
[2025-06-29 07:08:43] 第33行: 开始导入明细
[2025-06-29 07:08:43] 查找食材: 编码='HDY6675', 名称='黄豆芽'
[2025-06-29 07:08:43] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:08:43] 准备创建食材: {"code":"HDY6675","name":"\u9ec4\u8c46\u82bd","specification":"","unit":"\u7c89\u9762\u8c46\u5236\u54c1","category_id":1,"brand":"\u8c46\u5236\u54c1","origin":"\u65a4","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:08:43"}
[2025-06-29 07:08:43] ✅ 食材创建成功: ID=32
[2025-06-29 07:08:43] 第33行: 导入成功
[2025-06-29 07:08:43] --- 处理第34行 ---
[2025-06-29 07:08:43] 第34行字段: 编码='SDF2528', 名称='水豆腐', 数量='125'
[2025-06-29 07:08:43] 第34行: 开始导入明细
[2025-06-29 07:08:43] 查找食材: 编码='SDF2528', 名称='水豆腐'
[2025-06-29 07:08:43] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:08:43] 准备创建食材: {"code":"SDF2528","name":"\u6c34\u8c46\u8150","specification":"","unit":"\u7c89\u9762\u8c46\u5236\u54c1","category_id":1,"brand":"\u8c46\u5236\u54c1","origin":"\u677f","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:08:43"}
[2025-06-29 07:08:43] ✅ 食材创建成功: ID=33
[2025-06-29 07:08:44] 第34行: 导入成功
[2025-06-29 07:08:44] --- 处理第35行 ---
[2025-06-29 07:08:44] 第35行字段: 编码='GDFP4973', 名称='干豆腐片（薄）', 数量='52.5'
[2025-06-29 07:08:44] 第35行: 开始导入明细
[2025-06-29 07:08:44] 查找食材: 编码='GDFP4973', 名称='干豆腐片（薄）'
[2025-06-29 07:08:44] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:08:44] 准备创建食材: {"code":"GDFP4973","name":"\u5e72\u8c46\u8150\u7247\uff08\u8584\uff09","specification":"","unit":"\u7c89\u9762\u8c46\u5236\u54c1","category_id":1,"brand":"\u8c46\u5236\u54c1","origin":"\u65a4","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:08:44"}
[2025-06-29 07:08:44] ✅ 食材创建成功: ID=34
[2025-06-29 07:08:44] 第35行: 导入成功
[2025-06-29 07:08:44] --- 处理第36行 ---
[2025-06-29 07:08:44] 第36行字段: 编码='GDFHP1223', 名称='干豆腐片（厚）', 数量='160'
[2025-06-29 07:08:44] 第36行: 开始导入明细
[2025-06-29 07:08:44] 查找食材: 编码='GDFHP1223', 名称='干豆腐片（厚）'
[2025-06-29 07:08:44] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:08:44] 准备创建食材: {"code":"GDFHP1223","name":"\u5e72\u8c46\u8150\u7247\uff08\u539a\uff09","specification":"","unit":"\u7c89\u9762\u8c46\u5236\u54c1","category_id":1,"brand":"\u8c46\u5236\u54c1","origin":"\u65a4","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:08:44"}
[2025-06-29 07:08:44] ✅ 食材创建成功: ID=35
[2025-06-29 07:08:44] 第36行: 导入成功
[2025-06-29 07:08:44] --- 处理第37行 ---
[2025-06-29 07:08:44] 第37行字段: 编码='YF4729', 名称='圆粉', 数量='330'
[2025-06-29 07:08:44] 第37行: 开始导入明细
[2025-06-29 07:08:44] 查找食材: 编码='YF4729', 名称='圆粉'
[2025-06-29 07:08:44] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:08:44] 准备创建食材: {"code":"YF4729","name":"\u5706\u7c89","specification":"","unit":"\u7c89\u9762\u8c46\u5236\u54c1","category_id":1,"brand":"\u7c89\u9762\u5236\u54c1","origin":"\u65a4","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:08:44"}
[2025-06-29 07:08:44] ✅ 食材创建成功: ID=36
[2025-06-29 07:08:44] 第37行: 导入成功
[2025-06-29 07:08:44] --- 处理第38行 ---
[2025-06-29 07:08:44] 第38行字段: 编码='BF4586', 名称='扁粉', 数量='577.5'
[2025-06-29 07:08:44] 第38行: 开始导入明细
[2025-06-29 07:08:44] 查找食材: 编码='BF4586', 名称='扁粉'
[2025-06-29 07:08:44] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:08:44] 准备创建食材: {"code":"BF4586","name":"\u6241\u7c89","specification":"","unit":"\u7c89\u9762\u8c46\u5236\u54c1","category_id":1,"brand":"\u7c89\u9762\u5236\u54c1","origin":"\u65a4","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:08:44"}
[2025-06-29 07:08:44] ✅ 食材创建成功: ID=37
[2025-06-29 07:08:44] 第38行: 导入成功
[2025-06-29 07:08:44] --- 处理第39行 ---
[2025-06-29 07:08:44] 第39行字段: 编码='YF5478', 名称='油粉', 数量='49.5'
[2025-06-29 07:08:44] 第39行: 开始导入明细
[2025-06-29 07:08:44] 查找食材: 编码='YF5478', 名称='油粉'
[2025-06-29 07:08:44] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:08:44] 准备创建食材: {"code":"YF5478","name":"\u6cb9\u7c89","specification":"","unit":"\u7c89\u9762\u8c46\u5236\u54c1","category_id":1,"brand":"\u7c89\u9762\u5236\u54c1","origin":"\u65a4","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:08:44"}
[2025-06-29 07:08:44] ✅ 食材创建成功: ID=38
[2025-06-29 07:08:44] 第39行: 导入成功
[2025-06-29 07:08:44] --- 处理第40行 ---
[2025-06-29 07:08:44] 第40行字段: 编码='BF5568', 名称='薄粉（卷粉皮）', 数量='24'
[2025-06-29 07:08:44] 第40行: 开始导入明细
[2025-06-29 07:08:44] 查找食材: 编码='BF5568', 名称='薄粉（卷粉皮）'
[2025-06-29 07:08:44] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:08:44] 准备创建食材: {"code":"BF5568","name":"\u8584\u7c89\uff08\u5377\u7c89\u76ae\uff09","specification":"","unit":"\u7c89\u9762\u8c46\u5236\u54c1","category_id":1,"brand":"\u7c89\u9762\u5236\u54c1","origin":"\u65a4","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:08:44"}
[2025-06-29 07:08:44] ✅ 食材创建成功: ID=39
[2025-06-29 07:08:44] 第40行: 导入成功
[2025-06-29 07:08:44] --- 处理第41行 ---
[2025-06-29 07:08:44] 第41行字段: 编码='QTXW2925', 名称='秋田小町25KG', 数量='1240'
[2025-06-29 07:08:44] 第41行: 开始导入明细
[2025-06-29 07:08:44] 查找食材: 编码='QTXW2925', 名称='秋田小町25KG'
[2025-06-29 07:08:44] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:08:44] 准备创建食材: {"code":"QTXW2925","name":"\u79cb\u7530\u5c0f\u753a25KG","specification":"25KG","unit":"\u7c73\u9762\u7cae\u6cb9","category_id":1,"brand":"\u5927\u7c73\u7c7b","origin":"\u888b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:08:44"}
[2025-06-29 07:08:44] ✅ 食材创建成功: ID=40
[2025-06-29 07:08:44] 第41行: 导入成功
[2025-06-29 07:08:44] --- 处理第42行 ---
[2025-06-29 07:08:44] 第42行字段: 编码='GLHLJM1006', 名称='冠霖糊辣椒面', 数量='212'
[2025-06-29 07:08:44] 第42行: 开始导入明细
[2025-06-29 07:08:44] 查找食材: 编码='GLHLJM1006', 名称='冠霖糊辣椒面'
[2025-06-29 07:08:44] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:08:44] 准备创建食材: {"code":"GLHLJM1006","name":"\u51a0\u9716\u7cca\u8fa3\u6912\u9762","specification":"5\u65a4\/\u888b","unit":"\u8c03\u6599\u5e72\u8d27","category_id":1,"brand":"\u5e72\u8fa3\u6912\u82b1\u6912\u9762\u7c7b","origin":"\u888b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:08:44"}
[2025-06-29 07:08:44] ✅ 食材创建成功: ID=41
[2025-06-29 07:08:44] 第42行: 导入成功
[2025-06-29 07:08:44] --- 处理第43行 ---
[2025-06-29 07:08:44] 第43行字段: 编码='DFP8053', 名称='干豆腐皮', 数量='75'
[2025-06-29 07:08:44] 第43行: 开始导入明细
[2025-06-29 07:08:44] 查找食材: 编码='DFP8053', 名称='干豆腐皮'
[2025-06-29 07:08:44] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:08:44] 准备创建食材: {"code":"DFP8053","name":"\u5e72\u8c46\u8150\u76ae","specification":"\u7ea612.5\u65a4","unit":"\u8c03\u6599\u5e72\u8d27","category_id":1,"brand":"\u5e72\u8d27\u7c7b","origin":"\u4ef6","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:08:44"}
[2025-06-29 07:08:44] ✅ 食材创建成功: ID=42
[2025-06-29 07:08:44] 第43行: 导入成功
[2025-06-29 07:08:44] --- 处理第44行 ---
[2025-06-29 07:08:44] 第44行字段: 编码='CS7213', 名称='脆哨', 数量='340'
[2025-06-29 07:08:44] 第44行: 开始导入明细
[2025-06-29 07:08:44] 查找食材: 编码='CS7213', 名称='脆哨'
[2025-06-29 07:08:44] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:08:44] 准备创建食材: {"code":"CS7213","name":"\u8106\u54e8","specification":"","unit":"\u8c03\u6599\u5e72\u8d27","category_id":1,"brand":"\u5e72\u8d27\u7c7b","origin":"\u65a4","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:08:44"}
[2025-06-29 07:08:44] ✅ 食材创建成功: ID=43
[2025-06-29 07:08:45] 第44行: 导入成功
[2025-06-29 07:08:45] --- 处理第45行 ---
[2025-06-29 07:08:45] 第45行字段: 编码='XJ1638', 名称='香蕉', 数量='129.9'
[2025-06-29 07:08:45] 第45行: 开始导入明细
[2025-06-29 07:08:45] 查找食材: 编码='XJ1638', 名称='香蕉'
[2025-06-29 07:08:45] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:08:45] 准备创建食材: {"code":"XJ1638","name":"\u9999\u8549","specification":"","unit":"\u65f6\u4ee4\u6c34\u679c","category_id":1,"brand":"\u65f6\u4ee4\u6c34\u679c1","origin":"\u65a4","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:08:45"}
[2025-06-29 07:08:45] ✅ 食材创建成功: ID=44
[2025-06-29 07:08:45] 第45行: 导入成功
[2025-06-29 07:08:45] --- 处理第46行 ---
[2025-06-29 07:08:45] 第46行字段: 编码='HWZTX7875', 名称='茉莉香提', 数量='153.2'
[2025-06-29 07:08:45] 第46行: 开始导入明细
[2025-06-29 07:08:45] 查找食材: 编码='HWZTX7875', 名称='茉莉香提'
[2025-06-29 07:08:45] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:08:45] 准备创建食材: {"code":"HWZTX7875","name":"\u8309\u8389\u9999\u63d0","specification":"","unit":"\u65f6\u4ee4\u6c34\u679c","category_id":1,"brand":"\u65f6\u4ee4\u6c34\u679c1","origin":"\u65a4","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:08:45"}
[2025-06-29 07:08:45] ✅ 食材创建成功: ID=45
[2025-06-29 07:08:45] 第46行: 导入成功
[2025-06-29 07:08:45] --- 处理第47行 ---
[2025-06-29 07:08:45] 第47行字段: 编码='HMGXMH7349', 名称='哈密瓜（小蜜25号）大果', 数量='275'
[2025-06-29 07:08:45] 第47行: 开始导入明细
[2025-06-29 07:08:45] 查找食材: 编码='HMGXMH7349', 名称='哈密瓜（小蜜25号）大果'
[2025-06-29 07:08:45] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:08:45] 准备创建食材: {"code":"HMGXMH7349","name":"\u54c8\u5bc6\u74dc\uff08\u5c0f\u871c25\u53f7\uff09\u5927\u679c","specification":"\u6c99\u5730\u871c\u74dc\u5927\u679c","unit":"\u65f6\u4ee4\u6c34\u679c","category_id":1,"brand":"\u65f6\u4ee4\u6c34\u679c1","origin":"\u65a4","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:08:45"}
[2025-06-29 07:08:45] ✅ 食材创建成功: ID=46
[2025-06-29 07:08:45] 第47行: 导入成功
[2025-06-29 07:08:45] --- 处理第48行 ---
[2025-06-29 07:08:45] 第48行字段: 编码='XD6079', 名称='豇豆（绿）', 数量='126'
[2025-06-29 07:08:45] 第48行: 开始导入明细
[2025-06-29 07:08:45] 查找食材: 编码='XD6079', 名称='豇豆（绿）'
[2025-06-29 07:08:45] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:08:45] 准备创建食材: {"code":"XD6079","name":"\u8c47\u8c46\uff08\u7eff\uff09","specification":"","unit":"\u65b0\u9c9c\u852c\u83dc","category_id":1,"brand":"\u8304\u679c\u7c7b","origin":"\u65a4","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:08:45"}
[2025-06-29 07:08:45] ✅ 食材创建成功: ID=47
[2025-06-29 07:08:45] 第48行: 导入成功
[2025-06-29 07:08:45] --- 处理第49行 ---
[2025-06-29 07:08:45] 第49行字段: 编码='XTD9873', 名称='土豆', 数量='380'
[2025-06-29 07:08:45] 第49行: 开始导入明细
[2025-06-29 07:08:45] 查找食材: 编码='XTD9873', 名称='土豆'
[2025-06-29 07:08:45] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:08:45] 准备创建食材: {"code":"XTD9873","name":"\u571f\u8c46","specification":"","unit":"\u65b0\u9c9c\u852c\u83dc","category_id":1,"brand":"\u6839\u830e\u7c7b","origin":"\u65a4","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:08:45"}
[2025-06-29 07:08:45] ✅ 食材创建成功: ID=48
[2025-06-29 07:08:45] 第49行: 导入成功
[2025-06-29 07:08:45] --- 处理第50行 ---
[2025-06-29 07:08:45] 第50行字段: 编码='LJ0368', 名称='老姜（带土）', 数量='108'
[2025-06-29 07:08:45] 第50行: 开始导入明细
[2025-06-29 07:08:45] 查找食材: 编码='LJ0368', 名称='老姜（带土）'
[2025-06-29 07:08:45] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:08:45] 准备创建食材: {"code":"LJ0368","name":"\u8001\u59dc\uff08\u5e26\u571f\uff09","specification":"","unit":"\u65b0\u9c9c\u852c\u83dc","category_id":1,"brand":"\u8471\u59dc\u849c\u7c7b","origin":"\u65a4","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:08:45"}
[2025-06-29 07:08:45] ✅ 食材创建成功: ID=49
[2025-06-29 07:08:45] 第50行: 导入成功
[2025-06-29 07:08:45] --- 处理第51行 ---
[2025-06-29 07:08:45] 第51行字段: 编码='SM0321', 名称='蒜米', 数量='42.3'
[2025-06-29 07:08:45] 第51行: 开始导入明细
[2025-06-29 07:08:45] 查找食材: 编码='SM0321', 名称='蒜米'
[2025-06-29 07:08:45] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:08:45] 准备创建食材: {"code":"SM0321","name":"\u849c\u7c73","specification":"","unit":"\u65b0\u9c9c\u852c\u83dc","category_id":1,"brand":"\u8471\u59dc\u849c\u7c7b","origin":"\u888b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:08:45"}
[2025-06-29 07:08:45] ✅ 食材创建成功: ID=50
[2025-06-29 07:08:45] 第51行: 导入成功
[2025-06-29 07:08:45] --- 处理第52行 ---
[2025-06-29 07:08:45] 第52行字段: 编码='SPBM00003264', 名称='猪里脊肉', 数量='378.9'
[2025-06-29 07:08:45] 第52行: 开始导入明细
[2025-06-29 07:08:45] 查找食材: 编码='SPBM00003264', 名称='猪里脊肉'
[2025-06-29 07:08:45] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:08:45] 准备创建食材: {"code":"SPBM00003264","name":"\u732a\u91cc\u810a\u8089","specification":"","unit":"\u8089\u79bd\u7c7b","category_id":1,"brand":"\u732a\u8089\u7c7b","origin":"\u65a4","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:08:45"}
[2025-06-29 07:08:45] ✅ 食材创建成功: ID=51
[2025-06-29 07:08:45] 第52行: 导入成功
[2025-06-29 07:08:45] --- 处理第53行 ---
[2025-06-29 07:08:45] 第53行字段: 编码='HYML4819', 名称='黄玉米粒（新鲜）', 数量='175.5'
[2025-06-29 07:08:45] 第53行: 开始导入明细
[2025-06-29 07:08:45] 查找食材: 编码='HYML4819', 名称='黄玉米粒（新鲜）'
[2025-06-29 07:08:45] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:08:45] 准备创建食材: {"code":"HYML4819","name":"\u9ec4\u7389\u7c73\u7c92\uff08\u65b0\u9c9c\uff09","specification":"","unit":"\u65b0\u9c9c\u852c\u83dc","category_id":1,"brand":"\u8304\u679c\u7c7b","origin":"\u65a4","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:08:45"}
[2025-06-29 07:08:45] ✅ 食材创建成功: ID=52
[2025-06-29 07:08:45] 第53行: 导入成功
[2025-06-29 07:08:45] --- 处理第54行 ---
[2025-06-29 07:08:45] 第54行字段: 编码='', 名称='', 数量='11660.66'
[2025-06-29 07:08:45] 第54行: 关键字段为空，跳过
[2025-06-29 07:08:45] --- 处理第55行 ---
[2025-06-29 07:08:45] 第55行: 空行，跳过
[2025-06-29 07:08:45] --- 处理第56行 ---
[2025-06-29 07:08:45] 第56行: 空行，跳过
[2025-06-29 07:08:45] --- 处理第57行 ---
[2025-06-29 07:08:45] 第57行: 空行，跳过
[2025-06-29 07:08:45] --- 处理第58行 ---
[2025-06-29 07:08:45] 第58行: 空行，跳过
[2025-06-29 07:08:45] --- 处理第59行 ---
[2025-06-29 07:08:45] 第59行: 空行，跳过
[2025-06-29 07:08:45] --- 处理第60行 ---
[2025-06-29 07:08:45] 第60行: 空行，跳过
[2025-06-29 07:08:45] --- 处理第61行 ---
[2025-06-29 07:08:45] 第61行: 空行，跳过
[2025-06-29 07:08:45] --- 处理第62行 ---
[2025-06-29 07:08:45] 第62行: 空行，跳过
[2025-06-29 07:08:45] --- 处理第63行 ---
[2025-06-29 07:08:45] 第63行: 空行，跳过
[2025-06-29 07:08:45] --- 处理第64行 ---
[2025-06-29 07:08:45] 第64行: 空行，跳过
[2025-06-29 07:08:45] --- 处理第65行 ---
[2025-06-29 07:08:45] 第65行: 空行，跳过
[2025-06-29 07:08:45] --- 处理第66行 ---
[2025-06-29 07:08:45] 第66行: 空行，跳过
[2025-06-29 07:08:45] --- 处理第67行 ---
[2025-06-29 07:08:45] 第67行: 空行，跳过
[2025-06-29 07:08:45] --- 处理第68行 ---
[2025-06-29 07:08:45] 第68行: 空行，跳过
[2025-06-29 07:08:45] --- 处理第69行 ---
[2025-06-29 07:08:45] 第69行: 空行，跳过
[2025-06-29 07:08:45] --- 处理第70行 ---
[2025-06-29 07:08:45] 第70行: 空行，跳过
[2025-06-29 07:08:45] --- 处理第71行 ---
[2025-06-29 07:08:45] 第71行: 空行，跳过
[2025-06-29 07:08:45] --- 处理第72行 ---
[2025-06-29 07:08:45] 第72行: 空行，跳过
[2025-06-29 07:08:45] --- 处理第73行 ---
[2025-06-29 07:08:45] 第73行: 空行，跳过
[2025-06-29 07:08:45] --- 处理第74行 ---
[2025-06-29 07:08:45] 第74行: 空行，跳过
[2025-06-29 07:08:45] --- 处理第75行 ---
[2025-06-29 07:08:45] 第75行: 空行，跳过
[2025-06-29 07:08:45] --- 处理第76行 ---
[2025-06-29 07:08:45] 第76行: 空行，跳过
[2025-06-29 07:08:45] --- 处理第77行 ---
[2025-06-29 07:08:45] 第77行: 空行，跳过
[2025-06-29 07:08:45] --- 处理第78行 ---
[2025-06-29 07:08:45] 第78行: 空行，跳过
[2025-06-29 07:08:45] --- 处理第79行 ---
[2025-06-29 07:08:45] 第79行: 空行，跳过
[2025-06-29 07:08:45] --- 处理第80行 ---
[2025-06-29 07:08:45] 第80行: 空行，跳过
[2025-06-29 07:08:45] --- 处理第81行 ---
[2025-06-29 07:08:45] 第81行: 空行，跳过
[2025-06-29 07:08:45] --- 处理第82行 ---
[2025-06-29 07:08:45] 第82行: 空行，跳过
[2025-06-29 07:08:45] --- 处理第83行 ---
[2025-06-29 07:08:45] 第83行: 空行，跳过
[2025-06-29 07:08:45] --- 处理第84行 ---
[2025-06-29 07:08:45] 第84行: 空行，跳过
[2025-06-29 07:08:45] --- 处理第85行 ---
[2025-06-29 07:08:45] 第85行: 空行，跳过
[2025-06-29 07:08:45] --- 处理第86行 ---
[2025-06-29 07:08:45] 第86行: 空行，跳过
[2025-06-29 07:08:45] --- 处理第87行 ---
[2025-06-29 07:08:45] 第87行: 空行，跳过
[2025-06-29 07:08:45] --- 处理第88行 ---
[2025-06-29 07:08:45] 第88行: 空行，跳过
[2025-06-29 07:08:45] --- 处理第89行 ---
[2025-06-29 07:08:45] 第89行: 空行，跳过
[2025-06-29 07:08:45] --- 处理第90行 ---
[2025-06-29 07:08:45] 第90行: 空行，跳过
[2025-06-29 07:08:45] --- 处理第91行 ---
[2025-06-29 07:08:45] 第91行: 空行，跳过
[2025-06-29 07:08:45] --- 处理第92行 ---
[2025-06-29 07:08:45] 第92行: 空行，跳过
[2025-06-29 07:08:45] --- 处理第93行 ---
[2025-06-29 07:08:45] 第93行: 空行，跳过
[2025-06-29 07:08:45] --- 处理第94行 ---
[2025-06-29 07:08:45] 第94行: 空行，跳过
[2025-06-29 07:08:45] --- 处理第95行 ---
[2025-06-29 07:08:45] 第95行: 空行，跳过
[2025-06-29 07:08:45] --- 处理第96行 ---
[2025-06-29 07:08:45] 第96行: 空行，跳过
[2025-06-29 07:08:45] --- 处理第97行 ---
[2025-06-29 07:08:45] 第97行: 空行，跳过
[2025-06-29 07:08:45] --- 处理第98行 ---
[2025-06-29 07:08:45] 第98行: 空行，跳过
[2025-06-29 07:08:45] --- 处理第99行 ---
[2025-06-29 07:08:45] 第99行: 空行，跳过
[2025-06-29 07:08:45] --- 处理第100行 ---
[2025-06-29 07:08:45] 第100行: 空行，跳过
[2025-06-29 07:08:45] === 明细数据处理完成 ===
[2025-06-29 07:08:45] 成功: 46, 失败: 1
[2025-06-29 07:26:16] === 开始处理明细数据 ===
[2025-06-29 07:26:16] 数据总行数: 100
[2025-06-29 07:26:16] 检查第6行第1列: '商品明细'
[2025-06-29 07:26:16] 找到明细标题行: 第6行
[2025-06-29 07:26:16] 明细数据从第7行开始处理
[2025-06-29 07:26:16] --- 处理第7行 ---
[2025-06-29 07:26:16] 第7行字段: 编码='商品编码', 名称='商品名称', 数量='下单数量'
[2025-06-29 07:26:16] 第7行: 开始导入明细
[2025-06-29 07:26:16] 第7行: 导入失败 - 第7行: 数量必须大于0（第9列）
[2025-06-29 07:26:16] --- 处理第8行 ---
[2025-06-29 07:26:16] 第8行字段: 编码='DBC1685', 名称='大白菜', 数量='40'
[2025-06-29 07:26:16] 第8行: 开始导入明细
[2025-06-29 07:26:16] 查找食材: 编码='DBC1685', 名称='大白菜'
[2025-06-29 07:26:16] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:26:16] 准备创建食材: {"code":"DBC1685","name":"\u5927\u767d\u83dc","specification":"","unit":"\u65a4","category_id":1,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u53f6\u83dc\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:26:16"}
[2025-06-29 07:26:16] ✅ 食材创建成功: ID=53
[2025-06-29 07:26:16] 第8行: 导入成功
[2025-06-29 07:26:16] --- 处理第9行 ---
[2025-06-29 07:26:16] 第9行字段: 编码='LHB1033', 名称='莲花白（平顶）', 数量='40'
[2025-06-29 07:26:16] 第9行: 开始导入明细
[2025-06-29 07:26:16] 查找食材: 编码='LHB1033', 名称='莲花白（平顶）'
[2025-06-29 07:26:16] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:26:16] 准备创建食材: {"code":"LHB1033","name":"\u83b2\u82b1\u767d\uff08\u5e73\u9876\uff09","specification":"","unit":"\u65a4","category_id":1,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u53f6\u83dc\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:26:16"}
[2025-06-29 07:26:16] ✅ 食材创建成功: ID=54
[2025-06-29 07:26:16] 第9行: 导入成功
[2025-06-29 07:26:16] --- 处理第10行 ---
[2025-06-29 07:26:16] 第10行字段: 编码='XLH1581', 名称='西兰花', 数量='40'
[2025-06-29 07:26:16] 第10行: 开始导入明细
[2025-06-29 07:26:16] 查找食材: 编码='XLH1581', 名称='西兰花'
[2025-06-29 07:26:16] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:26:16] 准备创建食材: {"code":"XLH1581","name":"\u897f\u5170\u82b1","specification":"","unit":"\u65a4","category_id":1,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u8304\u679c\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:26:16"}
[2025-06-29 07:26:16] ✅ 食材创建成功: ID=55
[2025-06-29 07:26:16] 第10行: 导入成功
[2025-06-29 07:26:16] --- 处理第11行 ---
[2025-06-29 07:26:16] 第11行字段: 编码='XHS1526', 名称='西红柿', 数量='60'
[2025-06-29 07:26:16] 第11行: 开始导入明细
[2025-06-29 07:26:16] 查找食材: 编码='XHS1526', 名称='西红柿'
[2025-06-29 07:26:16] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:26:16] 准备创建食材: {"code":"XHS1526","name":"\u897f\u7ea2\u67ff","specification":"","unit":"\u65a4","category_id":1,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u8304\u679c\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:26:16"}
[2025-06-29 07:26:16] ✅ 食材创建成功: ID=56
[2025-06-29 07:26:16] 第11行: 导入成功
[2025-06-29 07:26:16] --- 处理第12行 ---
[2025-06-29 07:26:16] 第12行字段: 编码='WDHG7779', 名称='外地黄瓜', 数量='60'
[2025-06-29 07:26:16] 第12行: 开始导入明细
[2025-06-29 07:26:16] 查找食材: 编码='WDHG7779', 名称='外地黄瓜'
[2025-06-29 07:26:16] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:26:16] 准备创建食材: {"code":"WDHG7779","name":"\u5916\u5730\u9ec4\u74dc","specification":"","unit":"\u65a4","category_id":1,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u8304\u679c\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:26:16"}
[2025-06-29 07:26:16] ✅ 食材创建成功: ID=57
[2025-06-29 07:26:16] 第12行: 导入成功
[2025-06-29 07:26:16] --- 处理第13行 ---
[2025-06-29 07:26:16] 第13行字段: 编码='XNGC2635', 名称='小南瓜（长）', 数量='40'
[2025-06-29 07:26:16] 第13行: 开始导入明细
[2025-06-29 07:26:16] 查找食材: 编码='XNGC2635', 名称='小南瓜（长）'
[2025-06-29 07:26:16] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:26:16] 准备创建食材: {"code":"XNGC2635","name":"\u5c0f\u5357\u74dc\uff08\u957f\uff09","specification":"","unit":"\u65a4","category_id":1,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u8304\u679c\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:26:16"}
[2025-06-29 07:26:16] ✅ 食材创建成功: ID=58
[2025-06-29 07:26:16] 第13行: 导入成功
[2025-06-29 07:26:16] --- 处理第14行 ---
[2025-06-29 07:26:16] 第14行字段: 编码='dasd12', 名称='黄玉米棒', 数量='50'
[2025-06-29 07:26:16] 第14行: 开始导入明细
[2025-06-29 07:26:16] 查找食材: 编码='dasd12', 名称='黄玉米棒'
[2025-06-29 07:26:16] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:26:16] 准备创建食材: {"code":"dasd12","name":"\u9ec4\u7389\u7c73\u68d2","specification":"","unit":"\u65a4","category_id":1,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u8304\u679c\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:26:16"}
[2025-06-29 07:26:16] ✅ 食材创建成功: ID=59
[2025-06-29 07:26:16] 第14行: 导入成功
[2025-06-29 07:26:16] --- 处理第15行 ---
[2025-06-29 07:26:16] 第15行字段: 编码='SDCK1461', 名称='莴笋', 数量='50'
[2025-06-29 07:26:16] 第15行: 开始导入明细
[2025-06-29 07:26:16] 查找食材: 编码='SDCK1461', 名称='莴笋'
[2025-06-29 07:26:16] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:26:16] 准备创建食材: {"code":"SDCK1461","name":"\u83b4\u7b0b","specification":"","unit":"\u65a4","category_id":1,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u6839\u830e\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:26:16"}
[2025-06-29 07:26:16] ✅ 食材创建成功: ID=60
[2025-06-29 07:26:16] 第15行: 导入成功
[2025-06-29 07:26:16] --- 处理第16行 ---
[2025-06-29 07:26:16] 第16行字段: 编码='HLBWD2265', 名称='胡萝卜（外地）', 数量='30'
[2025-06-29 07:26:16] 第16行: 开始导入明细
[2025-06-29 07:26:16] 查找食材: 编码='HLBWD2265', 名称='胡萝卜（外地）'
[2025-06-29 07:26:16] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:26:16] 准备创建食材: {"code":"HLBWD2265","name":"\u80e1\u841d\u535c\uff08\u5916\u5730\uff09","specification":"","unit":"\u65a4","category_id":1,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u6839\u830e\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:26:16"}
[2025-06-29 07:26:16] ✅ 食材创建成功: ID=61
[2025-06-29 07:26:16] 第16行: 导入成功
[2025-06-29 07:26:16] --- 处理第17行 ---
[2025-06-29 07:26:16] 第17行字段: 编码='BLBG9279', 名称='白萝卜', 数量='30'
[2025-06-29 07:26:16] 第17行: 开始导入明细
[2025-06-29 07:26:16] 查找食材: 编码='BLBG9279', 名称='白萝卜'
[2025-06-29 07:26:16] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:26:16] 准备创建食材: {"code":"BLBG9279","name":"\u767d\u841d\u535c","specification":"","unit":"\u65a4","category_id":1,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u6839\u830e\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:26:16"}
[2025-06-29 07:26:16] ✅ 食材创建成功: ID=62
[2025-06-29 07:26:17] 第17行: 导入成功
[2025-06-29 07:26:17] --- 处理第18行 ---
[2025-06-29 07:26:17] 第18行字段: 编码='QCLBDG1821', 名称='芹菜（绿）', 数量='25'
[2025-06-29 07:26:17] 第18行: 开始导入明细
[2025-06-29 07:26:17] 查找食材: 编码='QCLBDG1821', 名称='芹菜（绿）'
[2025-06-29 07:26:17] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:26:17] 准备创建食材: {"code":"QCLBDG1821","name":"\u82b9\u83dc\uff08\u7eff\uff09","specification":"","unit":"\u65a4","category_id":1,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u6839\u830e\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:26:17"}
[2025-06-29 07:26:17] ✅ 食材创建成功: ID=63
[2025-06-29 07:26:17] 第18行: 导入成功
[2025-06-29 07:26:17] --- 处理第19行 ---
[2025-06-29 07:26:17] 第19行字段: 编码='XC1624', 名称='香菜', 数量='3'
[2025-06-29 07:26:17] 第19行: 开始导入明细
[2025-06-29 07:26:17] 查找食材: 编码='XC1624', 名称='香菜'
[2025-06-29 07:26:17] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:26:17] 准备创建食材: {"code":"XC1624","name":"\u9999\u83dc","specification":"","unit":"\u65a4","category_id":1,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u8471\u59dc\u849c\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:26:17"}
[2025-06-29 07:26:17] ✅ 食材创建成功: ID=64
[2025-06-29 07:26:17] 第19行: 导入成功
[2025-06-29 07:26:17] --- 处理第20行 ---
[2025-06-29 07:26:17] 第20行字段: 编码='SMBT0731', 名称='蒜苗', 数量='15'
[2025-06-29 07:26:17] 第20行: 开始导入明细
[2025-06-29 07:26:17] 查找食材: 编码='SMBT0731', 名称='蒜苗'
[2025-06-29 07:26:17] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:26:17] 准备创建食材: {"code":"SMBT0731","name":"\u849c\u82d7","specification":"","unit":"\u65a4","category_id":1,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u8471\u59dc\u849c\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:26:17"}
[2025-06-29 07:26:17] ✅ 食材创建成功: ID=65
[2025-06-29 07:26:17] 第20行: 导入成功
[2025-06-29 07:26:17] --- 处理第21行 ---
[2025-06-29 07:26:17] 第21行字段: 编码='XCBDDFC7870', 名称='小葱', 数量='10'
[2025-06-29 07:26:17] 第21行: 开始导入明细
[2025-06-29 07:26:17] 查找食材: 编码='XCBDDFC7870', 名称='小葱'
[2025-06-29 07:26:17] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:26:17] 准备创建食材: {"code":"XCBDDFC7870","name":"\u5c0f\u8471","specification":"","unit":"\u65a4","category_id":1,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u8471\u59dc\u849c\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:26:17"}
[2025-06-29 07:26:17] ✅ 食材创建成功: ID=66
[2025-06-29 07:26:17] 第21行: 导入成功
[2025-06-29 07:26:17] --- 处理第22行 ---
[2025-06-29 07:26:17] 第22行字段: 编码='XJJ0650', 名称='小米椒（小尖椒）', 数量='2'
[2025-06-29 07:26:17] 第22行: 开始导入明细
[2025-06-29 07:26:17] 查找食材: 编码='XJJ0650', 名称='小米椒（小尖椒）'
[2025-06-29 07:26:17] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:26:17] 准备创建食材: {"code":"XJJ0650","name":"\u5c0f\u7c73\u6912\uff08\u5c0f\u5c16\u6912\uff09","specification":"","unit":"\u65a4","category_id":1,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u8fa3\u6912\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:26:17"}
[2025-06-29 07:26:17] ✅ 食材创建成功: ID=67
[2025-06-29 07:26:17] 第22行: 导入成功
[2025-06-29 07:26:17] --- 处理第23行 ---
[2025-06-29 07:26:17] 第23行字段: 编码='XJ0544', 名称='青线椒', 数量='50'
[2025-06-29 07:26:17] 第23行: 开始导入明细
[2025-06-29 07:26:17] 查找食材: 编码='XJ0544', 名称='青线椒'
[2025-06-29 07:26:17] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:26:17] 准备创建食材: {"code":"XJ0544","name":"\u9752\u7ebf\u6912","specification":"","unit":"\u65a4","category_id":1,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u8fa3\u6912\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:26:17"}
[2025-06-29 07:26:17] ✅ 食材创建成功: ID=68
[2025-06-29 07:26:17] 第23行: 导入成功
[2025-06-29 07:26:17] --- 处理第24行 ---
[2025-06-29 07:26:17] 第24行字段: 编码='DQJ0496', 名称='大青椒', 数量='20'
[2025-06-29 07:26:17] 第24行: 开始导入明细
[2025-06-29 07:26:17] 查找食材: 编码='DQJ0496', 名称='大青椒'
[2025-06-29 07:26:17] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:26:17] 准备创建食材: {"code":"DQJ0496","name":"\u5927\u9752\u6912","specification":"","unit":"\u65a4","category_id":1,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u8fa3\u6912\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:26:17"}
[2025-06-29 07:26:17] ✅ 食材创建成功: ID=69
[2025-06-29 07:26:17] 第24行: 导入成功
[2025-06-29 07:26:17] --- 处理第25行 ---
[2025-06-29 07:26:17] 第25行字段: 编码='DHJ6400', 名称='大红椒', 数量='20'
[2025-06-29 07:26:17] 第25行: 开始导入明细
[2025-06-29 07:26:17] 查找食材: 编码='DHJ6400', 名称='大红椒'
[2025-06-29 07:26:17] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:26:17] 准备创建食材: {"code":"DHJ6400","name":"\u5927\u7ea2\u6912","specification":"","unit":"\u65a4","category_id":1,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u8fa3\u6912\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:26:17"}
[2025-06-29 07:26:17] ✅ 食材创建成功: ID=70
[2025-06-29 07:26:17] 第25行: 导入成功
[2025-06-29 07:26:17] --- 处理第26行 ---
[2025-06-29 07:26:17] 第26行字段: 编码='ZG0206', 名称='猪肝', 数量='25'
[2025-06-29 07:26:17] 第26行: 开始导入明细
[2025-06-29 07:26:17] 查找食材: 编码='ZG0206', 名称='猪肝'
[2025-06-29 07:26:17] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:26:17] 准备创建食材: {"code":"ZG0206","name":"\u732a\u809d","specification":"","unit":"\u65a4","category_id":1,"brand":"\u8089\u79bd\u7c7b","origin":"\u732a\u8089\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:26:17"}
[2025-06-29 07:26:17] ✅ 食材创建成功: ID=71
[2025-06-29 07:26:17] 第26行: 导入成功
[2025-06-29 07:26:17] --- 处理第27行 ---
[2025-06-29 07:26:17] 第27行字段: 编码='WHR4545', 名称='猪五花肉', 数量='30'
[2025-06-29 07:26:17] 第27行: 开始导入明细
[2025-06-29 07:26:17] 查找食材: 编码='WHR4545', 名称='猪五花肉'
[2025-06-29 07:26:17] ✅ 通过编码找到食材: ID=4, 名称=猪五花肉
[2025-06-29 07:26:17] 第27行: 导入成功
[2025-06-29 07:26:17] --- 处理第28行 ---
[2025-06-29 07:26:17] 第28行字段: 编码='ZQTRQG4200', 名称='猪前后腿肉去骨', 数量='140'
[2025-06-29 07:26:17] 第28行: 开始导入明细
[2025-06-29 07:26:17] 查找食材: 编码='ZQTRQG4200', 名称='猪前后腿肉去骨'
[2025-06-29 07:26:17] ✅ 通过编码找到食材: ID=3, 名称=猪前后腿肉去骨
[2025-06-29 07:26:17] 第28行: 导入成功
[2025-06-29 07:26:17] --- 处理第29行 ---
[2025-06-29 07:26:17] 第29行字段: 编码='ZPGZP8811', 名称='猪排骨（杂排）', 数量='10'
[2025-06-29 07:26:17] 第29行: 开始导入明细
[2025-06-29 07:26:17] 查找食材: 编码='ZPGZP8811', 名称='猪排骨（杂排）'
[2025-06-29 07:26:17] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:26:17] 准备创建食材: {"code":"ZPGZP8811","name":"\u732a\u6392\u9aa8\uff08\u6742\u6392\uff09","specification":"","unit":"\u65a4","category_id":1,"brand":"\u8089\u79bd\u7c7b","origin":"\u732a\u8089\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:26:17"}
[2025-06-29 07:26:17] ✅ 食材创建成功: ID=72
[2025-06-29 07:26:17] 第29行: 导入成功
[2025-06-29 07:26:17] --- 处理第30行 ---
[2025-06-29 07:26:17] 第30行字段: 编码='SPBM00003294', 名称='牛腩', 数量='30'
[2025-06-29 07:26:17] 第30行: 开始导入明细
[2025-06-29 07:26:17] 查找食材: 编码='SPBM00003294', 名称='牛腩'
[2025-06-29 07:26:17] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:26:17] 准备创建食材: {"code":"SPBM00003294","name":"\u725b\u8169","specification":"","unit":"\u65a4","category_id":1,"brand":"\u8089\u79bd\u7c7b","origin":"\u725b\u7f8a\u8089\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:26:17"}
[2025-06-29 07:26:17] ✅ 食材创建成功: ID=73
[2025-06-29 07:26:17] 第30行: 导入成功
[2025-06-29 07:26:17] --- 处理第31行 ---
[2025-06-29 07:26:17] 第31行字段: 编码='WZSNR3317', 名称='牛肉', 数量='20'
[2025-06-29 07:26:17] 第31行: 开始导入明细
[2025-06-29 07:26:17] 查找食材: 编码='WZSNR3317', 名称='牛肉'
[2025-06-29 07:26:17] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:26:17] 准备创建食材: {"code":"WZSNR3317","name":"\u725b\u8089","specification":"","unit":"\u65a4","category_id":1,"brand":"\u8089\u79bd\u7c7b","origin":"\u725b\u7f8a\u8089\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:26:17"}
[2025-06-29 07:26:17] ✅ 食材创建成功: ID=74
[2025-06-29 07:26:17] 第31行: 导入成功
[2025-06-29 07:26:17] --- 处理第32行 ---
[2025-06-29 07:26:17] 第32行字段: 编码='SHJ8439', 名称='三黄鸡（白条、无内脏）', 数量='70'
[2025-06-29 07:26:17] 第32行: 开始导入明细
[2025-06-29 07:26:17] 查找食材: 编码='SHJ8439', 名称='三黄鸡（白条、无内脏）'
[2025-06-29 07:26:17] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:26:17] 准备创建食材: {"code":"SHJ8439","name":"\u4e09\u9ec4\u9e21\uff08\u767d\u6761\u3001\u65e0\u5185\u810f\uff09","specification":"","unit":"\u65a4","category_id":1,"brand":"\u8089\u79bd\u7c7b","origin":"\u9e21\u9e2d\u9e45\u5154\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:26:17"}
[2025-06-29 07:26:17] ✅ 食材创建成功: ID=75
[2025-06-29 07:26:17] 第32行: 导入成功
[2025-06-29 07:26:17] --- 处理第33行 ---
[2025-06-29 07:26:17] 第33行字段: 编码='HDY6675', 名称='黄豆芽', 数量='30'
[2025-06-29 07:26:17] 第33行: 开始导入明细
[2025-06-29 07:26:17] 查找食材: 编码='HDY6675', 名称='黄豆芽'
[2025-06-29 07:26:17] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:26:17] 准备创建食材: {"code":"HDY6675","name":"\u9ec4\u8c46\u82bd","specification":"","unit":"\u65a4","category_id":1,"brand":"\u7c89\u9762\u8c46\u5236\u54c1","origin":"\u8c46\u5236\u54c1","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:26:17"}
[2025-06-29 07:26:17] ✅ 食材创建成功: ID=76
[2025-06-29 07:26:17] 第33行: 导入成功
[2025-06-29 07:26:17] --- 处理第34行 ---
[2025-06-29 07:26:17] 第34行字段: 编码='SDF2528', 名称='水豆腐', 数量='5'
[2025-06-29 07:26:17] 第34行: 开始导入明细
[2025-06-29 07:26:17] 查找食材: 编码='SDF2528', 名称='水豆腐'
[2025-06-29 07:26:17] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:26:17] 准备创建食材: {"code":"SDF2528","name":"\u6c34\u8c46\u8150","specification":"","unit":"\u677f","category_id":1,"brand":"\u7c89\u9762\u8c46\u5236\u54c1","origin":"\u8c46\u5236\u54c1","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:26:17"}
[2025-06-29 07:26:17] ✅ 食材创建成功: ID=77
[2025-06-29 07:26:18] 第34行: 导入成功
[2025-06-29 07:26:18] --- 处理第35行 ---
[2025-06-29 07:26:18] 第35行字段: 编码='GDFP4973', 名称='干豆腐片（薄）', 数量='15'
[2025-06-29 07:26:18] 第35行: 开始导入明细
[2025-06-29 07:26:18] 查找食材: 编码='GDFP4973', 名称='干豆腐片（薄）'
[2025-06-29 07:26:18] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:26:18] 准备创建食材: {"code":"GDFP4973","name":"\u5e72\u8c46\u8150\u7247\uff08\u8584\uff09","specification":"","unit":"\u65a4","category_id":1,"brand":"\u7c89\u9762\u8c46\u5236\u54c1","origin":"\u8c46\u5236\u54c1","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:26:18"}
[2025-06-29 07:26:18] ✅ 食材创建成功: ID=78
[2025-06-29 07:26:18] 第35行: 导入成功
[2025-06-29 07:26:18] --- 处理第36行 ---
[2025-06-29 07:26:18] 第36行字段: 编码='GDFHP1223', 名称='干豆腐片（厚）', 数量='40'
[2025-06-29 07:26:18] 第36行: 开始导入明细
[2025-06-29 07:26:18] 查找食材: 编码='GDFHP1223', 名称='干豆腐片（厚）'
[2025-06-29 07:26:18] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:26:18] 准备创建食材: {"code":"GDFHP1223","name":"\u5e72\u8c46\u8150\u7247\uff08\u539a\uff09","specification":"","unit":"\u65a4","category_id":1,"brand":"\u7c89\u9762\u8c46\u5236\u54c1","origin":"\u8c46\u5236\u54c1","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:26:18"}
[2025-06-29 07:26:18] ✅ 食材创建成功: ID=79
[2025-06-29 07:26:18] 第36行: 导入成功
[2025-06-29 07:26:18] --- 处理第37行 ---
[2025-06-29 07:26:18] 第37行字段: 编码='YF4729', 名称='圆粉', 数量='200'
[2025-06-29 07:26:18] 第37行: 开始导入明细
[2025-06-29 07:26:18] 查找食材: 编码='YF4729', 名称='圆粉'
[2025-06-29 07:26:18] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:26:18] 准备创建食材: {"code":"YF4729","name":"\u5706\u7c89","specification":"","unit":"\u65a4","category_id":1,"brand":"\u7c89\u9762\u8c46\u5236\u54c1","origin":"\u7c89\u9762\u5236\u54c1","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:26:18"}
[2025-06-29 07:26:18] ✅ 食材创建成功: ID=80
[2025-06-29 07:26:18] 第37行: 导入成功
[2025-06-29 07:26:18] --- 处理第38行 ---
[2025-06-29 07:26:18] 第38行字段: 编码='BF4586', 名称='扁粉', 数量='350'
[2025-06-29 07:26:18] 第38行: 开始导入明细
[2025-06-29 07:26:18] 查找食材: 编码='BF4586', 名称='扁粉'
[2025-06-29 07:26:18] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:26:18] 准备创建食材: {"code":"BF4586","name":"\u6241\u7c89","specification":"","unit":"\u65a4","category_id":1,"brand":"\u7c89\u9762\u8c46\u5236\u54c1","origin":"\u7c89\u9762\u5236\u54c1","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:26:18"}
[2025-06-29 07:26:18] ✅ 食材创建成功: ID=81
[2025-06-29 07:26:18] 第38行: 导入成功
[2025-06-29 07:26:18] --- 处理第39行 ---
[2025-06-29 07:26:18] 第39行字段: 编码='YF5478', 名称='油粉', 数量='30'
[2025-06-29 07:26:18] 第39行: 开始导入明细
[2025-06-29 07:26:18] 查找食材: 编码='YF5478', 名称='油粉'
[2025-06-29 07:26:18] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:26:18] 准备创建食材: {"code":"YF5478","name":"\u6cb9\u7c89","specification":"","unit":"\u65a4","category_id":1,"brand":"\u7c89\u9762\u8c46\u5236\u54c1","origin":"\u7c89\u9762\u5236\u54c1","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:26:18"}
[2025-06-29 07:26:18] ✅ 食材创建成功: ID=82
[2025-06-29 07:26:18] 第39行: 导入成功
[2025-06-29 07:26:18] --- 处理第40行 ---
[2025-06-29 07:26:18] 第40行字段: 编码='BF5568', 名称='薄粉（卷粉皮）', 数量='15'
[2025-06-29 07:26:18] 第40行: 开始导入明细
[2025-06-29 07:26:18] 查找食材: 编码='BF5568', 名称='薄粉（卷粉皮）'
[2025-06-29 07:26:18] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:26:18] 准备创建食材: {"code":"BF5568","name":"\u8584\u7c89\uff08\u5377\u7c89\u76ae\uff09","specification":"","unit":"\u65a4","category_id":1,"brand":"\u7c89\u9762\u8c46\u5236\u54c1","origin":"\u7c89\u9762\u5236\u54c1","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:26:18"}
[2025-06-29 07:26:18] ✅ 食材创建成功: ID=83
[2025-06-29 07:26:18] 第40行: 导入成功
[2025-06-29 07:26:18] --- 处理第41行 ---
[2025-06-29 07:26:18] 第41行字段: 编码='QTXW2925', 名称='秋田小町25KG', 数量='10'
[2025-06-29 07:26:18] 第41行: 开始导入明细
[2025-06-29 07:26:18] 查找食材: 编码='QTXW2925', 名称='秋田小町25KG'
[2025-06-29 07:26:18] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:26:18] 准备创建食材: {"code":"QTXW2925","name":"\u79cb\u7530\u5c0f\u753a25KG","specification":"25KG","unit":"\u888b","category_id":1,"brand":"\u7c73\u9762\u7cae\u6cb9","origin":"\u5927\u7c73\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:26:18"}
[2025-06-29 07:26:18] ✅ 食材创建成功: ID=84
[2025-06-29 07:26:18] 第41行: 导入成功
[2025-06-29 07:26:18] --- 处理第42行 ---
[2025-06-29 07:26:18] 第42行字段: 编码='GLHLJM1006', 名称='冠霖糊辣椒面', 数量='2'
[2025-06-29 07:26:18] 第42行: 开始导入明细
[2025-06-29 07:26:18] 查找食材: 编码='GLHLJM1006', 名称='冠霖糊辣椒面'
[2025-06-29 07:26:18] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:26:18] 准备创建食材: {"code":"GLHLJM1006","name":"\u51a0\u9716\u7cca\u8fa3\u6912\u9762","specification":"5\u65a4\/\u888b","unit":"\u888b","category_id":1,"brand":"\u8c03\u6599\u5e72\u8d27","origin":"\u5e72\u8fa3\u6912\u82b1\u6912\u9762\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:26:18"}
[2025-06-29 07:26:18] ✅ 食材创建成功: ID=85
[2025-06-29 07:26:18] 第42行: 导入成功
[2025-06-29 07:26:18] --- 处理第43行 ---
[2025-06-29 07:26:18] 第43行字段: 编码='DFP8053', 名称='干豆腐皮', 数量='1'
[2025-06-29 07:26:18] 第43行: 开始导入明细
[2025-06-29 07:26:18] 查找食材: 编码='DFP8053', 名称='干豆腐皮'
[2025-06-29 07:26:18] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:26:18] 准备创建食材: {"code":"DFP8053","name":"\u5e72\u8c46\u8150\u76ae","specification":"\u7ea612.5\u65a4","unit":"\u4ef6","category_id":1,"brand":"\u8c03\u6599\u5e72\u8d27","origin":"\u5e72\u8d27\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:26:18"}
[2025-06-29 07:26:18] ✅ 食材创建成功: ID=86
[2025-06-29 07:26:18] 第43行: 导入成功
[2025-06-29 07:26:18] --- 处理第44行 ---
[2025-06-29 07:26:18] 第44行字段: 编码='CS7213', 名称='脆哨', 数量='10'
[2025-06-29 07:26:18] 第44行: 开始导入明细
[2025-06-29 07:26:18] 查找食材: 编码='CS7213', 名称='脆哨'
[2025-06-29 07:26:18] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:26:18] 准备创建食材: {"code":"CS7213","name":"\u8106\u54e8","specification":"","unit":"\u65a4","category_id":1,"brand":"\u8c03\u6599\u5e72\u8d27","origin":"\u5e72\u8d27\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:26:18"}
[2025-06-29 07:26:18] ✅ 食材创建成功: ID=87
[2025-06-29 07:26:18] 第44行: 导入成功
[2025-06-29 07:26:18] --- 处理第45行 ---
[2025-06-29 07:26:18] 第45行字段: 编码='XJ1638', 名称='香蕉', 数量='30'
[2025-06-29 07:26:18] 第45行: 开始导入明细
[2025-06-29 07:26:18] 查找食材: 编码='XJ1638', 名称='香蕉'
[2025-06-29 07:26:18] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:26:18] 准备创建食材: {"code":"XJ1638","name":"\u9999\u8549","specification":"","unit":"\u65a4","category_id":1,"brand":"\u65f6\u4ee4\u6c34\u679c","origin":"\u65f6\u4ee4\u6c34\u679c1","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:26:18"}
[2025-06-29 07:26:18] ✅ 食材创建成功: ID=88
[2025-06-29 07:26:18] 第45行: 导入成功
[2025-06-29 07:26:18] --- 处理第46行 ---
[2025-06-29 07:26:18] 第46行字段: 编码='HWZTX7875', 名称='茉莉香提', 数量='20'
[2025-06-29 07:26:18] 第46行: 开始导入明细
[2025-06-29 07:26:18] 查找食材: 编码='HWZTX7875', 名称='茉莉香提'
[2025-06-29 07:26:18] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:26:18] 准备创建食材: {"code":"HWZTX7875","name":"\u8309\u8389\u9999\u63d0","specification":"","unit":"\u65a4","category_id":1,"brand":"\u65f6\u4ee4\u6c34\u679c","origin":"\u65f6\u4ee4\u6c34\u679c1","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:26:18"}
[2025-06-29 07:26:18] ✅ 食材创建成功: ID=89
[2025-06-29 07:26:18] 第46行: 导入成功
[2025-06-29 07:26:18] --- 处理第47行 ---
[2025-06-29 07:26:18] 第47行字段: 编码='HMGXMH7349', 名称='哈密瓜（小蜜25号）大果', 数量='50'
[2025-06-29 07:26:18] 第47行: 开始导入明细
[2025-06-29 07:26:18] 查找食材: 编码='HMGXMH7349', 名称='哈密瓜（小蜜25号）大果'
[2025-06-29 07:26:18] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:26:18] 准备创建食材: {"code":"HMGXMH7349","name":"\u54c8\u5bc6\u74dc\uff08\u5c0f\u871c25\u53f7\uff09\u5927\u679c","specification":"\u6c99\u5730\u871c\u74dc\u5927\u679c","unit":"\u65a4","category_id":1,"brand":"\u65f6\u4ee4\u6c34\u679c","origin":"\u65f6\u4ee4\u6c34\u679c1","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:26:18"}
[2025-06-29 07:26:18] ✅ 食材创建成功: ID=90
[2025-06-29 07:26:18] 第47行: 导入成功
[2025-06-29 07:26:18] --- 处理第48行 ---
[2025-06-29 07:26:18] 第48行字段: 编码='XD6079', 名称='豇豆（绿）', 数量='40'
[2025-06-29 07:26:18] 第48行: 开始导入明细
[2025-06-29 07:26:18] 查找食材: 编码='XD6079', 名称='豇豆（绿）'
[2025-06-29 07:26:18] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:26:18] 准备创建食材: {"code":"XD6079","name":"\u8c47\u8c46\uff08\u7eff\uff09","specification":"","unit":"\u65a4","category_id":1,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u8304\u679c\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:26:18"}
[2025-06-29 07:26:18] ✅ 食材创建成功: ID=91
[2025-06-29 07:26:18] 第48行: 导入成功
[2025-06-29 07:26:18] --- 处理第49行 ---
[2025-06-29 07:26:18] 第49行字段: 编码='XTD9873', 名称='土豆', 数量='250'
[2025-06-29 07:26:18] 第49行: 开始导入明细
[2025-06-29 07:26:18] 查找食材: 编码='XTD9873', 名称='土豆'
[2025-06-29 07:26:18] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:26:18] 准备创建食材: {"code":"XTD9873","name":"\u571f\u8c46","specification":"","unit":"\u65a4","category_id":1,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u6839\u830e\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:26:18"}
[2025-06-29 07:26:18] ✅ 食材创建成功: ID=92
[2025-06-29 07:26:18] 第49行: 导入成功
[2025-06-29 07:26:18] --- 处理第50行 ---
[2025-06-29 07:26:18] 第50行字段: 编码='LJ0368', 名称='老姜（带土）', 数量='20'
[2025-06-29 07:26:18] 第50行: 开始导入明细
[2025-06-29 07:26:18] 查找食材: 编码='LJ0368', 名称='老姜（带土）'
[2025-06-29 07:26:18] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:26:19] 准备创建食材: {"code":"LJ0368","name":"\u8001\u59dc\uff08\u5e26\u571f\uff09","specification":"","unit":"\u65a4","category_id":1,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u8471\u59dc\u849c\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:26:19"}
[2025-06-29 07:26:19] ✅ 食材创建成功: ID=93
[2025-06-29 07:26:19] 第50行: 导入成功
[2025-06-29 07:26:19] --- 处理第51行 ---
[2025-06-29 07:26:19] 第51行字段: 编码='SM0321', 名称='蒜米', 数量='2'
[2025-06-29 07:26:19] 第51行: 开始导入明细
[2025-06-29 07:26:19] 查找食材: 编码='SM0321', 名称='蒜米'
[2025-06-29 07:26:19] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:26:19] 准备创建食材: {"code":"SM0321","name":"\u849c\u7c73","specification":"","unit":"\u888b","category_id":1,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u8471\u59dc\u849c\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:26:19"}
[2025-06-29 07:26:19] ✅ 食材创建成功: ID=94
[2025-06-29 07:26:19] 第51行: 导入成功
[2025-06-29 07:26:19] --- 处理第52行 ---
[2025-06-29 07:26:19] 第52行字段: 编码='SPBM00003264', 名称='猪里脊肉', 数量='30'
[2025-06-29 07:26:19] 第52行: 开始导入明细
[2025-06-29 07:26:19] 查找食材: 编码='SPBM00003264', 名称='猪里脊肉'
[2025-06-29 07:26:19] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:26:19] 准备创建食材: {"code":"SPBM00003264","name":"\u732a\u91cc\u810a\u8089","specification":"","unit":"\u65a4","category_id":1,"brand":"\u8089\u79bd\u7c7b","origin":"\u732a\u8089\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:26:19"}
[2025-06-29 07:26:19] ✅ 食材创建成功: ID=95
[2025-06-29 07:26:19] 第52行: 导入成功
[2025-06-29 07:26:19] --- 处理第53行 ---
[2025-06-29 07:26:19] 第53行字段: 编码='HYML4819', 名称='黄玉米粒（新鲜）', 数量='30'
[2025-06-29 07:26:19] 第53行: 开始导入明细
[2025-06-29 07:26:19] 查找食材: 编码='HYML4819', 名称='黄玉米粒（新鲜）'
[2025-06-29 07:26:19] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:26:19] 准备创建食材: {"code":"HYML4819","name":"\u9ec4\u7389\u7c73\u7c92\uff08\u65b0\u9c9c\uff09","specification":"","unit":"\u65a4","category_id":1,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u8304\u679c\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:26:19"}
[2025-06-29 07:26:19] ✅ 食材创建成功: ID=96
[2025-06-29 07:26:19] 第53行: 导入成功
[2025-06-29 07:26:19] --- 处理第54行 ---
[2025-06-29 07:26:19] 第54行字段: 编码='', 名称='', 数量=''
[2025-06-29 07:26:19] 第54行: 关键字段为空，跳过
[2025-06-29 07:26:19] --- 处理第55行 ---
[2025-06-29 07:26:19] 第55行: 空行，跳过
[2025-06-29 07:26:19] --- 处理第56行 ---
[2025-06-29 07:26:19] 第56行: 空行，跳过
[2025-06-29 07:26:19] --- 处理第57行 ---
[2025-06-29 07:26:19] 第57行: 空行，跳过
[2025-06-29 07:26:19] --- 处理第58行 ---
[2025-06-29 07:26:19] 第58行: 空行，跳过
[2025-06-29 07:26:19] --- 处理第59行 ---
[2025-06-29 07:26:19] 第59行: 空行，跳过
[2025-06-29 07:26:19] --- 处理第60行 ---
[2025-06-29 07:26:19] 第60行: 空行，跳过
[2025-06-29 07:26:19] --- 处理第61行 ---
[2025-06-29 07:26:19] 第61行: 空行，跳过
[2025-06-29 07:26:19] --- 处理第62行 ---
[2025-06-29 07:26:19] 第62行: 空行，跳过
[2025-06-29 07:26:19] --- 处理第63行 ---
[2025-06-29 07:26:19] 第63行: 空行，跳过
[2025-06-29 07:26:19] --- 处理第64行 ---
[2025-06-29 07:26:19] 第64行: 空行，跳过
[2025-06-29 07:26:19] --- 处理第65行 ---
[2025-06-29 07:26:19] 第65行: 空行，跳过
[2025-06-29 07:26:19] --- 处理第66行 ---
[2025-06-29 07:26:19] 第66行: 空行，跳过
[2025-06-29 07:26:19] --- 处理第67行 ---
[2025-06-29 07:26:19] 第67行: 空行，跳过
[2025-06-29 07:26:19] --- 处理第68行 ---
[2025-06-29 07:26:19] 第68行: 空行，跳过
[2025-06-29 07:26:19] --- 处理第69行 ---
[2025-06-29 07:26:19] 第69行: 空行，跳过
[2025-06-29 07:26:19] --- 处理第70行 ---
[2025-06-29 07:26:19] 第70行: 空行，跳过
[2025-06-29 07:26:19] --- 处理第71行 ---
[2025-06-29 07:26:19] 第71行: 空行，跳过
[2025-06-29 07:26:19] --- 处理第72行 ---
[2025-06-29 07:26:19] 第72行: 空行，跳过
[2025-06-29 07:26:19] --- 处理第73行 ---
[2025-06-29 07:26:19] 第73行: 空行，跳过
[2025-06-29 07:26:19] --- 处理第74行 ---
[2025-06-29 07:26:19] 第74行: 空行，跳过
[2025-06-29 07:26:19] --- 处理第75行 ---
[2025-06-29 07:26:19] 第75行: 空行，跳过
[2025-06-29 07:26:19] --- 处理第76行 ---
[2025-06-29 07:26:19] 第76行: 空行，跳过
[2025-06-29 07:26:19] --- 处理第77行 ---
[2025-06-29 07:26:19] 第77行: 空行，跳过
[2025-06-29 07:26:19] --- 处理第78行 ---
[2025-06-29 07:26:19] 第78行: 空行，跳过
[2025-06-29 07:26:19] --- 处理第79行 ---
[2025-06-29 07:26:19] 第79行: 空行，跳过
[2025-06-29 07:26:19] --- 处理第80行 ---
[2025-06-29 07:26:19] 第80行: 空行，跳过
[2025-06-29 07:26:19] --- 处理第81行 ---
[2025-06-29 07:26:19] 第81行: 空行，跳过
[2025-06-29 07:26:19] --- 处理第82行 ---
[2025-06-29 07:26:19] 第82行: 空行，跳过
[2025-06-29 07:26:19] --- 处理第83行 ---
[2025-06-29 07:26:19] 第83行: 空行，跳过
[2025-06-29 07:26:19] --- 处理第84行 ---
[2025-06-29 07:26:19] 第84行: 空行，跳过
[2025-06-29 07:26:19] --- 处理第85行 ---
[2025-06-29 07:26:19] 第85行: 空行，跳过
[2025-06-29 07:26:19] --- 处理第86行 ---
[2025-06-29 07:26:19] 第86行: 空行，跳过
[2025-06-29 07:26:19] --- 处理第87行 ---
[2025-06-29 07:26:19] 第87行: 空行，跳过
[2025-06-29 07:26:19] --- 处理第88行 ---
[2025-06-29 07:26:19] 第88行: 空行，跳过
[2025-06-29 07:26:19] --- 处理第89行 ---
[2025-06-29 07:26:19] 第89行: 空行，跳过
[2025-06-29 07:26:19] --- 处理第90行 ---
[2025-06-29 07:26:19] 第90行: 空行，跳过
[2025-06-29 07:26:19] --- 处理第91行 ---
[2025-06-29 07:26:19] 第91行: 空行，跳过
[2025-06-29 07:26:19] --- 处理第92行 ---
[2025-06-29 07:26:19] 第92行: 空行，跳过
[2025-06-29 07:26:19] --- 处理第93行 ---
[2025-06-29 07:26:19] 第93行: 空行，跳过
[2025-06-29 07:26:19] --- 处理第94行 ---
[2025-06-29 07:26:19] 第94行: 空行，跳过
[2025-06-29 07:26:19] --- 处理第95行 ---
[2025-06-29 07:26:19] 第95行: 空行，跳过
[2025-06-29 07:26:19] --- 处理第96行 ---
[2025-06-29 07:26:19] 第96行: 空行，跳过
[2025-06-29 07:26:19] --- 处理第97行 ---
[2025-06-29 07:26:19] 第97行: 空行，跳过
[2025-06-29 07:26:19] --- 处理第98行 ---
[2025-06-29 07:26:19] 第98行: 空行，跳过
[2025-06-29 07:26:19] --- 处理第99行 ---
[2025-06-29 07:26:19] 第99行: 空行，跳过
[2025-06-29 07:26:19] --- 处理第100行 ---
[2025-06-29 07:26:19] 第100行: 空行，跳过
[2025-06-29 07:26:19] === 明细数据处理完成 ===
[2025-06-29 07:26:19] 成功: 46, 失败: 1
[2025-06-29 07:34:11] 查找分类: 名称='蔬菜类'
[2025-06-29 07:34:11] ⚠️ 分类不存在，开始自动创建: '蔬菜类'
[2025-06-29 07:34:11] ❌ 处理分类失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'code' doesn't have a default value，使用默认分类ID=1
[2025-06-29 07:34:11] 查找分类: 名称='新分类测试'
[2025-06-29 07:34:11] ⚠️ 分类不存在，开始自动创建: '新分类测试'
[2025-06-29 07:34:11] ❌ 处理分类失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'code' doesn't have a default value，使用默认分类ID=1
[2025-06-29 07:34:11] 分类名称为空，使用默认分类ID=1
[2025-06-29 07:34:11] 查找分类: 名称='肉类'
[2025-06-29 07:34:11] ⚠️ 分类不存在，开始自动创建: '肉类'
[2025-06-29 07:34:11] ❌ 处理分类失败: 查询执行失败: SQLSTATE[HY000]: General error: 1364 Field 'code' doesn't have a default value，使用默认分类ID=1
[2025-06-29 07:38:38] 查找分类: 名称='蔬菜类'
[2025-06-29 07:38:38] ⚠️ 分类不存在，开始自动创建: '蔬菜类'
[2025-06-29 07:38:38] ✅ 分类创建成功: ID=50, 名称='蔬菜类'
[2025-06-29 07:38:38] 查找分类: 名称='新分类测试'
[2025-06-29 07:38:38] ⚠️ 分类不存在，开始自动创建: '新分类测试'
[2025-06-29 07:38:38] ✅ 分类创建成功: ID=51, 名称='新分类测试'
[2025-06-29 07:38:38] 分类名称为空，使用默认分类ID=1
[2025-06-29 07:38:38] 查找分类: 名称='肉类'
[2025-06-29 07:38:38] ⚠️ 分类不存在，开始自动创建: '肉类'
[2025-06-29 07:38:38] ✅ 分类创建成功: ID=52, 名称='肉类'
[2025-06-29 07:40:27] === 开始处理明细数据 ===
[2025-06-29 07:40:27] 数据总行数: 100
[2025-06-29 07:40:27] 检查第6行第1列: '商品明细'
[2025-06-29 07:40:27] 找到明细标题行: 第6行
[2025-06-29 07:40:27] 明细数据从第7行开始处理
[2025-06-29 07:40:27] --- 处理第7行 ---
[2025-06-29 07:40:27] 第7行字段: 编码='商品编码', 名称='商品名称', 数量='下单数量'
[2025-06-29 07:40:27] 第7行: 开始导入明细
[2025-06-29 07:40:27] 第7行: 导入失败 - 第7行: 数量必须大于0（第9列）
[2025-06-29 07:40:27] --- 处理第8行 ---
[2025-06-29 07:40:27] 第8行字段: 编码='DBC1685', 名称='大白菜', 数量='40'
[2025-06-29 07:40:27] 第8行: 开始导入明细
[2025-06-29 07:40:27] 查找食材: 编码='DBC1685', 名称='大白菜'
[2025-06-29 07:40:27] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:40:27] 查找分类: 名称='叶菜类'
[2025-06-29 07:40:27] ✅ 找到分类: ID=8, 名称='叶菜类'
[2025-06-29 07:40:27] 准备创建食材: {"code":"DBC1685","name":"\u5927\u767d\u83dc","specification":"","unit":"\u65a4","category_id":8,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u53f6\u83dc\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:40:27"}
[2025-06-29 07:40:27] ✅ 食材创建成功: ID=97
[2025-06-29 07:40:27] 第8行: 导入成功
[2025-06-29 07:40:27] --- 处理第9行 ---
[2025-06-29 07:40:27] 第9行字段: 编码='LHB1033', 名称='莲花白（平顶）', 数量='40'
[2025-06-29 07:40:27] 第9行: 开始导入明细
[2025-06-29 07:40:27] 查找食材: 编码='LHB1033', 名称='莲花白（平顶）'
[2025-06-29 07:40:27] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:40:27] 查找分类: 名称='叶菜类'
[2025-06-29 07:40:27] ✅ 找到分类: ID=8, 名称='叶菜类'
[2025-06-29 07:40:27] 准备创建食材: {"code":"LHB1033","name":"\u83b2\u82b1\u767d\uff08\u5e73\u9876\uff09","specification":"","unit":"\u65a4","category_id":8,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u53f6\u83dc\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:40:27"}
[2025-06-29 07:40:27] ✅ 食材创建成功: ID=98
[2025-06-29 07:40:27] 第9行: 导入成功
[2025-06-29 07:40:27] --- 处理第10行 ---
[2025-06-29 07:40:27] 第10行字段: 编码='XLH1581', 名称='西兰花', 数量='40'
[2025-06-29 07:40:27] 第10行: 开始导入明细
[2025-06-29 07:40:27] 查找食材: 编码='XLH1581', 名称='西兰花'
[2025-06-29 07:40:27] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:40:27] 查找分类: 名称='茄果类'
[2025-06-29 07:40:27] ✅ 找到分类: ID=9, 名称='茄果类'
[2025-06-29 07:40:27] 准备创建食材: {"code":"XLH1581","name":"\u897f\u5170\u82b1","specification":"","unit":"\u65a4","category_id":9,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u8304\u679c\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:40:27"}
[2025-06-29 07:40:27] ✅ 食材创建成功: ID=99
[2025-06-29 07:40:27] 第10行: 导入成功
[2025-06-29 07:40:27] --- 处理第11行 ---
[2025-06-29 07:40:27] 第11行字段: 编码='XHS1526', 名称='西红柿', 数量='60'
[2025-06-29 07:40:27] 第11行: 开始导入明细
[2025-06-29 07:40:27] 查找食材: 编码='XHS1526', 名称='西红柿'
[2025-06-29 07:40:27] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:40:27] 查找分类: 名称='茄果类'
[2025-06-29 07:40:27] ✅ 找到分类: ID=9, 名称='茄果类'
[2025-06-29 07:40:27] 准备创建食材: {"code":"XHS1526","name":"\u897f\u7ea2\u67ff","specification":"","unit":"\u65a4","category_id":9,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u8304\u679c\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:40:27"}
[2025-06-29 07:40:27] ✅ 食材创建成功: ID=100
[2025-06-29 07:40:27] 第11行: 导入成功
[2025-06-29 07:40:27] --- 处理第12行 ---
[2025-06-29 07:40:27] 第12行字段: 编码='WDHG7779', 名称='外地黄瓜', 数量='60'
[2025-06-29 07:40:27] 第12行: 开始导入明细
[2025-06-29 07:40:27] 查找食材: 编码='WDHG7779', 名称='外地黄瓜'
[2025-06-29 07:40:27] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:40:27] 查找分类: 名称='茄果类'
[2025-06-29 07:40:27] ✅ 找到分类: ID=9, 名称='茄果类'
[2025-06-29 07:40:27] 准备创建食材: {"code":"WDHG7779","name":"\u5916\u5730\u9ec4\u74dc","specification":"","unit":"\u65a4","category_id":9,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u8304\u679c\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:40:27"}
[2025-06-29 07:40:27] ✅ 食材创建成功: ID=101
[2025-06-29 07:40:27] 第12行: 导入成功
[2025-06-29 07:40:27] --- 处理第13行 ---
[2025-06-29 07:40:27] 第13行字段: 编码='XNGC2635', 名称='小南瓜（长）', 数量='40'
[2025-06-29 07:40:27] 第13行: 开始导入明细
[2025-06-29 07:40:27] 查找食材: 编码='XNGC2635', 名称='小南瓜（长）'
[2025-06-29 07:40:27] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:40:27] 查找分类: 名称='茄果类'
[2025-06-29 07:40:28] ✅ 找到分类: ID=9, 名称='茄果类'
[2025-06-29 07:40:28] 准备创建食材: {"code":"XNGC2635","name":"\u5c0f\u5357\u74dc\uff08\u957f\uff09","specification":"","unit":"\u65a4","category_id":9,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u8304\u679c\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:40:28"}
[2025-06-29 07:40:28] ✅ 食材创建成功: ID=102
[2025-06-29 07:40:28] 第13行: 导入成功
[2025-06-29 07:40:28] --- 处理第14行 ---
[2025-06-29 07:40:28] 第14行字段: 编码='dasd12', 名称='黄玉米棒', 数量='50'
[2025-06-29 07:40:28] 第14行: 开始导入明细
[2025-06-29 07:40:28] 查找食材: 编码='dasd12', 名称='黄玉米棒'
[2025-06-29 07:40:28] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:40:28] 查找分类: 名称='茄果类'
[2025-06-29 07:40:28] ✅ 找到分类: ID=9, 名称='茄果类'
[2025-06-29 07:40:28] 准备创建食材: {"code":"dasd12","name":"\u9ec4\u7389\u7c73\u68d2","specification":"","unit":"\u65a4","category_id":9,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u8304\u679c\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:40:28"}
[2025-06-29 07:40:28] ✅ 食材创建成功: ID=103
[2025-06-29 07:40:28] 第14行: 导入成功
[2025-06-29 07:40:28] --- 处理第15行 ---
[2025-06-29 07:40:28] 第15行字段: 编码='SDCK1461', 名称='莴笋', 数量='50'
[2025-06-29 07:40:28] 第15行: 开始导入明细
[2025-06-29 07:40:28] 查找食材: 编码='SDCK1461', 名称='莴笋'
[2025-06-29 07:40:28] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:40:28] 查找分类: 名称='根茎类'
[2025-06-29 07:40:28] ✅ 找到分类: ID=10, 名称='根茎类'
[2025-06-29 07:40:28] 准备创建食材: {"code":"SDCK1461","name":"\u83b4\u7b0b","specification":"","unit":"\u65a4","category_id":10,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u6839\u830e\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:40:28"}
[2025-06-29 07:40:28] ✅ 食材创建成功: ID=104
[2025-06-29 07:40:28] 第15行: 导入成功
[2025-06-29 07:40:28] --- 处理第16行 ---
[2025-06-29 07:40:28] 第16行字段: 编码='HLBWD2265', 名称='胡萝卜（外地）', 数量='30'
[2025-06-29 07:40:28] 第16行: 开始导入明细
[2025-06-29 07:40:28] 查找食材: 编码='HLBWD2265', 名称='胡萝卜（外地）'
[2025-06-29 07:40:28] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:40:28] 查找分类: 名称='根茎类'
[2025-06-29 07:40:28] ✅ 找到分类: ID=10, 名称='根茎类'
[2025-06-29 07:40:28] 准备创建食材: {"code":"HLBWD2265","name":"\u80e1\u841d\u535c\uff08\u5916\u5730\uff09","specification":"","unit":"\u65a4","category_id":10,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u6839\u830e\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:40:28"}
[2025-06-29 07:40:28] ✅ 食材创建成功: ID=105
[2025-06-29 07:40:28] 第16行: 导入成功
[2025-06-29 07:40:28] --- 处理第17行 ---
[2025-06-29 07:40:28] 第17行字段: 编码='BLBG9279', 名称='白萝卜', 数量='30'
[2025-06-29 07:40:28] 第17行: 开始导入明细
[2025-06-29 07:40:28] 查找食材: 编码='BLBG9279', 名称='白萝卜'
[2025-06-29 07:40:28] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:40:28] 查找分类: 名称='根茎类'
[2025-06-29 07:40:28] ✅ 找到分类: ID=10, 名称='根茎类'
[2025-06-29 07:40:28] 准备创建食材: {"code":"BLBG9279","name":"\u767d\u841d\u535c","specification":"","unit":"\u65a4","category_id":10,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u6839\u830e\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:40:28"}
[2025-06-29 07:40:28] ✅ 食材创建成功: ID=106
[2025-06-29 07:40:28] 第17行: 导入成功
[2025-06-29 07:40:28] --- 处理第18行 ---
[2025-06-29 07:40:28] 第18行字段: 编码='QCLBDG1821', 名称='芹菜（绿）', 数量='25'
[2025-06-29 07:40:28] 第18行: 开始导入明细
[2025-06-29 07:40:28] 查找食材: 编码='QCLBDG1821', 名称='芹菜（绿）'
[2025-06-29 07:40:28] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:40:28] 查找分类: 名称='根茎类'
[2025-06-29 07:40:28] ✅ 找到分类: ID=10, 名称='根茎类'
[2025-06-29 07:40:28] 准备创建食材: {"code":"QCLBDG1821","name":"\u82b9\u83dc\uff08\u7eff\uff09","specification":"","unit":"\u65a4","category_id":10,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u6839\u830e\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:40:28"}
[2025-06-29 07:40:28] ✅ 食材创建成功: ID=107
[2025-06-29 07:40:28] 第18行: 导入成功
[2025-06-29 07:40:28] --- 处理第19行 ---
[2025-06-29 07:40:28] 第19行字段: 编码='XC1624', 名称='香菜', 数量='3'
[2025-06-29 07:40:28] 第19行: 开始导入明细
[2025-06-29 07:40:28] 查找食材: 编码='XC1624', 名称='香菜'
[2025-06-29 07:40:28] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:40:28] 查找分类: 名称='葱姜蒜类'
[2025-06-29 07:40:28] ✅ 找到分类: ID=11, 名称='葱姜蒜类'
[2025-06-29 07:40:28] 准备创建食材: {"code":"XC1624","name":"\u9999\u83dc","specification":"","unit":"\u65a4","category_id":11,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u8471\u59dc\u849c\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:40:28"}
[2025-06-29 07:40:28] ✅ 食材创建成功: ID=108
[2025-06-29 07:40:28] 第19行: 导入成功
[2025-06-29 07:40:28] --- 处理第20行 ---
[2025-06-29 07:40:28] 第20行字段: 编码='SMBT0731', 名称='蒜苗', 数量='15'
[2025-06-29 07:40:28] 第20行: 开始导入明细
[2025-06-29 07:40:28] 查找食材: 编码='SMBT0731', 名称='蒜苗'
[2025-06-29 07:40:28] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:40:28] 查找分类: 名称='葱姜蒜类'
[2025-06-29 07:40:28] ✅ 找到分类: ID=11, 名称='葱姜蒜类'
[2025-06-29 07:40:28] 准备创建食材: {"code":"SMBT0731","name":"\u849c\u82d7","specification":"","unit":"\u65a4","category_id":11,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u8471\u59dc\u849c\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:40:28"}
[2025-06-29 07:40:28] ✅ 食材创建成功: ID=109
[2025-06-29 07:40:28] 第20行: 导入成功
[2025-06-29 07:40:28] --- 处理第21行 ---
[2025-06-29 07:40:28] 第21行字段: 编码='XCBDDFC7870', 名称='小葱', 数量='10'
[2025-06-29 07:40:28] 第21行: 开始导入明细
[2025-06-29 07:40:28] 查找食材: 编码='XCBDDFC7870', 名称='小葱'
[2025-06-29 07:40:28] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:40:28] 查找分类: 名称='葱姜蒜类'
[2025-06-29 07:40:28] ✅ 找到分类: ID=11, 名称='葱姜蒜类'
[2025-06-29 07:40:28] 准备创建食材: {"code":"XCBDDFC7870","name":"\u5c0f\u8471","specification":"","unit":"\u65a4","category_id":11,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u8471\u59dc\u849c\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:40:28"}
[2025-06-29 07:40:28] ✅ 食材创建成功: ID=110
[2025-06-29 07:40:28] 第21行: 导入成功
[2025-06-29 07:40:28] --- 处理第22行 ---
[2025-06-29 07:40:28] 第22行字段: 编码='XJJ0650', 名称='小米椒（小尖椒）', 数量='2'
[2025-06-29 07:40:28] 第22行: 开始导入明细
[2025-06-29 07:40:28] 查找食材: 编码='XJJ0650', 名称='小米椒（小尖椒）'
[2025-06-29 07:40:28] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:40:28] 查找分类: 名称='辣椒类'
[2025-06-29 07:40:28] ✅ 找到分类: ID=12, 名称='辣椒类'
[2025-06-29 07:40:28] 准备创建食材: {"code":"XJJ0650","name":"\u5c0f\u7c73\u6912\uff08\u5c0f\u5c16\u6912\uff09","specification":"","unit":"\u65a4","category_id":12,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u8fa3\u6912\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:40:28"}
[2025-06-29 07:40:28] ✅ 食材创建成功: ID=111
[2025-06-29 07:40:28] 第22行: 导入成功
[2025-06-29 07:40:28] --- 处理第23行 ---
[2025-06-29 07:40:28] 第23行字段: 编码='XJ0544', 名称='青线椒', 数量='50'
[2025-06-29 07:40:28] 第23行: 开始导入明细
[2025-06-29 07:40:28] 查找食材: 编码='XJ0544', 名称='青线椒'
[2025-06-29 07:40:28] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:40:28] 查找分类: 名称='辣椒类'
[2025-06-29 07:40:28] ✅ 找到分类: ID=12, 名称='辣椒类'
[2025-06-29 07:40:28] 准备创建食材: {"code":"XJ0544","name":"\u9752\u7ebf\u6912","specification":"","unit":"\u65a4","category_id":12,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u8fa3\u6912\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:40:28"}
[2025-06-29 07:40:28] ✅ 食材创建成功: ID=112
[2025-06-29 07:40:28] 第23行: 导入成功
[2025-06-29 07:40:28] --- 处理第24行 ---
[2025-06-29 07:40:28] 第24行字段: 编码='DQJ0496', 名称='大青椒', 数量='20'
[2025-06-29 07:40:28] 第24行: 开始导入明细
[2025-06-29 07:40:28] 查找食材: 编码='DQJ0496', 名称='大青椒'
[2025-06-29 07:40:28] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:40:28] 查找分类: 名称='辣椒类'
[2025-06-29 07:40:28] ✅ 找到分类: ID=12, 名称='辣椒类'
[2025-06-29 07:40:28] 准备创建食材: {"code":"DQJ0496","name":"\u5927\u9752\u6912","specification":"","unit":"\u65a4","category_id":12,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u8fa3\u6912\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:40:28"}
[2025-06-29 07:40:28] ✅ 食材创建成功: ID=113
[2025-06-29 07:40:28] 第24行: 导入成功
[2025-06-29 07:40:28] --- 处理第25行 ---
[2025-06-29 07:40:28] 第25行字段: 编码='DHJ6400', 名称='大红椒', 数量='20'
[2025-06-29 07:40:28] 第25行: 开始导入明细
[2025-06-29 07:40:28] 查找食材: 编码='DHJ6400', 名称='大红椒'
[2025-06-29 07:40:28] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:40:28] 查找分类: 名称='辣椒类'
[2025-06-29 07:40:28] ✅ 找到分类: ID=12, 名称='辣椒类'
[2025-06-29 07:40:28] 准备创建食材: {"code":"DHJ6400","name":"\u5927\u7ea2\u6912","specification":"","unit":"\u65a4","category_id":12,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u8fa3\u6912\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:40:28"}
[2025-06-29 07:40:28] ✅ 食材创建成功: ID=114
[2025-06-29 07:40:28] 第25行: 导入成功
[2025-06-29 07:40:28] --- 处理第26行 ---
[2025-06-29 07:40:28] 第26行字段: 编码='ZG0206', 名称='猪肝', 数量='25'
[2025-06-29 07:40:28] 第26行: 开始导入明细
[2025-06-29 07:40:28] 查找食材: 编码='ZG0206', 名称='猪肝'
[2025-06-29 07:40:28] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:40:28] 查找分类: 名称='猪肉类'
[2025-06-29 07:40:28] ✅ 找到分类: ID=13, 名称='猪肉类'
[2025-06-29 07:40:28] 准备创建食材: {"code":"ZG0206","name":"\u732a\u809d","specification":"","unit":"\u65a4","category_id":13,"brand":"\u8089\u79bd\u7c7b","origin":"\u732a\u8089\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:40:28"}
[2025-06-29 07:40:28] ✅ 食材创建成功: ID=115
[2025-06-29 07:40:28] 第26行: 导入成功
[2025-06-29 07:40:28] --- 处理第27行 ---
[2025-06-29 07:40:28] 第27行字段: 编码='WHR4545', 名称='猪五花肉', 数量='30'
[2025-06-29 07:40:28] 第27行: 开始导入明细
[2025-06-29 07:40:28] 查找食材: 编码='WHR4545', 名称='猪五花肉'
[2025-06-29 07:40:28] ✅ 通过编码找到食材: ID=4, 名称=猪五花肉
[2025-06-29 07:40:28] 第27行: 导入成功
[2025-06-29 07:40:28] --- 处理第28行 ---
[2025-06-29 07:40:28] 第28行字段: 编码='ZQTRQG4200', 名称='猪前后腿肉去骨', 数量='140'
[2025-06-29 07:40:28] 第28行: 开始导入明细
[2025-06-29 07:40:28] 查找食材: 编码='ZQTRQG4200', 名称='猪前后腿肉去骨'
[2025-06-29 07:40:29] ✅ 通过编码找到食材: ID=3, 名称=猪前后腿肉去骨
[2025-06-29 07:40:29] 第28行: 导入成功
[2025-06-29 07:40:29] --- 处理第29行 ---
[2025-06-29 07:40:29] 第29行字段: 编码='ZPGZP8811', 名称='猪排骨（杂排）', 数量='10'
[2025-06-29 07:40:29] 第29行: 开始导入明细
[2025-06-29 07:40:29] 查找食材: 编码='ZPGZP8811', 名称='猪排骨（杂排）'
[2025-06-29 07:40:29] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:40:29] 查找分类: 名称='猪肉类'
[2025-06-29 07:40:29] ✅ 找到分类: ID=13, 名称='猪肉类'
[2025-06-29 07:40:29] 准备创建食材: {"code":"ZPGZP8811","name":"\u732a\u6392\u9aa8\uff08\u6742\u6392\uff09","specification":"","unit":"\u65a4","category_id":13,"brand":"\u8089\u79bd\u7c7b","origin":"\u732a\u8089\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:40:29"}
[2025-06-29 07:40:29] ✅ 食材创建成功: ID=116
[2025-06-29 07:40:29] 第29行: 导入成功
[2025-06-29 07:40:29] --- 处理第30行 ---
[2025-06-29 07:40:29] 第30行字段: 编码='SPBM00003294', 名称='牛腩', 数量='30'
[2025-06-29 07:40:29] 第30行: 开始导入明细
[2025-06-29 07:40:29] 查找食材: 编码='SPBM00003294', 名称='牛腩'
[2025-06-29 07:40:29] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:40:29] 查找分类: 名称='牛羊肉类'
[2025-06-29 07:40:29] ✅ 找到分类: ID=14, 名称='牛羊肉类'
[2025-06-29 07:40:29] 准备创建食材: {"code":"SPBM00003294","name":"\u725b\u8169","specification":"","unit":"\u65a4","category_id":14,"brand":"\u8089\u79bd\u7c7b","origin":"\u725b\u7f8a\u8089\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:40:29"}
[2025-06-29 07:40:29] ✅ 食材创建成功: ID=117
[2025-06-29 07:40:29] 第30行: 导入成功
[2025-06-29 07:40:29] --- 处理第31行 ---
[2025-06-29 07:40:29] 第31行字段: 编码='WZSNR3317', 名称='牛肉', 数量='20'
[2025-06-29 07:40:29] 第31行: 开始导入明细
[2025-06-29 07:40:29] 查找食材: 编码='WZSNR3317', 名称='牛肉'
[2025-06-29 07:40:29] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:40:29] 查找分类: 名称='牛羊肉类'
[2025-06-29 07:40:29] ✅ 找到分类: ID=14, 名称='牛羊肉类'
[2025-06-29 07:40:29] 准备创建食材: {"code":"WZSNR3317","name":"\u725b\u8089","specification":"","unit":"\u65a4","category_id":14,"brand":"\u8089\u79bd\u7c7b","origin":"\u725b\u7f8a\u8089\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:40:29"}
[2025-06-29 07:40:29] ✅ 食材创建成功: ID=118
[2025-06-29 07:40:29] 第31行: 导入成功
[2025-06-29 07:40:29] --- 处理第32行 ---
[2025-06-29 07:40:29] 第32行字段: 编码='SHJ8439', 名称='三黄鸡（白条、无内脏）', 数量='70'
[2025-06-29 07:40:29] 第32行: 开始导入明细
[2025-06-29 07:40:29] 查找食材: 编码='SHJ8439', 名称='三黄鸡（白条、无内脏）'
[2025-06-29 07:40:29] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:40:29] 查找分类: 名称='鸡鸭鹅兔类'
[2025-06-29 07:40:29] ✅ 找到分类: ID=15, 名称='鸡鸭鹅兔类'
[2025-06-29 07:40:29] 准备创建食材: {"code":"SHJ8439","name":"\u4e09\u9ec4\u9e21\uff08\u767d\u6761\u3001\u65e0\u5185\u810f\uff09","specification":"","unit":"\u65a4","category_id":15,"brand":"\u8089\u79bd\u7c7b","origin":"\u9e21\u9e2d\u9e45\u5154\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:40:29"}
[2025-06-29 07:40:29] ✅ 食材创建成功: ID=119
[2025-06-29 07:40:29] 第32行: 导入成功
[2025-06-29 07:40:29] --- 处理第33行 ---
[2025-06-29 07:40:29] 第33行字段: 编码='HDY6675', 名称='黄豆芽', 数量='30'
[2025-06-29 07:40:29] 第33行: 开始导入明细
[2025-06-29 07:40:29] 查找食材: 编码='HDY6675', 名称='黄豆芽'
[2025-06-29 07:40:29] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:40:29] 查找分类: 名称='豆制品'
[2025-06-29 07:40:29] ✅ 找到分类: ID=16, 名称='豆制品'
[2025-06-29 07:40:29] 准备创建食材: {"code":"HDY6675","name":"\u9ec4\u8c46\u82bd","specification":"","unit":"\u65a4","category_id":16,"brand":"\u7c89\u9762\u8c46\u5236\u54c1","origin":"\u8c46\u5236\u54c1","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:40:29"}
[2025-06-29 07:40:29] ✅ 食材创建成功: ID=120
[2025-06-29 07:40:29] 第33行: 导入成功
[2025-06-29 07:40:29] --- 处理第34行 ---
[2025-06-29 07:40:29] 第34行字段: 编码='SDF2528', 名称='水豆腐', 数量='5'
[2025-06-29 07:40:29] 第34行: 开始导入明细
[2025-06-29 07:40:29] 查找食材: 编码='SDF2528', 名称='水豆腐'
[2025-06-29 07:40:29] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:40:29] 查找分类: 名称='豆制品'
[2025-06-29 07:40:29] ✅ 找到分类: ID=16, 名称='豆制品'
[2025-06-29 07:40:29] 准备创建食材: {"code":"SDF2528","name":"\u6c34\u8c46\u8150","specification":"","unit":"\u677f","category_id":16,"brand":"\u7c89\u9762\u8c46\u5236\u54c1","origin":"\u8c46\u5236\u54c1","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:40:29"}
[2025-06-29 07:40:29] ✅ 食材创建成功: ID=121
[2025-06-29 07:40:29] 第34行: 导入成功
[2025-06-29 07:40:29] --- 处理第35行 ---
[2025-06-29 07:40:29] 第35行字段: 编码='GDFP4973', 名称='干豆腐片（薄）', 数量='15'
[2025-06-29 07:40:29] 第35行: 开始导入明细
[2025-06-29 07:40:29] 查找食材: 编码='GDFP4973', 名称='干豆腐片（薄）'
[2025-06-29 07:40:29] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:40:29] 查找分类: 名称='豆制品'
[2025-06-29 07:40:29] ✅ 找到分类: ID=16, 名称='豆制品'
[2025-06-29 07:40:29] 准备创建食材: {"code":"GDFP4973","name":"\u5e72\u8c46\u8150\u7247\uff08\u8584\uff09","specification":"","unit":"\u65a4","category_id":16,"brand":"\u7c89\u9762\u8c46\u5236\u54c1","origin":"\u8c46\u5236\u54c1","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:40:29"}
[2025-06-29 07:40:29] ✅ 食材创建成功: ID=122
[2025-06-29 07:40:29] 第35行: 导入成功
[2025-06-29 07:40:29] --- 处理第36行 ---
[2025-06-29 07:40:29] 第36行字段: 编码='GDFHP1223', 名称='干豆腐片（厚）', 数量='40'
[2025-06-29 07:40:29] 第36行: 开始导入明细
[2025-06-29 07:40:29] 查找食材: 编码='GDFHP1223', 名称='干豆腐片（厚）'
[2025-06-29 07:40:29] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:40:29] 查找分类: 名称='豆制品'
[2025-06-29 07:40:29] ✅ 找到分类: ID=16, 名称='豆制品'
[2025-06-29 07:40:29] 准备创建食材: {"code":"GDFHP1223","name":"\u5e72\u8c46\u8150\u7247\uff08\u539a\uff09","specification":"","unit":"\u65a4","category_id":16,"brand":"\u7c89\u9762\u8c46\u5236\u54c1","origin":"\u8c46\u5236\u54c1","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:40:29"}
[2025-06-29 07:40:29] ✅ 食材创建成功: ID=123
[2025-06-29 07:40:29] 第36行: 导入成功
[2025-06-29 07:40:29] --- 处理第37行 ---
[2025-06-29 07:40:29] 第37行字段: 编码='YF4729', 名称='圆粉', 数量='200'
[2025-06-29 07:40:29] 第37行: 开始导入明细
[2025-06-29 07:40:29] 查找食材: 编码='YF4729', 名称='圆粉'
[2025-06-29 07:40:29] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:40:29] 查找分类: 名称='粉面制品'
[2025-06-29 07:40:29] ✅ 找到分类: ID=17, 名称='粉面制品'
[2025-06-29 07:40:29] 准备创建食材: {"code":"YF4729","name":"\u5706\u7c89","specification":"","unit":"\u65a4","category_id":17,"brand":"\u7c89\u9762\u8c46\u5236\u54c1","origin":"\u7c89\u9762\u5236\u54c1","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:40:29"}
[2025-06-29 07:40:29] ✅ 食材创建成功: ID=124
[2025-06-29 07:40:29] 第37行: 导入成功
[2025-06-29 07:40:29] --- 处理第38行 ---
[2025-06-29 07:40:29] 第38行字段: 编码='BF4586', 名称='扁粉', 数量='350'
[2025-06-29 07:40:29] 第38行: 开始导入明细
[2025-06-29 07:40:29] 查找食材: 编码='BF4586', 名称='扁粉'
[2025-06-29 07:40:29] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:40:29] 查找分类: 名称='粉面制品'
[2025-06-29 07:40:29] ✅ 找到分类: ID=17, 名称='粉面制品'
[2025-06-29 07:40:29] 准备创建食材: {"code":"BF4586","name":"\u6241\u7c89","specification":"","unit":"\u65a4","category_id":17,"brand":"\u7c89\u9762\u8c46\u5236\u54c1","origin":"\u7c89\u9762\u5236\u54c1","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:40:29"}
[2025-06-29 07:40:29] ✅ 食材创建成功: ID=125
[2025-06-29 07:40:29] 第38行: 导入成功
[2025-06-29 07:40:29] --- 处理第39行 ---
[2025-06-29 07:40:29] 第39行字段: 编码='YF5478', 名称='油粉', 数量='30'
[2025-06-29 07:40:29] 第39行: 开始导入明细
[2025-06-29 07:40:29] 查找食材: 编码='YF5478', 名称='油粉'
[2025-06-29 07:40:29] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:40:29] 查找分类: 名称='粉面制品'
[2025-06-29 07:40:29] ✅ 找到分类: ID=17, 名称='粉面制品'
[2025-06-29 07:40:29] 准备创建食材: {"code":"YF5478","name":"\u6cb9\u7c89","specification":"","unit":"\u65a4","category_id":17,"brand":"\u7c89\u9762\u8c46\u5236\u54c1","origin":"\u7c89\u9762\u5236\u54c1","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:40:29"}
[2025-06-29 07:40:29] ✅ 食材创建成功: ID=126
[2025-06-29 07:40:29] 第39行: 导入成功
[2025-06-29 07:40:29] --- 处理第40行 ---
[2025-06-29 07:40:29] 第40行字段: 编码='BF5568', 名称='薄粉（卷粉皮）', 数量='15'
[2025-06-29 07:40:29] 第40行: 开始导入明细
[2025-06-29 07:40:29] 查找食材: 编码='BF5568', 名称='薄粉（卷粉皮）'
[2025-06-29 07:40:29] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:40:29] 查找分类: 名称='粉面制品'
[2025-06-29 07:40:29] ✅ 找到分类: ID=17, 名称='粉面制品'
[2025-06-29 07:40:29] 准备创建食材: {"code":"BF5568","name":"\u8584\u7c89\uff08\u5377\u7c89\u76ae\uff09","specification":"","unit":"\u65a4","category_id":17,"brand":"\u7c89\u9762\u8c46\u5236\u54c1","origin":"\u7c89\u9762\u5236\u54c1","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:40:29"}
[2025-06-29 07:40:29] ✅ 食材创建成功: ID=127
[2025-06-29 07:40:29] 第40行: 导入成功
[2025-06-29 07:40:29] --- 处理第41行 ---
[2025-06-29 07:40:29] 第41行字段: 编码='QTXW2925', 名称='秋田小町25KG', 数量='10'
[2025-06-29 07:40:29] 第41行: 开始导入明细
[2025-06-29 07:40:29] 查找食材: 编码='QTXW2925', 名称='秋田小町25KG'
[2025-06-29 07:40:29] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:40:29] 查找分类: 名称='大米类'
[2025-06-29 07:40:29] ✅ 找到分类: ID=18, 名称='大米类'
[2025-06-29 07:40:29] 准备创建食材: {"code":"QTXW2925","name":"\u79cb\u7530\u5c0f\u753a25KG","specification":"25KG","unit":"\u888b","category_id":18,"brand":"\u7c73\u9762\u7cae\u6cb9","origin":"\u5927\u7c73\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:40:29"}
[2025-06-29 07:40:29] ✅ 食材创建成功: ID=128
[2025-06-29 07:40:29] 第41行: 导入成功
[2025-06-29 07:40:29] --- 处理第42行 ---
[2025-06-29 07:40:29] 第42行字段: 编码='GLHLJM1006', 名称='冠霖糊辣椒面', 数量='2'
[2025-06-29 07:40:29] 第42行: 开始导入明细
[2025-06-29 07:40:29] 查找食材: 编码='GLHLJM1006', 名称='冠霖糊辣椒面'
[2025-06-29 07:40:29] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:40:29] 查找分类: 名称='干辣椒花椒面类'
[2025-06-29 07:40:29] ✅ 找到分类: ID=19, 名称='干辣椒花椒面类'
[2025-06-29 07:40:29] 准备创建食材: {"code":"GLHLJM1006","name":"\u51a0\u9716\u7cca\u8fa3\u6912\u9762","specification":"5\u65a4\/\u888b","unit":"\u888b","category_id":19,"brand":"\u8c03\u6599\u5e72\u8d27","origin":"\u5e72\u8fa3\u6912\u82b1\u6912\u9762\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:40:29"}
[2025-06-29 07:40:29] ✅ 食材创建成功: ID=129
[2025-06-29 07:40:30] 第42行: 导入成功
[2025-06-29 07:40:30] --- 处理第43行 ---
[2025-06-29 07:40:30] 第43行字段: 编码='DFP8053', 名称='干豆腐皮', 数量='1'
[2025-06-29 07:40:30] 第43行: 开始导入明细
[2025-06-29 07:40:30] 查找食材: 编码='DFP8053', 名称='干豆腐皮'
[2025-06-29 07:40:30] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:40:30] 查找分类: 名称='干货类'
[2025-06-29 07:40:30] ✅ 找到分类: ID=20, 名称='干货类'
[2025-06-29 07:40:30] 准备创建食材: {"code":"DFP8053","name":"\u5e72\u8c46\u8150\u76ae","specification":"\u7ea612.5\u65a4","unit":"\u4ef6","category_id":20,"brand":"\u8c03\u6599\u5e72\u8d27","origin":"\u5e72\u8d27\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:40:30"}
[2025-06-29 07:40:30] ✅ 食材创建成功: ID=130
[2025-06-29 07:40:30] 第43行: 导入成功
[2025-06-29 07:40:30] --- 处理第44行 ---
[2025-06-29 07:40:30] 第44行字段: 编码='CS7213', 名称='脆哨', 数量='10'
[2025-06-29 07:40:30] 第44行: 开始导入明细
[2025-06-29 07:40:30] 查找食材: 编码='CS7213', 名称='脆哨'
[2025-06-29 07:40:30] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:40:30] 查找分类: 名称='干货类'
[2025-06-29 07:40:30] ✅ 找到分类: ID=20, 名称='干货类'
[2025-06-29 07:40:30] 准备创建食材: {"code":"CS7213","name":"\u8106\u54e8","specification":"","unit":"\u65a4","category_id":20,"brand":"\u8c03\u6599\u5e72\u8d27","origin":"\u5e72\u8d27\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:40:30"}
[2025-06-29 07:40:30] ✅ 食材创建成功: ID=131
[2025-06-29 07:40:30] 第44行: 导入成功
[2025-06-29 07:40:30] --- 处理第45行 ---
[2025-06-29 07:40:30] 第45行字段: 编码='XJ1638', 名称='香蕉', 数量='30'
[2025-06-29 07:40:30] 第45行: 开始导入明细
[2025-06-29 07:40:30] 查找食材: 编码='XJ1638', 名称='香蕉'
[2025-06-29 07:40:30] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:40:30] 查找分类: 名称='时令水果1'
[2025-06-29 07:40:30] ✅ 找到分类: ID=21, 名称='时令水果1'
[2025-06-29 07:40:30] 准备创建食材: {"code":"XJ1638","name":"\u9999\u8549","specification":"","unit":"\u65a4","category_id":21,"brand":"\u65f6\u4ee4\u6c34\u679c","origin":"\u65f6\u4ee4\u6c34\u679c1","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:40:30"}
[2025-06-29 07:40:30] ✅ 食材创建成功: ID=132
[2025-06-29 07:40:30] 第45行: 导入成功
[2025-06-29 07:40:30] --- 处理第46行 ---
[2025-06-29 07:40:30] 第46行字段: 编码='HWZTX7875', 名称='茉莉香提', 数量='20'
[2025-06-29 07:40:30] 第46行: 开始导入明细
[2025-06-29 07:40:30] 查找食材: 编码='HWZTX7875', 名称='茉莉香提'
[2025-06-29 07:40:30] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:40:30] 查找分类: 名称='时令水果1'
[2025-06-29 07:40:30] ✅ 找到分类: ID=21, 名称='时令水果1'
[2025-06-29 07:40:30] 准备创建食材: {"code":"HWZTX7875","name":"\u8309\u8389\u9999\u63d0","specification":"","unit":"\u65a4","category_id":21,"brand":"\u65f6\u4ee4\u6c34\u679c","origin":"\u65f6\u4ee4\u6c34\u679c1","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:40:30"}
[2025-06-29 07:40:30] ✅ 食材创建成功: ID=133
[2025-06-29 07:40:30] 第46行: 导入成功
[2025-06-29 07:40:30] --- 处理第47行 ---
[2025-06-29 07:40:30] 第47行字段: 编码='HMGXMH7349', 名称='哈密瓜（小蜜25号）大果', 数量='50'
[2025-06-29 07:40:30] 第47行: 开始导入明细
[2025-06-29 07:40:30] 查找食材: 编码='HMGXMH7349', 名称='哈密瓜（小蜜25号）大果'
[2025-06-29 07:40:30] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:40:30] 查找分类: 名称='时令水果1'
[2025-06-29 07:40:30] ✅ 找到分类: ID=21, 名称='时令水果1'
[2025-06-29 07:40:30] 准备创建食材: {"code":"HMGXMH7349","name":"\u54c8\u5bc6\u74dc\uff08\u5c0f\u871c25\u53f7\uff09\u5927\u679c","specification":"\u6c99\u5730\u871c\u74dc\u5927\u679c","unit":"\u65a4","category_id":21,"brand":"\u65f6\u4ee4\u6c34\u679c","origin":"\u65f6\u4ee4\u6c34\u679c1","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:40:30"}
[2025-06-29 07:40:30] ✅ 食材创建成功: ID=134
[2025-06-29 07:40:30] 第47行: 导入成功
[2025-06-29 07:40:30] --- 处理第48行 ---
[2025-06-29 07:40:30] 第48行字段: 编码='XD6079', 名称='豇豆（绿）', 数量='40'
[2025-06-29 07:40:30] 第48行: 开始导入明细
[2025-06-29 07:40:30] 查找食材: 编码='XD6079', 名称='豇豆（绿）'
[2025-06-29 07:40:30] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:40:30] 查找分类: 名称='茄果类'
[2025-06-29 07:40:30] ✅ 找到分类: ID=9, 名称='茄果类'
[2025-06-29 07:40:30] 准备创建食材: {"code":"XD6079","name":"\u8c47\u8c46\uff08\u7eff\uff09","specification":"","unit":"\u65a4","category_id":9,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u8304\u679c\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:40:30"}
[2025-06-29 07:40:30] ✅ 食材创建成功: ID=135
[2025-06-29 07:40:30] 第48行: 导入成功
[2025-06-29 07:40:30] --- 处理第49行 ---
[2025-06-29 07:40:30] 第49行字段: 编码='XTD9873', 名称='土豆', 数量='250'
[2025-06-29 07:40:30] 第49行: 开始导入明细
[2025-06-29 07:40:30] 查找食材: 编码='XTD9873', 名称='土豆'
[2025-06-29 07:40:30] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:40:30] 查找分类: 名称='根茎类'
[2025-06-29 07:40:30] ✅ 找到分类: ID=10, 名称='根茎类'
[2025-06-29 07:40:30] 准备创建食材: {"code":"XTD9873","name":"\u571f\u8c46","specification":"","unit":"\u65a4","category_id":10,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u6839\u830e\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:40:30"}
[2025-06-29 07:40:30] ✅ 食材创建成功: ID=136
[2025-06-29 07:40:30] 第49行: 导入成功
[2025-06-29 07:40:30] --- 处理第50行 ---
[2025-06-29 07:40:30] 第50行字段: 编码='LJ0368', 名称='老姜（带土）', 数量='20'
[2025-06-29 07:40:30] 第50行: 开始导入明细
[2025-06-29 07:40:30] 查找食材: 编码='LJ0368', 名称='老姜（带土）'
[2025-06-29 07:40:30] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:40:30] 查找分类: 名称='葱姜蒜类'
[2025-06-29 07:40:30] ✅ 找到分类: ID=11, 名称='葱姜蒜类'
[2025-06-29 07:40:30] 准备创建食材: {"code":"LJ0368","name":"\u8001\u59dc\uff08\u5e26\u571f\uff09","specification":"","unit":"\u65a4","category_id":11,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u8471\u59dc\u849c\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:40:30"}
[2025-06-29 07:40:30] ✅ 食材创建成功: ID=137
[2025-06-29 07:40:30] 第50行: 导入成功
[2025-06-29 07:40:30] --- 处理第51行 ---
[2025-06-29 07:40:30] 第51行字段: 编码='SM0321', 名称='蒜米', 数量='2'
[2025-06-29 07:40:30] 第51行: 开始导入明细
[2025-06-29 07:40:30] 查找食材: 编码='SM0321', 名称='蒜米'
[2025-06-29 07:40:30] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:40:30] 查找分类: 名称='葱姜蒜类'
[2025-06-29 07:40:30] ✅ 找到分类: ID=11, 名称='葱姜蒜类'
[2025-06-29 07:40:30] 准备创建食材: {"code":"SM0321","name":"\u849c\u7c73","specification":"","unit":"\u888b","category_id":11,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u8471\u59dc\u849c\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:40:30"}
[2025-06-29 07:40:30] ✅ 食材创建成功: ID=138
[2025-06-29 07:40:30] 第51行: 导入成功
[2025-06-29 07:40:30] --- 处理第52行 ---
[2025-06-29 07:40:30] 第52行字段: 编码='SPBM00003264', 名称='猪里脊肉', 数量='30'
[2025-06-29 07:40:30] 第52行: 开始导入明细
[2025-06-29 07:40:30] 查找食材: 编码='SPBM00003264', 名称='猪里脊肉'
[2025-06-29 07:40:30] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:40:30] 查找分类: 名称='猪肉类'
[2025-06-29 07:40:30] ✅ 找到分类: ID=13, 名称='猪肉类'
[2025-06-29 07:40:30] 准备创建食材: {"code":"SPBM00003264","name":"\u732a\u91cc\u810a\u8089","specification":"","unit":"\u65a4","category_id":13,"brand":"\u8089\u79bd\u7c7b","origin":"\u732a\u8089\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:40:30"}
[2025-06-29 07:40:30] ✅ 食材创建成功: ID=139
[2025-06-29 07:40:30] 第52行: 导入成功
[2025-06-29 07:40:30] --- 处理第53行 ---
[2025-06-29 07:40:30] 第53行字段: 编码='HYML4819', 名称='黄玉米粒（新鲜）', 数量='30'
[2025-06-29 07:40:30] 第53行: 开始导入明细
[2025-06-29 07:40:30] 查找食材: 编码='HYML4819', 名称='黄玉米粒（新鲜）'
[2025-06-29 07:40:30] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:40:30] 查找分类: 名称='茄果类'
[2025-06-29 07:40:30] ✅ 找到分类: ID=9, 名称='茄果类'
[2025-06-29 07:40:30] 准备创建食材: {"code":"HYML4819","name":"\u9ec4\u7389\u7c73\u7c92\uff08\u65b0\u9c9c\uff09","specification":"","unit":"\u65a4","category_id":9,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u8304\u679c\u7c7b","shelf_life":"","shelf_life_days":0,"created_by":1,"status":1,"created_at":"2025-06-29 07:40:30"}
[2025-06-29 07:40:30] ✅ 食材创建成功: ID=140
[2025-06-29 07:40:30] 第53行: 导入成功
[2025-06-29 07:40:30] --- 处理第54行 ---
[2025-06-29 07:40:30] 第54行字段: 编码='', 名称='', 数量=''
[2025-06-29 07:40:30] 第54行: 关键字段为空，跳过
[2025-06-29 07:40:30] --- 处理第55行 ---
[2025-06-29 07:40:30] 第55行: 空行，跳过
[2025-06-29 07:40:30] --- 处理第56行 ---
[2025-06-29 07:40:30] 第56行: 空行，跳过
[2025-06-29 07:40:30] --- 处理第57行 ---
[2025-06-29 07:40:30] 第57行: 空行，跳过
[2025-06-29 07:40:30] --- 处理第58行 ---
[2025-06-29 07:40:30] 第58行: 空行，跳过
[2025-06-29 07:40:30] --- 处理第59行 ---
[2025-06-29 07:40:30] 第59行: 空行，跳过
[2025-06-29 07:40:30] --- 处理第60行 ---
[2025-06-29 07:40:30] 第60行: 空行，跳过
[2025-06-29 07:40:30] --- 处理第61行 ---
[2025-06-29 07:40:30] 第61行: 空行，跳过
[2025-06-29 07:40:30] --- 处理第62行 ---
[2025-06-29 07:40:30] 第62行: 空行，跳过
[2025-06-29 07:40:30] --- 处理第63行 ---
[2025-06-29 07:40:30] 第63行: 空行，跳过
[2025-06-29 07:40:30] --- 处理第64行 ---
[2025-06-29 07:40:30] 第64行: 空行，跳过
[2025-06-29 07:40:30] --- 处理第65行 ---
[2025-06-29 07:40:30] 第65行: 空行，跳过
[2025-06-29 07:40:30] --- 处理第66行 ---
[2025-06-29 07:40:30] 第66行: 空行，跳过
[2025-06-29 07:40:30] --- 处理第67行 ---
[2025-06-29 07:40:30] 第67行: 空行，跳过
[2025-06-29 07:40:30] --- 处理第68行 ---
[2025-06-29 07:40:30] 第68行: 空行，跳过
[2025-06-29 07:40:30] --- 处理第69行 ---
[2025-06-29 07:40:30] 第69行: 空行，跳过
[2025-06-29 07:40:30] --- 处理第70行 ---
[2025-06-29 07:40:30] 第70行: 空行，跳过
[2025-06-29 07:40:30] --- 处理第71行 ---
[2025-06-29 07:40:30] 第71行: 空行，跳过
[2025-06-29 07:40:30] --- 处理第72行 ---
[2025-06-29 07:40:30] 第72行: 空行，跳过
[2025-06-29 07:40:30] --- 处理第73行 ---
[2025-06-29 07:40:30] 第73行: 空行，跳过
[2025-06-29 07:40:30] --- 处理第74行 ---
[2025-06-29 07:40:30] 第74行: 空行，跳过
[2025-06-29 07:40:30] --- 处理第75行 ---
[2025-06-29 07:40:30] 第75行: 空行，跳过
[2025-06-29 07:40:30] --- 处理第76行 ---
[2025-06-29 07:40:30] 第76行: 空行，跳过
[2025-06-29 07:40:30] --- 处理第77行 ---
[2025-06-29 07:40:30] 第77行: 空行，跳过
[2025-06-29 07:40:30] --- 处理第78行 ---
[2025-06-29 07:40:30] 第78行: 空行，跳过
[2025-06-29 07:40:30] --- 处理第79行 ---
[2025-06-29 07:40:30] 第79行: 空行，跳过
[2025-06-29 07:40:30] --- 处理第80行 ---
[2025-06-29 07:40:30] 第80行: 空行，跳过
[2025-06-29 07:40:30] --- 处理第81行 ---
[2025-06-29 07:40:30] 第81行: 空行，跳过
[2025-06-29 07:40:30] --- 处理第82行 ---
[2025-06-29 07:40:30] 第82行: 空行，跳过
[2025-06-29 07:40:30] --- 处理第83行 ---
[2025-06-29 07:40:30] 第83行: 空行，跳过
[2025-06-29 07:40:30] --- 处理第84行 ---
[2025-06-29 07:40:30] 第84行: 空行，跳过
[2025-06-29 07:40:30] --- 处理第85行 ---
[2025-06-29 07:40:30] 第85行: 空行，跳过
[2025-06-29 07:40:30] --- 处理第86行 ---
[2025-06-29 07:40:30] 第86行: 空行，跳过
[2025-06-29 07:40:30] --- 处理第87行 ---
[2025-06-29 07:40:30] 第87行: 空行，跳过
[2025-06-29 07:40:30] --- 处理第88行 ---
[2025-06-29 07:40:30] 第88行: 空行，跳过
[2025-06-29 07:40:30] --- 处理第89行 ---
[2025-06-29 07:40:30] 第89行: 空行，跳过
[2025-06-29 07:40:30] --- 处理第90行 ---
[2025-06-29 07:40:30] 第90行: 空行，跳过
[2025-06-29 07:40:30] --- 处理第91行 ---
[2025-06-29 07:40:30] 第91行: 空行，跳过
[2025-06-29 07:40:30] --- 处理第92行 ---
[2025-06-29 07:40:30] 第92行: 空行，跳过
[2025-06-29 07:40:30] --- 处理第93行 ---
[2025-06-29 07:40:30] 第93行: 空行，跳过
[2025-06-29 07:40:30] --- 处理第94行 ---
[2025-06-29 07:40:30] 第94行: 空行，跳过
[2025-06-29 07:40:30] --- 处理第95行 ---
[2025-06-29 07:40:30] 第95行: 空行，跳过
[2025-06-29 07:40:30] --- 处理第96行 ---
[2025-06-29 07:40:30] 第96行: 空行，跳过
[2025-06-29 07:40:30] --- 处理第97行 ---
[2025-06-29 07:40:30] 第97行: 空行，跳过
[2025-06-29 07:40:30] --- 处理第98行 ---
[2025-06-29 07:40:30] 第98行: 空行，跳过
[2025-06-29 07:40:30] --- 处理第99行 ---
[2025-06-29 07:40:30] 第99行: 空行，跳过
[2025-06-29 07:40:30] --- 处理第100行 ---
[2025-06-29 07:40:30] 第100行: 空行，跳过
[2025-06-29 07:40:30] === 明细数据处理完成 ===
[2025-06-29 07:40:30] 成功: 46, 失败: 1
[2025-06-29 07:44:51] === 开始处理明细数据 ===
[2025-06-29 07:44:51] 数据总行数: 100
[2025-06-29 07:44:51] 检查第6行第1列: '商品明细'
[2025-06-29 07:44:51] 找到明细标题行: 第6行
[2025-06-29 07:44:51] 明细数据从第7行开始处理
[2025-06-29 07:44:51] --- 处理第7行 ---
[2025-06-29 07:44:51] 第7行字段: 编码='商品编码', 名称='商品名称', 数量='下单数量'
[2025-06-29 07:44:51] 第7行: 开始导入明细
[2025-06-29 07:44:51] 第7行: 导入失败 - 第7行: 数量必须大于0（第9列）
[2025-06-29 07:44:51] --- 处理第8行 ---
[2025-06-29 07:44:51] 第8行字段: 编码='DBC1685', 名称='大白菜', 数量='40'
[2025-06-29 07:44:51] 第8行: 开始导入明细
[2025-06-29 07:44:51] 查找食材: 编码='DBC1685', 名称='大白菜'
[2025-06-29 07:44:51] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:44:51] 查找分类: 名称='叶菜类'
[2025-06-29 07:44:51] ✅ 找到分类: ID=8, 名称='叶菜类'
[2025-06-29 07:44:51] 准备创建食材: {"code":"DBC1685","name":"\u5927\u767d\u83dc","specification":"","unit":"\u65a4","category_id":8,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u53f6\u83dc\u7c7b","shelf_life":"1\u5929","shelf_life_days":1,"min_stock":10,"created_by":1,"status":1,"created_at":"2025-06-29 07:44:51"}
[2025-06-29 07:44:51] ✅ 食材创建成功: ID=141
[2025-06-29 07:44:51] 第8行: 导入成功
[2025-06-29 07:44:51] --- 处理第9行 ---
[2025-06-29 07:44:51] 第9行字段: 编码='LHB1033', 名称='莲花白（平顶）', 数量='40'
[2025-06-29 07:44:51] 第9行: 开始导入明细
[2025-06-29 07:44:51] 查找食材: 编码='LHB1033', 名称='莲花白（平顶）'
[2025-06-29 07:44:51] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:44:51] 查找分类: 名称='叶菜类'
[2025-06-29 07:44:51] ✅ 找到分类: ID=8, 名称='叶菜类'
[2025-06-29 07:44:51] 准备创建食材: {"code":"LHB1033","name":"\u83b2\u82b1\u767d\uff08\u5e73\u9876\uff09","specification":"","unit":"\u65a4","category_id":8,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u53f6\u83dc\u7c7b","shelf_life":"1\u5929","shelf_life_days":1,"min_stock":10,"created_by":1,"status":1,"created_at":"2025-06-29 07:44:51"}
[2025-06-29 07:44:51] ✅ 食材创建成功: ID=142
[2025-06-29 07:44:51] 第9行: 导入成功
[2025-06-29 07:44:51] --- 处理第10行 ---
[2025-06-29 07:44:51] 第10行字段: 编码='XLH1581', 名称='西兰花', 数量='40'
[2025-06-29 07:44:51] 第10行: 开始导入明细
[2025-06-29 07:44:51] 查找食材: 编码='XLH1581', 名称='西兰花'
[2025-06-29 07:44:51] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:44:51] 查找分类: 名称='茄果类'
[2025-06-29 07:44:51] ✅ 找到分类: ID=9, 名称='茄果类'
[2025-06-29 07:44:51] 准备创建食材: {"code":"XLH1581","name":"\u897f\u5170\u82b1","specification":"","unit":"\u65a4","category_id":9,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u8304\u679c\u7c7b","shelf_life":"1\u5929","shelf_life_days":1,"min_stock":10,"created_by":1,"status":1,"created_at":"2025-06-29 07:44:51"}
[2025-06-29 07:44:51] ✅ 食材创建成功: ID=143
[2025-06-29 07:44:51] 第10行: 导入成功
[2025-06-29 07:44:51] --- 处理第11行 ---
[2025-06-29 07:44:51] 第11行字段: 编码='XHS1526', 名称='西红柿', 数量='60'
[2025-06-29 07:44:51] 第11行: 开始导入明细
[2025-06-29 07:44:51] 查找食材: 编码='XHS1526', 名称='西红柿'
[2025-06-29 07:44:51] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:44:51] 查找分类: 名称='茄果类'
[2025-06-29 07:44:51] ✅ 找到分类: ID=9, 名称='茄果类'
[2025-06-29 07:44:51] 准备创建食材: {"code":"XHS1526","name":"\u897f\u7ea2\u67ff","specification":"","unit":"\u65a4","category_id":9,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u8304\u679c\u7c7b","shelf_life":"1\u5929","shelf_life_days":1,"min_stock":10,"created_by":1,"status":1,"created_at":"2025-06-29 07:44:51"}
[2025-06-29 07:44:51] ✅ 食材创建成功: ID=144
[2025-06-29 07:44:51] 第11行: 导入成功
[2025-06-29 07:44:51] --- 处理第12行 ---
[2025-06-29 07:44:51] 第12行字段: 编码='WDHG7779', 名称='外地黄瓜', 数量='60'
[2025-06-29 07:44:51] 第12行: 开始导入明细
[2025-06-29 07:44:51] 查找食材: 编码='WDHG7779', 名称='外地黄瓜'
[2025-06-29 07:44:51] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:44:51] 查找分类: 名称='茄果类'
[2025-06-29 07:44:51] ✅ 找到分类: ID=9, 名称='茄果类'
[2025-06-29 07:44:51] 准备创建食材: {"code":"WDHG7779","name":"\u5916\u5730\u9ec4\u74dc","specification":"","unit":"\u65a4","category_id":9,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u8304\u679c\u7c7b","shelf_life":"1\u5929","shelf_life_days":1,"min_stock":10,"created_by":1,"status":1,"created_at":"2025-06-29 07:44:51"}
[2025-06-29 07:44:51] ✅ 食材创建成功: ID=145
[2025-06-29 07:44:51] 第12行: 导入成功
[2025-06-29 07:44:51] --- 处理第13行 ---
[2025-06-29 07:44:51] 第13行字段: 编码='XNGC2635', 名称='小南瓜（长）', 数量='40'
[2025-06-29 07:44:51] 第13行: 开始导入明细
[2025-06-29 07:44:51] 查找食材: 编码='XNGC2635', 名称='小南瓜（长）'
[2025-06-29 07:44:51] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:44:51] 查找分类: 名称='茄果类'
[2025-06-29 07:44:51] ✅ 找到分类: ID=9, 名称='茄果类'
[2025-06-29 07:44:51] 准备创建食材: {"code":"XNGC2635","name":"\u5c0f\u5357\u74dc\uff08\u957f\uff09","specification":"","unit":"\u65a4","category_id":9,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u8304\u679c\u7c7b","shelf_life":"1\u5929","shelf_life_days":1,"min_stock":10,"created_by":1,"status":1,"created_at":"2025-06-29 07:44:51"}
[2025-06-29 07:44:51] ✅ 食材创建成功: ID=146
[2025-06-29 07:44:51] 第13行: 导入成功
[2025-06-29 07:44:51] --- 处理第14行 ---
[2025-06-29 07:44:51] 第14行字段: 编码='dasd12', 名称='黄玉米棒', 数量='50'
[2025-06-29 07:44:51] 第14行: 开始导入明细
[2025-06-29 07:44:51] 查找食材: 编码='dasd12', 名称='黄玉米棒'
[2025-06-29 07:44:51] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:44:51] 查找分类: 名称='茄果类'
[2025-06-29 07:44:51] ✅ 找到分类: ID=9, 名称='茄果类'
[2025-06-29 07:44:51] 准备创建食材: {"code":"dasd12","name":"\u9ec4\u7389\u7c73\u68d2","specification":"","unit":"\u65a4","category_id":9,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u8304\u679c\u7c7b","shelf_life":"1\u5929","shelf_life_days":1,"min_stock":10,"created_by":1,"status":1,"created_at":"2025-06-29 07:44:51"}
[2025-06-29 07:44:51] ✅ 食材创建成功: ID=147
[2025-06-29 07:44:51] 第14行: 导入成功
[2025-06-29 07:44:51] --- 处理第15行 ---
[2025-06-29 07:44:51] 第15行字段: 编码='SDCK1461', 名称='莴笋', 数量='50'
[2025-06-29 07:44:51] 第15行: 开始导入明细
[2025-06-29 07:44:51] 查找食材: 编码='SDCK1461', 名称='莴笋'
[2025-06-29 07:44:51] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:44:51] 查找分类: 名称='根茎类'
[2025-06-29 07:44:51] ✅ 找到分类: ID=10, 名称='根茎类'
[2025-06-29 07:44:51] 准备创建食材: {"code":"SDCK1461","name":"\u83b4\u7b0b","specification":"","unit":"\u65a4","category_id":10,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u6839\u830e\u7c7b","shelf_life":"1\u5929","shelf_life_days":1,"min_stock":10,"created_by":1,"status":1,"created_at":"2025-06-29 07:44:51"}
[2025-06-29 07:44:51] ✅ 食材创建成功: ID=148
[2025-06-29 07:44:51] 第15行: 导入成功
[2025-06-29 07:44:51] --- 处理第16行 ---
[2025-06-29 07:44:51] 第16行字段: 编码='HLBWD2265', 名称='胡萝卜（外地）', 数量='30'
[2025-06-29 07:44:51] 第16行: 开始导入明细
[2025-06-29 07:44:51] 查找食材: 编码='HLBWD2265', 名称='胡萝卜（外地）'
[2025-06-29 07:44:51] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:44:51] 查找分类: 名称='根茎类'
[2025-06-29 07:44:51] ✅ 找到分类: ID=10, 名称='根茎类'
[2025-06-29 07:44:51] 准备创建食材: {"code":"HLBWD2265","name":"\u80e1\u841d\u535c\uff08\u5916\u5730\uff09","specification":"","unit":"\u65a4","category_id":10,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u6839\u830e\u7c7b","shelf_life":"1\u5929","shelf_life_days":1,"min_stock":10,"created_by":1,"status":1,"created_at":"2025-06-29 07:44:51"}
[2025-06-29 07:44:51] ✅ 食材创建成功: ID=149
[2025-06-29 07:44:51] 第16行: 导入成功
[2025-06-29 07:44:51] --- 处理第17行 ---
[2025-06-29 07:44:51] 第17行字段: 编码='BLBG9279', 名称='白萝卜', 数量='30'
[2025-06-29 07:44:51] 第17行: 开始导入明细
[2025-06-29 07:44:51] 查找食材: 编码='BLBG9279', 名称='白萝卜'
[2025-06-29 07:44:51] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:44:51] 查找分类: 名称='根茎类'
[2025-06-29 07:44:51] ✅ 找到分类: ID=10, 名称='根茎类'
[2025-06-29 07:44:51] 准备创建食材: {"code":"BLBG9279","name":"\u767d\u841d\u535c","specification":"","unit":"\u65a4","category_id":10,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u6839\u830e\u7c7b","shelf_life":"1\u5929","shelf_life_days":1,"min_stock":10,"created_by":1,"status":1,"created_at":"2025-06-29 07:44:51"}
[2025-06-29 07:44:51] ✅ 食材创建成功: ID=150
[2025-06-29 07:44:51] 第17行: 导入成功
[2025-06-29 07:44:51] --- 处理第18行 ---
[2025-06-29 07:44:51] 第18行字段: 编码='QCLBDG1821', 名称='芹菜（绿）', 数量='25'
[2025-06-29 07:44:51] 第18行: 开始导入明细
[2025-06-29 07:44:51] 查找食材: 编码='QCLBDG1821', 名称='芹菜（绿）'
[2025-06-29 07:44:51] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:44:51] 查找分类: 名称='根茎类'
[2025-06-29 07:44:51] ✅ 找到分类: ID=10, 名称='根茎类'
[2025-06-29 07:44:51] 准备创建食材: {"code":"QCLBDG1821","name":"\u82b9\u83dc\uff08\u7eff\uff09","specification":"","unit":"\u65a4","category_id":10,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u6839\u830e\u7c7b","shelf_life":"1\u5929","shelf_life_days":1,"min_stock":10,"created_by":1,"status":1,"created_at":"2025-06-29 07:44:51"}
[2025-06-29 07:44:51] ✅ 食材创建成功: ID=151
[2025-06-29 07:44:51] 第18行: 导入成功
[2025-06-29 07:44:51] --- 处理第19行 ---
[2025-06-29 07:44:51] 第19行字段: 编码='XC1624', 名称='香菜', 数量='3'
[2025-06-29 07:44:51] 第19行: 开始导入明细
[2025-06-29 07:44:51] 查找食材: 编码='XC1624', 名称='香菜'
[2025-06-29 07:44:51] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:44:51] 查找分类: 名称='葱姜蒜类'
[2025-06-29 07:44:51] ✅ 找到分类: ID=11, 名称='葱姜蒜类'
[2025-06-29 07:44:51] 准备创建食材: {"code":"XC1624","name":"\u9999\u83dc","specification":"","unit":"\u65a4","category_id":11,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u8471\u59dc\u849c\u7c7b","shelf_life":"1\u5929","shelf_life_days":1,"min_stock":10,"created_by":1,"status":1,"created_at":"2025-06-29 07:44:51"}
[2025-06-29 07:44:51] ✅ 食材创建成功: ID=152
[2025-06-29 07:44:51] 第19行: 导入成功
[2025-06-29 07:44:51] --- 处理第20行 ---
[2025-06-29 07:44:51] 第20行字段: 编码='SMBT0731', 名称='蒜苗', 数量='15'
[2025-06-29 07:44:51] 第20行: 开始导入明细
[2025-06-29 07:44:51] 查找食材: 编码='SMBT0731', 名称='蒜苗'
[2025-06-29 07:44:51] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:44:51] 查找分类: 名称='葱姜蒜类'
[2025-06-29 07:44:52] ✅ 找到分类: ID=11, 名称='葱姜蒜类'
[2025-06-29 07:44:52] 准备创建食材: {"code":"SMBT0731","name":"\u849c\u82d7","specification":"","unit":"\u65a4","category_id":11,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u8471\u59dc\u849c\u7c7b","shelf_life":"1\u5929","shelf_life_days":1,"min_stock":10,"created_by":1,"status":1,"created_at":"2025-06-29 07:44:52"}
[2025-06-29 07:44:52] ✅ 食材创建成功: ID=153
[2025-06-29 07:44:52] 第20行: 导入成功
[2025-06-29 07:44:52] --- 处理第21行 ---
[2025-06-29 07:44:52] 第21行字段: 编码='XCBDDFC7870', 名称='小葱', 数量='10'
[2025-06-29 07:44:52] 第21行: 开始导入明细
[2025-06-29 07:44:52] 查找食材: 编码='XCBDDFC7870', 名称='小葱'
[2025-06-29 07:44:52] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:44:52] 查找分类: 名称='葱姜蒜类'
[2025-06-29 07:44:52] ✅ 找到分类: ID=11, 名称='葱姜蒜类'
[2025-06-29 07:44:52] 准备创建食材: {"code":"XCBDDFC7870","name":"\u5c0f\u8471","specification":"","unit":"\u65a4","category_id":11,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u8471\u59dc\u849c\u7c7b","shelf_life":"1\u5929","shelf_life_days":1,"min_stock":10,"created_by":1,"status":1,"created_at":"2025-06-29 07:44:52"}
[2025-06-29 07:44:52] ✅ 食材创建成功: ID=154
[2025-06-29 07:44:52] 第21行: 导入成功
[2025-06-29 07:44:52] --- 处理第22行 ---
[2025-06-29 07:44:52] 第22行字段: 编码='XJJ0650', 名称='小米椒（小尖椒）', 数量='2'
[2025-06-29 07:44:52] 第22行: 开始导入明细
[2025-06-29 07:44:52] 查找食材: 编码='XJJ0650', 名称='小米椒（小尖椒）'
[2025-06-29 07:44:52] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:44:52] 查找分类: 名称='辣椒类'
[2025-06-29 07:44:52] ✅ 找到分类: ID=12, 名称='辣椒类'
[2025-06-29 07:44:52] 准备创建食材: {"code":"XJJ0650","name":"\u5c0f\u7c73\u6912\uff08\u5c0f\u5c16\u6912\uff09","specification":"","unit":"\u65a4","category_id":12,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u8fa3\u6912\u7c7b","shelf_life":"1\u5929","shelf_life_days":1,"min_stock":10,"created_by":1,"status":1,"created_at":"2025-06-29 07:44:52"}
[2025-06-29 07:44:52] ✅ 食材创建成功: ID=155
[2025-06-29 07:44:52] 第22行: 导入成功
[2025-06-29 07:44:52] --- 处理第23行 ---
[2025-06-29 07:44:52] 第23行字段: 编码='XJ0544', 名称='青线椒', 数量='50'
[2025-06-29 07:44:52] 第23行: 开始导入明细
[2025-06-29 07:44:52] 查找食材: 编码='XJ0544', 名称='青线椒'
[2025-06-29 07:44:52] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:44:52] 查找分类: 名称='辣椒类'
[2025-06-29 07:44:52] ✅ 找到分类: ID=12, 名称='辣椒类'
[2025-06-29 07:44:52] 准备创建食材: {"code":"XJ0544","name":"\u9752\u7ebf\u6912","specification":"","unit":"\u65a4","category_id":12,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u8fa3\u6912\u7c7b","shelf_life":"1\u5929","shelf_life_days":1,"min_stock":10,"created_by":1,"status":1,"created_at":"2025-06-29 07:44:52"}
[2025-06-29 07:44:52] ✅ 食材创建成功: ID=156
[2025-06-29 07:44:52] 第23行: 导入成功
[2025-06-29 07:44:52] --- 处理第24行 ---
[2025-06-29 07:44:52] 第24行字段: 编码='DQJ0496', 名称='大青椒', 数量='20'
[2025-06-29 07:44:52] 第24行: 开始导入明细
[2025-06-29 07:44:52] 查找食材: 编码='DQJ0496', 名称='大青椒'
[2025-06-29 07:44:52] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:44:52] 查找分类: 名称='辣椒类'
[2025-06-29 07:44:52] ✅ 找到分类: ID=12, 名称='辣椒类'
[2025-06-29 07:44:52] 准备创建食材: {"code":"DQJ0496","name":"\u5927\u9752\u6912","specification":"","unit":"\u65a4","category_id":12,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u8fa3\u6912\u7c7b","shelf_life":"1\u5929","shelf_life_days":1,"min_stock":10,"created_by":1,"status":1,"created_at":"2025-06-29 07:44:52"}
[2025-06-29 07:44:52] ✅ 食材创建成功: ID=157
[2025-06-29 07:44:52] 第24行: 导入成功
[2025-06-29 07:44:52] --- 处理第25行 ---
[2025-06-29 07:44:52] 第25行字段: 编码='DHJ6400', 名称='大红椒', 数量='20'
[2025-06-29 07:44:52] 第25行: 开始导入明细
[2025-06-29 07:44:52] 查找食材: 编码='DHJ6400', 名称='大红椒'
[2025-06-29 07:44:52] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:44:52] 查找分类: 名称='辣椒类'
[2025-06-29 07:44:52] ✅ 找到分类: ID=12, 名称='辣椒类'
[2025-06-29 07:44:52] 准备创建食材: {"code":"DHJ6400","name":"\u5927\u7ea2\u6912","specification":"","unit":"\u65a4","category_id":12,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u8fa3\u6912\u7c7b","shelf_life":"1\u5929","shelf_life_days":1,"min_stock":10,"created_by":1,"status":1,"created_at":"2025-06-29 07:44:52"}
[2025-06-29 07:44:52] ✅ 食材创建成功: ID=158
[2025-06-29 07:44:52] 第25行: 导入成功
[2025-06-29 07:44:52] --- 处理第26行 ---
[2025-06-29 07:44:52] 第26行字段: 编码='ZG0206', 名称='猪肝', 数量='25'
[2025-06-29 07:44:52] 第26行: 开始导入明细
[2025-06-29 07:44:52] 查找食材: 编码='ZG0206', 名称='猪肝'
[2025-06-29 07:44:52] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:44:52] 查找分类: 名称='猪肉类'
[2025-06-29 07:44:52] ✅ 找到分类: ID=13, 名称='猪肉类'
[2025-06-29 07:44:52] 准备创建食材: {"code":"ZG0206","name":"\u732a\u809d","specification":"","unit":"\u65a4","category_id":13,"brand":"\u8089\u79bd\u7c7b","origin":"\u732a\u8089\u7c7b","shelf_life":"1\u5929","shelf_life_days":1,"min_stock":10,"created_by":1,"status":1,"created_at":"2025-06-29 07:44:52"}
[2025-06-29 07:44:52] ✅ 食材创建成功: ID=159
[2025-06-29 07:44:52] 第26行: 导入成功
[2025-06-29 07:44:52] --- 处理第27行 ---
[2025-06-29 07:44:52] 第27行字段: 编码='WHR4545', 名称='猪五花肉', 数量='30'
[2025-06-29 07:44:52] 第27行: 开始导入明细
[2025-06-29 07:44:52] 查找食材: 编码='WHR4545', 名称='猪五花肉'
[2025-06-29 07:44:52] ✅ 通过编码找到食材: ID=4, 名称=猪五花肉
[2025-06-29 07:44:52] 第27行: 导入成功
[2025-06-29 07:44:52] --- 处理第28行 ---
[2025-06-29 07:44:52] 第28行字段: 编码='ZQTRQG4200', 名称='猪前后腿肉去骨', 数量='140'
[2025-06-29 07:44:52] 第28行: 开始导入明细
[2025-06-29 07:44:52] 查找食材: 编码='ZQTRQG4200', 名称='猪前后腿肉去骨'
[2025-06-29 07:44:52] ✅ 通过编码找到食材: ID=3, 名称=猪前后腿肉去骨
[2025-06-29 07:44:52] 第28行: 导入成功
[2025-06-29 07:44:52] --- 处理第29行 ---
[2025-06-29 07:44:52] 第29行字段: 编码='ZPGZP8811', 名称='猪排骨（杂排）', 数量='10'
[2025-06-29 07:44:52] 第29行: 开始导入明细
[2025-06-29 07:44:52] 查找食材: 编码='ZPGZP8811', 名称='猪排骨（杂排）'
[2025-06-29 07:44:52] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:44:52] 查找分类: 名称='猪肉类'
[2025-06-29 07:44:52] ✅ 找到分类: ID=13, 名称='猪肉类'
[2025-06-29 07:44:52] 准备创建食材: {"code":"ZPGZP8811","name":"\u732a\u6392\u9aa8\uff08\u6742\u6392\uff09","specification":"","unit":"\u65a4","category_id":13,"brand":"\u8089\u79bd\u7c7b","origin":"\u732a\u8089\u7c7b","shelf_life":"1\u5929","shelf_life_days":1,"min_stock":10,"created_by":1,"status":1,"created_at":"2025-06-29 07:44:52"}
[2025-06-29 07:44:52] ✅ 食材创建成功: ID=160
[2025-06-29 07:44:52] 第29行: 导入成功
[2025-06-29 07:44:52] --- 处理第30行 ---
[2025-06-29 07:44:52] 第30行字段: 编码='SPBM00003294', 名称='牛腩', 数量='30'
[2025-06-29 07:44:52] 第30行: 开始导入明细
[2025-06-29 07:44:52] 查找食材: 编码='SPBM00003294', 名称='牛腩'
[2025-06-29 07:44:52] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:44:52] 查找分类: 名称='牛羊肉类'
[2025-06-29 07:44:52] ✅ 找到分类: ID=14, 名称='牛羊肉类'
[2025-06-29 07:44:52] 准备创建食材: {"code":"SPBM00003294","name":"\u725b\u8169","specification":"","unit":"\u65a4","category_id":14,"brand":"\u8089\u79bd\u7c7b","origin":"\u725b\u7f8a\u8089\u7c7b","shelf_life":"1\u5929","shelf_life_days":1,"min_stock":10,"created_by":1,"status":1,"created_at":"2025-06-29 07:44:52"}
[2025-06-29 07:44:52] ✅ 食材创建成功: ID=161
[2025-06-29 07:44:52] 第30行: 导入成功
[2025-06-29 07:44:52] --- 处理第31行 ---
[2025-06-29 07:44:52] 第31行字段: 编码='WZSNR3317', 名称='牛肉', 数量='20'
[2025-06-29 07:44:52] 第31行: 开始导入明细
[2025-06-29 07:44:52] 查找食材: 编码='WZSNR3317', 名称='牛肉'
[2025-06-29 07:44:52] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:44:52] 查找分类: 名称='牛羊肉类'
[2025-06-29 07:44:52] ✅ 找到分类: ID=14, 名称='牛羊肉类'
[2025-06-29 07:44:52] 准备创建食材: {"code":"WZSNR3317","name":"\u725b\u8089","specification":"","unit":"\u65a4","category_id":14,"brand":"\u8089\u79bd\u7c7b","origin":"\u725b\u7f8a\u8089\u7c7b","shelf_life":"1\u5929","shelf_life_days":1,"min_stock":10,"created_by":1,"status":1,"created_at":"2025-06-29 07:44:52"}
[2025-06-29 07:44:52] ✅ 食材创建成功: ID=162
[2025-06-29 07:44:52] 第31行: 导入成功
[2025-06-29 07:44:52] --- 处理第32行 ---
[2025-06-29 07:44:52] 第32行字段: 编码='SHJ8439', 名称='三黄鸡（白条、无内脏）', 数量='70'
[2025-06-29 07:44:52] 第32行: 开始导入明细
[2025-06-29 07:44:52] 查找食材: 编码='SHJ8439', 名称='三黄鸡（白条、无内脏）'
[2025-06-29 07:44:52] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:44:52] 查找分类: 名称='鸡鸭鹅兔类'
[2025-06-29 07:44:52] ✅ 找到分类: ID=15, 名称='鸡鸭鹅兔类'
[2025-06-29 07:44:52] 准备创建食材: {"code":"SHJ8439","name":"\u4e09\u9ec4\u9e21\uff08\u767d\u6761\u3001\u65e0\u5185\u810f\uff09","specification":"","unit":"\u65a4","category_id":15,"brand":"\u8089\u79bd\u7c7b","origin":"\u9e21\u9e2d\u9e45\u5154\u7c7b","shelf_life":"1\u5929","shelf_life_days":1,"min_stock":10,"created_by":1,"status":1,"created_at":"2025-06-29 07:44:52"}
[2025-06-29 07:44:52] ✅ 食材创建成功: ID=163
[2025-06-29 07:44:52] 第32行: 导入成功
[2025-06-29 07:44:52] --- 处理第33行 ---
[2025-06-29 07:44:52] 第33行字段: 编码='HDY6675', 名称='黄豆芽', 数量='30'
[2025-06-29 07:44:52] 第33行: 开始导入明细
[2025-06-29 07:44:52] 查找食材: 编码='HDY6675', 名称='黄豆芽'
[2025-06-29 07:44:52] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:44:52] 查找分类: 名称='豆制品'
[2025-06-29 07:44:52] ✅ 找到分类: ID=16, 名称='豆制品'
[2025-06-29 07:44:52] 准备创建食材: {"code":"HDY6675","name":"\u9ec4\u8c46\u82bd","specification":"","unit":"\u65a4","category_id":16,"brand":"\u7c89\u9762\u8c46\u5236\u54c1","origin":"\u8c46\u5236\u54c1","shelf_life":"1\u5929","shelf_life_days":1,"min_stock":10,"created_by":1,"status":1,"created_at":"2025-06-29 07:44:52"}
[2025-06-29 07:44:52] ✅ 食材创建成功: ID=164
[2025-06-29 07:44:52] 第33行: 导入成功
[2025-06-29 07:44:52] --- 处理第34行 ---
[2025-06-29 07:44:52] 第34行字段: 编码='SDF2528', 名称='水豆腐', 数量='5'
[2025-06-29 07:44:52] 第34行: 开始导入明细
[2025-06-29 07:44:52] 查找食材: 编码='SDF2528', 名称='水豆腐'
[2025-06-29 07:44:52] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:44:52] 查找分类: 名称='豆制品'
[2025-06-29 07:44:52] ✅ 找到分类: ID=16, 名称='豆制品'
[2025-06-29 07:44:52] 准备创建食材: {"code":"SDF2528","name":"\u6c34\u8c46\u8150","specification":"","unit":"\u677f","category_id":16,"brand":"\u7c89\u9762\u8c46\u5236\u54c1","origin":"\u8c46\u5236\u54c1","shelf_life":"1\u5929","shelf_life_days":1,"min_stock":10,"created_by":1,"status":1,"created_at":"2025-06-29 07:44:52"}
[2025-06-29 07:44:53] ✅ 食材创建成功: ID=165
[2025-06-29 07:44:53] 第34行: 导入成功
[2025-06-29 07:44:53] --- 处理第35行 ---
[2025-06-29 07:44:53] 第35行字段: 编码='GDFP4973', 名称='干豆腐片（薄）', 数量='15'
[2025-06-29 07:44:53] 第35行: 开始导入明细
[2025-06-29 07:44:53] 查找食材: 编码='GDFP4973', 名称='干豆腐片（薄）'
[2025-06-29 07:44:53] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:44:53] 查找分类: 名称='豆制品'
[2025-06-29 07:44:53] ✅ 找到分类: ID=16, 名称='豆制品'
[2025-06-29 07:44:53] 准备创建食材: {"code":"GDFP4973","name":"\u5e72\u8c46\u8150\u7247\uff08\u8584\uff09","specification":"","unit":"\u65a4","category_id":16,"brand":"\u7c89\u9762\u8c46\u5236\u54c1","origin":"\u8c46\u5236\u54c1","shelf_life":"1\u5929","shelf_life_days":1,"min_stock":10,"created_by":1,"status":1,"created_at":"2025-06-29 07:44:53"}
[2025-06-29 07:44:53] ✅ 食材创建成功: ID=166
[2025-06-29 07:44:53] 第35行: 导入成功
[2025-06-29 07:44:53] --- 处理第36行 ---
[2025-06-29 07:44:53] 第36行字段: 编码='GDFHP1223', 名称='干豆腐片（厚）', 数量='40'
[2025-06-29 07:44:53] 第36行: 开始导入明细
[2025-06-29 07:44:53] 查找食材: 编码='GDFHP1223', 名称='干豆腐片（厚）'
[2025-06-29 07:44:53] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:44:53] 查找分类: 名称='豆制品'
[2025-06-29 07:44:53] ✅ 找到分类: ID=16, 名称='豆制品'
[2025-06-29 07:44:53] 准备创建食材: {"code":"GDFHP1223","name":"\u5e72\u8c46\u8150\u7247\uff08\u539a\uff09","specification":"","unit":"\u65a4","category_id":16,"brand":"\u7c89\u9762\u8c46\u5236\u54c1","origin":"\u8c46\u5236\u54c1","shelf_life":"1\u5929","shelf_life_days":1,"min_stock":10,"created_by":1,"status":1,"created_at":"2025-06-29 07:44:53"}
[2025-06-29 07:44:53] ✅ 食材创建成功: ID=167
[2025-06-29 07:44:53] 第36行: 导入成功
[2025-06-29 07:44:53] --- 处理第37行 ---
[2025-06-29 07:44:53] 第37行字段: 编码='YF4729', 名称='圆粉', 数量='200'
[2025-06-29 07:44:53] 第37行: 开始导入明细
[2025-06-29 07:44:53] 查找食材: 编码='YF4729', 名称='圆粉'
[2025-06-29 07:44:53] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:44:53] 查找分类: 名称='粉面制品'
[2025-06-29 07:44:53] ✅ 找到分类: ID=17, 名称='粉面制品'
[2025-06-29 07:44:53] 准备创建食材: {"code":"YF4729","name":"\u5706\u7c89","specification":"","unit":"\u65a4","category_id":17,"brand":"\u7c89\u9762\u8c46\u5236\u54c1","origin":"\u7c89\u9762\u5236\u54c1","shelf_life":"1\u5929","shelf_life_days":1,"min_stock":10,"created_by":1,"status":1,"created_at":"2025-06-29 07:44:53"}
[2025-06-29 07:44:53] ✅ 食材创建成功: ID=168
[2025-06-29 07:44:53] 第37行: 导入成功
[2025-06-29 07:44:53] --- 处理第38行 ---
[2025-06-29 07:44:53] 第38行字段: 编码='BF4586', 名称='扁粉', 数量='350'
[2025-06-29 07:44:53] 第38行: 开始导入明细
[2025-06-29 07:44:53] 查找食材: 编码='BF4586', 名称='扁粉'
[2025-06-29 07:44:53] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:44:53] 查找分类: 名称='粉面制品'
[2025-06-29 07:44:53] ✅ 找到分类: ID=17, 名称='粉面制品'
[2025-06-29 07:44:53] 准备创建食材: {"code":"BF4586","name":"\u6241\u7c89","specification":"","unit":"\u65a4","category_id":17,"brand":"\u7c89\u9762\u8c46\u5236\u54c1","origin":"\u7c89\u9762\u5236\u54c1","shelf_life":"1\u5929","shelf_life_days":1,"min_stock":10,"created_by":1,"status":1,"created_at":"2025-06-29 07:44:53"}
[2025-06-29 07:44:53] ✅ 食材创建成功: ID=169
[2025-06-29 07:44:53] 第38行: 导入成功
[2025-06-29 07:44:53] --- 处理第39行 ---
[2025-06-29 07:44:53] 第39行字段: 编码='YF5478', 名称='油粉', 数量='30'
[2025-06-29 07:44:53] 第39行: 开始导入明细
[2025-06-29 07:44:53] 查找食材: 编码='YF5478', 名称='油粉'
[2025-06-29 07:44:53] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:44:53] 查找分类: 名称='粉面制品'
[2025-06-29 07:44:53] ✅ 找到分类: ID=17, 名称='粉面制品'
[2025-06-29 07:44:53] 准备创建食材: {"code":"YF5478","name":"\u6cb9\u7c89","specification":"","unit":"\u65a4","category_id":17,"brand":"\u7c89\u9762\u8c46\u5236\u54c1","origin":"\u7c89\u9762\u5236\u54c1","shelf_life":"1\u5929","shelf_life_days":1,"min_stock":10,"created_by":1,"status":1,"created_at":"2025-06-29 07:44:53"}
[2025-06-29 07:44:53] ✅ 食材创建成功: ID=170
[2025-06-29 07:44:53] 第39行: 导入成功
[2025-06-29 07:44:53] --- 处理第40行 ---
[2025-06-29 07:44:53] 第40行字段: 编码='BF5568', 名称='薄粉（卷粉皮）', 数量='15'
[2025-06-29 07:44:53] 第40行: 开始导入明细
[2025-06-29 07:44:53] 查找食材: 编码='BF5568', 名称='薄粉（卷粉皮）'
[2025-06-29 07:44:53] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:44:53] 查找分类: 名称='粉面制品'
[2025-06-29 07:44:53] ✅ 找到分类: ID=17, 名称='粉面制品'
[2025-06-29 07:44:53] 准备创建食材: {"code":"BF5568","name":"\u8584\u7c89\uff08\u5377\u7c89\u76ae\uff09","specification":"","unit":"\u65a4","category_id":17,"brand":"\u7c89\u9762\u8c46\u5236\u54c1","origin":"\u7c89\u9762\u5236\u54c1","shelf_life":"1\u5929","shelf_life_days":1,"min_stock":10,"created_by":1,"status":1,"created_at":"2025-06-29 07:44:53"}
[2025-06-29 07:44:53] ✅ 食材创建成功: ID=171
[2025-06-29 07:44:53] 第40行: 导入成功
[2025-06-29 07:44:53] --- 处理第41行 ---
[2025-06-29 07:44:53] 第41行字段: 编码='QTXW2925', 名称='秋田小町25KG', 数量='10'
[2025-06-29 07:44:53] 第41行: 开始导入明细
[2025-06-29 07:44:53] 查找食材: 编码='QTXW2925', 名称='秋田小町25KG'
[2025-06-29 07:44:53] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:44:53] 查找分类: 名称='大米类'
[2025-06-29 07:44:53] ✅ 找到分类: ID=18, 名称='大米类'
[2025-06-29 07:44:53] 准备创建食材: {"code":"QTXW2925","name":"\u79cb\u7530\u5c0f\u753a25KG","specification":"25KG","unit":"\u888b","category_id":18,"brand":"\u7c73\u9762\u7cae\u6cb9","origin":"\u5927\u7c73\u7c7b","shelf_life":"1\u5929","shelf_life_days":1,"min_stock":10,"created_by":1,"status":1,"created_at":"2025-06-29 07:44:53"}
[2025-06-29 07:44:53] ✅ 食材创建成功: ID=172
[2025-06-29 07:44:53] 第41行: 导入成功
[2025-06-29 07:44:53] --- 处理第42行 ---
[2025-06-29 07:44:53] 第42行字段: 编码='GLHLJM1006', 名称='冠霖糊辣椒面', 数量='2'
[2025-06-29 07:44:53] 第42行: 开始导入明细
[2025-06-29 07:44:53] 查找食材: 编码='GLHLJM1006', 名称='冠霖糊辣椒面'
[2025-06-29 07:44:53] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:44:53] 查找分类: 名称='干辣椒花椒面类'
[2025-06-29 07:44:53] ✅ 找到分类: ID=19, 名称='干辣椒花椒面类'
[2025-06-29 07:44:53] 准备创建食材: {"code":"GLHLJM1006","name":"\u51a0\u9716\u7cca\u8fa3\u6912\u9762","specification":"5\u65a4\/\u888b","unit":"\u888b","category_id":19,"brand":"\u8c03\u6599\u5e72\u8d27","origin":"\u5e72\u8fa3\u6912\u82b1\u6912\u9762\u7c7b","shelf_life":"1\u5929","shelf_life_days":1,"min_stock":10,"created_by":1,"status":1,"created_at":"2025-06-29 07:44:53"}
[2025-06-29 07:44:53] ✅ 食材创建成功: ID=173
[2025-06-29 07:44:53] 第42行: 导入成功
[2025-06-29 07:44:53] --- 处理第43行 ---
[2025-06-29 07:44:53] 第43行字段: 编码='DFP8053', 名称='干豆腐皮', 数量='1'
[2025-06-29 07:44:53] 第43行: 开始导入明细
[2025-06-29 07:44:53] 查找食材: 编码='DFP8053', 名称='干豆腐皮'
[2025-06-29 07:44:53] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:44:53] 查找分类: 名称='干货类'
[2025-06-29 07:44:53] ✅ 找到分类: ID=20, 名称='干货类'
[2025-06-29 07:44:53] 准备创建食材: {"code":"DFP8053","name":"\u5e72\u8c46\u8150\u76ae","specification":"\u7ea612.5\u65a4","unit":"\u4ef6","category_id":20,"brand":"\u8c03\u6599\u5e72\u8d27","origin":"\u5e72\u8d27\u7c7b","shelf_life":"1\u5929","shelf_life_days":1,"min_stock":10,"created_by":1,"status":1,"created_at":"2025-06-29 07:44:53"}
[2025-06-29 07:44:53] ✅ 食材创建成功: ID=174
[2025-06-29 07:44:53] 第43行: 导入成功
[2025-06-29 07:44:53] --- 处理第44行 ---
[2025-06-29 07:44:53] 第44行字段: 编码='CS7213', 名称='脆哨', 数量='10'
[2025-06-29 07:44:53] 第44行: 开始导入明细
[2025-06-29 07:44:53] 查找食材: 编码='CS7213', 名称='脆哨'
[2025-06-29 07:44:53] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:44:53] 查找分类: 名称='干货类'
[2025-06-29 07:44:53] ✅ 找到分类: ID=20, 名称='干货类'
[2025-06-29 07:44:53] 准备创建食材: {"code":"CS7213","name":"\u8106\u54e8","specification":"","unit":"\u65a4","category_id":20,"brand":"\u8c03\u6599\u5e72\u8d27","origin":"\u5e72\u8d27\u7c7b","shelf_life":"1\u5929","shelf_life_days":1,"min_stock":10,"created_by":1,"status":1,"created_at":"2025-06-29 07:44:53"}
[2025-06-29 07:44:53] ✅ 食材创建成功: ID=175
[2025-06-29 07:44:53] 第44行: 导入成功
[2025-06-29 07:44:53] --- 处理第45行 ---
[2025-06-29 07:44:53] 第45行字段: 编码='XJ1638', 名称='香蕉', 数量='30'
[2025-06-29 07:44:53] 第45行: 开始导入明细
[2025-06-29 07:44:53] 查找食材: 编码='XJ1638', 名称='香蕉'
[2025-06-29 07:44:53] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:44:53] 查找分类: 名称='时令水果1'
[2025-06-29 07:44:53] ✅ 找到分类: ID=21, 名称='时令水果1'
[2025-06-29 07:44:53] 准备创建食材: {"code":"XJ1638","name":"\u9999\u8549","specification":"","unit":"\u65a4","category_id":21,"brand":"\u65f6\u4ee4\u6c34\u679c","origin":"\u65f6\u4ee4\u6c34\u679c1","shelf_life":"1\u5929","shelf_life_days":1,"min_stock":10,"created_by":1,"status":1,"created_at":"2025-06-29 07:44:53"}
[2025-06-29 07:44:53] ✅ 食材创建成功: ID=176
[2025-06-29 07:44:53] 第45行: 导入成功
[2025-06-29 07:44:53] --- 处理第46行 ---
[2025-06-29 07:44:53] 第46行字段: 编码='HWZTX7875', 名称='茉莉香提', 数量='20'
[2025-06-29 07:44:53] 第46行: 开始导入明细
[2025-06-29 07:44:53] 查找食材: 编码='HWZTX7875', 名称='茉莉香提'
[2025-06-29 07:44:53] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:44:53] 查找分类: 名称='时令水果1'
[2025-06-29 07:44:53] ✅ 找到分类: ID=21, 名称='时令水果1'
[2025-06-29 07:44:53] 准备创建食材: {"code":"HWZTX7875","name":"\u8309\u8389\u9999\u63d0","specification":"","unit":"\u65a4","category_id":21,"brand":"\u65f6\u4ee4\u6c34\u679c","origin":"\u65f6\u4ee4\u6c34\u679c1","shelf_life":"1\u5929","shelf_life_days":1,"min_stock":10,"created_by":1,"status":1,"created_at":"2025-06-29 07:44:53"}
[2025-06-29 07:44:53] ✅ 食材创建成功: ID=177
[2025-06-29 07:44:53] 第46行: 导入成功
[2025-06-29 07:44:53] --- 处理第47行 ---
[2025-06-29 07:44:53] 第47行字段: 编码='HMGXMH7349', 名称='哈密瓜（小蜜25号）大果', 数量='50'
[2025-06-29 07:44:53] 第47行: 开始导入明细
[2025-06-29 07:44:53] 查找食材: 编码='HMGXMH7349', 名称='哈密瓜（小蜜25号）大果'
[2025-06-29 07:44:53] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:44:53] 查找分类: 名称='时令水果1'
[2025-06-29 07:44:54] ✅ 找到分类: ID=21, 名称='时令水果1'
[2025-06-29 07:44:54] 准备创建食材: {"code":"HMGXMH7349","name":"\u54c8\u5bc6\u74dc\uff08\u5c0f\u871c25\u53f7\uff09\u5927\u679c","specification":"\u6c99\u5730\u871c\u74dc\u5927\u679c","unit":"\u65a4","category_id":21,"brand":"\u65f6\u4ee4\u6c34\u679c","origin":"\u65f6\u4ee4\u6c34\u679c1","shelf_life":"1\u5929","shelf_life_days":1,"min_stock":10,"created_by":1,"status":1,"created_at":"2025-06-29 07:44:54"}
[2025-06-29 07:44:54] ✅ 食材创建成功: ID=178
[2025-06-29 07:44:54] 第47行: 导入成功
[2025-06-29 07:44:54] --- 处理第48行 ---
[2025-06-29 07:44:54] 第48行字段: 编码='XD6079', 名称='豇豆（绿）', 数量='40'
[2025-06-29 07:44:54] 第48行: 开始导入明细
[2025-06-29 07:44:54] 查找食材: 编码='XD6079', 名称='豇豆（绿）'
[2025-06-29 07:44:54] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:44:54] 查找分类: 名称='茄果类'
[2025-06-29 07:44:54] ✅ 找到分类: ID=9, 名称='茄果类'
[2025-06-29 07:44:54] 准备创建食材: {"code":"XD6079","name":"\u8c47\u8c46\uff08\u7eff\uff09","specification":"","unit":"\u65a4","category_id":9,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u8304\u679c\u7c7b","shelf_life":"1\u5929","shelf_life_days":1,"min_stock":10,"created_by":1,"status":1,"created_at":"2025-06-29 07:44:54"}
[2025-06-29 07:44:54] ✅ 食材创建成功: ID=179
[2025-06-29 07:44:54] 第48行: 导入成功
[2025-06-29 07:44:54] --- 处理第49行 ---
[2025-06-29 07:44:54] 第49行字段: 编码='XTD9873', 名称='土豆', 数量='250'
[2025-06-29 07:44:54] 第49行: 开始导入明细
[2025-06-29 07:44:54] 查找食材: 编码='XTD9873', 名称='土豆'
[2025-06-29 07:44:54] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:44:54] 查找分类: 名称='根茎类'
[2025-06-29 07:44:54] ✅ 找到分类: ID=10, 名称='根茎类'
[2025-06-29 07:44:54] 准备创建食材: {"code":"XTD9873","name":"\u571f\u8c46","specification":"","unit":"\u65a4","category_id":10,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u6839\u830e\u7c7b","shelf_life":"1\u5929","shelf_life_days":1,"min_stock":10,"created_by":1,"status":1,"created_at":"2025-06-29 07:44:54"}
[2025-06-29 07:44:54] ✅ 食材创建成功: ID=180
[2025-06-29 07:44:54] 第49行: 导入成功
[2025-06-29 07:44:54] --- 处理第50行 ---
[2025-06-29 07:44:54] 第50行字段: 编码='LJ0368', 名称='老姜（带土）', 数量='20'
[2025-06-29 07:44:54] 第50行: 开始导入明细
[2025-06-29 07:44:54] 查找食材: 编码='LJ0368', 名称='老姜（带土）'
[2025-06-29 07:44:54] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:44:54] 查找分类: 名称='葱姜蒜类'
[2025-06-29 07:44:54] ✅ 找到分类: ID=11, 名称='葱姜蒜类'
[2025-06-29 07:44:54] 准备创建食材: {"code":"LJ0368","name":"\u8001\u59dc\uff08\u5e26\u571f\uff09","specification":"","unit":"\u65a4","category_id":11,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u8471\u59dc\u849c\u7c7b","shelf_life":"1\u5929","shelf_life_days":1,"min_stock":10,"created_by":1,"status":1,"created_at":"2025-06-29 07:44:54"}
[2025-06-29 07:44:54] ✅ 食材创建成功: ID=181
[2025-06-29 07:44:54] 第50行: 导入成功
[2025-06-29 07:44:54] --- 处理第51行 ---
[2025-06-29 07:44:54] 第51行字段: 编码='SM0321', 名称='蒜米', 数量='2'
[2025-06-29 07:44:54] 第51行: 开始导入明细
[2025-06-29 07:44:54] 查找食材: 编码='SM0321', 名称='蒜米'
[2025-06-29 07:44:54] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:44:54] 查找分类: 名称='葱姜蒜类'
[2025-06-29 07:44:54] ✅ 找到分类: ID=11, 名称='葱姜蒜类'
[2025-06-29 07:44:54] 准备创建食材: {"code":"SM0321","name":"\u849c\u7c73","specification":"","unit":"\u888b","category_id":11,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u8471\u59dc\u849c\u7c7b","shelf_life":"1\u5929","shelf_life_days":1,"min_stock":10,"created_by":1,"status":1,"created_at":"2025-06-29 07:44:54"}
[2025-06-29 07:44:54] ✅ 食材创建成功: ID=182
[2025-06-29 07:44:54] 第51行: 导入成功
[2025-06-29 07:44:54] --- 处理第52行 ---
[2025-06-29 07:44:54] 第52行字段: 编码='SPBM00003264', 名称='猪里脊肉', 数量='30'
[2025-06-29 07:44:54] 第52行: 开始导入明细
[2025-06-29 07:44:54] 查找食材: 编码='SPBM00003264', 名称='猪里脊肉'
[2025-06-29 07:44:54] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:44:54] 查找分类: 名称='猪肉类'
[2025-06-29 07:44:54] ✅ 找到分类: ID=13, 名称='猪肉类'
[2025-06-29 07:44:54] 准备创建食材: {"code":"SPBM00003264","name":"\u732a\u91cc\u810a\u8089","specification":"","unit":"\u65a4","category_id":13,"brand":"\u8089\u79bd\u7c7b","origin":"\u732a\u8089\u7c7b","shelf_life":"1\u5929","shelf_life_days":1,"min_stock":10,"created_by":1,"status":1,"created_at":"2025-06-29 07:44:54"}
[2025-06-29 07:44:54] ✅ 食材创建成功: ID=183
[2025-06-29 07:44:54] 第52行: 导入成功
[2025-06-29 07:44:54] --- 处理第53行 ---
[2025-06-29 07:44:54] 第53行字段: 编码='HYML4819', 名称='黄玉米粒（新鲜）', 数量='30'
[2025-06-29 07:44:54] 第53行: 开始导入明细
[2025-06-29 07:44:54] 查找食材: 编码='HYML4819', 名称='黄玉米粒（新鲜）'
[2025-06-29 07:44:54] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:44:54] 查找分类: 名称='茄果类'
[2025-06-29 07:44:54] ✅ 找到分类: ID=9, 名称='茄果类'
[2025-06-29 07:44:54] 准备创建食材: {"code":"HYML4819","name":"\u9ec4\u7389\u7c73\u7c92\uff08\u65b0\u9c9c\uff09","specification":"","unit":"\u65a4","category_id":9,"brand":"\u65b0\u9c9c\u852c\u83dc","origin":"\u8304\u679c\u7c7b","shelf_life":"1\u5929","shelf_life_days":1,"min_stock":10,"created_by":1,"status":1,"created_at":"2025-06-29 07:44:54"}
[2025-06-29 07:44:54] ✅ 食材创建成功: ID=184
[2025-06-29 07:44:54] 第53行: 导入成功
[2025-06-29 07:44:54] --- 处理第54行 ---
[2025-06-29 07:44:54] 第54行字段: 编码='', 名称='', 数量=''
[2025-06-29 07:44:54] 第54行: 关键字段为空，跳过
[2025-06-29 07:44:54] --- 处理第55行 ---
[2025-06-29 07:44:54] 第55行: 空行，跳过
[2025-06-29 07:44:54] --- 处理第56行 ---
[2025-06-29 07:44:54] 第56行: 空行，跳过
[2025-06-29 07:44:54] --- 处理第57行 ---
[2025-06-29 07:44:54] 第57行: 空行，跳过
[2025-06-29 07:44:54] --- 处理第58行 ---
[2025-06-29 07:44:54] 第58行: 空行，跳过
[2025-06-29 07:44:54] --- 处理第59行 ---
[2025-06-29 07:44:54] 第59行: 空行，跳过
[2025-06-29 07:44:54] --- 处理第60行 ---
[2025-06-29 07:44:54] 第60行: 空行，跳过
[2025-06-29 07:44:54] --- 处理第61行 ---
[2025-06-29 07:44:54] 第61行: 空行，跳过
[2025-06-29 07:44:54] --- 处理第62行 ---
[2025-06-29 07:44:54] 第62行: 空行，跳过
[2025-06-29 07:44:54] --- 处理第63行 ---
[2025-06-29 07:44:54] 第63行: 空行，跳过
[2025-06-29 07:44:54] --- 处理第64行 ---
[2025-06-29 07:44:54] 第64行: 空行，跳过
[2025-06-29 07:44:54] --- 处理第65行 ---
[2025-06-29 07:44:54] 第65行: 空行，跳过
[2025-06-29 07:44:54] --- 处理第66行 ---
[2025-06-29 07:44:54] 第66行: 空行，跳过
[2025-06-29 07:44:54] --- 处理第67行 ---
[2025-06-29 07:44:54] 第67行: 空行，跳过
[2025-06-29 07:44:54] --- 处理第68行 ---
[2025-06-29 07:44:54] 第68行: 空行，跳过
[2025-06-29 07:44:54] --- 处理第69行 ---
[2025-06-29 07:44:54] 第69行: 空行，跳过
[2025-06-29 07:44:54] --- 处理第70行 ---
[2025-06-29 07:44:54] 第70行: 空行，跳过
[2025-06-29 07:44:54] --- 处理第71行 ---
[2025-06-29 07:44:54] 第71行: 空行，跳过
[2025-06-29 07:44:54] --- 处理第72行 ---
[2025-06-29 07:44:54] 第72行: 空行，跳过
[2025-06-29 07:44:54] --- 处理第73行 ---
[2025-06-29 07:44:54] 第73行: 空行，跳过
[2025-06-29 07:44:54] --- 处理第74行 ---
[2025-06-29 07:44:54] 第74行: 空行，跳过
[2025-06-29 07:44:54] --- 处理第75行 ---
[2025-06-29 07:44:54] 第75行: 空行，跳过
[2025-06-29 07:44:54] --- 处理第76行 ---
[2025-06-29 07:44:54] 第76行: 空行，跳过
[2025-06-29 07:44:54] --- 处理第77行 ---
[2025-06-29 07:44:54] 第77行: 空行，跳过
[2025-06-29 07:44:54] --- 处理第78行 ---
[2025-06-29 07:44:54] 第78行: 空行，跳过
[2025-06-29 07:44:54] --- 处理第79行 ---
[2025-06-29 07:44:54] 第79行: 空行，跳过
[2025-06-29 07:44:54] --- 处理第80行 ---
[2025-06-29 07:44:54] 第80行: 空行，跳过
[2025-06-29 07:44:54] --- 处理第81行 ---
[2025-06-29 07:44:54] 第81行: 空行，跳过
[2025-06-29 07:44:54] --- 处理第82行 ---
[2025-06-29 07:44:54] 第82行: 空行，跳过
[2025-06-29 07:44:54] --- 处理第83行 ---
[2025-06-29 07:44:54] 第83行: 空行，跳过
[2025-06-29 07:44:54] --- 处理第84行 ---
[2025-06-29 07:44:54] 第84行: 空行，跳过
[2025-06-29 07:44:54] --- 处理第85行 ---
[2025-06-29 07:44:54] 第85行: 空行，跳过
[2025-06-29 07:44:54] --- 处理第86行 ---
[2025-06-29 07:44:54] 第86行: 空行，跳过
[2025-06-29 07:44:54] --- 处理第87行 ---
[2025-06-29 07:44:54] 第87行: 空行，跳过
[2025-06-29 07:44:54] --- 处理第88行 ---
[2025-06-29 07:44:54] 第88行: 空行，跳过
[2025-06-29 07:44:54] --- 处理第89行 ---
[2025-06-29 07:44:54] 第89行: 空行，跳过
[2025-06-29 07:44:54] --- 处理第90行 ---
[2025-06-29 07:44:54] 第90行: 空行，跳过
[2025-06-29 07:44:54] --- 处理第91行 ---
[2025-06-29 07:44:54] 第91行: 空行，跳过
[2025-06-29 07:44:54] --- 处理第92行 ---
[2025-06-29 07:44:54] 第92行: 空行，跳过
[2025-06-29 07:44:54] --- 处理第93行 ---
[2025-06-29 07:44:54] 第93行: 空行，跳过
[2025-06-29 07:44:54] --- 处理第94行 ---
[2025-06-29 07:44:54] 第94行: 空行，跳过
[2025-06-29 07:44:54] --- 处理第95行 ---
[2025-06-29 07:44:54] 第95行: 空行，跳过
[2025-06-29 07:44:54] --- 处理第96行 ---
[2025-06-29 07:44:54] 第96行: 空行，跳过
[2025-06-29 07:44:54] --- 处理第97行 ---
[2025-06-29 07:44:54] 第97行: 空行，跳过
[2025-06-29 07:44:54] --- 处理第98行 ---
[2025-06-29 07:44:54] 第98行: 空行，跳过
[2025-06-29 07:44:54] --- 处理第99行 ---
[2025-06-29 07:44:54] 第99行: 空行，跳过
[2025-06-29 07:44:54] --- 处理第100行 ---
[2025-06-29 07:44:54] 第100行: 空行，跳过
[2025-06-29 07:44:54] === 明细数据处理完成 ===
[2025-06-29 07:44:54] 成功: 46, 失败: 1
[2025-06-29 07:45:10] 查找食材: 编码='TEST001', 名称='测试食材1'
[2025-06-29 07:45:10] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:45:10] 分类名称为空，使用默认分类ID=1
[2025-06-29 07:45:10] 准备创建食材: {"code":"TEST001","name":"\u6d4b\u8bd5\u98df\u67501","specification":"500g","unit":"\u5305","category_id":1,"brand":"\u6d4b\u8bd5\u54c1\u724c","origin":"\u6d4b\u8bd5\u4ea7\u5730","shelf_life":"1\u5929","shelf_life_days":1,"min_stock":10,"created_by":1,"status":1,"created_at":"2025-06-29 07:45:10"}
[2025-06-29 07:45:10] ✅ 食材创建成功: ID=186
[2025-06-29 07:45:10] 查找食材: 编码='TEST002', 名称='测试食材2'
[2025-06-29 07:45:10] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:45:10] 分类名称为空，使用默认分类ID=1
[2025-06-29 07:45:10] 准备创建食材: {"code":"TEST002","name":"\u6d4b\u8bd5\u98df\u67502","specification":"1kg","unit":"\u888b","category_id":1,"brand":"","origin":"","shelf_life":"1\u5929","shelf_life_days":1,"min_stock":10,"created_by":1,"status":1,"created_at":"2025-06-29 07:45:10"}
[2025-06-29 07:45:10] ✅ 食材创建成功: ID=187
[2025-06-29 07:45:10] 查找食材: 编码='', 名称='无编码测试食材'
[2025-06-29 07:45:10] ⚠️ 食材不存在，开始自动创建
[2025-06-29 07:45:10] 分类名称为空，使用默认分类ID=1
[2025-06-29 07:45:10] 准备创建食材: {"code":"AUTO_1751183110_3055","name":"\u65e0\u7f16\u7801\u6d4b\u8bd5\u98df\u6750","specification":"","unit":"\u4e2a","category_id":1,"brand":"","origin":"","shelf_life":"1\u5929","shelf_life_days":1,"min_stock":10,"created_by":1,"status":1,"created_at":"2025-06-29 07:45:10"}
[2025-06-29 07:45:10] ✅ 食材创建成功: ID=188
[2025-06-29 07:46:29] === 开始处理明细数据 ===
[2025-06-29 07:46:29] 数据总行数: 100
[2025-06-29 07:46:29] 检查第6行第1列: '商品明细'
[2025-06-29 07:46:29] 找到明细标题行: 第6行
[2025-06-29 07:46:29] 明细数据从第7行开始处理
[2025-06-29 07:46:29] --- 处理第7行 ---
[2025-06-29 07:46:29] 第7行字段: 编码='商品编码', 名称='商品名称', 数量='下单数量'
[2025-06-29 07:46:29] 第7行: 开始导入明细
[2025-06-29 07:46:29] 第7行: 导入失败 - 第7行: 数量必须大于0（第9列）
[2025-06-29 07:46:29] --- 处理第8行 ---
[2025-06-29 07:46:29] 第8行字段: 编码='DBC1685', 名称='大白菜', 数量='40'
[2025-06-29 07:46:29] 第8行: 开始导入明细
[2025-06-29 07:46:29] 查找食材: 编码='DBC1685', 名称='大白菜'
[2025-06-29 07:46:29] ✅ 通过编码找到食材: ID=141, 名称=大白菜
[2025-06-29 07:46:29] 第8行: 导入成功
[2025-06-29 07:46:29] --- 处理第9行 ---
[2025-06-29 07:46:29] 第9行字段: 编码='LHB1033', 名称='莲花白（平顶）', 数量='40'
[2025-06-29 07:46:29] 第9行: 开始导入明细
[2025-06-29 07:46:29] 查找食材: 编码='LHB1033', 名称='莲花白（平顶）'
[2025-06-29 07:46:29] ✅ 通过编码找到食材: ID=142, 名称=莲花白（平顶）
[2025-06-29 07:46:29] 第9行: 导入成功
[2025-06-29 07:46:29] --- 处理第10行 ---
[2025-06-29 07:46:29] 第10行字段: 编码='XLH1581', 名称='西兰花', 数量='40'
[2025-06-29 07:46:29] 第10行: 开始导入明细
[2025-06-29 07:46:29] 查找食材: 编码='XLH1581', 名称='西兰花'
[2025-06-29 07:46:29] ✅ 通过编码找到食材: ID=143, 名称=西兰花
[2025-06-29 07:46:29] 第10行: 导入成功
[2025-06-29 07:46:29] --- 处理第11行 ---
[2025-06-29 07:46:29] 第11行字段: 编码='XHS1526', 名称='西红柿', 数量='60'
[2025-06-29 07:46:29] 第11行: 开始导入明细
[2025-06-29 07:46:29] 查找食材: 编码='XHS1526', 名称='西红柿'
[2025-06-29 07:46:29] ✅ 通过编码找到食材: ID=144, 名称=西红柿
[2025-06-29 07:46:29] 第11行: 导入成功
[2025-06-29 07:46:29] --- 处理第12行 ---
[2025-06-29 07:46:29] 第12行字段: 编码='WDHG7779', 名称='外地黄瓜', 数量='60'
[2025-06-29 07:46:29] 第12行: 开始导入明细
[2025-06-29 07:46:29] 查找食材: 编码='WDHG7779', 名称='外地黄瓜'
[2025-06-29 07:46:29] ✅ 通过编码找到食材: ID=145, 名称=外地黄瓜
[2025-06-29 07:46:29] 第12行: 导入成功
[2025-06-29 07:46:29] --- 处理第13行 ---
[2025-06-29 07:46:29] 第13行字段: 编码='XNGC2635', 名称='小南瓜（长）', 数量='40'
[2025-06-29 07:46:29] 第13行: 开始导入明细
[2025-06-29 07:46:29] 查找食材: 编码='XNGC2635', 名称='小南瓜（长）'
[2025-06-29 07:46:29] ✅ 通过编码找到食材: ID=146, 名称=小南瓜（长）
[2025-06-29 07:46:29] 第13行: 导入成功
[2025-06-29 07:46:29] --- 处理第14行 ---
[2025-06-29 07:46:29] 第14行字段: 编码='dasd12', 名称='黄玉米棒', 数量='50'
[2025-06-29 07:46:29] 第14行: 开始导入明细
[2025-06-29 07:46:29] 查找食材: 编码='dasd12', 名称='黄玉米棒'
[2025-06-29 07:46:29] ✅ 通过编码找到食材: ID=147, 名称=黄玉米棒
[2025-06-29 07:46:29] 第14行: 导入成功
[2025-06-29 07:46:29] --- 处理第15行 ---
[2025-06-29 07:46:29] 第15行字段: 编码='SDCK1461', 名称='莴笋', 数量='50'
[2025-06-29 07:46:29] 第15行: 开始导入明细
[2025-06-29 07:46:29] 查找食材: 编码='SDCK1461', 名称='莴笋'
[2025-06-29 07:46:29] ✅ 通过编码找到食材: ID=148, 名称=莴笋
[2025-06-29 07:46:29] 第15行: 导入成功
[2025-06-29 07:46:29] --- 处理第16行 ---
[2025-06-29 07:46:29] 第16行字段: 编码='HLBWD2265', 名称='胡萝卜（外地）', 数量='30'
[2025-06-29 07:46:29] 第16行: 开始导入明细
[2025-06-29 07:46:29] 查找食材: 编码='HLBWD2265', 名称='胡萝卜（外地）'
[2025-06-29 07:46:29] ✅ 通过编码找到食材: ID=149, 名称=胡萝卜（外地）
[2025-06-29 07:46:29] 第16行: 导入成功
[2025-06-29 07:46:29] --- 处理第17行 ---
[2025-06-29 07:46:29] 第17行字段: 编码='BLBG9279', 名称='白萝卜', 数量='30'
[2025-06-29 07:46:29] 第17行: 开始导入明细
[2025-06-29 07:46:29] 查找食材: 编码='BLBG9279', 名称='白萝卜'
[2025-06-29 07:46:29] ✅ 通过编码找到食材: ID=150, 名称=白萝卜
[2025-06-29 07:46:29] 第17行: 导入成功
[2025-06-29 07:46:29] --- 处理第18行 ---
[2025-06-29 07:46:29] 第18行字段: 编码='QCLBDG1821', 名称='芹菜（绿）', 数量='25'
[2025-06-29 07:46:29] 第18行: 开始导入明细
[2025-06-29 07:46:29] 查找食材: 编码='QCLBDG1821', 名称='芹菜（绿）'
[2025-06-29 07:46:29] ✅ 通过编码找到食材: ID=151, 名称=芹菜（绿）
[2025-06-29 07:46:29] 第18行: 导入成功
[2025-06-29 07:46:29] --- 处理第19行 ---
[2025-06-29 07:46:29] 第19行字段: 编码='XC1624', 名称='香菜', 数量='3'
[2025-06-29 07:46:29] 第19行: 开始导入明细
[2025-06-29 07:46:29] 查找食材: 编码='XC1624', 名称='香菜'
[2025-06-29 07:46:29] ✅ 通过编码找到食材: ID=152, 名称=香菜
[2025-06-29 07:46:29] 第19行: 导入成功
[2025-06-29 07:46:29] --- 处理第20行 ---
[2025-06-29 07:46:29] 第20行字段: 编码='SMBT0731', 名称='蒜苗', 数量='15'
[2025-06-29 07:46:29] 第20行: 开始导入明细
[2025-06-29 07:46:29] 查找食材: 编码='SMBT0731', 名称='蒜苗'
[2025-06-29 07:46:29] ✅ 通过编码找到食材: ID=153, 名称=蒜苗
[2025-06-29 07:46:29] 第20行: 导入成功
[2025-06-29 07:46:29] --- 处理第21行 ---
[2025-06-29 07:46:29] 第21行字段: 编码='XCBDDFC7870', 名称='小葱', 数量='10'
[2025-06-29 07:46:29] 第21行: 开始导入明细
[2025-06-29 07:46:29] 查找食材: 编码='XCBDDFC7870', 名称='小葱'
[2025-06-29 07:46:29] ✅ 通过编码找到食材: ID=154, 名称=小葱
[2025-06-29 07:46:29] 第21行: 导入成功
[2025-06-29 07:46:29] --- 处理第22行 ---
[2025-06-29 07:46:29] 第22行字段: 编码='XJJ0650', 名称='小米椒（小尖椒）', 数量='2'
[2025-06-29 07:46:29] 第22行: 开始导入明细
[2025-06-29 07:46:29] 查找食材: 编码='XJJ0650', 名称='小米椒（小尖椒）'
[2025-06-29 07:46:29] ✅ 通过编码找到食材: ID=155, 名称=小米椒（小尖椒）
[2025-06-29 07:46:29] 第22行: 导入成功
[2025-06-29 07:46:29] --- 处理第23行 ---
[2025-06-29 07:46:29] 第23行字段: 编码='XJ0544', 名称='青线椒', 数量='50'
[2025-06-29 07:46:29] 第23行: 开始导入明细
[2025-06-29 07:46:29] 查找食材: 编码='XJ0544', 名称='青线椒'
[2025-06-29 07:46:29] ✅ 通过编码找到食材: ID=156, 名称=青线椒
[2025-06-29 07:46:29] 第23行: 导入成功
[2025-06-29 07:46:29] --- 处理第24行 ---
[2025-06-29 07:46:29] 第24行字段: 编码='DQJ0496', 名称='大青椒', 数量='20'
[2025-06-29 07:46:29] 第24行: 开始导入明细
[2025-06-29 07:46:29] 查找食材: 编码='DQJ0496', 名称='大青椒'
[2025-06-29 07:46:29] ✅ 通过编码找到食材: ID=157, 名称=大青椒
[2025-06-29 07:46:30] 第24行: 导入成功
[2025-06-29 07:46:30] --- 处理第25行 ---
[2025-06-29 07:46:30] 第25行字段: 编码='DHJ6400', 名称='大红椒', 数量='20'
[2025-06-29 07:46:30] 第25行: 开始导入明细
[2025-06-29 07:46:30] 查找食材: 编码='DHJ6400', 名称='大红椒'
[2025-06-29 07:46:30] ✅ 通过编码找到食材: ID=158, 名称=大红椒
[2025-06-29 07:46:30] 第25行: 导入成功
[2025-06-29 07:46:30] --- 处理第26行 ---
[2025-06-29 07:46:30] 第26行字段: 编码='ZG0206', 名称='猪肝', 数量='25'
[2025-06-29 07:46:30] 第26行: 开始导入明细
[2025-06-29 07:46:30] 查找食材: 编码='ZG0206', 名称='猪肝'
[2025-06-29 07:46:30] ✅ 通过编码找到食材: ID=159, 名称=猪肝
[2025-06-29 07:46:30] 第26行: 导入成功
[2025-06-29 07:46:30] --- 处理第27行 ---
[2025-06-29 07:46:30] 第27行字段: 编码='WHR4545', 名称='猪五花肉', 数量='30'
[2025-06-29 07:46:30] 第27行: 开始导入明细
[2025-06-29 07:46:30] 查找食材: 编码='WHR4545', 名称='猪五花肉'
[2025-06-29 07:46:30] ✅ 通过编码找到食材: ID=4, 名称=猪五花肉
[2025-06-29 07:46:30] 第27行: 导入成功
[2025-06-29 07:46:30] --- 处理第28行 ---
[2025-06-29 07:46:30] 第28行字段: 编码='ZQTRQG4200', 名称='猪前后腿肉去骨', 数量='140'
[2025-06-29 07:46:30] 第28行: 开始导入明细
[2025-06-29 07:46:30] 查找食材: 编码='ZQTRQG4200', 名称='猪前后腿肉去骨'
[2025-06-29 07:46:30] ✅ 通过编码找到食材: ID=3, 名称=猪前后腿肉去骨
[2025-06-29 07:46:30] 第28行: 导入成功
[2025-06-29 07:46:30] --- 处理第29行 ---
[2025-06-29 07:46:30] 第29行字段: 编码='ZPGZP8811', 名称='猪排骨（杂排）', 数量='10'
[2025-06-29 07:46:30] 第29行: 开始导入明细
[2025-06-29 07:46:30] 查找食材: 编码='ZPGZP8811', 名称='猪排骨（杂排）'
[2025-06-29 07:46:30] ✅ 通过编码找到食材: ID=160, 名称=猪排骨（杂排）
[2025-06-29 07:46:30] 第29行: 导入成功
[2025-06-29 07:46:30] --- 处理第30行 ---
[2025-06-29 07:46:30] 第30行字段: 编码='SPBM00003294', 名称='牛腩', 数量='30'
[2025-06-29 07:46:30] 第30行: 开始导入明细
[2025-06-29 07:46:30] 查找食材: 编码='SPBM00003294', 名称='牛腩'
[2025-06-29 07:46:30] ✅ 通过编码找到食材: ID=161, 名称=牛腩
[2025-06-29 07:46:30] 第30行: 导入成功
[2025-06-29 07:46:30] --- 处理第31行 ---
[2025-06-29 07:46:30] 第31行字段: 编码='WZSNR3317', 名称='牛肉', 数量='20'
[2025-06-29 07:46:30] 第31行: 开始导入明细
[2025-06-29 07:46:30] 查找食材: 编码='WZSNR3317', 名称='牛肉'
[2025-06-29 07:46:30] ✅ 通过编码找到食材: ID=162, 名称=牛肉
[2025-06-29 07:46:30] 第31行: 导入成功
[2025-06-29 07:46:30] --- 处理第32行 ---
[2025-06-29 07:46:30] 第32行字段: 编码='SHJ8439', 名称='三黄鸡（白条、无内脏）', 数量='70'
[2025-06-29 07:46:30] 第32行: 开始导入明细
[2025-06-29 07:46:30] 查找食材: 编码='SHJ8439', 名称='三黄鸡（白条、无内脏）'
[2025-06-29 07:46:30] ✅ 通过编码找到食材: ID=163, 名称=三黄鸡（白条、无内脏）
[2025-06-29 07:46:30] 第32行: 导入成功
[2025-06-29 07:46:30] --- 处理第33行 ---
[2025-06-29 07:46:30] 第33行字段: 编码='HDY6675', 名称='黄豆芽', 数量='30'
[2025-06-29 07:46:30] 第33行: 开始导入明细
[2025-06-29 07:46:30] 查找食材: 编码='HDY6675', 名称='黄豆芽'
[2025-06-29 07:46:30] ✅ 通过编码找到食材: ID=164, 名称=黄豆芽
[2025-06-29 07:46:30] 第33行: 导入成功
[2025-06-29 07:46:30] --- 处理第34行 ---
[2025-06-29 07:46:30] 第34行字段: 编码='SDF2528', 名称='水豆腐', 数量='5'
[2025-06-29 07:46:30] 第34行: 开始导入明细
[2025-06-29 07:46:30] 查找食材: 编码='SDF2528', 名称='水豆腐'
[2025-06-29 07:46:30] ✅ 通过编码找到食材: ID=165, 名称=水豆腐
[2025-06-29 07:46:30] 第34行: 导入成功
[2025-06-29 07:46:30] --- 处理第35行 ---
[2025-06-29 07:46:30] 第35行字段: 编码='GDFP4973', 名称='干豆腐片（薄）', 数量='15'
[2025-06-29 07:46:30] 第35行: 开始导入明细
[2025-06-29 07:46:30] 查找食材: 编码='GDFP4973', 名称='干豆腐片（薄）'
[2025-06-29 07:46:30] ✅ 通过编码找到食材: ID=166, 名称=干豆腐片（薄）
[2025-06-29 07:46:30] 第35行: 导入成功
[2025-06-29 07:46:30] --- 处理第36行 ---
[2025-06-29 07:46:30] 第36行字段: 编码='GDFHP1223', 名称='干豆腐片（厚）', 数量='40'
[2025-06-29 07:46:30] 第36行: 开始导入明细
[2025-06-29 07:46:30] 查找食材: 编码='GDFHP1223', 名称='干豆腐片（厚）'
[2025-06-29 07:46:30] ✅ 通过编码找到食材: ID=167, 名称=干豆腐片（厚）
[2025-06-29 07:46:30] 第36行: 导入成功
[2025-06-29 07:46:30] --- 处理第37行 ---
[2025-06-29 07:46:30] 第37行字段: 编码='YF4729', 名称='圆粉', 数量='200'
[2025-06-29 07:46:30] 第37行: 开始导入明细
[2025-06-29 07:46:30] 查找食材: 编码='YF4729', 名称='圆粉'
[2025-06-29 07:46:30] ✅ 通过编码找到食材: ID=168, 名称=圆粉
[2025-06-29 07:46:30] 第37行: 导入成功
[2025-06-29 07:46:30] --- 处理第38行 ---
[2025-06-29 07:46:30] 第38行字段: 编码='BF4586', 名称='扁粉', 数量='350'
[2025-06-29 07:46:30] 第38行: 开始导入明细
[2025-06-29 07:46:30] 查找食材: 编码='BF4586', 名称='扁粉'
[2025-06-29 07:46:30] ✅ 通过编码找到食材: ID=169, 名称=扁粉
[2025-06-29 07:46:30] 第38行: 导入成功
[2025-06-29 07:46:30] --- 处理第39行 ---
[2025-06-29 07:46:30] 第39行字段: 编码='YF5478', 名称='油粉', 数量='30'
[2025-06-29 07:46:30] 第39行: 开始导入明细
[2025-06-29 07:46:30] 查找食材: 编码='YF5478', 名称='油粉'
[2025-06-29 07:46:30] ✅ 通过编码找到食材: ID=170, 名称=油粉
[2025-06-29 07:46:30] 第39行: 导入成功
[2025-06-29 07:46:30] --- 处理第40行 ---
[2025-06-29 07:46:30] 第40行字段: 编码='BF5568', 名称='薄粉（卷粉皮）', 数量='15'
[2025-06-29 07:46:30] 第40行: 开始导入明细
[2025-06-29 07:46:30] 查找食材: 编码='BF5568', 名称='薄粉（卷粉皮）'
[2025-06-29 07:46:30] ✅ 通过编码找到食材: ID=171, 名称=薄粉（卷粉皮）
[2025-06-29 07:46:30] 第40行: 导入成功
[2025-06-29 07:46:30] --- 处理第41行 ---
[2025-06-29 07:46:30] 第41行字段: 编码='QTXW2925', 名称='秋田小町25KG', 数量='10'
[2025-06-29 07:46:30] 第41行: 开始导入明细
[2025-06-29 07:46:30] 查找食材: 编码='QTXW2925', 名称='秋田小町25KG'
[2025-06-29 07:46:30] ✅ 通过编码找到食材: ID=172, 名称=秋田小町25KG
[2025-06-29 07:46:30] 第41行: 导入成功
[2025-06-29 07:46:30] --- 处理第42行 ---
[2025-06-29 07:46:30] 第42行字段: 编码='GLHLJM1006', 名称='冠霖糊辣椒面', 数量='2'
[2025-06-29 07:46:30] 第42行: 开始导入明细
[2025-06-29 07:46:30] 查找食材: 编码='GLHLJM1006', 名称='冠霖糊辣椒面'
[2025-06-29 07:46:30] ✅ 通过编码找到食材: ID=173, 名称=冠霖糊辣椒面
[2025-06-29 07:46:30] 第42行: 导入成功
[2025-06-29 07:46:30] --- 处理第43行 ---
[2025-06-29 07:46:30] 第43行字段: 编码='DFP8053', 名称='干豆腐皮', 数量='1'
[2025-06-29 07:46:30] 第43行: 开始导入明细
[2025-06-29 07:46:30] 查找食材: 编码='DFP8053', 名称='干豆腐皮'
[2025-06-29 07:46:30] ✅ 通过编码找到食材: ID=174, 名称=干豆腐皮
[2025-06-29 07:46:30] 第43行: 导入成功
[2025-06-29 07:46:30] --- 处理第44行 ---
[2025-06-29 07:46:30] 第44行字段: 编码='CS7213', 名称='脆哨', 数量='10'
[2025-06-29 07:46:30] 第44行: 开始导入明细
[2025-06-29 07:46:30] 查找食材: 编码='CS7213', 名称='脆哨'
[2025-06-29 07:46:30] ✅ 通过编码找到食材: ID=175, 名称=脆哨
[2025-06-29 07:46:30] 第44行: 导入成功
[2025-06-29 07:46:30] --- 处理第45行 ---
[2025-06-29 07:46:30] 第45行字段: 编码='XJ1638', 名称='香蕉', 数量='30'
[2025-06-29 07:46:30] 第45行: 开始导入明细
[2025-06-29 07:46:30] 查找食材: 编码='XJ1638', 名称='香蕉'
[2025-06-29 07:46:30] ✅ 通过编码找到食材: ID=176, 名称=香蕉
[2025-06-29 07:46:30] 第45行: 导入成功
[2025-06-29 07:46:30] --- 处理第46行 ---
[2025-06-29 07:46:30] 第46行字段: 编码='HWZTX7875', 名称='茉莉香提', 数量='20'
[2025-06-29 07:46:30] 第46行: 开始导入明细
[2025-06-29 07:46:30] 查找食材: 编码='HWZTX7875', 名称='茉莉香提'
[2025-06-29 07:46:30] ✅ 通过编码找到食材: ID=177, 名称=茉莉香提
[2025-06-29 07:46:30] 第46行: 导入成功
[2025-06-29 07:46:30] --- 处理第47行 ---
[2025-06-29 07:46:30] 第47行字段: 编码='HMGXMH7349', 名称='哈密瓜（小蜜25号）大果', 数量='50'
[2025-06-29 07:46:30] 第47行: 开始导入明细
[2025-06-29 07:46:30] 查找食材: 编码='HMGXMH7349', 名称='哈密瓜（小蜜25号）大果'
[2025-06-29 07:46:30] ✅ 通过编码找到食材: ID=178, 名称=哈密瓜（小蜜25号）大果
[2025-06-29 07:46:30] 第47行: 导入成功
[2025-06-29 07:46:30] --- 处理第48行 ---
[2025-06-29 07:46:30] 第48行字段: 编码='XD6079', 名称='豇豆（绿）', 数量='40'
[2025-06-29 07:46:30] 第48行: 开始导入明细
[2025-06-29 07:46:30] 查找食材: 编码='XD6079', 名称='豇豆（绿）'
[2025-06-29 07:46:30] ✅ 通过编码找到食材: ID=179, 名称=豇豆（绿）
[2025-06-29 07:46:30] 第48行: 导入成功
[2025-06-29 07:46:30] --- 处理第49行 ---
[2025-06-29 07:46:30] 第49行字段: 编码='XTD9873', 名称='土豆', 数量='250'
[2025-06-29 07:46:30] 第49行: 开始导入明细
[2025-06-29 07:46:30] 查找食材: 编码='XTD9873', 名称='土豆'
[2025-06-29 07:46:30] ✅ 通过编码找到食材: ID=180, 名称=土豆
[2025-06-29 07:46:30] 第49行: 导入成功
[2025-06-29 07:46:30] --- 处理第50行 ---
[2025-06-29 07:46:30] 第50行字段: 编码='LJ0368', 名称='老姜（带土）', 数量='20'
[2025-06-29 07:46:30] 第50行: 开始导入明细
[2025-06-29 07:46:30] 查找食材: 编码='LJ0368', 名称='老姜（带土）'
[2025-06-29 07:46:30] ✅ 通过编码找到食材: ID=181, 名称=老姜（带土）
[2025-06-29 07:46:30] 第50行: 导入成功
[2025-06-29 07:46:30] --- 处理第51行 ---
[2025-06-29 07:46:30] 第51行字段: 编码='SM0321', 名称='蒜米', 数量='2'
[2025-06-29 07:46:30] 第51行: 开始导入明细
[2025-06-29 07:46:30] 查找食材: 编码='SM0321', 名称='蒜米'
[2025-06-29 07:46:30] ✅ 通过编码找到食材: ID=182, 名称=蒜米
[2025-06-29 07:46:30] 第51行: 导入成功
[2025-06-29 07:46:30] --- 处理第52行 ---
[2025-06-29 07:46:30] 第52行字段: 编码='SPBM00003264', 名称='猪里脊肉', 数量='30'
[2025-06-29 07:46:30] 第52行: 开始导入明细
[2025-06-29 07:46:30] 查找食材: 编码='SPBM00003264', 名称='猪里脊肉'
[2025-06-29 07:46:30] ✅ 通过编码找到食材: ID=183, 名称=猪里脊肉
[2025-06-29 07:46:30] 第52行: 导入成功
[2025-06-29 07:46:30] --- 处理第53行 ---
[2025-06-29 07:46:30] 第53行字段: 编码='HYML4819', 名称='黄玉米粒（新鲜）', 数量='30'
[2025-06-29 07:46:30] 第53行: 开始导入明细
[2025-06-29 07:46:30] 查找食材: 编码='HYML4819', 名称='黄玉米粒（新鲜）'
[2025-06-29 07:46:30] ✅ 通过编码找到食材: ID=184, 名称=黄玉米粒（新鲜）
[2025-06-29 07:46:30] 第53行: 导入成功
[2025-06-29 07:46:30] --- 处理第54行 ---
[2025-06-29 07:46:30] 第54行字段: 编码='', 名称='', 数量=''
[2025-06-29 07:46:30] 第54行: 关键字段为空，跳过
[2025-06-29 07:46:30] --- 处理第55行 ---
[2025-06-29 07:46:30] 第55行: 空行，跳过
[2025-06-29 07:46:30] --- 处理第56行 ---
[2025-06-29 07:46:30] 第56行: 空行，跳过
[2025-06-29 07:46:30] --- 处理第57行 ---
[2025-06-29 07:46:30] 第57行: 空行，跳过
[2025-06-29 07:46:30] --- 处理第58行 ---
[2025-06-29 07:46:30] 第58行: 空行，跳过
[2025-06-29 07:46:30] --- 处理第59行 ---
[2025-06-29 07:46:30] 第59行: 空行，跳过
[2025-06-29 07:46:30] --- 处理第60行 ---
[2025-06-29 07:46:30] 第60行: 空行，跳过
[2025-06-29 07:46:30] --- 处理第61行 ---
[2025-06-29 07:46:30] 第61行: 空行，跳过
[2025-06-29 07:46:30] --- 处理第62行 ---
[2025-06-29 07:46:30] 第62行: 空行，跳过
[2025-06-29 07:46:30] --- 处理第63行 ---
[2025-06-29 07:46:30] 第63行: 空行，跳过
[2025-06-29 07:46:30] --- 处理第64行 ---
[2025-06-29 07:46:30] 第64行: 空行，跳过
[2025-06-29 07:46:30] --- 处理第65行 ---
[2025-06-29 07:46:30] 第65行: 空行，跳过
[2025-06-29 07:46:30] --- 处理第66行 ---
[2025-06-29 07:46:30] 第66行: 空行，跳过
[2025-06-29 07:46:30] --- 处理第67行 ---
[2025-06-29 07:46:30] 第67行: 空行，跳过
[2025-06-29 07:46:30] --- 处理第68行 ---
[2025-06-29 07:46:30] 第68行: 空行，跳过
[2025-06-29 07:46:30] --- 处理第69行 ---
[2025-06-29 07:46:30] 第69行: 空行，跳过
[2025-06-29 07:46:30] --- 处理第70行 ---
[2025-06-29 07:46:30] 第70行: 空行，跳过
[2025-06-29 07:46:30] --- 处理第71行 ---
[2025-06-29 07:46:30] 第71行: 空行，跳过
[2025-06-29 07:46:30] --- 处理第72行 ---
[2025-06-29 07:46:30] 第72行: 空行，跳过
[2025-06-29 07:46:30] --- 处理第73行 ---
[2025-06-29 07:46:30] 第73行: 空行，跳过
[2025-06-29 07:46:30] --- 处理第74行 ---
[2025-06-29 07:46:30] 第74行: 空行，跳过
[2025-06-29 07:46:30] --- 处理第75行 ---
[2025-06-29 07:46:30] 第75行: 空行，跳过
[2025-06-29 07:46:30] --- 处理第76行 ---
[2025-06-29 07:46:30] 第76行: 空行，跳过
[2025-06-29 07:46:30] --- 处理第77行 ---
[2025-06-29 07:46:30] 第77行: 空行，跳过
[2025-06-29 07:46:30] --- 处理第78行 ---
[2025-06-29 07:46:30] 第78行: 空行，跳过
[2025-06-29 07:46:30] --- 处理第79行 ---
[2025-06-29 07:46:30] 第79行: 空行，跳过
[2025-06-29 07:46:30] --- 处理第80行 ---
[2025-06-29 07:46:30] 第80行: 空行，跳过
[2025-06-29 07:46:30] --- 处理第81行 ---
[2025-06-29 07:46:30] 第81行: 空行，跳过
[2025-06-29 07:46:30] --- 处理第82行 ---
[2025-06-29 07:46:30] 第82行: 空行，跳过
[2025-06-29 07:46:30] --- 处理第83行 ---
[2025-06-29 07:46:30] 第83行: 空行，跳过
[2025-06-29 07:46:30] --- 处理第84行 ---
[2025-06-29 07:46:30] 第84行: 空行，跳过
[2025-06-29 07:46:30] --- 处理第85行 ---
[2025-06-29 07:46:30] 第85行: 空行，跳过
[2025-06-29 07:46:30] --- 处理第86行 ---
[2025-06-29 07:46:30] 第86行: 空行，跳过
[2025-06-29 07:46:30] --- 处理第87行 ---
[2025-06-29 07:46:30] 第87行: 空行，跳过
[2025-06-29 07:46:30] --- 处理第88行 ---
[2025-06-29 07:46:30] 第88行: 空行，跳过
[2025-06-29 07:46:30] --- 处理第89行 ---
[2025-06-29 07:46:30] 第89行: 空行，跳过
[2025-06-29 07:46:30] --- 处理第90行 ---
[2025-06-29 07:46:30] 第90行: 空行，跳过
[2025-06-29 07:46:30] --- 处理第91行 ---
[2025-06-29 07:46:30] 第91行: 空行，跳过
[2025-06-29 07:46:30] --- 处理第92行 ---
[2025-06-29 07:46:30] 第92行: 空行，跳过
[2025-06-29 07:46:30] --- 处理第93行 ---
[2025-06-29 07:46:30] 第93行: 空行，跳过
[2025-06-29 07:46:30] --- 处理第94行 ---
[2025-06-29 07:46:30] 第94行: 空行，跳过
[2025-06-29 07:46:30] --- 处理第95行 ---
[2025-06-29 07:46:30] 第95行: 空行，跳过
[2025-06-29 07:46:30] --- 处理第96行 ---
[2025-06-29 07:46:30] 第96行: 空行，跳过
[2025-06-29 07:46:30] --- 处理第97行 ---
[2025-06-29 07:46:30] 第97行: 空行，跳过
[2025-06-29 07:46:30] --- 处理第98行 ---
[2025-06-29 07:46:30] 第98行: 空行，跳过
[2025-06-29 07:46:30] --- 处理第99行 ---
[2025-06-29 07:46:30] 第99行: 空行，跳过
[2025-06-29 07:46:30] --- 处理第100行 ---
[2025-06-29 07:46:30] 第100行: 空行，跳过
[2025-06-29 07:46:30] === 明细数据处理完成 ===
[2025-06-29 07:46:30] 成功: 46, 失败: 1
[2025-06-29 09:03:34] === 开始处理明细数据 ===
[2025-06-29 09:03:34] 数据总行数: 100
[2025-06-29 09:03:34] 检查第6行第1列: '商品明细'
[2025-06-29 09:03:34] 找到明细标题行: 第6行
[2025-06-29 09:03:34] 明细数据从第7行开始处理
[2025-06-29 09:03:34] --- 处理第7行 ---
[2025-06-29 09:03:34] 第7行字段: 编码='商品编码', 名称='商品名称', 数量='下单数量'
[2025-06-29 09:03:34] 第7行: 开始导入明细
[2025-06-29 09:03:34] 第7行: 导入失败 - 第7行: 数量必须大于0（第9列）
[2025-06-29 09:03:34] --- 处理第8行 ---
[2025-06-29 09:03:34] 第8行字段: 编码='DBC1685', 名称='大白菜', 数量='40'
[2025-06-29 09:03:34] 第8行: 开始导入明细
[2025-06-29 09:03:34] 查找食材: 编码='DBC1685', 名称='大白菜'
[2025-06-29 09:03:34] ✅ 通过编码找到食材: ID=141, 名称=大白菜
[2025-06-29 09:03:34] 第8行: 导入成功
[2025-06-29 09:03:34] --- 处理第9行 ---
[2025-06-29 09:03:34] 第9行字段: 编码='LHB1033', 名称='莲花白（平顶）', 数量='40'
[2025-06-29 09:03:34] 第9行: 开始导入明细
[2025-06-29 09:03:34] 查找食材: 编码='LHB1033', 名称='莲花白（平顶）'
[2025-06-29 09:03:34] ✅ 通过编码找到食材: ID=142, 名称=莲花白（平顶）
[2025-06-29 09:03:34] 第9行: 导入成功
[2025-06-29 09:03:34] --- 处理第10行 ---
[2025-06-29 09:03:34] 第10行字段: 编码='XLH1581', 名称='西兰花', 数量='40'
[2025-06-29 09:03:34] 第10行: 开始导入明细
[2025-06-29 09:03:34] 查找食材: 编码='XLH1581', 名称='西兰花'
[2025-06-29 09:03:34] ✅ 通过编码找到食材: ID=143, 名称=西兰花
[2025-06-29 09:03:34] 第10行: 导入成功
[2025-06-29 09:03:34] --- 处理第11行 ---
[2025-06-29 09:03:34] 第11行字段: 编码='XHS1526', 名称='西红柿', 数量='60'
[2025-06-29 09:03:34] 第11行: 开始导入明细
[2025-06-29 09:03:34] 查找食材: 编码='XHS1526', 名称='西红柿'
[2025-06-29 09:03:34] ✅ 通过编码找到食材: ID=144, 名称=西红柿
[2025-06-29 09:03:34] 第11行: 导入成功
[2025-06-29 09:03:34] --- 处理第12行 ---
[2025-06-29 09:03:34] 第12行字段: 编码='WDHG7779', 名称='外地黄瓜', 数量='60'
[2025-06-29 09:03:34] 第12行: 开始导入明细
[2025-06-29 09:03:34] 查找食材: 编码='WDHG7779', 名称='外地黄瓜'
[2025-06-29 09:03:34] ✅ 通过编码找到食材: ID=145, 名称=外地黄瓜
[2025-06-29 09:03:34] 第12行: 导入成功
[2025-06-29 09:03:34] --- 处理第13行 ---
[2025-06-29 09:03:34] 第13行字段: 编码='XNGC2635', 名称='小南瓜（长）', 数量='40'
[2025-06-29 09:03:34] 第13行: 开始导入明细
[2025-06-29 09:03:34] 查找食材: 编码='XNGC2635', 名称='小南瓜（长）'
[2025-06-29 09:03:34] ✅ 通过编码找到食材: ID=146, 名称=小南瓜（长）
[2025-06-29 09:03:34] 第13行: 导入成功
[2025-06-29 09:03:34] --- 处理第14行 ---
[2025-06-29 09:03:34] 第14行字段: 编码='dasd12', 名称='黄玉米棒', 数量='50'
[2025-06-29 09:03:34] 第14行: 开始导入明细
[2025-06-29 09:03:34] 查找食材: 编码='dasd12', 名称='黄玉米棒'
[2025-06-29 09:03:34] ✅ 通过编码找到食材: ID=147, 名称=黄玉米棒
[2025-06-29 09:03:34] 第14行: 导入成功
[2025-06-29 09:03:34] --- 处理第15行 ---
[2025-06-29 09:03:34] 第15行字段: 编码='SDCK1461', 名称='莴笋', 数量='50'
[2025-06-29 09:03:34] 第15行: 开始导入明细
[2025-06-29 09:03:34] 查找食材: 编码='SDCK1461', 名称='莴笋'
[2025-06-29 09:03:34] ✅ 通过编码找到食材: ID=148, 名称=莴笋
[2025-06-29 09:03:34] 第15行: 导入成功
[2025-06-29 09:03:34] --- 处理第16行 ---
[2025-06-29 09:03:34] 第16行字段: 编码='HLBWD2265', 名称='胡萝卜（外地）', 数量='30'
[2025-06-29 09:03:34] 第16行: 开始导入明细
[2025-06-29 09:03:34] 查找食材: 编码='HLBWD2265', 名称='胡萝卜（外地）'
[2025-06-29 09:03:34] ✅ 通过编码找到食材: ID=149, 名称=胡萝卜（外地）
[2025-06-29 09:03:34] 第16行: 导入成功
[2025-06-29 09:03:34] --- 处理第17行 ---
[2025-06-29 09:03:34] 第17行字段: 编码='BLBG9279', 名称='白萝卜', 数量='30'
[2025-06-29 09:03:34] 第17行: 开始导入明细
[2025-06-29 09:03:34] 查找食材: 编码='BLBG9279', 名称='白萝卜'
[2025-06-29 09:03:34] ✅ 通过编码找到食材: ID=150, 名称=白萝卜
[2025-06-29 09:03:34] 第17行: 导入成功
[2025-06-29 09:03:34] --- 处理第18行 ---
[2025-06-29 09:03:34] 第18行字段: 编码='QCLBDG1821', 名称='芹菜（绿）', 数量='25'
[2025-06-29 09:03:34] 第18行: 开始导入明细
[2025-06-29 09:03:34] 查找食材: 编码='QCLBDG1821', 名称='芹菜（绿）'
[2025-06-29 09:03:34] ✅ 通过编码找到食材: ID=151, 名称=芹菜（绿）
[2025-06-29 09:03:34] 第18行: 导入成功
[2025-06-29 09:03:34] --- 处理第19行 ---
[2025-06-29 09:03:34] 第19行字段: 编码='XC1624', 名称='香菜', 数量='3'
[2025-06-29 09:03:34] 第19行: 开始导入明细
[2025-06-29 09:03:34] 查找食材: 编码='XC1624', 名称='香菜'
[2025-06-29 09:03:34] ✅ 通过编码找到食材: ID=152, 名称=香菜
[2025-06-29 09:03:34] 第19行: 导入成功
[2025-06-29 09:03:34] --- 处理第20行 ---
[2025-06-29 09:03:34] 第20行字段: 编码='SMBT0731', 名称='蒜苗', 数量='15'
[2025-06-29 09:03:34] 第20行: 开始导入明细
[2025-06-29 09:03:34] 查找食材: 编码='SMBT0731', 名称='蒜苗'
[2025-06-29 09:03:34] ✅ 通过编码找到食材: ID=153, 名称=蒜苗
[2025-06-29 09:03:34] 第20行: 导入成功
[2025-06-29 09:03:34] --- 处理第21行 ---
[2025-06-29 09:03:34] 第21行字段: 编码='XCBDDFC7870', 名称='小葱', 数量='10'
[2025-06-29 09:03:34] 第21行: 开始导入明细
[2025-06-29 09:03:34] 查找食材: 编码='XCBDDFC7870', 名称='小葱'
[2025-06-29 09:03:34] ✅ 通过编码找到食材: ID=154, 名称=小葱
[2025-06-29 09:03:34] 第21行: 导入成功
[2025-06-29 09:03:34] --- 处理第22行 ---
[2025-06-29 09:03:34] 第22行字段: 编码='XJJ0650', 名称='小米椒（小尖椒）', 数量='2'
[2025-06-29 09:03:34] 第22行: 开始导入明细
[2025-06-29 09:03:34] 查找食材: 编码='XJJ0650', 名称='小米椒（小尖椒）'
[2025-06-29 09:03:34] ✅ 通过编码找到食材: ID=155, 名称=小米椒（小尖椒）
[2025-06-29 09:03:34] 第22行: 导入成功
[2025-06-29 09:03:34] --- 处理第23行 ---
[2025-06-29 09:03:34] 第23行字段: 编码='XJ0544', 名称='青线椒', 数量='50'
[2025-06-29 09:03:34] 第23行: 开始导入明细
[2025-06-29 09:03:34] 查找食材: 编码='XJ0544', 名称='青线椒'
[2025-06-29 09:03:34] ✅ 通过编码找到食材: ID=156, 名称=青线椒
[2025-06-29 09:03:34] 第23行: 导入成功
[2025-06-29 09:03:34] --- 处理第24行 ---
[2025-06-29 09:03:34] 第24行字段: 编码='DQJ0496', 名称='大青椒', 数量='20'
[2025-06-29 09:03:34] 第24行: 开始导入明细
[2025-06-29 09:03:34] 查找食材: 编码='DQJ0496', 名称='大青椒'
[2025-06-29 09:03:34] ✅ 通过编码找到食材: ID=157, 名称=大青椒
[2025-06-29 09:03:34] 第24行: 导入成功
[2025-06-29 09:03:34] --- 处理第25行 ---
[2025-06-29 09:03:34] 第25行字段: 编码='DHJ6400', 名称='大红椒', 数量='20'
[2025-06-29 09:03:34] 第25行: 开始导入明细
[2025-06-29 09:03:34] 查找食材: 编码='DHJ6400', 名称='大红椒'
[2025-06-29 09:03:34] ✅ 通过编码找到食材: ID=158, 名称=大红椒
[2025-06-29 09:03:34] 第25行: 导入成功
[2025-06-29 09:03:34] --- 处理第26行 ---
[2025-06-29 09:03:34] 第26行字段: 编码='ZG0206', 名称='猪肝', 数量='25'
[2025-06-29 09:03:34] 第26行: 开始导入明细
[2025-06-29 09:03:34] 查找食材: 编码='ZG0206', 名称='猪肝'
[2025-06-29 09:03:34] ✅ 通过编码找到食材: ID=159, 名称=猪肝
[2025-06-29 09:03:34] 第26行: 导入成功
[2025-06-29 09:03:34] --- 处理第27行 ---
[2025-06-29 09:03:34] 第27行字段: 编码='WHR4545', 名称='猪五花肉', 数量='30'
[2025-06-29 09:03:34] 第27行: 开始导入明细
[2025-06-29 09:03:34] 查找食材: 编码='WHR4545', 名称='猪五花肉'
[2025-06-29 09:03:34] ✅ 通过编码找到食材: ID=4, 名称=猪五花肉
[2025-06-29 09:03:34] 第27行: 导入成功
[2025-06-29 09:03:34] --- 处理第28行 ---
[2025-06-29 09:03:34] 第28行字段: 编码='ZQTRQG4200', 名称='猪前后腿肉去骨', 数量='140'
[2025-06-29 09:03:34] 第28行: 开始导入明细
[2025-06-29 09:03:34] 查找食材: 编码='ZQTRQG4200', 名称='猪前后腿肉去骨'
[2025-06-29 09:03:34] ✅ 通过编码找到食材: ID=3, 名称=猪前后腿肉去骨
[2025-06-29 09:03:34] 第28行: 导入成功
[2025-06-29 09:03:34] --- 处理第29行 ---
[2025-06-29 09:03:34] 第29行字段: 编码='ZPGZP8811', 名称='猪排骨（杂排）', 数量='10'
[2025-06-29 09:03:34] 第29行: 开始导入明细
[2025-06-29 09:03:34] 查找食材: 编码='ZPGZP8811', 名称='猪排骨（杂排）'
[2025-06-29 09:03:34] ✅ 通过编码找到食材: ID=160, 名称=猪排骨（杂排）
[2025-06-29 09:03:35] 第29行: 导入成功
[2025-06-29 09:03:35] --- 处理第30行 ---
[2025-06-29 09:03:35] 第30行字段: 编码='SPBM00003294', 名称='牛腩', 数量='30'
[2025-06-29 09:03:35] 第30行: 开始导入明细
[2025-06-29 09:03:35] 查找食材: 编码='SPBM00003294', 名称='牛腩'
[2025-06-29 09:03:35] ✅ 通过编码找到食材: ID=161, 名称=牛腩
[2025-06-29 09:03:35] 第30行: 导入成功
[2025-06-29 09:03:35] --- 处理第31行 ---
[2025-06-29 09:03:35] 第31行字段: 编码='WZSNR3317', 名称='牛肉', 数量='20'
[2025-06-29 09:03:35] 第31行: 开始导入明细
[2025-06-29 09:03:35] 查找食材: 编码='WZSNR3317', 名称='牛肉'
[2025-06-29 09:03:35] ✅ 通过编码找到食材: ID=162, 名称=牛肉
[2025-06-29 09:03:35] 第31行: 导入成功
[2025-06-29 09:03:35] --- 处理第32行 ---
[2025-06-29 09:03:35] 第32行字段: 编码='SHJ8439', 名称='三黄鸡（白条、无内脏）', 数量='70'
[2025-06-29 09:03:35] 第32行: 开始导入明细
[2025-06-29 09:03:35] 查找食材: 编码='SHJ8439', 名称='三黄鸡（白条、无内脏）'
[2025-06-29 09:03:35] ✅ 通过编码找到食材: ID=163, 名称=三黄鸡（白条、无内脏）
[2025-06-29 09:03:35] 第32行: 导入成功
[2025-06-29 09:03:35] --- 处理第33行 ---
[2025-06-29 09:03:35] 第33行字段: 编码='HDY6675', 名称='黄豆芽', 数量='30'
[2025-06-29 09:03:35] 第33行: 开始导入明细
[2025-06-29 09:03:35] 查找食材: 编码='HDY6675', 名称='黄豆芽'
[2025-06-29 09:03:35] ✅ 通过编码找到食材: ID=164, 名称=黄豆芽
[2025-06-29 09:03:35] 第33行: 导入成功
[2025-06-29 09:03:35] --- 处理第34行 ---
[2025-06-29 09:03:35] 第34行字段: 编码='SDF2528', 名称='水豆腐', 数量='5'
[2025-06-29 09:03:35] 第34行: 开始导入明细
[2025-06-29 09:03:35] 查找食材: 编码='SDF2528', 名称='水豆腐'
[2025-06-29 09:03:35] ✅ 通过编码找到食材: ID=165, 名称=水豆腐
[2025-06-29 09:03:35] 第34行: 导入成功
[2025-06-29 09:03:35] --- 处理第35行 ---
[2025-06-29 09:03:35] 第35行字段: 编码='GDFP4973', 名称='干豆腐片（薄）', 数量='15'
[2025-06-29 09:03:35] 第35行: 开始导入明细
[2025-06-29 09:03:35] 查找食材: 编码='GDFP4973', 名称='干豆腐片（薄）'
[2025-06-29 09:03:35] ✅ 通过编码找到食材: ID=166, 名称=干豆腐片（薄）
[2025-06-29 09:03:35] 第35行: 导入成功
[2025-06-29 09:03:35] --- 处理第36行 ---
[2025-06-29 09:03:35] 第36行字段: 编码='GDFHP1223', 名称='干豆腐片（厚）', 数量='40'
[2025-06-29 09:03:35] 第36行: 开始导入明细
[2025-06-29 09:03:35] 查找食材: 编码='GDFHP1223', 名称='干豆腐片（厚）'
[2025-06-29 09:03:35] ✅ 通过编码找到食材: ID=167, 名称=干豆腐片（厚）
[2025-06-29 09:03:35] 第36行: 导入成功
[2025-06-29 09:03:35] --- 处理第37行 ---
[2025-06-29 09:03:35] 第37行字段: 编码='YF4729', 名称='圆粉', 数量='200'
[2025-06-29 09:03:35] 第37行: 开始导入明细
[2025-06-29 09:03:35] 查找食材: 编码='YF4729', 名称='圆粉'
[2025-06-29 09:03:35] ✅ 通过编码找到食材: ID=168, 名称=圆粉
[2025-06-29 09:03:35] 第37行: 导入成功
[2025-06-29 09:03:35] --- 处理第38行 ---
[2025-06-29 09:03:35] 第38行字段: 编码='BF4586', 名称='扁粉', 数量='350'
[2025-06-29 09:03:35] 第38行: 开始导入明细
[2025-06-29 09:03:35] 查找食材: 编码='BF4586', 名称='扁粉'
[2025-06-29 09:03:35] ✅ 通过编码找到食材: ID=169, 名称=扁粉
[2025-06-29 09:03:35] 第38行: 导入成功
[2025-06-29 09:03:35] --- 处理第39行 ---
[2025-06-29 09:03:35] 第39行字段: 编码='YF5478', 名称='油粉', 数量='30'
[2025-06-29 09:03:35] 第39行: 开始导入明细
[2025-06-29 09:03:35] 查找食材: 编码='YF5478', 名称='油粉'
[2025-06-29 09:03:35] ✅ 通过编码找到食材: ID=170, 名称=油粉
[2025-06-29 09:03:35] 第39行: 导入成功
[2025-06-29 09:03:35] --- 处理第40行 ---
[2025-06-29 09:03:35] 第40行字段: 编码='BF5568', 名称='薄粉（卷粉皮）', 数量='15'
[2025-06-29 09:03:35] 第40行: 开始导入明细
[2025-06-29 09:03:35] 查找食材: 编码='BF5568', 名称='薄粉（卷粉皮）'
[2025-06-29 09:03:35] ✅ 通过编码找到食材: ID=171, 名称=薄粉（卷粉皮）
[2025-06-29 09:03:35] 第40行: 导入成功
[2025-06-29 09:03:35] --- 处理第41行 ---
[2025-06-29 09:03:35] 第41行字段: 编码='QTXW2925', 名称='秋田小町25KG', 数量='10'
[2025-06-29 09:03:35] 第41行: 开始导入明细
[2025-06-29 09:03:35] 查找食材: 编码='QTXW2925', 名称='秋田小町25KG'
[2025-06-29 09:03:35] ✅ 通过编码找到食材: ID=172, 名称=秋田小町25KG
[2025-06-29 09:03:35] 第41行: 导入成功
[2025-06-29 09:03:35] --- 处理第42行 ---
[2025-06-29 09:03:35] 第42行字段: 编码='GLHLJM1006', 名称='冠霖糊辣椒面', 数量='2'
[2025-06-29 09:03:35] 第42行: 开始导入明细
[2025-06-29 09:03:35] 查找食材: 编码='GLHLJM1006', 名称='冠霖糊辣椒面'
[2025-06-29 09:03:35] ✅ 通过编码找到食材: ID=173, 名称=冠霖糊辣椒面
[2025-06-29 09:03:35] 第42行: 导入成功
[2025-06-29 09:03:35] --- 处理第43行 ---
[2025-06-29 09:03:35] 第43行字段: 编码='DFP8053', 名称='干豆腐皮', 数量='1'
[2025-06-29 09:03:35] 第43行: 开始导入明细
[2025-06-29 09:03:35] 查找食材: 编码='DFP8053', 名称='干豆腐皮'
[2025-06-29 09:03:35] ✅ 通过编码找到食材: ID=174, 名称=干豆腐皮
[2025-06-29 09:03:35] 第43行: 导入成功
[2025-06-29 09:03:35] --- 处理第44行 ---
[2025-06-29 09:03:35] 第44行字段: 编码='CS7213', 名称='脆哨', 数量='10'
[2025-06-29 09:03:35] 第44行: 开始导入明细
[2025-06-29 09:03:35] 查找食材: 编码='CS7213', 名称='脆哨'
[2025-06-29 09:03:35] ✅ 通过编码找到食材: ID=175, 名称=脆哨
[2025-06-29 09:03:35] 第44行: 导入成功
[2025-06-29 09:03:35] --- 处理第45行 ---
[2025-06-29 09:03:35] 第45行字段: 编码='XJ1638', 名称='香蕉', 数量='30'
[2025-06-29 09:03:35] 第45行: 开始导入明细
[2025-06-29 09:03:35] 查找食材: 编码='XJ1638', 名称='香蕉'
[2025-06-29 09:03:35] ✅ 通过编码找到食材: ID=176, 名称=香蕉
[2025-06-29 09:03:35] 第45行: 导入成功
[2025-06-29 09:03:35] --- 处理第46行 ---
[2025-06-29 09:03:35] 第46行字段: 编码='HWZTX7875', 名称='茉莉香提', 数量='20'
[2025-06-29 09:03:35] 第46行: 开始导入明细
[2025-06-29 09:03:35] 查找食材: 编码='HWZTX7875', 名称='茉莉香提'
[2025-06-29 09:03:35] ✅ 通过编码找到食材: ID=177, 名称=茉莉香提
[2025-06-29 09:03:35] 第46行: 导入成功
[2025-06-29 09:03:35] --- 处理第47行 ---
[2025-06-29 09:03:35] 第47行字段: 编码='HMGXMH7349', 名称='哈密瓜（小蜜25号）大果', 数量='50'
[2025-06-29 09:03:35] 第47行: 开始导入明细
[2025-06-29 09:03:35] 查找食材: 编码='HMGXMH7349', 名称='哈密瓜（小蜜25号）大果'
[2025-06-29 09:03:35] ✅ 通过编码找到食材: ID=178, 名称=哈密瓜（小蜜25号）大果
[2025-06-29 09:03:35] 第47行: 导入成功
[2025-06-29 09:03:35] --- 处理第48行 ---
[2025-06-29 09:03:35] 第48行字段: 编码='XD6079', 名称='豇豆（绿）', 数量='40'
[2025-06-29 09:03:35] 第48行: 开始导入明细
[2025-06-29 09:03:35] 查找食材: 编码='XD6079', 名称='豇豆（绿）'
[2025-06-29 09:03:35] ✅ 通过编码找到食材: ID=179, 名称=豇豆（绿）
[2025-06-29 09:03:35] 第48行: 导入成功
[2025-06-29 09:03:35] --- 处理第49行 ---
[2025-06-29 09:03:35] 第49行字段: 编码='XTD9873', 名称='土豆', 数量='250'
[2025-06-29 09:03:35] 第49行: 开始导入明细
[2025-06-29 09:03:35] 查找食材: 编码='XTD9873', 名称='土豆'
[2025-06-29 09:03:35] ✅ 通过编码找到食材: ID=180, 名称=土豆
[2025-06-29 09:03:35] 第49行: 导入成功
[2025-06-29 09:03:35] --- 处理第50行 ---
[2025-06-29 09:03:35] 第50行字段: 编码='LJ0368', 名称='老姜（带土）', 数量='20'
[2025-06-29 09:03:35] 第50行: 开始导入明细
[2025-06-29 09:03:35] 查找食材: 编码='LJ0368', 名称='老姜（带土）'
[2025-06-29 09:03:35] ✅ 通过编码找到食材: ID=181, 名称=老姜（带土）
[2025-06-29 09:03:35] 第50行: 导入成功
[2025-06-29 09:03:35] --- 处理第51行 ---
[2025-06-29 09:03:35] 第51行字段: 编码='SM0321', 名称='蒜米', 数量='2'
[2025-06-29 09:03:35] 第51行: 开始导入明细
[2025-06-29 09:03:35] 查找食材: 编码='SM0321', 名称='蒜米'
[2025-06-29 09:03:35] ✅ 通过编码找到食材: ID=182, 名称=蒜米
[2025-06-29 09:03:35] 第51行: 导入成功
[2025-06-29 09:03:35] --- 处理第52行 ---
[2025-06-29 09:03:35] 第52行字段: 编码='SPBM00003264', 名称='猪里脊肉', 数量='30'
[2025-06-29 09:03:35] 第52行: 开始导入明细
[2025-06-29 09:03:35] 查找食材: 编码='SPBM00003264', 名称='猪里脊肉'
[2025-06-29 09:03:35] ✅ 通过编码找到食材: ID=183, 名称=猪里脊肉
[2025-06-29 09:03:35] 第52行: 导入成功
[2025-06-29 09:03:35] --- 处理第53行 ---
[2025-06-29 09:03:35] 第53行字段: 编码='HYML4819', 名称='黄玉米粒（新鲜）', 数量='30'
[2025-06-29 09:03:35] 第53行: 开始导入明细
[2025-06-29 09:03:35] 查找食材: 编码='HYML4819', 名称='黄玉米粒（新鲜）'
[2025-06-29 09:03:35] ✅ 通过编码找到食材: ID=184, 名称=黄玉米粒（新鲜）
[2025-06-29 09:03:35] 第53行: 导入成功
[2025-06-29 09:03:35] --- 处理第54行 ---
[2025-06-29 09:03:35] 第54行字段: 编码='', 名称='', 数量=''
[2025-06-29 09:03:35] 第54行: 关键字段为空，跳过
[2025-06-29 09:03:35] --- 处理第55行 ---
[2025-06-29 09:03:35] 第55行: 空行，跳过
[2025-06-29 09:03:35] --- 处理第56行 ---
[2025-06-29 09:03:35] 第56行: 空行，跳过
[2025-06-29 09:03:35] --- 处理第57行 ---
[2025-06-29 09:03:35] 第57行: 空行，跳过
[2025-06-29 09:03:35] --- 处理第58行 ---
[2025-06-29 09:03:35] 第58行: 空行，跳过
[2025-06-29 09:03:35] --- 处理第59行 ---
[2025-06-29 09:03:35] 第59行: 空行，跳过
[2025-06-29 09:03:35] --- 处理第60行 ---
[2025-06-29 09:03:35] 第60行: 空行，跳过
[2025-06-29 09:03:35] --- 处理第61行 ---
[2025-06-29 09:03:35] 第61行: 空行，跳过
[2025-06-29 09:03:35] --- 处理第62行 ---
[2025-06-29 09:03:35] 第62行: 空行，跳过
[2025-06-29 09:03:35] --- 处理第63行 ---
[2025-06-29 09:03:35] 第63行: 空行，跳过
[2025-06-29 09:03:35] --- 处理第64行 ---
[2025-06-29 09:03:35] 第64行: 空行，跳过
[2025-06-29 09:03:35] --- 处理第65行 ---
[2025-06-29 09:03:35] 第65行: 空行，跳过
[2025-06-29 09:03:35] --- 处理第66行 ---
[2025-06-29 09:03:35] 第66行: 空行，跳过
[2025-06-29 09:03:35] --- 处理第67行 ---
[2025-06-29 09:03:35] 第67行: 空行，跳过
[2025-06-29 09:03:35] --- 处理第68行 ---
[2025-06-29 09:03:35] 第68行: 空行，跳过
[2025-06-29 09:03:35] --- 处理第69行 ---
[2025-06-29 09:03:35] 第69行: 空行，跳过
[2025-06-29 09:03:35] --- 处理第70行 ---
[2025-06-29 09:03:35] 第70行: 空行，跳过
[2025-06-29 09:03:35] --- 处理第71行 ---
[2025-06-29 09:03:35] 第71行: 空行，跳过
[2025-06-29 09:03:35] --- 处理第72行 ---
[2025-06-29 09:03:35] 第72行: 空行，跳过
[2025-06-29 09:03:35] --- 处理第73行 ---
[2025-06-29 09:03:35] 第73行: 空行，跳过
[2025-06-29 09:03:35] --- 处理第74行 ---
[2025-06-29 09:03:35] 第74行: 空行，跳过
[2025-06-29 09:03:35] --- 处理第75行 ---
[2025-06-29 09:03:35] 第75行: 空行，跳过
[2025-06-29 09:03:35] --- 处理第76行 ---
[2025-06-29 09:03:35] 第76行: 空行，跳过
[2025-06-29 09:03:35] --- 处理第77行 ---
[2025-06-29 09:03:35] 第77行: 空行，跳过
[2025-06-29 09:03:35] --- 处理第78行 ---
[2025-06-29 09:03:35] 第78行: 空行，跳过
[2025-06-29 09:03:35] --- 处理第79行 ---
[2025-06-29 09:03:35] 第79行: 空行，跳过
[2025-06-29 09:03:35] --- 处理第80行 ---
[2025-06-29 09:03:35] 第80行: 空行，跳过
[2025-06-29 09:03:35] --- 处理第81行 ---
[2025-06-29 09:03:35] 第81行: 空行，跳过
[2025-06-29 09:03:35] --- 处理第82行 ---
[2025-06-29 09:03:35] 第82行: 空行，跳过
[2025-06-29 09:03:35] --- 处理第83行 ---
[2025-06-29 09:03:35] 第83行: 空行，跳过
[2025-06-29 09:03:35] --- 处理第84行 ---
[2025-06-29 09:03:35] 第84行: 空行，跳过
[2025-06-29 09:03:35] --- 处理第85行 ---
[2025-06-29 09:03:35] 第85行: 空行，跳过
[2025-06-29 09:03:35] --- 处理第86行 ---
[2025-06-29 09:03:35] 第86行: 空行，跳过
[2025-06-29 09:03:35] --- 处理第87行 ---
[2025-06-29 09:03:35] 第87行: 空行，跳过
[2025-06-29 09:03:35] --- 处理第88行 ---
[2025-06-29 09:03:35] 第88行: 空行，跳过
[2025-06-29 09:03:35] --- 处理第89行 ---
[2025-06-29 09:03:35] 第89行: 空行，跳过
[2025-06-29 09:03:35] --- 处理第90行 ---
[2025-06-29 09:03:35] 第90行: 空行，跳过
[2025-06-29 09:03:35] --- 处理第91行 ---
[2025-06-29 09:03:35] 第91行: 空行，跳过
[2025-06-29 09:03:35] --- 处理第92行 ---
[2025-06-29 09:03:35] 第92行: 空行，跳过
[2025-06-29 09:03:35] --- 处理第93行 ---
[2025-06-29 09:03:35] 第93行: 空行，跳过
[2025-06-29 09:03:35] --- 处理第94行 ---
[2025-06-29 09:03:35] 第94行: 空行，跳过
[2025-06-29 09:03:35] --- 处理第95行 ---
[2025-06-29 09:03:35] 第95行: 空行，跳过
[2025-06-29 09:03:35] --- 处理第96行 ---
[2025-06-29 09:03:35] 第96行: 空行，跳过
[2025-06-29 09:03:35] --- 处理第97行 ---
[2025-06-29 09:03:35] 第97行: 空行，跳过
[2025-06-29 09:03:35] --- 处理第98行 ---
[2025-06-29 09:03:35] 第98行: 空行，跳过
[2025-06-29 09:03:35] --- 处理第99行 ---
[2025-06-29 09:03:35] 第99行: 空行，跳过
[2025-06-29 09:03:35] --- 处理第100行 ---
[2025-06-29 09:03:35] 第100行: 空行，跳过
[2025-06-29 09:03:35] === 明细数据处理完成 ===
[2025-06-29 09:03:35] 成功: 46, 失败: 1
[2025-06-29 09:34:19] === 开始处理明细数据 ===
[2025-06-29 09:34:19] 数据总行数: 100
[2025-06-29 09:34:19] 检查第6行第1列: '商品明细'
[2025-06-29 09:34:19] 找到明细标题行: 第6行
[2025-06-29 09:34:19] 明细数据从第7行开始处理
[2025-06-29 09:34:19] --- 处理第7行 ---
[2025-06-29 09:34:19] 第7行字段: 编码='商品编码', 名称='商品名称', 数量='下单数量'
[2025-06-29 09:34:19] 第7行: 开始导入明细
[2025-06-29 09:34:19] 第7行: 导入失败 - 第7行: 数量必须大于0（第9列）
[2025-06-29 09:34:19] --- 处理第8行 ---
[2025-06-29 09:34:19] 第8行字段: 编码='DBC1685', 名称='大白菜', 数量='40'
[2025-06-29 09:34:19] 第8行: 开始导入明细
[2025-06-29 09:34:19] 查找食材: 编码='DBC1685', 名称='大白菜'
[2025-06-29 09:34:19] ✅ 通过编码找到食材: ID=141, 名称=大白菜
[2025-06-29 09:34:19] 第8行: 导入成功
[2025-06-29 09:34:19] --- 处理第9行 ---
[2025-06-29 09:34:19] 第9行字段: 编码='LHB1033', 名称='莲花白（平顶）', 数量='40'
[2025-06-29 09:34:19] 第9行: 开始导入明细
[2025-06-29 09:34:19] 查找食材: 编码='LHB1033', 名称='莲花白（平顶）'
[2025-06-29 09:34:19] ✅ 通过编码找到食材: ID=142, 名称=莲花白（平顶）
[2025-06-29 09:34:19] 第9行: 导入成功
[2025-06-29 09:34:19] --- 处理第10行 ---
[2025-06-29 09:34:19] 第10行字段: 编码='XLH1581', 名称='西兰花', 数量='40'
[2025-06-29 09:34:19] 第10行: 开始导入明细
[2025-06-29 09:34:19] 查找食材: 编码='XLH1581', 名称='西兰花'
[2025-06-29 09:34:19] ✅ 通过编码找到食材: ID=143, 名称=西兰花
[2025-06-29 09:34:19] 第10行: 导入成功
[2025-06-29 09:34:19] --- 处理第11行 ---
[2025-06-29 09:34:19] 第11行字段: 编码='XHS1526', 名称='西红柿', 数量='60'
[2025-06-29 09:34:19] 第11行: 开始导入明细
[2025-06-29 09:34:19] 查找食材: 编码='XHS1526', 名称='西红柿'
[2025-06-29 09:34:19] ✅ 通过编码找到食材: ID=144, 名称=西红柿
[2025-06-29 09:34:19] 第11行: 导入成功
[2025-06-29 09:34:19] --- 处理第12行 ---
[2025-06-29 09:34:19] 第12行字段: 编码='WDHG7779', 名称='外地黄瓜', 数量='60'
[2025-06-29 09:34:19] 第12行: 开始导入明细
[2025-06-29 09:34:19] 查找食材: 编码='WDHG7779', 名称='外地黄瓜'
[2025-06-29 09:34:19] ✅ 通过编码找到食材: ID=145, 名称=外地黄瓜
[2025-06-29 09:34:19] 第12行: 导入成功
[2025-06-29 09:34:19] --- 处理第13行 ---
[2025-06-29 09:34:19] 第13行字段: 编码='XNGC2635', 名称='小南瓜（长）', 数量='40'
[2025-06-29 09:34:19] 第13行: 开始导入明细
[2025-06-29 09:34:19] 查找食材: 编码='XNGC2635', 名称='小南瓜（长）'
[2025-06-29 09:34:19] ✅ 通过编码找到食材: ID=146, 名称=小南瓜（长）
[2025-06-29 09:34:19] 第13行: 导入成功
[2025-06-29 09:34:19] --- 处理第14行 ---
[2025-06-29 09:34:19] 第14行字段: 编码='dasd12', 名称='黄玉米棒', 数量='50'
[2025-06-29 09:34:19] 第14行: 开始导入明细
[2025-06-29 09:34:19] 查找食材: 编码='dasd12', 名称='黄玉米棒'
[2025-06-29 09:34:19] ✅ 通过编码找到食材: ID=147, 名称=黄玉米棒
[2025-06-29 09:34:19] 第14行: 导入成功
[2025-06-29 09:34:19] --- 处理第15行 ---
[2025-06-29 09:34:19] 第15行字段: 编码='SDCK1461', 名称='莴笋', 数量='50'
[2025-06-29 09:34:19] 第15行: 开始导入明细
[2025-06-29 09:34:19] 查找食材: 编码='SDCK1461', 名称='莴笋'
[2025-06-29 09:34:19] ✅ 通过编码找到食材: ID=148, 名称=莴笋
[2025-06-29 09:34:19] 第15行: 导入成功
[2025-06-29 09:34:19] --- 处理第16行 ---
[2025-06-29 09:34:19] 第16行字段: 编码='HLBWD2265', 名称='胡萝卜（外地）', 数量='30'
[2025-06-29 09:34:19] 第16行: 开始导入明细
[2025-06-29 09:34:19] 查找食材: 编码='HLBWD2265', 名称='胡萝卜（外地）'
[2025-06-29 09:34:19] ✅ 通过编码找到食材: ID=149, 名称=胡萝卜（外地）
[2025-06-29 09:34:19] 第16行: 导入成功
[2025-06-29 09:34:19] --- 处理第17行 ---
[2025-06-29 09:34:19] 第17行字段: 编码='BLBG9279', 名称='白萝卜', 数量='30'
[2025-06-29 09:34:19] 第17行: 开始导入明细
[2025-06-29 09:34:19] 查找食材: 编码='BLBG9279', 名称='白萝卜'
[2025-06-29 09:34:19] ✅ 通过编码找到食材: ID=150, 名称=白萝卜
[2025-06-29 09:34:19] 第17行: 导入成功
[2025-06-29 09:34:19] --- 处理第18行 ---
[2025-06-29 09:34:19] 第18行字段: 编码='QCLBDG1821', 名称='芹菜（绿）', 数量='25'
[2025-06-29 09:34:19] 第18行: 开始导入明细
[2025-06-29 09:34:19] 查找食材: 编码='QCLBDG1821', 名称='芹菜（绿）'
[2025-06-29 09:34:19] ✅ 通过编码找到食材: ID=151, 名称=芹菜（绿）
[2025-06-29 09:34:19] 第18行: 导入成功
[2025-06-29 09:34:19] --- 处理第19行 ---
[2025-06-29 09:34:19] 第19行字段: 编码='XC1624', 名称='香菜', 数量='3'
[2025-06-29 09:34:19] 第19行: 开始导入明细
[2025-06-29 09:34:19] 查找食材: 编码='XC1624', 名称='香菜'
[2025-06-29 09:34:19] ✅ 通过编码找到食材: ID=152, 名称=香菜
[2025-06-29 09:34:19] 第19行: 导入成功
[2025-06-29 09:34:19] --- 处理第20行 ---
[2025-06-29 09:34:19] 第20行字段: 编码='SMBT0731', 名称='蒜苗', 数量='15'
[2025-06-29 09:34:19] 第20行: 开始导入明细
[2025-06-29 09:34:19] 查找食材: 编码='SMBT0731', 名称='蒜苗'
[2025-06-29 09:34:19] ✅ 通过编码找到食材: ID=153, 名称=蒜苗
[2025-06-29 09:34:19] 第20行: 导入成功
[2025-06-29 09:34:19] --- 处理第21行 ---
[2025-06-29 09:34:19] 第21行字段: 编码='XCBDDFC7870', 名称='小葱', 数量='10'
[2025-06-29 09:34:19] 第21行: 开始导入明细
[2025-06-29 09:34:19] 查找食材: 编码='XCBDDFC7870', 名称='小葱'
[2025-06-29 09:34:20] ✅ 通过编码找到食材: ID=154, 名称=小葱
[2025-06-29 09:34:20] 第21行: 导入成功
[2025-06-29 09:34:20] --- 处理第22行 ---
[2025-06-29 09:34:20] 第22行字段: 编码='XJJ0650', 名称='小米椒（小尖椒）', 数量='2'
[2025-06-29 09:34:20] 第22行: 开始导入明细
[2025-06-29 09:34:20] 查找食材: 编码='XJJ0650', 名称='小米椒（小尖椒）'
[2025-06-29 09:34:20] ✅ 通过编码找到食材: ID=155, 名称=小米椒（小尖椒）
[2025-06-29 09:34:20] 第22行: 导入成功
[2025-06-29 09:34:20] --- 处理第23行 ---
[2025-06-29 09:34:20] 第23行字段: 编码='XJ0544', 名称='青线椒', 数量='50'
[2025-06-29 09:34:20] 第23行: 开始导入明细
[2025-06-29 09:34:20] 查找食材: 编码='XJ0544', 名称='青线椒'
[2025-06-29 09:34:20] ✅ 通过编码找到食材: ID=156, 名称=青线椒
[2025-06-29 09:34:20] 第23行: 导入成功
[2025-06-29 09:34:20] --- 处理第24行 ---
[2025-06-29 09:34:20] 第24行字段: 编码='DQJ0496', 名称='大青椒', 数量='20'
[2025-06-29 09:34:20] 第24行: 开始导入明细
[2025-06-29 09:34:20] 查找食材: 编码='DQJ0496', 名称='大青椒'
[2025-06-29 09:34:20] ✅ 通过编码找到食材: ID=157, 名称=大青椒
[2025-06-29 09:34:20] 第24行: 导入成功
[2025-06-29 09:34:20] --- 处理第25行 ---
[2025-06-29 09:34:20] 第25行字段: 编码='DHJ6400', 名称='大红椒', 数量='20'
[2025-06-29 09:34:20] 第25行: 开始导入明细
[2025-06-29 09:34:20] 查找食材: 编码='DHJ6400', 名称='大红椒'
[2025-06-29 09:34:20] ✅ 通过编码找到食材: ID=158, 名称=大红椒
[2025-06-29 09:34:20] 第25行: 导入成功
[2025-06-29 09:34:20] --- 处理第26行 ---
[2025-06-29 09:34:20] 第26行字段: 编码='ZG0206', 名称='猪肝', 数量='25'
[2025-06-29 09:34:20] 第26行: 开始导入明细
[2025-06-29 09:34:20] 查找食材: 编码='ZG0206', 名称='猪肝'
[2025-06-29 09:34:20] ✅ 通过编码找到食材: ID=159, 名称=猪肝
[2025-06-29 09:34:20] 第26行: 导入成功
[2025-06-29 09:34:20] --- 处理第27行 ---
[2025-06-29 09:34:20] 第27行字段: 编码='WHR4545', 名称='猪五花肉', 数量='30'
[2025-06-29 09:34:20] 第27行: 开始导入明细
[2025-06-29 09:34:20] 查找食材: 编码='WHR4545', 名称='猪五花肉'
[2025-06-29 09:34:20] ✅ 通过编码找到食材: ID=4, 名称=猪五花肉
[2025-06-29 09:34:20] 第27行: 导入成功
[2025-06-29 09:34:20] --- 处理第28行 ---
[2025-06-29 09:34:20] 第28行字段: 编码='ZQTRQG4200', 名称='猪前后腿肉去骨', 数量='140'
[2025-06-29 09:34:20] 第28行: 开始导入明细
[2025-06-29 09:34:20] 查找食材: 编码='ZQTRQG4200', 名称='猪前后腿肉去骨'
[2025-06-29 09:34:20] ✅ 通过编码找到食材: ID=3, 名称=猪前后腿肉去骨
[2025-06-29 09:34:20] 第28行: 导入成功
[2025-06-29 09:34:20] --- 处理第29行 ---
[2025-06-29 09:34:20] 第29行字段: 编码='ZPGZP8811', 名称='猪排骨（杂排）', 数量='10'
[2025-06-29 09:34:20] 第29行: 开始导入明细
[2025-06-29 09:34:20] 查找食材: 编码='ZPGZP8811', 名称='猪排骨（杂排）'
[2025-06-29 09:34:20] ✅ 通过编码找到食材: ID=160, 名称=猪排骨（杂排）
[2025-06-29 09:34:20] 第29行: 导入成功
[2025-06-29 09:34:20] --- 处理第30行 ---
[2025-06-29 09:34:20] 第30行字段: 编码='SPBM00003294', 名称='牛腩', 数量='30'
[2025-06-29 09:34:20] 第30行: 开始导入明细
[2025-06-29 09:34:20] 查找食材: 编码='SPBM00003294', 名称='牛腩'
[2025-06-29 09:34:20] ✅ 通过编码找到食材: ID=161, 名称=牛腩
[2025-06-29 09:34:20] 第30行: 导入成功
[2025-06-29 09:34:20] --- 处理第31行 ---
[2025-06-29 09:34:20] 第31行字段: 编码='WZSNR3317', 名称='牛肉', 数量='20'
[2025-06-29 09:34:20] 第31行: 开始导入明细
[2025-06-29 09:34:20] 查找食材: 编码='WZSNR3317', 名称='牛肉'
[2025-06-29 09:34:20] ✅ 通过编码找到食材: ID=162, 名称=牛肉
[2025-06-29 09:34:20] 第31行: 导入成功
[2025-06-29 09:34:20] --- 处理第32行 ---
[2025-06-29 09:34:20] 第32行字段: 编码='SHJ8439', 名称='三黄鸡（白条、无内脏）', 数量='70'
[2025-06-29 09:34:20] 第32行: 开始导入明细
[2025-06-29 09:34:20] 查找食材: 编码='SHJ8439', 名称='三黄鸡（白条、无内脏）'
[2025-06-29 09:34:20] ✅ 通过编码找到食材: ID=163, 名称=三黄鸡（白条、无内脏）
[2025-06-29 09:34:20] 第32行: 导入成功
[2025-06-29 09:34:20] --- 处理第33行 ---
[2025-06-29 09:34:20] 第33行字段: 编码='HDY6675', 名称='黄豆芽', 数量='30'
[2025-06-29 09:34:20] 第33行: 开始导入明细
[2025-06-29 09:34:20] 查找食材: 编码='HDY6675', 名称='黄豆芽'
[2025-06-29 09:34:20] ✅ 通过编码找到食材: ID=164, 名称=黄豆芽
[2025-06-29 09:34:20] 第33行: 导入成功
[2025-06-29 09:34:20] --- 处理第34行 ---
[2025-06-29 09:34:20] 第34行字段: 编码='SDF2528', 名称='水豆腐', 数量='5'
[2025-06-29 09:34:20] 第34行: 开始导入明细
[2025-06-29 09:34:20] 查找食材: 编码='SDF2528', 名称='水豆腐'
[2025-06-29 09:34:20] ✅ 通过编码找到食材: ID=165, 名称=水豆腐
[2025-06-29 09:34:20] 第34行: 导入成功
[2025-06-29 09:34:20] --- 处理第35行 ---
[2025-06-29 09:34:20] 第35行字段: 编码='GDFP4973', 名称='干豆腐片（薄）', 数量='15'
[2025-06-29 09:34:20] 第35行: 开始导入明细
[2025-06-29 09:34:20] 查找食材: 编码='GDFP4973', 名称='干豆腐片（薄）'
[2025-06-29 09:34:20] ✅ 通过编码找到食材: ID=166, 名称=干豆腐片（薄）
[2025-06-29 09:34:20] 第35行: 导入成功
[2025-06-29 09:34:20] --- 处理第36行 ---
[2025-06-29 09:34:20] 第36行字段: 编码='GDFHP1223', 名称='干豆腐片（厚）', 数量='40'
[2025-06-29 09:34:20] 第36行: 开始导入明细
[2025-06-29 09:34:20] 查找食材: 编码='GDFHP1223', 名称='干豆腐片（厚）'
[2025-06-29 09:34:20] ✅ 通过编码找到食材: ID=167, 名称=干豆腐片（厚）
[2025-06-29 09:34:20] 第36行: 导入成功
[2025-06-29 09:34:20] --- 处理第37行 ---
[2025-06-29 09:34:20] 第37行字段: 编码='YF4729', 名称='圆粉', 数量='200'
[2025-06-29 09:34:20] 第37行: 开始导入明细
[2025-06-29 09:34:20] 查找食材: 编码='YF4729', 名称='圆粉'
[2025-06-29 09:34:20] ✅ 通过编码找到食材: ID=168, 名称=圆粉
[2025-06-29 09:34:20] 第37行: 导入成功
[2025-06-29 09:34:20] --- 处理第38行 ---
[2025-06-29 09:34:20] 第38行字段: 编码='BF4586', 名称='扁粉', 数量='350'
[2025-06-29 09:34:20] 第38行: 开始导入明细
[2025-06-29 09:34:20] 查找食材: 编码='BF4586', 名称='扁粉'
[2025-06-29 09:34:20] ✅ 通过编码找到食材: ID=169, 名称=扁粉
[2025-06-29 09:34:20] 第38行: 导入成功
[2025-06-29 09:34:20] --- 处理第39行 ---
[2025-06-29 09:34:20] 第39行字段: 编码='YF5478', 名称='油粉', 数量='30'
[2025-06-29 09:34:20] 第39行: 开始导入明细
[2025-06-29 09:34:20] 查找食材: 编码='YF5478', 名称='油粉'
[2025-06-29 09:34:20] ✅ 通过编码找到食材: ID=170, 名称=油粉
[2025-06-29 09:34:20] 第39行: 导入成功
[2025-06-29 09:34:20] --- 处理第40行 ---
[2025-06-29 09:34:20] 第40行字段: 编码='BF5568', 名称='薄粉（卷粉皮）', 数量='15'
[2025-06-29 09:34:20] 第40行: 开始导入明细
[2025-06-29 09:34:20] 查找食材: 编码='BF5568', 名称='薄粉（卷粉皮）'
[2025-06-29 09:34:20] ✅ 通过编码找到食材: ID=171, 名称=薄粉（卷粉皮）
[2025-06-29 09:34:20] 第40行: 导入成功
[2025-06-29 09:34:20] --- 处理第41行 ---
[2025-06-29 09:34:20] 第41行字段: 编码='QTXW2925', 名称='秋田小町25KG', 数量='10'
[2025-06-29 09:34:20] 第41行: 开始导入明细
[2025-06-29 09:34:20] 查找食材: 编码='QTXW2925', 名称='秋田小町25KG'
[2025-06-29 09:34:20] ✅ 通过编码找到食材: ID=172, 名称=秋田小町25KG
[2025-06-29 09:34:20] 第41行: 导入成功
[2025-06-29 09:34:20] --- 处理第42行 ---
[2025-06-29 09:34:20] 第42行字段: 编码='GLHLJM1006', 名称='冠霖糊辣椒面', 数量='2'
[2025-06-29 09:34:20] 第42行: 开始导入明细
[2025-06-29 09:34:20] 查找食材: 编码='GLHLJM1006', 名称='冠霖糊辣椒面'
[2025-06-29 09:34:20] ✅ 通过编码找到食材: ID=173, 名称=冠霖糊辣椒面
[2025-06-29 09:34:20] 第42行: 导入成功
[2025-06-29 09:34:20] --- 处理第43行 ---
[2025-06-29 09:34:20] 第43行字段: 编码='DFP8053', 名称='干豆腐皮', 数量='1'
[2025-06-29 09:34:20] 第43行: 开始导入明细
[2025-06-29 09:34:20] 查找食材: 编码='DFP8053', 名称='干豆腐皮'
[2025-06-29 09:34:20] ✅ 通过编码找到食材: ID=174, 名称=干豆腐皮
[2025-06-29 09:34:20] 第43行: 导入成功
[2025-06-29 09:34:20] --- 处理第44行 ---
[2025-06-29 09:34:20] 第44行字段: 编码='CS7213', 名称='脆哨', 数量='10'
[2025-06-29 09:34:20] 第44行: 开始导入明细
[2025-06-29 09:34:20] 查找食材: 编码='CS7213', 名称='脆哨'
[2025-06-29 09:34:20] ✅ 通过编码找到食材: ID=175, 名称=脆哨
[2025-06-29 09:34:20] 第44行: 导入成功
[2025-06-29 09:34:20] --- 处理第45行 ---
[2025-06-29 09:34:20] 第45行字段: 编码='XJ1638', 名称='香蕉', 数量='30'
[2025-06-29 09:34:20] 第45行: 开始导入明细
[2025-06-29 09:34:20] 查找食材: 编码='XJ1638', 名称='香蕉'
[2025-06-29 09:34:20] ✅ 通过编码找到食材: ID=176, 名称=香蕉
[2025-06-29 09:34:20] 第45行: 导入成功
[2025-06-29 09:34:20] --- 处理第46行 ---
[2025-06-29 09:34:20] 第46行字段: 编码='HWZTX7875', 名称='茉莉香提', 数量='20'
[2025-06-29 09:34:20] 第46行: 开始导入明细
[2025-06-29 09:34:20] 查找食材: 编码='HWZTX7875', 名称='茉莉香提'
[2025-06-29 09:34:20] ✅ 通过编码找到食材: ID=177, 名称=茉莉香提
[2025-06-29 09:34:20] 第46行: 导入成功
[2025-06-29 09:34:20] --- 处理第47行 ---
[2025-06-29 09:34:20] 第47行字段: 编码='HMGXMH7349', 名称='哈密瓜（小蜜25号）大果', 数量='50'
[2025-06-29 09:34:20] 第47行: 开始导入明细
[2025-06-29 09:34:20] 查找食材: 编码='HMGXMH7349', 名称='哈密瓜（小蜜25号）大果'
[2025-06-29 09:34:20] ✅ 通过编码找到食材: ID=178, 名称=哈密瓜（小蜜25号）大果
[2025-06-29 09:34:20] 第47行: 导入成功
[2025-06-29 09:34:20] --- 处理第48行 ---
[2025-06-29 09:34:20] 第48行字段: 编码='XD6079', 名称='豇豆（绿）', 数量='40'
[2025-06-29 09:34:20] 第48行: 开始导入明细
[2025-06-29 09:34:20] 查找食材: 编码='XD6079', 名称='豇豆（绿）'
[2025-06-29 09:34:20] ✅ 通过编码找到食材: ID=179, 名称=豇豆（绿）
[2025-06-29 09:34:20] 第48行: 导入成功
[2025-06-29 09:34:20] --- 处理第49行 ---
[2025-06-29 09:34:20] 第49行字段: 编码='XTD9873', 名称='土豆', 数量='250'
[2025-06-29 09:34:20] 第49行: 开始导入明细
[2025-06-29 09:34:20] 查找食材: 编码='XTD9873', 名称='土豆'
[2025-06-29 09:34:20] ✅ 通过编码找到食材: ID=180, 名称=土豆
[2025-06-29 09:34:20] 第49行: 导入成功
[2025-06-29 09:34:20] --- 处理第50行 ---
[2025-06-29 09:34:20] 第50行字段: 编码='LJ0368', 名称='老姜（带土）', 数量='20'
[2025-06-29 09:34:20] 第50行: 开始导入明细
[2025-06-29 09:34:20] 查找食材: 编码='LJ0368', 名称='老姜（带土）'
[2025-06-29 09:34:20] ✅ 通过编码找到食材: ID=181, 名称=老姜（带土）
[2025-06-29 09:34:20] 第50行: 导入成功
[2025-06-29 09:34:20] --- 处理第51行 ---
[2025-06-29 09:34:20] 第51行字段: 编码='SM0321', 名称='蒜米', 数量='2'
[2025-06-29 09:34:20] 第51行: 开始导入明细
[2025-06-29 09:34:20] 查找食材: 编码='SM0321', 名称='蒜米'
[2025-06-29 09:34:20] ✅ 通过编码找到食材: ID=182, 名称=蒜米
[2025-06-29 09:34:20] 第51行: 导入成功
[2025-06-29 09:34:20] --- 处理第52行 ---
[2025-06-29 09:34:20] 第52行字段: 编码='SPBM00003264', 名称='猪里脊肉', 数量='30'
[2025-06-29 09:34:20] 第52行: 开始导入明细
[2025-06-29 09:34:20] 查找食材: 编码='SPBM00003264', 名称='猪里脊肉'
[2025-06-29 09:34:20] ✅ 通过编码找到食材: ID=183, 名称=猪里脊肉
[2025-06-29 09:34:20] 第52行: 导入成功
[2025-06-29 09:34:20] --- 处理第53行 ---
[2025-06-29 09:34:20] 第53行字段: 编码='HYML4819', 名称='黄玉米粒（新鲜）', 数量='30'
[2025-06-29 09:34:20] 第53行: 开始导入明细
[2025-06-29 09:34:20] 查找食材: 编码='HYML4819', 名称='黄玉米粒（新鲜）'
[2025-06-29 09:34:20] ✅ 通过编码找到食材: ID=184, 名称=黄玉米粒（新鲜）
[2025-06-29 09:34:20] 第53行: 导入成功
[2025-06-29 09:34:20] --- 处理第54行 ---
[2025-06-29 09:34:20] 第54行字段: 编码='', 名称='', 数量=''
[2025-06-29 09:34:20] 第54行: 关键字段为空，跳过
[2025-06-29 09:34:20] --- 处理第55行 ---
[2025-06-29 09:34:20] 第55行: 空行，跳过
[2025-06-29 09:34:20] --- 处理第56行 ---
[2025-06-29 09:34:20] 第56行: 空行，跳过
[2025-06-29 09:34:20] --- 处理第57行 ---
[2025-06-29 09:34:20] 第57行: 空行，跳过
[2025-06-29 09:34:20] --- 处理第58行 ---
[2025-06-29 09:34:20] 第58行: 空行，跳过
[2025-06-29 09:34:20] --- 处理第59行 ---
[2025-06-29 09:34:20] 第59行: 空行，跳过
[2025-06-29 09:34:20] --- 处理第60行 ---
[2025-06-29 09:34:20] 第60行: 空行，跳过
[2025-06-29 09:34:20] --- 处理第61行 ---
[2025-06-29 09:34:20] 第61行: 空行，跳过
[2025-06-29 09:34:20] --- 处理第62行 ---
[2025-06-29 09:34:20] 第62行: 空行，跳过
[2025-06-29 09:34:20] --- 处理第63行 ---
[2025-06-29 09:34:20] 第63行: 空行，跳过
[2025-06-29 09:34:20] --- 处理第64行 ---
[2025-06-29 09:34:20] 第64行: 空行，跳过
[2025-06-29 09:34:20] --- 处理第65行 ---
[2025-06-29 09:34:20] 第65行: 空行，跳过
[2025-06-29 09:34:20] --- 处理第66行 ---
[2025-06-29 09:34:20] 第66行: 空行，跳过
[2025-06-29 09:34:20] --- 处理第67行 ---
[2025-06-29 09:34:20] 第67行: 空行，跳过
[2025-06-29 09:34:20] --- 处理第68行 ---
[2025-06-29 09:34:20] 第68行: 空行，跳过
[2025-06-29 09:34:20] --- 处理第69行 ---
[2025-06-29 09:34:20] 第69行: 空行，跳过
[2025-06-29 09:34:20] --- 处理第70行 ---
[2025-06-29 09:34:20] 第70行: 空行，跳过
[2025-06-29 09:34:20] --- 处理第71行 ---
[2025-06-29 09:34:20] 第71行: 空行，跳过
[2025-06-29 09:34:20] --- 处理第72行 ---
[2025-06-29 09:34:20] 第72行: 空行，跳过
[2025-06-29 09:34:20] --- 处理第73行 ---
[2025-06-29 09:34:20] 第73行: 空行，跳过
[2025-06-29 09:34:20] --- 处理第74行 ---
[2025-06-29 09:34:20] 第74行: 空行，跳过
[2025-06-29 09:34:20] --- 处理第75行 ---
[2025-06-29 09:34:20] 第75行: 空行，跳过
[2025-06-29 09:34:20] --- 处理第76行 ---
[2025-06-29 09:34:20] 第76行: 空行，跳过
[2025-06-29 09:34:20] --- 处理第77行 ---
[2025-06-29 09:34:20] 第77行: 空行，跳过
[2025-06-29 09:34:21] --- 处理第78行 ---
[2025-06-29 09:34:21] 第78行: 空行，跳过
[2025-06-29 09:34:21] --- 处理第79行 ---
[2025-06-29 09:34:21] 第79行: 空行，跳过
[2025-06-29 09:34:21] --- 处理第80行 ---
[2025-06-29 09:34:21] 第80行: 空行，跳过
[2025-06-29 09:34:21] --- 处理第81行 ---
[2025-06-29 09:34:21] 第81行: 空行，跳过
[2025-06-29 09:34:21] --- 处理第82行 ---
[2025-06-29 09:34:21] 第82行: 空行，跳过
[2025-06-29 09:34:21] --- 处理第83行 ---
[2025-06-29 09:34:21] 第83行: 空行，跳过
[2025-06-29 09:34:21] --- 处理第84行 ---
[2025-06-29 09:34:21] 第84行: 空行，跳过
[2025-06-29 09:34:21] --- 处理第85行 ---
[2025-06-29 09:34:21] 第85行: 空行，跳过
[2025-06-29 09:34:21] --- 处理第86行 ---
[2025-06-29 09:34:21] 第86行: 空行，跳过
[2025-06-29 09:34:21] --- 处理第87行 ---
[2025-06-29 09:34:21] 第87行: 空行，跳过
[2025-06-29 09:34:21] --- 处理第88行 ---
[2025-06-29 09:34:21] 第88行: 空行，跳过
[2025-06-29 09:34:21] --- 处理第89行 ---
[2025-06-29 09:34:21] 第89行: 空行，跳过
[2025-06-29 09:34:21] --- 处理第90行 ---
[2025-06-29 09:34:21] 第90行: 空行，跳过
[2025-06-29 09:34:21] --- 处理第91行 ---
[2025-06-29 09:34:21] 第91行: 空行，跳过
[2025-06-29 09:34:21] --- 处理第92行 ---
[2025-06-29 09:34:21] 第92行: 空行，跳过
[2025-06-29 09:34:21] --- 处理第93行 ---
[2025-06-29 09:34:21] 第93行: 空行，跳过
[2025-06-29 09:34:21] --- 处理第94行 ---
[2025-06-29 09:34:21] 第94行: 空行，跳过
[2025-06-29 09:34:21] --- 处理第95行 ---
[2025-06-29 09:34:21] 第95行: 空行，跳过
[2025-06-29 09:34:21] --- 处理第96行 ---
[2025-06-29 09:34:21] 第96行: 空行，跳过
[2025-06-29 09:34:21] --- 处理第97行 ---
[2025-06-29 09:34:21] 第97行: 空行，跳过
[2025-06-29 09:34:21] --- 处理第98行 ---
[2025-06-29 09:34:21] 第98行: 空行，跳过
[2025-06-29 09:34:21] --- 处理第99行 ---
[2025-06-29 09:34:21] 第99行: 空行，跳过
[2025-06-29 09:34:21] --- 处理第100行 ---
[2025-06-29 09:34:21] 第100行: 空行，跳过
[2025-06-29 09:34:21] === 明细数据处理完成 ===
[2025-06-29 09:34:21] 成功: 46, 失败: 1
