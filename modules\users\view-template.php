<?php require_once dirname(__DIR__, 2) . '/includes/header-modular.php'; ?>

<div class="main-content">
    <?php require_once dirname(__DIR__, 2) . '/includes/sidebar-modular.php'; ?>

    <div class="content">
        <div class="content-header">
            <h1>
                <i class="fas fa-eye"></i>
                查看用户
            </h1>
            <div class="header-actions">
                <a href="index.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i>
                    返回列表
                </a>
                <?php if (!empty($user['id'])): ?>
                <a href="index.php?action=edit&id=<?= htmlspecialchars($user['id']) ?>" class="btn btn-warning">
                    <i class="fas fa-edit"></i>
                    编辑
                </a>
                <?php endif; ?>
            </div>
        </div>

        <?php if (isset($error)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle"></i>
            <?= htmlspecialchars($error) ?>
        </div>
        <?php endif; ?>

        <div class="card">
            <div class="card-header">
                <div class="card-title"><i class="fas fa-user"></i> 基本信息</div>
            </div>
            <div class="row" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(260px, 1fr)); gap: 16px;">
                <div class="col">
                    <div><strong>用户名：</strong> <?= htmlspecialchars($user['name'] ?? '-') ?></div>
                    <div><strong>真实姓名：</strong> <?= htmlspecialchars($user['real_name'] ?? '-') ?></div>
                    <div><strong>邮箱：</strong> <?= htmlspecialchars($user['email'] ?? '-') ?></div>
                </div>
                <div class="col">
                    <div><strong>手机号：</strong> <?= htmlspecialchars($user['phone'] ?? '-') ?></div>
                    <div><strong>角色：</strong> <?= htmlspecialchars($user['role_display_name'] ?? $user['role_name'] ?? '-') ?></div>
                    <div><strong>状态：</strong> <?= isset($user['status']) && (int)$user['status'] === 1 ? '启用' : '停用' ?></div>
                </div>
                <div class="col">
                    <div><strong>创建时间：</strong> <?= htmlspecialchars($user['created_at'] ?? '-') ?></div>
                    <div><strong>最后登录：</strong> <?= htmlspecialchars($user['last_login_at'] ?? '-') ?></div>
                </div>
            </div>
        </div>

        <div class="table-container">
            <table class="table">
                <thead>
                    <tr>
                        <th>时间</th>
                        <th>动作</th>
                        <th>详情</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($logs)): ?>
                        <?php foreach ($logs as $log): ?>
                            <tr>
                                <td><?= htmlspecialchars($log['created_at'] ?? '-') ?></td>
                                <td><?= htmlspecialchars($log['action'] ?? '-') ?></td>
                                <td><?= htmlspecialchars($log['details'] ?? '-') ?></td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="3" class="text-center text-muted">
                                <i class="fas fa-inbox"></i> 暂无日志
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?php require_once dirname(__DIR__, 2) . '/includes/footer-modular.php'; ?>


