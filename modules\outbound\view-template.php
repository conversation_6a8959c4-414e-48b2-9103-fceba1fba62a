<?php require_once dirname(__DIR__, 2) . '/includes/header-modular.php'; ?>

<div class="main-content">
    <?php require_once dirname(__DIR__, 2) . '/includes/sidebar-modular.php'; ?>
    
    <div class="content">
        <div class="content-header">
            <h1>
                <i class="fas fa-eye"></i>
                出库记录详情
            </h1>
            <div class="header-actions">
                <a href="index.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i>
                    返回列表
                </a>
            </div>
        </div>

        <div class="detail-container">
            <div class="detail-card">
                <div class="card-header">
                    <h3>
                        <i class="fas fa-info-circle"></i>
                        基本信息
                    </h3>
                    <div class="status-badge">
                        <span class="badge badge-success">已出库</span>
                    </div>
                </div>
                
                <div class="card-content">
                    <div class="detail-grid">
                        <div class="detail-item">
                            <label>批次号</label>
                            <span class="value">
                                <code><?= htmlspecialchars($data['record']['batch_number']) ?></code>
                            </span>
                        </div>

                        <div class="detail-item">
                            <label>食材名称</label>
                            <span class="value">
                                <strong><?= htmlspecialchars($data['record']['ingredient_name']) ?></strong>
                            </span>
                        </div>

                        <div class="detail-item">
                            <label>食材分类</label>
                            <span class="value">
                                <span class="badge badge-info"><?= htmlspecialchars($data['record']['category_name'] ?? '') ?></span>
                            </span>
                        </div>

                        <div class="detail-item">
                            <label>出库数量</label>
                            <span class="value quantity">
                                <?= number_format($data['record']['quantity'], 1) ?> 
                                <small class="unit"><?= htmlspecialchars($data['record']['unit']) ?></small>
                            </span>
                        </div>

                        <div class="detail-item">
                            <label>单价</label>
                            <span class="value">
                                ¥<?= number_format($data['record']['unit_price'], 2) ?>
                            </span>
                        </div>

                        <div class="detail-item">
                            <label>总价值</label>
                            <span class="value amount">
                                ¥<?= number_format($data['record']['total_amount'], 2) ?>
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="detail-card">
                <div class="card-header">
                    <h3>
                        <i class="fas fa-utensils"></i>
                        用餐信息
                    </h3>
                </div>
                
                <div class="card-content">
                    <div class="detail-grid">
                        <div class="detail-item">
                            <label>餐次</label>
                            <span class="value">
                                <?php
                                $mealTypes = [
                                    'breakfast' => '<span class="badge badge-warning">早餐</span>',
                                    'lunch' => '<span class="badge badge-success">午餐</span>',
                                    'dinner' => '<span class="badge badge-info">晚餐</span>'
                                ];
                                echo $mealTypes[$data['record']['meal_type']] ?? $data['record']['meal_type'];
                                ?>
                            </span>
                        </div>

                        <div class="detail-item">
                            <label>用餐日期</label>
                            <span class="value">
                                <i class="fas fa-calendar-alt"></i>
                                <?= date('Y年m月d日', strtotime($data['record']['meal_date'])) ?>
                                <small class="text-muted">(<?= date('w', strtotime($data['record']['meal_date'])) == 0 ? '周日' : '周' . ['', '一', '二', '三', '四', '五', '六'][date('w', strtotime($data['record']['meal_date']))] ?>)</small>
                            </span>
                        </div>

                        <div class="detail-item">
                            <label>具体用途</label>
                            <span class="value">
                                <?= htmlspecialchars($data['record']['purpose'] ?? '未指定') ?>
                            </span>
                        </div>

                        <div class="detail-item">
                            <label>操作员</label>
                            <span class="value">
                                <i class="fas fa-user"></i>
                                <?= htmlspecialchars($data['record']['operator_name'] ?? '') ?>
                            </span>
                        </div>

                        <div class="detail-item">
                            <label>出库时间</label>
                            <span class="value">
                                <i class="fas fa-clock"></i>
                                <?= date('Y-m-d H:i:s', strtotime($data['record']['created_at'])) ?>
                            </span>
                        </div>

                        <?php if (!empty($data['record']['notes'])): ?>
                        <div class="detail-item full-width">
                            <label>备注信息</label>
                            <span class="value notes">
                                <?= nl2br(htmlspecialchars($data['record']['notes'])) ?>
                            </span>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- 相关操作 -->
            <div class="action-card">
                <h3>
                    <i class="fas fa-tools"></i>
                    相关操作
                </h3>
                <div class="action-buttons">
                    <a href="index.php?action=delete&id=<?= $data['record']['id'] ?>" 
                       class="btn btn-danger"
                       onclick="return confirm('确定要删除这条出库记录吗？删除后会恢复相应的库存。')">
                        <i class="fas fa-trash"></i>
                        删除记录
                    </a>
                    <button type="button" class="btn btn-info" onclick="printRecord()">
                        <i class="fas fa-print"></i>
                        打印记录
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.detail-container {
    max-width: 800px;
    margin: 0 auto;
}

.detail-card {
    background: white;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
}

.card-header {
    background: #f8fafc;
    padding: 20px;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h3 {
    margin: 0;
    color: #2d3748;
    display: flex;
    align-items: center;
    gap: 10px;
}

.status-badge {
    display: flex;
    gap: 10px;
}

.card-content {
    padding: 20px;
}

.detail-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.detail-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.detail-item.full-width {
    grid-column: 1 / -1;
}

.detail-item label {
    font-weight: 600;
    color: #4a5568;
    font-size: 14px;
}

.detail-item .value {
    color: #2d3748;
    font-size: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.detail-item .value i {
    color: #a0aec0;
    font-size: 14px;
}

.quantity {
    font-weight: bold;
    color: #2b6cb0;
}

.unit {
    color: #718096;
    font-weight: normal;
}

.amount {
    font-weight: bold;
    color: #38a169;
    font-size: 18px;
}

.notes {
    background: #f7fafc;
    padding: 10px;
    border-radius: 4px;
    border-left: 4px solid #4299e1;
    font-style: italic;
}

.badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: normal;
}

.badge-info { background: #bee3f8; color: #2b6cb0; }
.badge-success { background: #c6f6d5; color: #22543d; }
.badge-warning { background: #faf089; color: #744210; }

.action-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.action-card h3 {
    margin: 0 0 15px 0;
    color: #2d3748;
    display: flex;
    align-items: center;
    gap: 10px;
}

.action-buttons {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.text-muted {
    color: #a0aec0;
    font-size: 14px;
}

@media (max-width: 768px) {
    .detail-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .card-header {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }
    
    .action-buttons {
        flex-direction: column;
    }
}

@media print {
    .content-header,
    .action-card,
    .sidebar {
        display: none !important;
    }
    
    .main-content {
        margin: 0 !important;
    }
    
    .content {
        margin: 0 !important;
        padding: 0 !important;
    }
    
    .detail-container {
        max-width: none;
        margin: 0;
    }
    
    .detail-card {
        box-shadow: none;
        border: 1px solid #e2e8f0;
        margin-bottom: 20px;
    }
}
</style>

<script>
function printRecord() {
    window.print();
}
</script>

<?php require_once dirname(__DIR__, 2) . '/includes/footer-modular.php'; ?>