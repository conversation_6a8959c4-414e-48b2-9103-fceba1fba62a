<?php
/**
 * 分类API功能测试
 */
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>分类API功能测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>

<h2>分类API功能测试</h2>

<div class="test-section">
    <h3>1. 环境检查</h3>
    <?php
    echo "<p class='info'>PHP版本: " . PHP_VERSION . "</p>";
    echo "<p class='info'>当前时间: " . date('Y-m-d H:i:s') . "</p>";
    
    $extensions = ['pdo', 'pdo_mysql', 'json'];
    foreach ($extensions as $ext) {
        $status = extension_loaded($ext) ? 'success' : 'error';
        $symbol = extension_loaded($ext) ? '✅' : '❌';
        echo "<p class='$status'>$symbol $ext 扩展</p>";
    }
    ?>
</div>

<div class="test-section">
    <h3>2. 文件存在性检查</h3>
    <?php
    $files = [
        '../modules/categories/check-category-name.php' => '原版API文件',
        '../modules/categories/check-name-simple.php' => '简化版API文件',
        '../includes/Database.php' => '数据库类文件',
        '../config/database.php' => '数据库配置文件'
    ];
    
    foreach ($files as $file => $description) {
        $exists = file_exists($file);
        $status = $exists ? 'success' : 'error';
        $symbol = $exists ? '✅' : '❌';
        echo "<p class='$status'>$symbol $description ($file)</p>";
    }
    ?>
</div>

<div class="test-section">
    <h3>3. 数据库连接测试</h3>
    <?php
    try {
        require_once '../includes/Database.php';
        $db = Database::getInstance();
        echo "<p class='success'>✅ 数据库连接成功</p>";
        
        // 测试查询
        $categories = $db->fetchAll("SELECT name FROM ingredient_categories LIMIT 5");
        echo "<p class='info'>现有分类: " . implode(', ', array_column($categories, 'name')) . "</p>";
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ 数据库连接失败: " . $e->getMessage() . "</p>";
    }
    ?>
</div>

<div class="test-section">
    <h3>4. API端点测试</h3>
    
    <h4>4.1 简化版API测试</h4>
    <div id="simpleApiTest">
        <button onclick="testSimpleApi()">测试简化版API</button>
        <div id="simpleApiResult"></div>
    </div>
    
    <h4>4.2 原版API测试</h4>
    <div id="originalApiTest">
        <button onclick="testOriginalApi()">测试原版API</button>
        <div id="originalApiResult"></div>
    </div>
</div>

<div class="test-section">
    <h3>5. 分类数据分析</h3>
    <?php
    try {
        $db = Database::getInstance();
        
        // 统计分析
        $totalCount = $db->fetchOne("SELECT COUNT(*) as count FROM ingredient_categories");
        $activeCount = $db->fetchOne("SELECT COUNT(*) as count FROM ingredient_categories WHERE status = 1");
        $inactiveCount = $db->fetchOne("SELECT COUNT(*) as count FROM ingredient_categories WHERE status = 0");
        
        echo "<table>";
        echo "<tr><th>统计项</th><th>数量</th></tr>";
        echo "<tr><td>总分类数</td><td>{$totalCount['count']}</td></tr>";
        echo "<tr><td>启用分类</td><td>{$activeCount['count']}</td></tr>";
        echo "<tr><td>禁用分类</td><td>{$inactiveCount['count']}</td></tr>";
        echo "</table>";
        
        // 重复名称检查
        $duplicates = $db->fetchAll("
            SELECT name, COUNT(*) as count 
            FROM ingredient_categories 
            GROUP BY name 
            HAVING COUNT(*) > 1
        ");
        
        if (!empty($duplicates)) {
            echo "<h4>重复名称检查：</h4>";
            echo "<table>";
            echo "<tr><th>分类名称</th><th>重复次数</th></tr>";
            foreach ($duplicates as $dup) {
                echo "<tr><td>" . htmlspecialchars($dup['name']) . "</td><td>{$dup['count']}</td></tr>";
            }
            echo "</table>";
        } else {
            echo "<p class='success'>✅ 没有重复的分类名称</p>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ 数据分析失败: " . $e->getMessage() . "</p>";
    }
    ?>
</div>

<div class="test-section">
    <h3>6. 性能测试</h3>
    <button onclick="performanceTest()">执行性能测试</button>
    <div id="performanceResult"></div>
</div>

<script>
function testSimpleApi() {
    const resultDiv = document.getElementById('simpleApiResult');
    resultDiv.innerHTML = '<p>正在测试...</p>';
    
    const testCases = [
        { name: '蔬菜类', expected: 'exists' },
        { name: '新分类_' + Date.now(), expected: 'available' },
        { name: '', expected: 'error' }
    ];
    
    let results = [];
    let completed = 0;
    
    testCases.forEach((testCase, index) => {
        fetch('../modules/categories/check-name-simple.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: 'name=' + encodeURIComponent(testCase.name)
        })
        .then(response => response.json())
        .then(data => {
            results[index] = {
                input: testCase.name,
                expected: testCase.expected,
                result: data,
                success: (testCase.expected === 'exists' && !data.available) || 
                        (testCase.expected === 'available' && data.available) ||
                        (testCase.expected === 'error' && data.error)
            };
            
            completed++;
            if (completed === testCases.length) {
                displayResults(results, 'simpleApiResult');
            }
        })
        .catch(error => {
            results[index] = { input: testCase.name, error: error.message };
            completed++;
            if (completed === testCases.length) {
                displayResults(results, 'simpleApiResult');
            }
        });
    });
}

function testOriginalApi() {
    const resultDiv = document.getElementById('originalApiResult');
    resultDiv.innerHTML = '<p>正在测试...</p>';
    
    fetch('../modules/categories/check-category-name.php', {
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: 'name=测试分类'
    })
    .then(response => response.json())
    .then(data => {
        resultDiv.innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
    })
    .catch(error => {
        resultDiv.innerHTML = '<p class="error">❌ 测试失败: ' + error.message + '</p>';
    });
}

function displayResults(results, containerId) {
    const container = document.getElementById(containerId);
    let html = '<table><tr><th>输入</th><th>期望</th><th>结果</th><th>状态</th></tr>';
    
    results.forEach(result => {
        const status = result.success ? '✅ 通过' : '❌ 失败';
        const statusClass = result.success ? 'success' : 'error';
        
        html += `<tr>
            <td>${result.input || '(空)'}</td>
            <td>${result.expected || 'N/A'}</td>
            <td><pre>${JSON.stringify(result.result || result.error, null, 2)}</pre></td>
            <td class="${statusClass}">${status}</td>
        </tr>`;
    });
    
    html += '</table>';
    container.innerHTML = html;
}

function performanceTest() {
    const resultDiv = document.getElementById('performanceResult');
    resultDiv.innerHTML = '<p>正在执行性能测试...</p>';
    
    const startTime = performance.now();
    const requests = [];
    
    // 并发测试10个请求
    for (let i = 0; i < 10; i++) {
        requests.push(
            fetch('../modules/categories/check-name-simple.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: 'name=性能测试_' + i
            })
        );
    }
    
    Promise.all(requests)
        .then(responses => Promise.all(responses.map(r => r.json())))
        .then(results => {
            const endTime = performance.now();
            const totalTime = endTime - startTime;
            const avgTime = totalTime / 10;
            
            resultDiv.innerHTML = `
                <p class="success">✅ 性能测试完成</p>
                <p>总耗时: ${totalTime.toFixed(2)}ms</p>
                <p>平均耗时: ${avgTime.toFixed(2)}ms</p>
                <p>成功请求: ${results.filter(r => !r.error).length}/10</p>
            `;
        })
        .catch(error => {
            resultDiv.innerHTML = '<p class="error">❌ 性能测试失败: ' + error.message + '</p>';
        });
}
</script>

<hr>
<p><a href="../modules/categories/index.php">分类管理</a> | <a href="../index.php">返回首页</a></p>

</body>
</html>
