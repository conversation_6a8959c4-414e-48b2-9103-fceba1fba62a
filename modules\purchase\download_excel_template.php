<?php
/**
 * 采购单Excel模板下载
 */

// 设置响应头
header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
header('Content-Disposition: attachment; filename="采购单导入模板.xlsx"');
header('Cache-Control: max-age=0');

/**
 * 创建简单的XLSX文件
 */
function createSimpleXlsx($data)
{
    $tempDir = sys_get_temp_dir() . '/xlsx_' . uniqid();
    mkdir($tempDir);
    mkdir($tempDir . '/xl');
    mkdir($tempDir . '/xl/worksheets');
    mkdir($tempDir . '/_rels');
    mkdir($tempDir . '/xl/_rels');

    // 创建 [Content_Types].xml
    $contentTypes = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Types xmlns="http://schemas.openxmlformats.org/package/2006/content-types">
    <Default Extension="rels" ContentType="application/vnd.openxmlformats-package.relationships+xml"/>
    <Default Extension="xml" ContentType="application/xml"/>
    <Override PartName="/xl/workbook.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml"/>
    <Override PartName="/xl/worksheets/sheet1.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml"/>
    <Override PartName="/xl/sharedStrings.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml"/>
</Types>';
    file_put_contents($tempDir . '/[Content_Types].xml', $contentTypes);

    // 创建 _rels/.rels
    $rels = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships">
    <Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument" Target="xl/workbook.xml"/>
</Relationships>';
    file_put_contents($tempDir . '/_rels/.rels', $rels);

    // 创建 xl/_rels/workbook.xml.rels
    $workbookRels = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships">
    <Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet" Target="worksheets/sheet1.xml"/>
    <Relationship Id="rId2" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/sharedStrings" Target="sharedStrings.xml"/>
</Relationships>';
    file_put_contents($tempDir . '/xl/_rels/workbook.xml.rels', $workbookRels);

    // 创建 xl/workbook.xml
    $workbook = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<workbook xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships">
    <sheets>
        <sheet name="采购单模板" sheetId="1" r:id="rId1"/>
    </sheets>
</workbook>';
    file_put_contents($tempDir . '/xl/workbook.xml', $workbook);

    // 收集所有字符串
    $strings = [];
    $stringMap = [];
    foreach ($data as $row) {
        foreach ($row as $cell) {
            if (is_string($cell) && !is_numeric($cell)) {
                if (!isset($stringMap[$cell])) {
                    $stringMap[$cell] = count($strings);
                    $strings[] = $cell;
                }
            }
        }
    }

    // 创建 xl/sharedStrings.xml
    $sharedStrings = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<sst xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" count="' . count($strings) . '" uniqueCount="' . count($strings) . '">';
    foreach ($strings as $string) {
        $sharedStrings .= '<si><t>' . htmlspecialchars($string, ENT_XML1, 'UTF-8') . '</t></si>';
    }
    $sharedStrings .= '</sst>';
    file_put_contents($tempDir . '/xl/sharedStrings.xml', $sharedStrings);

    // 创建 xl/worksheets/sheet1.xml
    $worksheet = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<worksheet xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships">
    <sheetData>';

    foreach ($data as $rowIndex => $row) {
        $rowNum = $rowIndex + 1;
        $worksheet .= '<row r="' . $rowNum . '">';
        
        foreach ($row as $colIndex => $cell) {
            $colLetter = chr(65 + $colIndex); // A, B, C, ...
            $cellRef = $colLetter . $rowNum;
            
            if (is_string($cell) && !is_numeric($cell)) {
                // 字符串类型，使用共享字符串
                $stringIndex = $stringMap[$cell];
                $worksheet .= '<c r="' . $cellRef . '" t="s"><v>' . $stringIndex . '</v></c>';
            } else {
                // 数字类型
                $worksheet .= '<c r="' . $cellRef . '"><v>' . htmlspecialchars($cell, ENT_XML1, 'UTF-8') . '</v></c>';
            }
        }
        
        $worksheet .= '</row>';
    }

    $worksheet .= '</sheetData></worksheet>';
    file_put_contents($tempDir . '/xl/worksheets/sheet1.xml', $worksheet);

    // 创建ZIP文件
    $zipFile = tempnam(sys_get_temp_dir(), 'xlsx') . '.xlsx';
    $zip = new ZipArchive();
    
    if ($zip->open($zipFile, ZipArchive::CREATE) !== TRUE) {
        throw new Exception('无法创建XLSX文件');
    }

    // 添加所有文件到ZIP
    $iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($tempDir));
    foreach ($iterator as $file) {
        if ($file->isFile()) {
            $relativePath = str_replace($tempDir . DIRECTORY_SEPARATOR, '', $file->getPathname());
            $relativePath = str_replace('\\', '/', $relativePath);
            $zip->addFile($file->getPathname(), $relativePath);
        }
    }

    $zip->close();

    // 清理临时目录
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($tempDir, RecursiveDirectoryIterator::SKIP_DOTS),
        RecursiveIteratorIterator::CHILD_FIRST
    );
    foreach ($iterator as $file) {
        if ($file->isDir()) {
            rmdir($file->getPathname());
        } else {
            unlink($file->getPathname());
        }
    }
    rmdir($tempDir);

    return $zipFile;
}

// 基于test.xlsx格式的模板数据
$templateData = [
    ['供应商名称', '订单日期', '食材名称', '数量', '单价', '备注'], // 标题行
    ['新鲜蔬菜供应商', '2024-01-15', '白菜', '50', '2.50', '早餐用'],
    ['新鲜蔬菜供应商', '2024-01-15', '土豆', '30', '3.00', '午餐用'],
    ['肉类批发商', '2024-01-15', '猪肉', '20', '25.00', '午餐用'],
    ['肉类批发商', '2024-01-15', '鸡蛋', '40', '8.50', '早餐用'],
    ['水产供应商', '2024-01-15', '鲫鱼', '15', '12.00', '晚餐用'],
    ['粮食供应商', '2024-01-15', '大米', '100', '4.20', '主食用'],
    ['调料供应商', '2024-01-15', '食用油', '5', '35.00', '厨房用'],
    ['调料供应商', '2024-01-15', '生抽', '10', '8.50', '调味用']
];

try {
    // 创建Excel文件
    $excelFile = createSimpleXlsx($templateData);
    
    // 输出文件内容
    readfile($excelFile);
    
    // 清理临时文件
    unlink($excelFile);
    
} catch (Exception $e) {
    // 如果创建失败，返回错误信息
    header('Content-Type: text/plain; charset=utf-8');
    echo '模板生成失败: ' . $e->getMessage();
    echo "\n\n请手动创建Excel文件，包含以下列：\n";
    echo "供应商名称 | 订单日期 | 食材名称 | 数量 | 单价 | 备注\n";
    echo "\n示例数据：\n";
    echo "新鲜蔬菜供应商 | 2024-01-15 | 白菜 | 50 | 2.50 | 早餐用\n";
    echo "肉类批发商 | 2024-01-15 | 猪肉 | 20 | 25.00 | 午餐用\n";
}
?>
