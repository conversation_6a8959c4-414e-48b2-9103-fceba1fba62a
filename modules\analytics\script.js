// 趋势分析交互脚本

// 全局变量
let currentChart = null;
let chartData = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initAnalytics();
});

// 初始化分析页面
function initAnalytics() {
    // 初始化图表
    if (typeof analyticsData !== 'undefined') {
        chartData = analyticsData;
        initCurrentChart();
        initOverviewCharts();
    }

    // 初始化交互功能
    initInteractions();

    // 初始化动画
    initAnimations();
}

// 初始化当前图表
function initCurrentChart() {
    const chartContainer = document.querySelector('.trend-chart canvas');
    if (!chartContainer) return;
    
    const ctx = chartContainer.getContext('2d');
    const metric = getCurrentMetric();
    
    switch (metric) {
        case 'purchase':
            initPurchaseTrendChart(ctx);
            break;
        case 'inventory':
            initInventoryTrendChart(ctx);
            break;
        case 'cost':
            initCostTrendChart(ctx);
            break;
        case 'supplier':
            initSupplierTrendChart(ctx);
            break;
    }
}

// 获取当前指标
function getCurrentMetric() {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('metric') || 'purchase';
}

// 初始化库存趋势图表
function initInventoryTrendChart(ctx) {
    if (!chartData || !chartData.datasets) return;
    
    currentChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: chartData.labels,
            datasets: [{
                label: '库存价值 (¥)',
                data: chartData.datasets[0].data,
                borderColor: '#10b981',
                backgroundColor: 'rgba(16, 185, 129, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                yAxisID: 'y'
            }, {
                label: '周转率',
                data: chartData.datasets[1].data,
                borderColor: '#f59e0b',
                backgroundColor: 'rgba(245, 158, 11, 0.1)',
                borderWidth: 3,
                fill: false,
                tension: 0.4,
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                intersect: false,
                mode: 'index'
            },
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            if (context.datasetIndex === 0) {
                                return '库存价值: ¥' + context.parsed.y.toLocaleString();
                            } else {
                                return '周转率: ' + context.parsed.y.toFixed(1);
                            }
                        }
                    }
                }
            },
            scales: {
                x: {
                    display: true,
                    title: {
                        display: true,
                        text: '时间'
                    }
                },
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: '库存价值 (¥)'
                    },
                    ticks: {
                        callback: function(value) {
                            return '¥' + value.toLocaleString();
                        }
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: '周转率'
                    },
                    grid: {
                        drawOnChartArea: false,
                    },
                    ticks: {
                        callback: function(value) {
                            return value.toFixed(1);
                        }
                    }
                }
            }
        }
    });
}

// 初始化成本趋势图表
function initCostTrendChart(ctx) {
    if (!chartData || !chartData.datasets) return;
    
    currentChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: chartData.labels,
            datasets: [{
                label: '每餐成本 (¥)',
                data: chartData.datasets[0].data,
                borderColor: '#ef4444',
                backgroundColor: 'rgba(239, 68, 68, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                yAxisID: 'y'
            }, {
                label: '日节约金额 (¥)',
                data: chartData.datasets[1].data,
                borderColor: '#10b981',
                backgroundColor: 'rgba(16, 185, 129, 0.1)',
                borderWidth: 3,
                fill: false,
                tension: 0.4,
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                intersect: false,
                mode: 'index'
            },
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            if (context.datasetIndex === 0) {
                                return '每餐成本: ¥' + context.parsed.y.toFixed(2);
                            } else {
                                return '日节约: ¥' + context.parsed.y.toLocaleString();
                            }
                        }
                    }
                }
            },
            scales: {
                x: {
                    display: true,
                    title: {
                        display: true,
                        text: '时间'
                    }
                },
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: '每餐成本 (¥)'
                    },
                    ticks: {
                        callback: function(value) {
                            return '¥' + value.toFixed(2);
                        }
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: '日节约金额 (¥)'
                    },
                    grid: {
                        drawOnChartArea: false,
                    },
                    ticks: {
                        callback: function(value) {
                            return '¥' + value.toLocaleString();
                        }
                    }
                }
            }
        }
    });
}

// 初始化供应商趋势图表
function initSupplierTrendChart(ctx) {
    if (!chartData || !chartData.performance_trends) return;
    
    const performanceData = chartData.performance_trends;
    
    currentChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: performanceData.labels,
            datasets: [{
                label: '平均质量评分',
                data: performanceData.datasets[0].data,
                borderColor: '#3b82f6',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                borderWidth: 3,
                fill: false,
                tension: 0.4
            }, {
                label: '准时交付率 (%)',
                data: performanceData.datasets[1].data,
                borderColor: '#10b981',
                backgroundColor: 'rgba(16, 185, 129, 0.1)',
                borderWidth: 3,
                fill: false,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                intersect: false,
                mode: 'index'
            },
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            if (context.datasetIndex === 0) {
                                return '质量评分: ' + context.parsed.y + ' 分';
                            } else {
                                return '交付率: ' + context.parsed.y + '%';
                            }
                        }
                    }
                }
            },
            scales: {
                x: {
                    display: true,
                    title: {
                        display: true,
                        text: '时间'
                    }
                },
                y: {
                    display: true,
                    title: {
                        display: true,
                        text: '评分/百分比'
                    },
                    min: 0,
                    max: 100,
                    ticks: {
                        callback: function(value) {
                            return value;
                        }
                    }
                }
            }
        }
    });
}

// 初始化交互功能
function initInteractions() {
    // 添加图表控制按钮事件
    const chartToggles = document.querySelectorAll('.chart-toggle');
    chartToggles.forEach(toggle => {
        toggle.addEventListener('click', function() {
            const type = this.getAttribute('onclick').match(/'([^']+)'/)[1];
            if (typeof toggleChart === 'function') {
                toggleChart(type);
            }
        });
    });
    
    // 添加指标标签点击事件
    const metricTabs = document.querySelectorAll('.metric-tab');
    metricTabs.forEach(tab => {
        tab.addEventListener('click', function(e) {
            // 添加加载动画
            showLoadingAnimation();
        });
    });
}

// 初始化概览图表
function initOverviewCharts() {
    if (!chartData) return;

    // 主要趋势图
    initMainTrendChart();

    // 分布饼图
    initDistributionChart();

    // 对比柱状图
    initComparisonChart();

    // 预测图表
    initPredictionChart();
}

// 主要趋势图
function initMainTrendChart() {
    const canvas = document.getElementById('mainTrendChart');
    if (!canvas) return;

    const ctx = canvas.getContext('2d');

    new Chart(ctx, {
        type: 'line',
        data: {
            labels: chartData.labels || ['1月', '2月', '3月', '4月', '5月', '6月'],
            datasets: [{
                label: '主要指标',
                data: chartData.datasets ? chartData.datasets[0].data : [120, 135, 150, 142, 168, 175],
                borderColor: '#3b82f6',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

// 分布饼图
function initDistributionChart() {
    const canvas = document.getElementById('distributionChart');
    if (!canvas) return;

    const ctx = canvas.getContext('2d');

    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['蔬菜', '肉类', '粮食', '调料', '其他'],
            datasets: [{
                data: [35, 25, 20, 12, 8],
                backgroundColor: [
                    '#10b981',
                    '#f59e0b',
                    '#3b82f6',
                    '#ef4444',
                    '#8b5cf6'
                ],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 15,
                        usePointStyle: true
                    }
                }
            }
        }
    });
}

// 对比柱状图
function initComparisonChart() {
    const canvas = document.getElementById('comparisonChart');
    if (!canvas) return;

    const ctx = canvas.getContext('2d');

    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['本月', '上月', '去年同期'],
            datasets: [{
                label: '采购金额',
                data: [85000, 78000, 72000],
                backgroundColor: ['#10b981', '#f59e0b', '#6b7280'],
                borderRadius: 4,
                borderSkipped: false,
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '¥' + (value / 1000) + 'K';
                        }
                    }
                }
            }
        }
    });
}

// 预测图表
function initPredictionChart() {
    const canvas = document.getElementById('predictionChart');
    if (!canvas) return;

    const ctx = canvas.getContext('2d');

    new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月(预测)', '8月(预测)'],
            datasets: [{
                label: '实际数据',
                data: [120, 135, 150, 142, 168, 175, null, null],
                borderColor: '#3b82f6',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                borderWidth: 3,
                fill: false,
                tension: 0.4
            }, {
                label: '预测数据',
                data: [null, null, null, null, null, 175, 182, 190],
                borderColor: '#f59e0b',
                backgroundColor: 'rgba(245, 158, 11, 0.1)',
                borderWidth: 3,
                borderDash: [5, 5],
                fill: false,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

// 初始化动画
function initAnimations() {
    // 统计卡片动画
    animateStatCards();
    
    // 进度条动画
    animateProgressBars();
    
    // 置信度计量器动画
    animateConfidenceMeters();
}

// 统计卡片动画
function animateStatCards() {
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
}

// 进度条动画
function animateProgressBars() {
    const progressBars = document.querySelectorAll('.progress-fill');
    progressBars.forEach(bar => {
        const targetWidth = bar.style.width;
        bar.style.width = '0%';
        
        setTimeout(() => {
            bar.style.transition = 'width 1.5s ease';
            bar.style.width = targetWidth;
        }, 500);
    });
}

// 置信度计量器动画
function animateConfidenceMeters() {
    const confidenceFills = document.querySelectorAll('.confidence-fill');
    confidenceFills.forEach(fill => {
        const targetWidth = fill.style.width;
        fill.style.width = '0%';
        
        setTimeout(() => {
            fill.style.transition = 'width 2s ease';
            fill.style.width = targetWidth;
        }, 800);
    });
}

// 显示加载动画
function showLoadingAnimation() {
    const content = document.querySelector('.analytics-content');
    if (!content) return;
    
    // 创建加载覆盖层
    const overlay = document.createElement('div');
    overlay.className = 'loading-overlay';
    overlay.innerHTML = `
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>正在加载数据...</p>
        </div>
    `;
    
    overlay.style.cssText = `
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 100;
    `;
    
    content.style.position = 'relative';
    content.appendChild(overlay);
    
    // 2秒后移除加载动画
    setTimeout(() => {
        if (overlay.parentNode) {
            overlay.parentNode.removeChild(overlay);
        }
    }, 2000);
}

// 导出分析数据
function exportAnalytics() {
    const metric = getCurrentMetric();
    const timeRange = new URLSearchParams(window.location.search).get('range') || '30d';
    
    const exportData = {
        metric: metric,
        timeRange: timeRange,
        timestamp: new Date().toISOString(),
        data: gatherAnalyticsData()
    };
    
    const fileName = `趋势分析_${getMetricName(metric)}_${formatDate(new Date())}.json`;
    downloadJSON(exportData, fileName);
    
    showNotification('趋势分析数据导出成功！', 'success');
}

// 获取指标名称
function getMetricName(metric) {
    const names = {
        'purchase': '采购趋势',
        'inventory': '库存趋势',
        'cost': '成本趋势',
        'supplier': '供应商趋势'
    };
    return names[metric] || '趋势分析';
}

// 收集分析数据
function gatherAnalyticsData() {
    const data = {
        charts: {},
        insights: {},
        predictions: {}
    };
    
    // 收集图表数据
    if (currentChart) {
        data.charts = {
            labels: currentChart.data.labels,
            datasets: currentChart.data.datasets.map(dataset => ({
                label: dataset.label,
                data: dataset.data
            }))
        };
    }
    
    // 收集洞察数据
    const findings = document.querySelectorAll('.findings-list li');
    data.insights.findings = Array.from(findings).map(item => item.textContent.trim());
    
    const recommendations = document.querySelectorAll('.recommendations-list li');
    data.insights.recommendations = Array.from(recommendations).map(item => item.textContent.trim());
    
    // 收集预测数据
    const predictionValue = document.querySelector('.prediction-value');
    if (predictionValue) {
        data.predictions.value = predictionValue.textContent.trim();
    }
    
    const confidenceValue = document.querySelector('.confidence-value');
    if (confidenceValue) {
        data.predictions.confidence = confidenceValue.textContent.trim();
    }
    
    return data;
}

// 工具函数
function downloadJSON(data, filename) {
    const jsonStr = JSON.stringify(data, null, 2);
    const blob = new Blob([jsonStr], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    URL.revokeObjectURL(url);
}

function formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}${month}${day}`;
}

function showNotification(message, type = 'info') {
    // 复用报表模块的通知功能
    if (typeof window.showNotification === 'function') {
        window.showNotification(message, type);
        return;
    }
    
    // 简单的alert作为后备
    alert(message);
}

// 添加加载动画样式
const style = document.createElement('style');
style.textContent = `
    .loading-spinner {
        text-align: center;
        color: #6b7280;
    }
    
    .spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #e5e7eb;
        border-top: 4px solid #3b82f6;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 15px;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    .loading-spinner p {
        margin: 0;
        font-size: 14px;
    }
`;
document.head.appendChild(style);
