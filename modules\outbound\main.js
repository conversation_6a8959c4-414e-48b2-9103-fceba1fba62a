/**
 * 出库管理模块JavaScript
 */

// DOM加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    initializeOutboundModule();
});

/**
 * 初始化出库模块
 */
function initializeOutboundModule() {
    // 初始化所有功能
    initializeFormValidation();
    initializeSearchForm();
    initializeBatchForm();
    initializeDeleteConfirmation();
}

/**
 * 初始化表单验证
 */
function initializeFormValidation() {
    const forms = document.querySelectorAll('form');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!validateForm(this)) {
                e.preventDefault();
                return false;
            }
        });
    });
}

/**
 * 验证表单
 */
function validateForm(form) {
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;
    
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            showFieldError(field, '此字段为必填项');
            isValid = false;
        } else {
            clearFieldError(field);
        }
    });
    
    // 验证数量字段
    const quantityFields = form.querySelectorAll('input[type="number"]');
    quantityFields.forEach(field => {
        if (field.value && parseFloat(field.value) <= 0) {
            showFieldError(field, '数量必须大于0');
            isValid = false;
        }
    });
    
    return isValid;
}

/**
 * 显示字段错误
 */
function showFieldError(field, message) {
    clearFieldError(field);
    
    field.classList.add('error');
    const errorDiv = document.createElement('div');
    errorDiv.className = 'field-error';
    errorDiv.textContent = message;
    
    field.parentNode.appendChild(errorDiv);
}

/**
 * 清除字段错误
 */
function clearFieldError(field) {
    field.classList.remove('error');
    const errorDiv = field.parentNode.querySelector('.field-error');
    if (errorDiv) {
        errorDiv.remove();
    }
}

/**
 * 初始化搜索表单
 */
function initializeSearchForm() {
    const searchForm = document.querySelector('.search-form');
    if (!searchForm) return;
    
    // 实时搜索
    const searchInput = searchForm.querySelector('input[name="search"]');
    if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                if (this.value.length >= 2 || this.value.length === 0) {
                    searchForm.submit();
                }
            }, 500);
        });
    }
    
    // 重置按钮
    const resetBtn = searchForm.querySelector('.btn-secondary');
    if (resetBtn) {
        resetBtn.addEventListener('click', function(e) {
            e.preventDefault();
            searchForm.reset();
            window.location.href = 'index.php';
        });
    }
}

/**
 * 初始化批量表单
 */
function initializeBatchForm() {
    const batchForm = document.getElementById('batch-form');
    if (!batchForm) return;
    
    // 添加食材按钮
    const addBtn = document.querySelector('[onclick="addItem()"]');
    if (addBtn) {
        addBtn.addEventListener('click', addItem);
    }
    
    // 监听食材选择变化
    document.addEventListener('change', function(e) {
        if (e.target.classList.contains('ingredient-select')) {
            updateItemInfo(e.target);
        }
    });
    
    // 监听数量变化
    document.addEventListener('input', function(e) {
        if (e.target.classList.contains('quantity-input')) {
            validateQuantity(e.target);
            updateSummary();
        }
    });
}

/**
 * 验证出库数量
 */
function validateQuantity(input) {
    const itemRow = input.closest('.item-row');
    if (!itemRow) return;
    
    const select = itemRow.querySelector('.ingredient-select');
    if (!select.value) return;
    
    const option = select.options[select.selectedIndex];
    const maxStock = parseFloat(option.dataset.stock);
    const quantity = parseFloat(input.value);
    const stockInfo = itemRow.querySelector('.stock-info');
    
    if (quantity > maxStock) {
        input.classList.add('error');
        stockInfo.innerHTML = `<i class="fas fa-exclamation-triangle"></i> 数量不能超过库存 ${maxStock}`;
        stockInfo.className = 'stock-info error';
    } else if (quantity > maxStock * 0.8) {
        input.classList.remove('error');
        stockInfo.innerHTML = `<i class="fas fa-exclamation-triangle"></i> 注意：出库数量较大`;
        stockInfo.className = 'stock-info warning';
    } else {
        input.classList.remove('error');
        stockInfo.innerHTML = `库存：${maxStock} ${option.dataset.unit}`;
        stockInfo.className = 'stock-info';
    }
}

/**
 * 初始化删除确认
 */
function initializeDeleteConfirmation() {
    const deleteLinks = document.querySelectorAll('a[href*="action=delete"]');
    
    deleteLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            if (!confirm('确定要删除这条出库记录吗？删除后会恢复相应的库存。')) {
                e.preventDefault();
                return false;
            }
        });
    });
}

/**
 * 显示加载状态
 */
function showLoading(button) {
    if (!button) return;
    
    button.disabled = true;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 处理中...';
    
    // 存储原始文本以便恢复
    button.dataset.originalText = originalText;
}

/**
 * 隐藏加载状态
 */
function hideLoading(button) {
    if (!button) return;
    
    button.disabled = false;
    if (button.dataset.originalText) {
        button.innerHTML = button.dataset.originalText;
        delete button.dataset.originalText;
    }
}

/**
 * 显示提示消息
 */
function showMessage(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type}`;
    alertDiv.innerHTML = `
        <i class="fas fa-${type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
        ${message}
    `;
    
    const content = document.querySelector('.content');
    content.insertBefore(alertDiv, content.firstChild);
    
    // 3秒后自动消失
    setTimeout(() => {
        alertDiv.remove();
    }, 3000);
}

/**
 * 格式化数字
 */
function formatNumber(num, decimals = 2) {
    return parseFloat(num).toFixed(decimals);
}

/**
 * 格式化货币
 */
function formatCurrency(amount) {
    return '¥' + parseFloat(amount).toFixed(2);
}

/**
 * 防抖函数
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * 节流函数
 */
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// 导出全局函数（供模板中的内联事件使用）
window.addItem = addItem;
window.removeItem = removeItem;
window.updateItemInfo = updateItemInfo;
window.updateSummary = updateSummary;
window.showMessage = showMessage;