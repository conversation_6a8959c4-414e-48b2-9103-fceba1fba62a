<?php
/**
 * 简化版分类导入页面
 */

// 引入通用头部
require_once '../../includes/header.php';

// 处理文件上传
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['import_file'])) {
    echo '<div style="padding: 20px; background: #d4edda; border: 1px solid #c3e6cb; margin: 20px; border-radius: 4px;">';
    echo '<h3>文件上传成功！</h3>';
    echo '<p>文件名: ' . htmlspecialchars($_FILES['import_file']['name']) . '</p>';
    echo '<p>文件大小: ' . number_format($_FILES['import_file']['size']) . ' 字节</p>';
    echo '<p>文件类型: ' . htmlspecialchars($_FILES['import_file']['type']) . '</p>';
    echo '<p><a href="index.php">返回分类管理</a></p>';
    echo '</div>';
    require_once '../../includes/footer.php';
    exit;
}
?>

<style>
.container { max-width: 800px; margin: 40px auto; padding: 20px; }
.upload-section { background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
.upload-area { border: 2px dashed #ddd; padding: 40px; text-align: center; margin: 20px 0; border-radius: 8px; }
.upload-area:hover { border-color: #007bff; background-color: #f8f9fa; }
.btn { padding: 12px 24px; margin: 10px 5px; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; }
.btn-primary { background: #007bff; color: white; }
.btn-success { background: #28a745; color: white; }
.btn-secondary { background: #6c757d; color: white; }
.file-info { background: #f8f9fa; padding: 15px; margin: 15px 0; border-radius: 4px; border: 1px solid #dee2e6; }
.form-group { margin: 15px 0; }
.form-check { margin: 10px 0; }
</style>

<div class="container">
    <h1>🔄 导入食材分类 (简化版)</h1>
    
    <div class="upload-section">
        <h3>选择CSV文件</h3>
        
        <form method="POST" enctype="multipart/form-data" id="uploadForm">
            <div class="upload-area" onclick="document.getElementById('fileInput').click()">
                <div style="font-size: 48px; color: #ccc; margin-bottom: 10px;">📁</div>
                <h4>点击此处选择文件</h4>
                <p>支持 CSV 格式，文件大小不超过 2MB</p>
                
                <input type="file" id="fileInput" name="import_file" 
                       accept=".csv,.xlsx,.xls" required style="display: none;">
                
                <div style="margin-top: 20px;">
                    <button type="button" class="btn btn-primary" onclick="document.getElementById('fileInput').click()">
                        📂 选择文件
                    </button>
                    <a href="download_template.php" class="btn btn-success">
                        📥 下载模板
                    </a>
                </div>
            </div>
            
            <div id="fileInfo" class="file-info" style="display: none;">
                <h4>已选择文件:</h4>
                <p><strong>文件名:</strong> <span id="fileName"></span></p>
                <p><strong>文件大小:</strong> <span id="fileSize"></span></p>
                <button type="button" class="btn btn-secondary" onclick="clearFile()">
                    🗑️ 清除文件
                </button>
            </div>
            
            <div class="form-group">
                <div class="form-check">
                    <input type="checkbox" id="skipDuplicates" name="skip_duplicates" checked>
                    <label for="skipDuplicates">跳过重复的分类名称</label>
                </div>
                <div class="form-check">
                    <input type="checkbox" id="updateExisting" name="update_existing">
                    <label for="updateExisting">更新已存在的分类信息</label>
                </div>
            </div>
            
            <div style="text-align: center; margin-top: 30px;">
                <button type="submit" class="btn btn-primary" id="submitBtn" disabled>
                    🚀 开始导入
                </button>
                <a href="index.php" class="btn btn-secondary">
                    ❌ 取消
                </a>
            </div>
        </form>
    </div>
    
    <div style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 8px;">
        <h4>📋 CSV格式说明</h4>
        <p>CSV文件应包含以下列（按顺序）：</p>
        <ol>
            <li><strong>分类名称</strong> (必填) - 分类的名称</li>
            <li><strong>父分类名称</strong> (可选) - 留空表示一级分类</li>
            <li><strong>分类代码</strong> (可选) - 留空自动生成</li>
            <li><strong>描述</strong> (可选) - 分类描述信息</li>
            <li><strong>排序</strong> (可选) - 排序数字，越小越靠前</li>
        </ol>
        
        <h4>📝 示例数据</h4>
        <table style="width: 100%; border-collapse: collapse; margin-top: 10px;">
            <tr style="background: #e9ecef;">
                <th style="border: 1px solid #ddd; padding: 8px;">分类名称</th>
                <th style="border: 1px solid #ddd; padding: 8px;">父分类名称</th>
                <th style="border: 1px solid #ddd; padding: 8px;">分类代码</th>
                <th style="border: 1px solid #ddd; padding: 8px;">描述</th>
                <th style="border: 1px solid #ddd; padding: 8px;">排序</th>
            </tr>
            <tr>
                <td style="border: 1px solid #ddd; padding: 8px;">蔬菜类</td>
                <td style="border: 1px solid #ddd; padding: 8px;"></td>
                <td style="border: 1px solid #ddd; padding: 8px;">VEG</td>
                <td style="border: 1px solid #ddd; padding: 8px;">各种新鲜蔬菜</td>
                <td style="border: 1px solid #ddd; padding: 8px;">1</td>
            </tr>
            <tr>
                <td style="border: 1px solid #ddd; padding: 8px;">叶菜类</td>
                <td style="border: 1px solid #ddd; padding: 8px;">蔬菜类</td>
                <td style="border: 1px solid #ddd; padding: 8px;">VEG_LEAF</td>
                <td style="border: 1px solid #ddd; padding: 8px;">叶类蔬菜</td>
                <td style="border: 1px solid #ddd; padding: 8px;">1</td>
            </tr>
        </table>
    </div>
</div>

<script>
console.log('🚀 简化版导入页面加载');

document.addEventListener('DOMContentLoaded', function() {
    const fileInput = document.getElementById('fileInput');
    const fileInfo = document.getElementById('fileInfo');
    const submitBtn = document.getElementById('submitBtn');
    
    console.log('📋 元素检查:', {
        fileInput: !!fileInput,
        fileInfo: !!fileInfo,
        submitBtn: !!submitBtn
    });
    
    // 文件选择事件
    fileInput.addEventListener('change', function(e) {
        console.log('📁 文件选择事件触发');
        const file = e.target.files[0];
        
        if (file) {
            console.log('✅ 文件选择成功:', file.name, file.size, 'bytes');
            
            // 显示文件信息
            document.getElementById('fileName').textContent = file.name;
            document.getElementById('fileSize').textContent = formatFileSize(file.size);
            fileInfo.style.display = 'block';
            submitBtn.disabled = false;
            
            // 验证文件
            if (validateFile(file)) {
                console.log('✅ 文件验证通过');
            } else {
                console.log('❌ 文件验证失败');
            }
        } else {
            console.log('⚠️ 没有选择文件');
            fileInfo.style.display = 'none';
            submitBtn.disabled = true;
        }
    });
    
    // 表单提交
    document.getElementById('uploadForm').addEventListener('submit', function(e) {
        console.log('📤 表单提交');
        const file = fileInput.files[0];
        
        if (!file) {
            e.preventDefault();
            alert('请选择要导入的文件');
            return;
        }
        
        console.log('✅ 开始上传文件:', file.name);
        submitBtn.disabled = true;
        submitBtn.innerHTML = '⏳ 导入中...';
    });
});

// 清除文件
function clearFile() {
    console.log('🗑️ 清除文件');
    document.getElementById('fileInput').value = '';
    document.getElementById('fileInfo').style.display = 'none';
    document.getElementById('submitBtn').disabled = true;
}

// 验证文件
function validateFile(file) {
    const allowedTypes = ['text/csv', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'];
    const maxSize = 2 * 1024 * 1024; // 2MB
    
    if (!allowedTypes.includes(file.type) && !file.name.match(/\.(csv|xlsx|xls)$/i)) {
        alert('请选择 CSV 或 Excel 格式的文件');
        return false;
    }
    
    if (file.size > maxSize) {
        alert('文件大小不能超过 2MB');
        return false;
    }
    
    return true;
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
</script>

<?php require_once '../../includes/footer.php'; ?>
