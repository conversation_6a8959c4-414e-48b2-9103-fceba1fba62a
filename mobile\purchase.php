<?php
/**
 * 移动端采购管理页面
 */
require_once '../includes/Database.php';

// 获取搜索参数
$search = $_GET['search'] ?? '';
$status = $_GET['status'] ?? '';

try {
    $db = Database::getInstance();
    
    // 构建查询条件
    $whereConditions = ['1=1'];
    $params = [];
    
    if ($search) {
        $whereConditions[] = '(po.order_number LIKE ? OR s.name LIKE ?)';
        $params[] = '%' . $search . '%';
        $params[] = '%' . $search . '%';
    }
    
    if ($status) {
        $whereConditions[] = 'po.status = ?';
        $params[] = $status;
    }
    
    $whereClause = implode(' AND ', $whereConditions);
    
    // 获取采购单数据
    $orders = $db->fetchAll("
        SELECT 
            po.id,
            po.order_number,
            po.total_amount,
            po.status,
            po.created_at,
            s.name as supplier_name,
            s.phone as supplier_phone,
            (SELECT COUNT(*) FROM purchase_order_items poi WHERE poi.order_id = po.id) as items_count
        FROM purchase_orders po
        LEFT JOIN suppliers s ON po.supplier_id = s.id
        WHERE $whereClause
        ORDER BY po.created_at DESC
        LIMIT 50
    ", $params);
    
    // 获取统计数据
    $stats = $db->fetchOne("
        SELECT
            COUNT(*) as total_orders,
            SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as pending_orders,
            SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as confirmed_orders,
            SUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as shipped_orders,
            SUM(CASE WHEN status = 4 THEN 1 ELSE 0 END) as completed_orders,
            SUM(CASE WHEN status = 5 THEN 1 ELSE 0 END) as cancelled_orders,
            SUM(total_amount) as total_amount
        FROM purchase_orders
        WHERE DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
    ") ?? [
        'total_orders' => 0,
        'pending_orders' => 0,
        'confirmed_orders' => 0,
        'shipped_orders' => 0,
        'completed_orders' => 0,
        'cancelled_orders' => 0,
        'total_amount' => 0
    ];
    
} catch (Exception $e) {
    $orders = [];
    $stats = [
        'total_orders' => 0,
        'pending_orders' => 0,
        'confirmed_orders' => 0,
        'shipped_orders' => 0,
        'completed_orders' => 0,
        'cancelled_orders' => 0,
        'total_amount' => 0
    ];
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>采购管理 - 移动端</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="style.css" rel="stylesheet">
    <style>
        /* 采购管理特有样式 */
        .search-section {
            background: white;
            margin: 60px 0 15px 0;
            padding: 15px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .search-form {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }
        
        .search-input-group {
            display: flex;
            gap: 8px;
        }
        
        .search-input {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            font-size: 16px;
        }
        
        .search-btn {
            padding: 12px 20px;
            background: #4299e1;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
        }
        
        .filter-select {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
            background: white;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        
        .stat-card {
            background: white;
            padding: 16px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 8px;
            font-size: 18px;
            color: white;
        }
        
        .stat-icon.blue { background: #4299e1; }
        .stat-icon.yellow { background: #f6e05e; }
        .stat-icon.green { background: #48bb78; }
        .stat-icon.purple { background: #9f7aea; }
        
        .stat-number {
            font-size: 20px;
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: 12px;
            color: #718096;
        }

        .section-header {
            padding: 0 16px;
            margin-bottom: 16px;
        }

        .section-title {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 18px;
            font-weight: 700;
            color: #1a202c;
            margin: 0;
        }

        .section-title i {
            color: #4299e1;
            font-size: 20px;
        }

        .count-badge {
            background: linear-gradient(135deg, #4299e1, #667eea);
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            margin-left: auto;
        }
        
        .orders-list {
            display: grid;
            gap: 24px;
            padding: 0 16px;
            margin-bottom: 30px;
        }

        .order-item-link {
            text-decoration: none;
            color: inherit;
            display: block;
        }

        .order-item {
            background: white;
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
            border: 1px solid #f1f5f9;
            position: relative;
            transition: all 0.3s ease;
            margin-bottom: 12px;
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .order-item-link:hover .order-item {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.12);
            border-color: #e2e8f0;
        }

        .order-item-link:active .order-item {
            transform: translateY(0);
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
        }




        
        .order-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .order-number {
            font-size: 16px;
            font-weight: 700;
            color: #1a202c;
        }

        .order-status {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .order-info {
            display: flex;
            gap: 20px;
            align-items: center;
            flex-wrap: wrap;
        }

        .info-row {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 14px;
            color: #64748b;
        }

        .info-row i {
            color: #4299e1;
            width: 14px;
            text-align: center;
        }

        .status-1 { background: linear-gradient(135deg, #fbbf24, #f59e0b); color: white; }
        .status-2 { background: linear-gradient(135deg, #10b981, #059669); color: white; }
        .status-3 { background: linear-gradient(135deg, #3b82f6, #2563eb); color: white; }
        .status-4 { background: linear-gradient(135deg, #8b5cf6, #7c3aed); color: white; }
        .status-5 { background: linear-gradient(135deg, #ef4444, #dc2626); color: white; }
        


        .order-amount {
            font-size: 18px;
            font-weight: 700;
            color: #059669;
            background: #f0fdf4;
            padding: 8px 12px;
            border-radius: 8px;
            border: 1px solid #bbf7d0;
            align-self: flex-start;
        }
        
        .empty-state {
            background: white;
            border-radius: 16px;
            text-align: center;
            padding: 60px 20px;
            color: #718096;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
            border: 1px solid #f1f5f9;
        }

        .empty-state i {
            font-size: 64px;
            margin-bottom: 20px;
            opacity: 0.3;
            background: linear-gradient(135deg, #4299e1, #667eea);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .empty-state p {
            font-size: 16px;
            font-weight: 500;
            margin: 0;
        }
        
        .summary-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            margin-top: 15px;
        }
        
        .summary-label {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 4px;
        }
        
        .summary-amount {
            font-size: 24px;
            font-weight: bold;
        }
        
        .fab {
            position: fixed;
            bottom: 80px;
            right: 20px;
            width: 56px;
            height: 56px;
            background: linear-gradient(135deg, #4299e1, #667eea);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            box-shadow: 0 8px 25px rgba(66, 153, 225, 0.4);
            text-decoration: none;
            z-index: 100;
            transition: all 0.3s ease;
        }

        .fab:hover {
            transform: scale(1.1);
            box-shadow: 0 12px 35px rgba(66, 153, 225, 0.6);
        }

        /* 卡片进入动画 */
        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .order-item {
            animation: slideInUp 0.6s ease forwards;
        }

        .order-item:nth-child(1) { animation-delay: 0.1s; }
        .order-item:nth-child(2) { animation-delay: 0.2s; }
        .order-item:nth-child(3) { animation-delay: 0.3s; }
        .order-item:nth-child(4) { animation-delay: 0.4s; }
        .order-item:nth-child(5) { animation-delay: 0.5s; }

        /* 响应式设计 */
        @media (max-width: 480px) {
            .order-item {
                padding: 16px;
            }

            .order-number {
                font-size: 16px;
            }

            .order-amount {
                font-size: 18px;
            }

            .order-info {
                padding: 12px;
            }
        }
        
        .fab:hover {
            background: #3182ce;
            color: white;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    <header class="mobile-header">
        <div class="header-content">
            <a href="index.php" class="back-btn">
                <i class="fas fa-arrow-left"></i>
            </a>
            <div class="header-title">
                <span>采购管理</span>
            </div>
            <div class="header-actions">
                <button class="refresh-btn" onclick="location.reload()">
                    <i class="fas fa-sync-alt"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- 主要内容 -->
    <main class="mobile-main">
        <!-- 搜索区域 -->
        <div class="search-section">
            <form method="GET" class="search-form">
                <div class="search-input-group">
                    <input type="text" name="search" class="search-input" 
                           placeholder="搜索采购单号或供应商..." value="<?= htmlspecialchars($search) ?>">
                    <button type="submit" class="search-btn">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
                <select name="status" class="filter-select">
                    <option value="">全部状态</option>
                    <option value="1" <?= $status === '1' ? 'selected' : '' ?>>待确认</option>
                    <option value="2" <?= $status === '2' ? 'selected' : '' ?>>已确认</option>
                    <option value="3" <?= $status === '3' ? 'selected' : '' ?>>已发货</option>
                    <option value="4" <?= $status === '4' ? 'selected' : '' ?>>已完成</option>
                    <option value="5" <?= $status === '5' ? 'selected' : '' ?>>已取消</option>
                </select>
            </form>
        </div>

        <!-- 统计卡片 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon blue">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <div class="stat-number"><?= number_format($stats['total_orders']) ?></div>
                <div class="stat-label">总订单数</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon yellow">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-number"><?= number_format($stats['pending_orders']) ?></div>
                <div class="stat-label">待确认</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon green">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-number"><?= number_format($stats['confirmed_orders']) ?></div>
                <div class="stat-label">已确认</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon purple">
                    <i class="fas fa-calendar-check"></i>
                </div>
                <div class="stat-number"><?= number_format($stats['completed_orders']) ?></div>
                <div class="stat-label">已完成</div>
            </div>
        </div>

        <!-- 采购单列表 -->
        <div class="section-header">
            <h3 class="section-title">
                <i class="fas fa-list-alt"></i>
                采购单列表
                <?php if (!empty($orders)): ?>
                    <span class="count-badge"><?= count($orders) ?></span>
                <?php endif; ?>
            </h3>
        </div>
        <div class="orders-list">
            <?php if (!empty($orders)): ?>
                <?php foreach ($orders as $index => $order): ?>
                <a href="purchase-detail.php?id=<?= $order['id'] ?>" class="order-item-link">
                    <div class="order-item">
                        <div class="order-header">
                            <div class="order-number"><?= htmlspecialchars($order['order_number']) ?></div>
                            <span class="order-status status-<?= $order['status'] ?>">
                                <?php
                                $statusText = [
                                    1 => '待确认',
                                    2 => '已确认',
                                    3 => '已发货',
                                    4 => '已完成',
                                    5 => '已取消'
                                ];
                                echo $statusText[$order['status']] ?? '未知';
                                ?>
                            </span>
                        </div>

                        <div class="order-info">
                            <div class="info-row">
                                <i class="fas fa-truck"></i>
                                <span><?= htmlspecialchars($order['supplier_name'] ?? '未指定供应商') ?></span>
                            </div>
                            <div class="info-row">
                                <i class="fas fa-boxes"></i>
                                <span><?= $order['items_count'] ?> 个商品</span>
                            </div>
                            <div class="info-row">
                                <i class="fas fa-clock"></i>
                                <span><?= date('m-d H:i', strtotime($order['created_at'])) ?></span>
                            </div>
                        </div>

                        <div class="order-amount">¥<?= number_format($order['total_amount'], 2) ?></div>
                    </div>
                </a>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="empty-state">
                    <i class="fas fa-shopping-cart"></i>
                    <p>暂无采购单数据</p>
                </div>
            <?php endif; ?>
        </div>

        <!-- 采购金额汇总 -->
        <?php if ($stats['total_amount'] > 0): ?>
        <div class="summary-card">
            <div class="summary-label">本月采购总额</div>
            <div class="summary-amount">¥<?= number_format($stats['total_amount'], 2) ?></div>
        </div>
        <?php endif; ?>

        <!-- 底部间距 -->
        <div class="bottom-spacer"></div>
    </main>

    <!-- 悬浮按钮 -->
    <a href="../modules/purchase/create.php" class="fab" title="新建采购单">
        <i class="fas fa-plus"></i>
    </a>

    <!-- 底部导航 -->
    <nav class="bottom-nav">
        <a href="index.php" class="nav-item">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </a>
        <a href="inbound.php" class="nav-item">
            <i class="fas fa-box"></i>
            <span>入库</span>
        </a>
        <a href="inventory.php" class="nav-item">
            <i class="fas fa-warehouse"></i>
            <span>库存</span>
        </a>
        <a href="purchase.php" class="nav-item active">
            <i class="fas fa-shopping-cart"></i>
            <span>采购</span>
        </a>
        <a href="reports.php" class="nav-item">
            <i class="fas fa-chart-bar"></i>
            <span>报表</span>
        </a>
        <a href="profile.php" class="nav-item">
            <i class="fas fa-user"></i>
            <span>我的</span>
        </a>
    </nav>

    <script>


        // 搜索功能增强
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing...');

            const searchInput = document.querySelector('input[name="search"]');
            const statusSelect = document.querySelector('select[name="status"]');

            // 实时搜索（防抖）
            let searchTimeout;
            if (searchInput) {
                searchInput.addEventListener('input', function() {
                    clearTimeout(searchTimeout);
                    searchTimeout = setTimeout(() => {
                        this.form.submit();
                    }, 500);
                });
            }

            // 状态筛选
            if (statusSelect) {
                statusSelect.addEventListener('change', function() {
                    this.form.submit();
                });
            }


        });
    </script>
    <script src="main.js"></script>
</body>
</html>