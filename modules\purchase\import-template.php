<?php
/**
 * 采购管理 - 批量导入模板
 */
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($page_title) ?></title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../../includes/styles.css?v=<?= time() ?>" rel="stylesheet">
    <link href="../../assets/css/common.css?v=<?= time() ?>" rel="stylesheet">
    <link href="../../assets/css/table-enhanced.css?v=<?= time() ?>" rel="stylesheet">
    <link href="style.css?v=<?= time() ?>" rel="stylesheet">
</head>
<body>

<?php include 'sidebar.php'; ?>

<!-- 主内容区 -->
<div class="main-content">
    <!-- 顶部栏 -->
    <div class="topbar">
        <div class="topbar-left">
            <h1><i class="fas fa-upload"></i> 批量导入采购单</h1>
        </div>
        <div class="topbar-right">
            <a href="index.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> 返回列表
            </a>
        </div>
    </div>

    <!-- 错误信息 -->
    <?php if (isset($error_message)): ?>
    <div class="alert alert-danger">
        <i class="fas fa-exclamation-circle"></i> <?= htmlspecialchars($error_message) ?>
    </div>
    <?php endif; ?>

    <div class="content">
        <!-- 上传区域 -->
        <div class="upload-section">
            <div class="section-header">
                <h3><i class="fas fa-upload"></i> 选择导入文件</h3>
            </div>

            <form method="POST" enctype="multipart/form-data" id="importForm" class="import-form">
                <div class="upload-area" id="uploadArea">
                    <div class="upload-icon">
                        <i class="fas fa-cloud-upload-alt"></i>
                    </div>
                    <div class="upload-text">
                        <h4>拖拽文件到此处或点击选择</h4>
                        <p>支持 CSV 格式，文件大小不超过 2MB</p>
                    </div>
                    <input type="file" id="import_file" name="import_file"
                           accept=".csv,.xlsx,.xls" required hidden>
                    <button type="button" class="btn btn-outline-primary" onclick="document.getElementById('import_file').click()">
                        <i class="fas fa-folder-open"></i> 选择文件
                    </button>
                </div>

                <div class="file-info" id="fileInfo" style="display: none;">
                    <div class="file-details">
                        <div class="file-icon">
                            <i class="fas fa-file-csv"></i>
                        </div>
                        <div class="file-meta">
                            <div class="file-name" id="fileName"></div>
                            <div class="file-size" id="fileSize"></div>
                        </div>
                        <button type="button" class="btn-remove" onclick="removeFile()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="file-preview" id="filePreview"></div>
                </div>

                <!-- 供应商选择 -->
                <div class="supplier-selection" id="supplierSelection" style="display: none;">
                    <div class="section-header">
                        <h4><i class="fas fa-building"></i> 选择供应商</h4>
                        <p class="section-description">Excel文件中没有供应商信息，请选择对应的供应商</p>
                    </div>
                    <div class="form-group">
                        <label for="supplier_id" class="form-label">供应商 <span class="required">*</span></label>
                        <select id="supplier_id" name="supplier_id" class="form-control" required>
                            <option value="">请选择供应商</option>
                            <?php if (isset($suppliers) && !empty($suppliers)): ?>
                                <?php foreach ($suppliers as $supplier): ?>
                                    <option value="<?= $supplier['id'] ?>">
                                        <?= htmlspecialchars($supplier['name']) ?>
                                        <?php if (!empty($supplier['contact_person'])): ?>
                                            - <?= htmlspecialchars($supplier['contact_person']) ?>
                                        <?php endif; ?>
                                    </option>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </select>
                        <div class="form-help">
                            <i class="fas fa-info-circle"></i>
                            导入的所有采购单将关联到此供应商
                        </div>
                    </div>
                </div>

                <div class="import-options">
                    <label class="checkbox-label">
                        <input type="checkbox" id="skipErrors" name="skip_errors" checked>
                        <span class="checkmark"></span>
                        跳过错误行，继续导入其他数据
                    </label>
                    <label class="checkbox-label">
                        <input type="checkbox" id="mergeOrders" name="merge_orders" checked>
                        <span class="checkmark"></span>
                        自动合并相同供应商和日期的订单
                    </label>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary" id="submitBtn" disabled>
                        <i class="fas fa-upload"></i> 开始导入
                    </button>
                    <a href="index.php" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i> 取消
                    </a>
                </div>
            </form>
        </div>

        <!-- 说明和示例 -->
        <div class="info-section">
            <!-- 格式说明 -->
            <div class="info-card">
                <div class="card-header">
                    <h4><i class="fas fa-info-circle"></i> 格式要求</h4>
                    <div class="template-downloads">
                        <a href="download_template_based_on_test.php" class="btn btn-sm btn-primary">
                            <i class="fas fa-download"></i> 下载模板(基于test.xlsx格式)
                        </a>
                        <a href="download_excel_template.php" class="btn btn-sm btn-success">
                            <i class="fas fa-download"></i> 下载简单列表模板
                        </a>
                        <a href="download_order_form_template.php" class="btn btn-sm btn-info">
                            <i class="fas fa-download"></i> 下载订货单模板
                        </a>
                    </div>
                </div>
                <div class="card-content">
                    <div class="format-tabs">
                        <div class="tab-buttons">
                            <button class="tab-btn active" onclick="showTab('test-format')">test.xlsx格式</button>
                            <button class="tab-btn" onclick="showTab('simple-format')">简单列表格式</button>
                            <button class="tab-btn" onclick="showTab('order-format')">订货单格式</button>
                        </div>

                        <div id="test-format" class="tab-content active">
                            <p><strong>基于您的test.xlsx文件格式：</strong></p>
                            <div class="format-description">
                                <p>系统会自动检测Excel文件格式，支持以下两种主要格式：</p>
                                <ul>
                                    <li><strong>订货单格式</strong>：包含完整的订单头部信息和明细数据</li>
                                    <li><strong>简单列表格式</strong>：每行一个采购项的简单表格</li>
                                </ul>
                                <p class="highlight">推荐使用"基于test.xlsx格式"的模板，确保与您现有文件格式完全兼容。</p>
                            </div>
                        </div>

                        <div id="simple-format" class="tab-content">
                            <p>简单列表格式应包含以下列（按顺序）：</p>
                            <div class="format-list">
                                <div class="format-item required">供应商名称 <span class="required-mark">*</span></div>
                                <div class="format-item required">订单日期 <span class="required-mark">*</span></div>
                                <div class="format-item required">食材名称 <span class="required-mark">*</span></div>
                                <div class="format-item required">数量 <span class="required-mark">*</span></div>
                                <div class="format-item required">单价 <span class="required-mark">*</span></div>
                                <div class="format-item">备注</div>
                            </div>
                        </div>

                        <div id="order-format" class="tab-content">
                            <p>订货单格式包含：</p>
                            <div class="format-list">
                                <div class="format-section">
                                    <h5>头部信息：</h5>
                                    <div class="format-item">订单号、订单日期、联系人</div>
                                    <div class="format-item">送货地址、联系电话</div>
                                    <div class="format-item">订单金额、实际金额、预期交货日期</div>
                                </div>
                                <div class="format-section">
                                    <h5>明细信息：</h5>
                                    <div class="format-item">商品编码、名称、规格、单位</div>
                                    <div class="format-item">数量、单价、小计、实收数量</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <p class="note"><span class="required-mark">*</span> 表示必填字段</p>
                </div>
            </div>

            <!-- 注意事项 -->
            <div class="info-card">
                <div class="card-header">
                    <h4><i class="fas fa-exclamation-triangle"></i> 注意事项</h4>
                </div>
                <div class="card-content">
                    <ul class="notes-list">
                        <li>第一行为标题行，导入时会自动跳过</li>
                        <li>供应商和食材名称必须在系统中已存在</li>
                        <li>相同供应商、日期和备注的订单会合并</li>
                        <li>文件大小不超过 2MB</li>
                    </ul>
                </div>
            </div>

            <!-- 示例数据 -->
            <div class="info-card">
                <div class="card-header">
                    <h4><i class="fas fa-table"></i> 示例数据</h4>
                </div>
                <div class="card-content">
                    <div class="example-table">
                        <table>
                            <thead>
                                <tr>
                                    <th>供应商名称</th>
                                    <th>订货日期</th>
                                    <th>食材名称</th>
                                    <th>数量</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>绿色蔬菜供应商</td>
                                    <td>2024-12-01</td>
                                    <td>白菜</td>
                                    <td>50</td>
                                </tr>
                                <tr>
                                    <td>优质肉类供应商</td>
                                    <td>2024-12-01</td>
                                    <td>猪肉</td>
                                    <td>20</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>


</div>

<script src="../../assets/js/common.js"></script>
<script>

// 文件上传处理
document.addEventListener('DOMContentLoaded', function() {
    const fileInput = document.getElementById('import_file');
    const uploadArea = document.getElementById('uploadArea');
    const fileInfo = document.getElementById('fileInfo');
    const submitBtn = document.getElementById('submitBtn');

    // 拖拽上传
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
    });

    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');

        const files = e.dataTransfer.files;
        if (files.length > 0) {
            fileInput.files = files;
            handleFileSelect(files[0]);
        }
    });

    // 文件选择
    fileInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            handleFileSelect(file);
        }
    });

    // 处理文件选择
    function handleFileSelect(file) {
        // 验证文件
        if (!validateFile(file)) {
            return;
        }

        // 显示文件信息
        showFileInfo(file);

        // 显示供应商选择区域
        document.getElementById('supplierSelection').style.display = 'block';

        // 更新提交按钮状态
        updateSubmitButton();

        // 预览文件内容
        previewFile(file);
    }

    // 文件验证
    function validateFile(file) {
        // 检查文件大小 (2MB)
        if (file.size > 2 * 1024 * 1024) {
            showError('文件大小不能超过 2MB');
            return false;
        }

        // 检查文件类型
        const allowedTypes = ['.csv', '.xlsx', '.xls'];
        const fileName = file.name.toLowerCase();
        const isValidType = allowedTypes.some(type => fileName.endsWith(type));

        if (!isValidType) {
            showError('只支持 CSV、Excel 格式的文件');
            return false;
        }

        return true;
    }

    // 显示文件信息
    function showFileInfo(file) {
        document.getElementById('fileName').textContent = file.name;
        document.getElementById('fileSize').textContent = formatFileSize(file.size);

        uploadArea.style.display = 'none';
        fileInfo.style.display = 'block';
    }

    // 移除文件
    window.removeFile = function() {
        fileInput.value = '';
        uploadArea.style.display = 'block';
        fileInfo.style.display = 'none';
        submitBtn.disabled = true;
        document.getElementById('filePreview').innerHTML = '';
    }

    // 预览文件内容
    function previewFile(file) {
        if (file.name.toLowerCase().endsWith('.csv')) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const csv = e.target.result;
                const lines = csv.split('\n').slice(0, 4); // 只显示前4行

                let preview = '<div class="file-preview-content">';
                preview += '<h5>文件预览（前3行数据）：</h5>';
                preview += '<div class="preview-lines">';

                lines.forEach((line, index) => {
                    if (line.trim()) {
                        const isHeader = index === 0;
                        preview += `<div class="preview-line ${isHeader ? 'header' : ''}">`;
                        preview += `<span class="line-number">${index + 1}</span>`;
                        preview += `<span class="line-content">${line}</span>`;
                        preview += '</div>';
                    }
                });

                preview += '</div></div>';
                document.getElementById('filePreview').innerHTML = preview;
            };
            reader.readAsText(file, 'UTF-8');
        }
    }

    // 格式化文件大小
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // 显示错误信息
    function showError(message) {
        // 创建错误提示
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-toast';
        errorDiv.innerHTML = `<i class="fas fa-exclamation-circle"></i> ${message}`;
        document.body.appendChild(errorDiv);

        // 3秒后移除
        setTimeout(() => {
            errorDiv.remove();
        }, 3000);
    }

    // 表单提交处理
    document.getElementById('importForm').addEventListener('submit', function(e) {
        const fileInput = document.getElementById('import_file');
        if (!fileInput.files.length) {
            e.preventDefault();
            showError('请选择要导入的文件');
            return;
        }

        // 显示加载状态
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 导入中...';

        // 显示进度条
        showProgress();
    });

    // 显示进度条
    function showProgress() {
        const progressDiv = document.createElement('div');
        progressDiv.className = 'import-progress';
        progressDiv.innerHTML = `
            <div class="progress-content">
                <h4><i class="fas fa-upload"></i> 正在导入数据...</h4>
                <div class="progress-bar">
                    <div class="progress-fill"></div>
                </div>
                <p>请稍候，正在处理您的文件...</p>
            </div>
        `;
        document.body.appendChild(progressDiv);
    }
});

// 标签页切换功能
function showTab(tabId) {
    // 隐藏所有标签内容
    const tabContents = document.querySelectorAll('.tab-content');
    tabContents.forEach(content => {
        content.classList.remove('active');
    });

    // 移除所有按钮的活动状态
    const tabButtons = document.querySelectorAll('.tab-btn');
    tabButtons.forEach(btn => {
        btn.classList.remove('active');
    });

    // 显示选中的标签内容
    document.getElementById(tabId).classList.add('active');

    // 激活对应的按钮
    event.target.classList.add('active');
}

// 供应商选择处理
document.addEventListener('DOMContentLoaded', function() {
    const supplierSelect = document.getElementById('supplier_id');
    if (supplierSelect) {
        supplierSelect.addEventListener('change', function() {
            updateSubmitButton();
        });
    }
});

// 更新提交按钮状态
function updateSubmitButton() {
    const fileInput = document.getElementById('import_file');
    const supplierSelect = document.getElementById('supplier_id');
    const submitBtn = document.getElementById('submitBtn');

    if (fileInput && supplierSelect && submitBtn) {
        const hasFile = fileInput.files.length > 0;
        const hasSupplier = supplierSelect.value !== '';

        submitBtn.disabled = !(hasFile && hasSupplier);

        // 更新按钮文本提示
        if (!hasFile) {
            submitBtn.innerHTML = '<i class="fas fa-upload"></i> 请先选择文件';
        } else if (!hasSupplier) {
            submitBtn.innerHTML = '<i class="fas fa-upload"></i> 请选择供应商';
        } else {
            submitBtn.innerHTML = '<i class="fas fa-upload"></i> 开始导入';
        }
    }
}

// 移除文件时隐藏供应商选择
function removeFile() {
    document.getElementById('import_file').value = '';
    document.getElementById('fileInfo').style.display = 'none';
    document.getElementById('supplierSelection').style.display = 'none';
    updateSubmitButton();
}
</script>
</body>
</html>
