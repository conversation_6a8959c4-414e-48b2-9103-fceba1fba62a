2025-06-28 10:03:53 - API调用开始
2025-06-28 10:03:53 - POST数据: <PERSON><PERSON><PERSON>
(
    [name] => 杂项
)

2025-06-28 10:03:53 - 检查名称: 杂项, 排除ID: 0
2025-06-28 10:03:53 - Database类加载成功
2025-06-28 10:18:15 - API调用开始
2025-06-28 10:18:15 - POST数据: Array
(
    [name] => 杂项
)

2025-06-28 10:18:15 - 检查名称: 杂项, 排除ID: 0
2025-06-28 10:18:15 - Database类加载成功
2025-06-28 10:18:15 - 数据库连接成功
2025-06-28 10:18:15 - 新建模式查询: SELECT id, name FROM ingredient_categories WHERE name = ?, 参数: 杂项
2025-06-28 10:18:15 - 查询结果: found id=1
2025-06-28 10:18:15 - API调用成功完成
2025-06-28 11:57:56 - API调用开始
2025-06-28 11:57:56 - POST数据: Arra<PERSON>
(
    [name] => 杂项
)

2025-06-28 11:57:56 - 检查名称: 杂项, 排除ID: 0
2025-06-28 11:57:56 - Database类加载成功
2025-06-28 11:57:56 - 数据库连接成功
2025-06-28 11:57:56 - 新建模式查询: SELECT id, name FROM ingredient_categories WHERE name = ?, 参数: 杂项
2025-06-28 11:57:56 - 查询结果: found id=1
2025-06-28 11:57:56 - API调用成功完成
2025-06-28 12:04:41 - API调用开始
2025-06-28 12:04:41 - POST数据: Array
(
    [name] => 蔬菜类
)

2025-06-28 12:04:41 - 检查名称: 蔬菜类, 排除ID: 0
2025-06-28 12:04:41 - Database类加载成功
2025-06-28 12:04:41 - 数据库连接成功
2025-06-28 12:04:41 - 新建模式查询: SELECT id, name FROM ingredient_categories WHERE name = ?, 参数: 蔬菜类
2025-06-28 12:04:41 - 查询结果: not found
2025-06-28 12:04:41 - API调用成功完成
2025-06-28 13:06:18 - API调用开始
2025-06-28 13:06:18 - POST数据: Array
(
    [name] => 肉鸟类
)

2025-06-28 13:06:18 - 检查名称: 肉鸟类, 排除ID: 0
2025-06-28 13:06:18 - Database类加载成功
2025-06-28 13:06:18 - 数据库连接成功
2025-06-28 13:06:18 - 新建模式查询: SELECT id, name FROM ingredient_categories WHERE name = ?, 参数: 肉鸟类
2025-06-28 13:06:18 - 查询结果: not found
2025-06-28 13:06:18 - API调用成功完成
2025-06-28 13:06:19 - API调用开始
2025-06-28 13:06:19 - POST数据: Array
(
    [name] => 肉
)

2025-06-28 13:06:19 - 检查名称: 肉, 排除ID: 0
2025-06-28 13:06:19 - Database类加载成功
2025-06-28 13:06:19 - 数据库连接成功
2025-06-28 13:06:19 - 新建模式查询: SELECT id, name FROM ingredient_categories WHERE name = ?, 参数: 肉
2025-06-28 13:06:19 - 查询结果: not found
2025-06-28 13:06:19 - API调用成功完成
2025-06-28 13:06:20 - API调用开始
2025-06-28 13:06:20 - POST数据: Array
(
    [name] => 肉类
)

2025-06-28 13:06:20 - 检查名称: 肉类, 排除ID: 0
2025-06-28 13:06:20 - Database类加载成功
2025-06-28 13:06:20 - 数据库连接成功
2025-06-28 13:06:20 - 新建模式查询: SELECT id, name FROM ingredient_categories WHERE name = ?, 参数: 肉类
2025-06-28 13:06:20 - 查询结果: not found
2025-06-28 13:06:20 - API调用成功完成
2025-06-28 13:15:05 - API调用开始
2025-06-28 13:15:05 - POST数据: Array
(
    [name] => 蔬菜类
)

2025-06-28 13:15:05 - 检查名称: 蔬菜类, 排除ID: 0
2025-06-28 13:15:05 - Database类加载成功
2025-06-28 13:15:05 - 数据库连接成功
2025-06-28 13:15:05 - 新建模式查询: SELECT id, name FROM ingredient_categories WHERE name = ?, 参数: 蔬菜类
2025-06-28 13:15:05 - 查询结果: not found
2025-06-28 13:15:05 - API调用成功完成
2025-06-28 16:25:38 - API调用开始
2025-06-28 16:25:38 - POST数据: Array
(
    [name] => 手套
)

2025-06-28 16:25:38 - 检查名称: 手套, 排除ID: 0
2025-06-28 16:25:38 - Database类加载成功
2025-06-28 16:25:38 - 数据库连接成功
2025-06-28 16:25:38 - 新建模式查询: SELECT id, name FROM ingredient_categories WHERE name = ?, 参数: 手套
2025-06-28 16:25:38 - 查询结果: not found
2025-06-28 16:25:38 - API调用成功完成
2025-06-28 17:01:51 - API调用开始
2025-06-28 17:01:51 - POST数据: Array
(
    [name] => 酒肉
)

2025-06-28 17:01:51 - 检查名称: 酒肉, 排除ID: 0
2025-06-28 17:01:51 - Database类加载成功
2025-06-28 17:01:51 - 数据库连接成功
2025-06-28 17:01:51 - 新建模式查询: SELECT id, name FROM ingredient_categories WHERE name = ?, 参数: 酒肉
2025-06-28 17:01:51 - 查询结果: not found
2025-06-28 17:01:51 - API调用成功完成
2025-06-28 17:01:56 - API调用开始
2025-06-28 17:01:56 - POST数据: Array
(
    [name] => 猪肉
)

2025-06-28 17:01:56 - 检查名称: 猪肉, 排除ID: 0
2025-06-28 17:01:56 - Database类加载成功
2025-06-28 17:01:56 - 数据库连接成功
2025-06-28 17:01:56 - 新建模式查询: SELECT id, name FROM ingredient_categories WHERE name = ?, 参数: 猪肉
2025-06-28 17:01:56 - 查询结果: not found
2025-06-28 17:01:56 - API调用成功完成
