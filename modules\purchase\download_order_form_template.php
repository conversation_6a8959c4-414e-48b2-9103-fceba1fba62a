<?php
/**
 * 订货单格式Excel模板下载
 */

// 设置响应头
header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
header('Content-Disposition: attachment; filename="订货单格式模板.xlsx"');
header('Cache-Control: max-age=0');

/**
 * 创建订货单格式的XLSX文件
 */
function createOrderFormXlsx()
{
    $tempDir = sys_get_temp_dir() . '/xlsx_' . uniqid();
    mkdir($tempDir);
    mkdir($tempDir . '/xl');
    mkdir($tempDir . '/xl/worksheets');
    mkdir($tempDir . '/_rels');
    mkdir($tempDir . '/xl/_rels');

    // 创建 [Content_Types].xml
    $contentTypes = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Types xmlns="http://schemas.openxmlformats.org/package/2006/content-types">
    <Default Extension="rels" ContentType="application/vnd.openxmlformats-package.relationships+xml"/>
    <Default Extension="xml" ContentType="application/xml"/>
    <Override PartName="/xl/workbook.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml"/>
    <Override PartName="/xl/worksheets/sheet1.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml"/>
    <Override PartName="/xl/sharedStrings.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml"/>
</Types>';
    file_put_contents($tempDir . '/[Content_Types].xml', $contentTypes);

    // 创建 _rels/.rels
    $rels = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships">
    <Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument" Target="xl/workbook.xml"/>
</Relationships>';
    file_put_contents($tempDir . '/_rels/.rels', $rels);

    // 创建 xl/_rels/workbook.xml.rels
    $workbookRels = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships">
    <Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet" Target="worksheets/sheet1.xml"/>
    <Relationship Id="rId2" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/sharedStrings" Target="sharedStrings.xml"/>
</Relationships>';
    file_put_contents($tempDir . '/xl/_rels/workbook.xml.rels', $workbookRels);

    // 创建 xl/workbook.xml
    $workbook = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<workbook xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships">
    <sheets>
        <sheet name="订货单" sheetId="1" r:id="rId1"/>
    </sheets>
</workbook>';
    file_put_contents($tempDir . '/xl/workbook.xml', $workbook);

    // 准备订货单格式的数据
    $orderFormData = [
        // 第1行：订单基本信息
        ['订单号:', 'ORD' . date('YmdHis'), '', '', date('Y-m-d'), '', '', '', '', '', '', '', '', '', '', '', ''],
        // 第2行：联系信息
        ['', '', '', '', '', '', '', '', '', '', '', '张三', '', '', '', '', ''],
        // 第3行：地址信息
        ['送货地址:', '学校食堂一楼', '', '', '', '', '', '', '', '', '', '13800138000', '', '', '', '', ''],
        // 第4行：金额信息
        ['订单金额:', '1500.00', '', '', '', '实际金额:', '1500.00', '', '', '', '', date('Y-m-d'), '', '', '', '', ''],
        // 第5行：空行
        ['', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''],
        // 第6行：明细标题
        ['商品编码', '商品名称', '规格', '单位', '品牌', '产地', '保质期', '单价', '数量', '小计', '税率', '实收数量', '合格数量', '不合格数量', '损耗数量', '拒收数量', '备注'],
        // 第7行开始：明细数据
        ['VEG001', '白菜', '500g/包', '包', '绿源', '本地', '3天', '2.50', '20', '50.00', '0%', '20', '20', '0', '0', '0', '新鲜'],
        ['MEAT001', '猪肉', '1kg/块', 'kg', '优质', '本地', '2天', '25.00', '10', '250.00', '0%', '10', '10', '0', '0', '0', ''],
        ['FISH001', '鲫鱼', '500g/条', '条', '鲜活', '本地', '1天', '12.00', '15', '180.00', '0%', '15', '15', '0', '0', '0', '活鱼'],
        ['GRAIN001', '大米', '10kg/袋', '袋', '优质', '东北', '12个月', '45.00', '5', '225.00', '0%', '5', '5', '0', '0', '0', ''],
        ['OIL001', '食用油', '5L/桶', '桶', '金龙鱼', '本地', '18个月', '35.00', '3', '105.00', '0%', '3', '3', '0', '0', '0', ''],
        ['SEASON001', '生抽', '500ml/瓶', '瓶', '海天', '广东', '24个月', '8.50', '10', '85.00', '0%', '10', '10', '0', '0', '0', ''],
    ];

    // 收集所有字符串
    $strings = [];
    $stringMap = [];
    foreach ($orderFormData as $row) {
        foreach ($row as $cell) {
            if (is_string($cell) && !is_numeric($cell) && !empty($cell)) {
                if (!isset($stringMap[$cell])) {
                    $stringMap[$cell] = count($strings);
                    $strings[] = $cell;
                }
            }
        }
    }

    // 创建 xl/sharedStrings.xml
    $sharedStrings = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<sst xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" count="' . count($strings) . '" uniqueCount="' . count($strings) . '">';
    foreach ($strings as $string) {
        $sharedStrings .= '<si><t>' . htmlspecialchars($string, ENT_XML1, 'UTF-8') . '</t></si>';
    }
    $sharedStrings .= '</sst>';
    file_put_contents($tempDir . '/xl/sharedStrings.xml', $sharedStrings);

    // 创建 xl/worksheets/sheet1.xml
    $worksheet = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<worksheet xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships">
    <sheetData>';

    foreach ($orderFormData as $rowIndex => $row) {
        $rowNum = $rowIndex + 1;
        $worksheet .= '<row r="' . $rowNum . '">';
        
        foreach ($row as $colIndex => $cell) {
            $colLetter = chr(65 + $colIndex); // A, B, C, ...
            if ($colIndex >= 26) {
                $colLetter = chr(64 + intval($colIndex / 26)) . chr(65 + ($colIndex % 26));
            }
            $cellRef = $colLetter . $rowNum;
            
            if (is_string($cell) && !is_numeric($cell) && !empty($cell)) {
                // 字符串类型，使用共享字符串
                $stringIndex = $stringMap[$cell];
                $worksheet .= '<c r="' . $cellRef . '" t="s"><v>' . $stringIndex . '</v></c>';
            } elseif (!empty($cell)) {
                // 数字类型或非空值
                $worksheet .= '<c r="' . $cellRef . '"><v>' . htmlspecialchars($cell, ENT_XML1, 'UTF-8') . '</v></c>';
            }
        }
        
        $worksheet .= '</row>';
    }

    $worksheet .= '</sheetData></worksheet>';
    file_put_contents($tempDir . '/xl/worksheets/sheet1.xml', $worksheet);

    // 创建ZIP文件
    $zipFile = tempnam(sys_get_temp_dir(), 'xlsx') . '.xlsx';
    $zip = new ZipArchive();
    
    if ($zip->open($zipFile, ZipArchive::CREATE) !== TRUE) {
        throw new Exception('无法创建XLSX文件');
    }

    // 添加所有文件到ZIP
    $iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($tempDir));
    foreach ($iterator as $file) {
        if ($file->isFile()) {
            $relativePath = str_replace($tempDir . DIRECTORY_SEPARATOR, '', $file->getPathname());
            $relativePath = str_replace('\\', '/', $relativePath);
            $zip->addFile($file->getPathname(), $relativePath);
        }
    }

    $zip->close();

    // 清理临时目录
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($tempDir, RecursiveDirectoryIterator::SKIP_DOTS),
        RecursiveIteratorIterator::CHILD_FIRST
    );
    foreach ($iterator as $file) {
        if ($file->isDir()) {
            rmdir($file->getPathname());
        } else {
            unlink($file->getPathname());
        }
    }
    rmdir($tempDir);

    return $zipFile;
}

try {
    // 创建Excel文件
    $excelFile = createOrderFormXlsx();
    
    // 输出文件内容
    readfile($excelFile);
    
    // 清理临时文件
    unlink($excelFile);
    
} catch (Exception $e) {
    // 如果创建失败，返回错误信息
    header('Content-Type: text/plain; charset=utf-8');
    echo '订货单模板生成失败: ' . $e->getMessage();
    echo "\n\n请手动创建Excel文件，格式如下：\n";
    echo "\n第1行：订单号: [订单号] | | | [订单日期]\n";
    echo "第2行：| | | | | | | | | | | [联系人]\n";
    echo "第3行：送货地址: [地址] | | | | | | | | | | | [联系电话]\n";
    echo "第4行：订单金额: [金额] | | | | 实际金额: [金额] | | | | | | [预期交货日期]\n";
    echo "第5行：空行\n";
    echo "第6行：商品编码 | 商品名称 | 规格 | 单位 | 品牌 | 产地 | 保质期 | 单价 | 数量 | 小计 | 税率 | 实收数量 | 合格数量 | 不合格数量 | 损耗数量 | 拒收数量 | 备注\n";
    echo "第7行开始：明细数据...\n";
}
?>
