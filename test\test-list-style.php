<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试移动端列表样式</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .btn { display: inline-block; padding: 8px 16px; background: #007cba; color: white; text-decoration: none; border-radius: 4px; margin: 5px; }
        .btn-success { background: #28a745; }
        .btn-mobile { background: #17a2b8; }
        .btn-primary { background: #667eea; }
        .feature-box { background: #e6f3ff; padding: 15px; border-left: 4px solid #007cba; margin: 15px 0; border-radius: 5px; }
        .step-box { background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; border-radius: 5px; margin: 10px 0; }
        .mobile-demo { background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0; text-align: center; }
        .mobile-frame { width: 320px; height: 640px; border: 8px solid #333; border-radius: 25px; margin: 0 auto; background: white; position: relative; overflow: hidden; box-shadow: 0 10px 30px rgba(0,0,0,0.3); }
        .mobile-screen { width: 100%; height: 100%; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; display: flex; flex-direction: column; }
        .mobile-header { background: rgba(255,255,255,0.95); color: #667eea; padding: 15px; display: flex; align-items: center; justify-content: center; font-weight: bold; }
        .mobile-content { flex: 1; padding: 20px; display: flex; flex-direction: column; gap: 12px; overflow-y: auto; }
        .mobile-list-item { background: white; border-radius: 12px; padding: 15px; color: #333; border-left: 4px solid #667eea; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
        .comparison-table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        .comparison-table th, .comparison-table td { border: 1px solid #ddd; padding: 10px; text-align: left; }
        .comparison-table th { background: #f5f5f5; }
        .before-col { background: #fff3cd; }
        .after-col { background: #d4edda; }
        .code-box { background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 12px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>✅ 移动端列表样式已完成！</h1>
    
    <div class="test-section">
        <h2>🎯 功能概述</h2>
        
        <div class="feature-box">
            <h4>📋 简化的列表设计</h4>
            <p>将采购单商品从卡片形式改为更简洁的列表形式，隐藏了单价和预计金额信息，突出重点操作，提升用户体验。</p>
            
            <h5>主要改进：</h5>
            <ul>
                <li>📋 <strong>列表布局</strong>：从卡片式改为紧凑的列表式布局</li>
                <li>🎯 <strong>信息简化</strong>：隐藏单价和预计金额，只显示关键信息</li>
                <li>⚖️ <strong>突出称重</strong>：重量输入和拍照功能更加突出</li>
                <li>📱 <strong>空间优化</strong>：更好地利用移动端屏幕空间</li>
                <li>🎨 <strong>视觉清爽</strong>：减少视觉干扰，专注核心操作</li>
                <li>🚀 <strong>操作高效</strong>：简化界面，提升操作效率</li>
            </ul>
        </div>
    </div>
    
    <div class="test-section">
        <h2>📊 设计对比</h2>
        
        <table class="comparison-table">
            <thead>
                <tr>
                    <th>设计元素</th>
                    <th class="before-col">卡片形式</th>
                    <th class="after-col">列表形式</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>布局方式</strong></td>
                    <td class="before-col">大卡片，垂直堆叠</td>
                    <td class="after-col">紧凑列表，水平布局</td>
                </tr>
                <tr>
                    <td><strong>信息显示</strong></td>
                    <td class="before-col">显示采购数量、单价、预计金额</td>
                    <td class="after-col">只显示采购数量</td>
                </tr>
                <tr>
                    <td><strong>重量输入</strong></td>
                    <td class="before-col">独立区域，较大空间</td>
                    <td class="after-col">与拍照按钮并排，紧凑布局</td>
                </tr>
                <tr>
                    <td><strong>拍照按钮</strong></td>
                    <td class="before-col">全宽按钮</td>
                    <td class="after-col">紧凑按钮，简化文字</td>
                </tr>
                <tr>
                    <td><strong>视觉重点</strong></td>
                    <td class="before-col">信息展示为主</td>
                    <td class="after-col">操作功能为主</td>
                </tr>
                <tr>
                    <td><strong>屏幕利用</strong></td>
                    <td class="before-col">每个商品占用较多空间</td>
                    <td class="after-col">更多商品可同时显示</td>
                </tr>
                <tr>
                    <td><strong>操作效率</strong></td>
                    <td class="before-col">需要滚动查看更多商品</td>
                    <td class="after-col">可快速浏览所有商品</td>
                </tr>
            </tbody>
        </table>
    </div>
    
    <div class="test-section">
        <h2>📱 移动端界面预览</h2>
        
        <div class="mobile-demo">
            <h4>新的列表样式效果</h4>
            <div class="mobile-frame">
                <div class="mobile-screen">
                    <div class="mobile-header">
                        📋 采购单商品列表
                    </div>
                    <div class="mobile-content">
                        <div class="mobile-list-item">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                <div style="font-weight: 600; font-size: 14px;">
                                    🥬 白菜 <span style="background: #667eea; color: white; font-size: 8px; padding: 1px 4px; border-radius: 8px;">采购单</span>
                                </div>
                                <div style="font-size: 10px; color: #f39c12;">
                                    ⏰ 待称重
                                </div>
                            </div>
                            <div style="font-size: 12px; color: #666; margin-bottom: 12px;">
                                🛒 采购: 50斤
                            </div>
                            <div style="display: flex; gap: 12px; align-items: flex-end;">
                                <div style="flex: 1;">
                                    <div style="font-size: 11px; font-weight: 600; margin-bottom: 4px;">实际重量</div>
                                    <div style="display: flex; background: white; border: 2px solid #e9ecef; border-radius: 6px; overflow: hidden;">
                                        <input style="flex: 1; padding: 6px; border: none; text-align: center; font-size: 12px;" placeholder="0.00">
                                        <div style="padding: 6px 8px; background: #f8f9fa; font-size: 10px;">斤</div>
                                    </div>
                                </div>
                                <button style="padding: 6px 10px; background: #667eea; color: white; border: none; border-radius: 6px; font-size: 10px;">
                                    📷 拍照
                                </button>
                            </div>
                        </div>
                        
                        <div class="mobile-list-item">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                <div style="font-weight: 600; font-size: 14px;">
                                    🥩 猪肉 <span style="background: #667eea; color: white; font-size: 8px; padding: 1px 4px; border-radius: 8px;">采购单</span>
                                </div>
                                <div style="font-size: 10px; color: #27ae60;">
                                    ✅ 已完成
                                </div>
                            </div>
                            <div style="font-size: 12px; color: #666; margin-bottom: 12px;">
                                🛒 采购: 20斤
                            </div>
                            <div style="display: flex; gap: 12px; align-items: flex-end;">
                                <div style="flex: 1;">
                                    <div style="font-size: 11px; font-weight: 600; margin-bottom: 4px;">实际重量</div>
                                    <div style="display: flex; background: white; border: 2px solid #e9ecef; border-radius: 6px; overflow: hidden;">
                                        <input style="flex: 1; padding: 6px; border: none; text-align: center; font-size: 12px;" value="19.5">
                                        <div style="padding: 6px 8px; background: #f8f9fa; font-size: 10px;">斤</div>
                                    </div>
                                </div>
                                <div style="display: flex; gap: 4px;">
                                    <button style="padding: 6px 8px; background: #27ae60; color: white; border: none; border-radius: 6px; font-size: 10px;">
                                        ✅ 已拍摄
                                    </button>
                                    <button style="padding: 6px 8px; background: #4facfe; color: white; border: none; border-radius: 6px; font-size: 10px;">
                                        👁️ 查看
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mobile-list-item">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                <div style="font-weight: 600; font-size: 14px;">
                                    🥕 胡萝卜 <span style="background: #667eea; color: white; font-size: 8px; padding: 1px 4px; border-radius: 8px;">采购单</span>
                                </div>
                                <div style="font-size: 10px; color: #3498db;">
                                    📷 待拍照
                                </div>
                            </div>
                            <div style="font-size: 12px; color: #666; margin-bottom: 12px;">
                                🛒 采购: 30斤
                            </div>
                            <div style="display: flex; gap: 12px; align-items: flex-end;">
                                <div style="flex: 1;">
                                    <div style="font-size: 11px; font-weight: 600; margin-bottom: 4px;">实际重量</div>
                                    <div style="display: flex; background: white; border: 2px solid #e9ecef; border-radius: 6px; overflow: hidden;">
                                        <input style="flex: 1; padding: 6px; border: none; text-align: center; font-size: 12px;" value="28.5">
                                        <div style="padding: 6px 8px; background: #f8f9fa; font-size: 10px;">斤</div>
                                    </div>
                                </div>
                                <button style="padding: 6px 10px; background: #667eea; color: white; border: none; border-radius: 6px; font-size: 10px;">
                                    📷 拍照
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="test-section">
        <h2>🎨 设计特色</h2>
        
        <h4>列表布局优势：</h4>
        <div class="feature-box">
            <h5>空间利用</h5>
            <ul>
                <li><strong>紧凑设计</strong>：每个商品占用更少的垂直空间</li>
                <li><strong>信息密度</strong>：在有限屏幕上显示更多商品</li>
                <li><strong>快速浏览</strong>：用户可以快速浏览所有商品</li>
                <li><strong>减少滚动</strong>：减少页面滚动操作</li>
            </ul>
            
            <h5>视觉层次</h5>
            <ul>
                <li><strong>左边框标识</strong>：蓝色左边框突出采购单商品</li>
                <li><strong>状态指示</strong>：右上角清晰显示完成状态</li>
                <li><strong>操作区域</strong>：重量输入和拍照按钮并排布局</li>
                <li><strong>信息分层</strong>：商品名称、采购数量、操作区域层次分明</li>
            </ul>
        </div>
        
        <h4>交互优化：</h4>
        <div class="feature-box">
            <h5>操作便捷</h5>
            <ul>
                <li><strong>重量输入</strong>：大号输入框，方便数字输入</li>
                <li><strong>拍照按钮</strong>：紧凑设计，文字简化</li>
                <li><strong>状态反馈</strong>：实时显示每个商品的完成状态</li>
                <li><strong>触摸友好</strong>：按钮大小适合手指操作</li>
            </ul>
            
            <h5>信息精简</h5>
            <ul>
                <li><strong>隐藏价格</strong>：不显示单价和预计金额</li>
                <li><strong>突出数量</strong>：只显示采购数量信息</li>
                <li><strong>专注操作</strong>：界面专注于称重和拍照操作</li>
                <li><strong>减少干扰</strong>：去除不必要的视觉元素</li>
            </ul>
        </div>
    </div>
    
    <div class="test-section">
        <h2>🔧 技术实现</h2>
        
        <h4>HTML结构变化：</h4>
        <div class="code-box">
// 新的列表结构<br>
&lt;div class="order-item-row"&gt;<br>
&nbsp;&nbsp;&nbsp;&nbsp;&lt;div class="item-info"&gt;<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;div class="item-name-row"&gt;商品名称 + 状态&lt;/div&gt;<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;div class="item-quantity-info"&gt;采购数量&lt;/div&gt;<br>
&nbsp;&nbsp;&nbsp;&nbsp;&lt;/div&gt;<br>
&nbsp;&nbsp;&nbsp;&nbsp;&lt;div class="weight-input-row"&gt;重量输入 + 拍照按钮&lt;/div&gt;<br>
&lt;/div&gt;
        </div>
        
        <h4>CSS样式优化：</h4>
        <div class="code-box">
/* 列表行样式 */<br>
.order-item-row {<br>
&nbsp;&nbsp;&nbsp;&nbsp;border-left: 4px solid #667eea;<br>
&nbsp;&nbsp;&nbsp;&nbsp;padding: 15px;<br>
&nbsp;&nbsp;&nbsp;&nbsp;margin-bottom: 12px;<br>
&nbsp;&nbsp;&nbsp;&nbsp;border-radius: 12px;<br>
}<br><br>
/* 重量输入行 */<br>
.weight-input-row {<br>
&nbsp;&nbsp;&nbsp;&nbsp;display: flex;<br>
&nbsp;&nbsp;&nbsp;&nbsp;gap: 15px;<br>
&nbsp;&nbsp;&nbsp;&nbsp;align-items: flex-end;<br>
}
        </div>
        
        <h4>JavaScript逻辑调整：</h4>
        <div class="code-box">
// 简化的商品渲染<br>
itemsList.innerHTML = addedItems.map(item => `<br>
&nbsp;&nbsp;&nbsp;&nbsp;&lt;div class="order-item-row"&gt;<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;div class="item-name-row"&gt;<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;${item.ingredient_name}<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;span class="order-badge"&gt;采购单&lt;/span&gt;<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;/div&gt;<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;div class="item-quantity-info"&gt;<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;采购: ${item.quantity}${item.unit}<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;/div&gt;<br>
&nbsp;&nbsp;&nbsp;&nbsp;&lt;/div&gt;<br>
`).join('');
        </div>
    </div>
    
    <div class="test-section">
        <h2>🧪 测试步骤</h2>
        
        <div class="step-box">
            <h4>步骤1：访问移动端入库页面</h4>
            <p><a href="../mobile/inbound.php" class="btn btn-primary">📱 打开移动端入库页面</a></p>
            <p><strong>操作：</strong>选择一个采购单，进入第二步</p>
            <p><strong>预期结果：</strong></p>
            <ul>
                <li>商品以列表形式显示</li>
                <li>每个商品占用较少垂直空间</li>
                <li>不显示单价和预计金额</li>
                <li>只显示采购数量信息</li>
            </ul>
        </div>
        
        <div class="step-box">
            <h4>步骤2：检查布局效果</h4>
            <p><strong>操作：</strong>滚动查看所有商品</p>
            <p><strong>预期结果：</strong></p>
            <ul>
                <li>列表布局紧凑整齐</li>
                <li>左边框蓝色标识清晰</li>
                <li>重量输入和拍照按钮并排</li>
                <li>状态指示器位置合适</li>
            </ul>
        </div>
        
        <div class="step-box">
            <h4>步骤3：测试操作功能</h4>
            <p><strong>操作：</strong>输入重量并拍摄照片</p>
            <p><strong>预期结果：</strong></p>
            <ul>
                <li>重量输入框响应正常</li>
                <li>拍照按钮功能正常</li>
                <li>状态更新正确</li>
                <li>照片查看功能正常</li>
            </ul>
        </div>
        
        <div class="step-box">
            <h4>步骤4：对比用户体验</h4>
            <p><strong>操作：</strong>与之前的卡片样式对比</p>
            <p><strong>预期结果：</strong></p>
            <ul>
                <li>界面更加简洁</li>
                <li>操作更加高效</li>
                <li>信息层次更清晰</li>
                <li>屏幕利用率更高</li>
            </ul>
        </div>
    </div>
    
    <div class="test-section">
        <h2>✅ 功能验证清单</h2>
        
        <h4>界面布局：</h4>
        <ul>
            <li>□ 列表形式显示正确</li>
            <li>□ 左边框标识清晰</li>
            <li>□ 商品信息布局合理</li>
            <li>□ 重量输入区域合适</li>
            <li>□ 拍照按钮位置正确</li>
        </ul>
        
        <h4>信息显示：</h4>
        <ul>
            <li>□ 商品名称显示正确</li>
            <li>□ 采购数量显示正确</li>
            <li>□ 单价信息已隐藏</li>
            <li>□ 预计金额已隐藏</li>
            <li>□ 状态指示器正常</li>
        </ul>
        
        <h4>交互功能：</h4>
        <ul>
            <li>□ 重量输入功能正常</li>
            <li>□ 拍照功能正常</li>
            <li>□ 照片查看功能正常</li>
            <li>□ 状态更新正确</li>
            <li>□ 表单提交正常</li>
        </ul>
        
        <h4>用户体验：</h4>
        <ul>
            <li>□ 界面简洁清爽</li>
            <li>□ 操作流程顺畅</li>
            <li>□ 信息层次清晰</li>
            <li>□ 屏幕利用率高</li>
            <li>□ 触摸操作友好</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>🚀 开始测试</h2>
        
        <p>移动端列表样式已完成，现在可以开始测试：</p>
        
        <div style="text-align: center; margin: 20px 0;">
            <a href="../mobile/inbound.php" class="btn btn-primary" style="font-size: 1.1rem; padding: 12px 24px;">
                📋 测试新的列表样式
            </a>
        </div>
        
        <h4>测试重点：</h4>
        <ol>
            <li><strong>布局效果</strong>：确认列表布局美观整齐</li>
            <li><strong>信息显示</strong>：验证信息简化效果</li>
            <li><strong>操作体验</strong>：测试重量输入和拍照功能</li>
            <li><strong>空间利用</strong>：评估屏幕空间利用率</li>
            <li><strong>整体体验</strong>：对比之前的卡片样式</li>
        </ol>
        
        <h4>设计优势：</h4>
        <ul>
            <li><strong>简洁高效</strong>：去除冗余信息，专注核心操作</li>
            <li><strong>空间优化</strong>：更好地利用移动端屏幕空间</li>
            <li><strong>操作便捷</strong>：重量输入和拍照功能更加突出</li>
            <li><strong>视觉清爽</strong>：减少视觉干扰，提升用户体验</li>
        </ul>
        
        <div style="background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h5>🎉 设计亮点</h5>
            <p>新的列表样式设计更适合移动端使用场景：</p>
            <ul>
                <li><strong>信息精简</strong>：隐藏价格信息，专注称重操作</li>
                <li><strong>布局紧凑</strong>：列表形式提高屏幕利用率</li>
                <li><strong>操作高效</strong>：重量输入和拍照功能更加便捷</li>
                <li><strong>视觉统一</strong>：保持与整体设计风格的一致性</li>
            </ul>
            <p>这使得移动端入库操作更加高效和用户友好！</p>
        </div>
    </div>
</body>
</html>
