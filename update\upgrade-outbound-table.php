<?php
/**
 * 出库记录表升级脚本
 * 确保 outbound_records 表结构满足新的出库管理功能需求
 */

// 数据库配置
$host = '************';
$port = 3306;
$dbname = 'sc';
$username = 'sc';
$password = 'pw5K4SsM7kZsjdxy';

echo "<h2>出库记录表升级脚本</h2>";

try {
    // 使用PDO连接数据库
    $pdo = new PDO("mysql:host=$host;port=$port;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p style='color: green;'>✅ 数据库连接成功</p>";
    
    // 检查表是否存在
    $stmt = $pdo->query("SHOW TABLES LIKE 'outbound_records'");
    $tableExists = $stmt->rowCount() > 0;
    
    if (!$tableExists) {
        echo "<p style='color: orange;'>⚠️ outbound_records 表不存在，开始创建...</p>";
        
        // 创建出库记录表
        $createTableSQL = "
        CREATE TABLE `outbound_records` (
            `id` bigint(20) NOT NULL AUTO_INCREMENT,
            `ingredient_id` bigint(20) NOT NULL COMMENT '食材ID',
            `quantity` decimal(10,2) NOT NULL COMMENT '出库数量',
            `unit_price` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '单价',
            `total_amount` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT '总金额',
            `batch_number` varchar(64) NOT NULL COMMENT '批次号',
            `meal_type` enum('breakfast','lunch','dinner') NOT NULL COMMENT '餐次类型',
            `meal_date` date NOT NULL COMMENT '用餐日期',
            `purpose` varchar(200) DEFAULT NULL COMMENT '用途说明',
            `operator_name` varchar(50) NOT NULL COMMENT '操作员姓名',
            `notes` text DEFAULT NULL COMMENT '备注信息',
            `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1正常 0删除',
            `created_by` bigint(20) NOT NULL DEFAULT 1 COMMENT '创建人ID',
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            PRIMARY KEY (`id`),
            UNIQUE KEY `batch_number` (`batch_number`),
            KEY `ingredient_id` (`ingredient_id`),
            KEY `meal_date` (`meal_date`),
            KEY `meal_type` (`meal_type`),
            KEY `created_at` (`created_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='出库记录表';
        ";
        
        $pdo->exec($createTableSQL);
        echo "<p style='color: green;'>✅ outbound_records 表创建成功</p>";
    } else {
        echo "<p style='color: blue;'>ℹ️ outbound_records 表已存在，检查字段结构...</p>";
        
        // 获取现有字段
        $stmt = $pdo->query("DESCRIBE outbound_records");
        $existingFields = [];
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $existingFields[$row['Field']] = $row;
        }
        
        // 需要的字段定义
        $requiredFields = [
            'id' => "ADD COLUMN `id` bigint(20) NOT NULL AUTO_INCREMENT PRIMARY KEY FIRST",
            'ingredient_id' => "ADD COLUMN `ingredient_id` bigint(20) NOT NULL COMMENT '食材ID'",
            'quantity' => "ADD COLUMN `quantity` decimal(10,2) NOT NULL COMMENT '出库数量'",
            'unit_price' => "ADD COLUMN `unit_price` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '单价'",
            'total_amount' => "ADD COLUMN `total_amount` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT '总金额'",
            'batch_number' => "ADD COLUMN `batch_number` varchar(64) NOT NULL COMMENT '批次号'",
            'meal_type' => "ADD COLUMN `meal_type` enum('breakfast','lunch','dinner') NOT NULL COMMENT '餐次类型'",
            'meal_date' => "ADD COLUMN `meal_date` date NOT NULL COMMENT '用餐日期'",
            'purpose' => "ADD COLUMN `purpose` varchar(200) DEFAULT NULL COMMENT '用途说明'",
            'operator_name' => "ADD COLUMN `operator_name` varchar(50) NOT NULL COMMENT '操作员姓名'",
            'notes' => "ADD COLUMN `notes` text DEFAULT NULL COMMENT '备注信息'",
            'status' => "ADD COLUMN `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1正常 0删除'",
            'created_by' => "ADD COLUMN `created_by` bigint(20) NOT NULL DEFAULT 1 COMMENT '创建人ID'",
            'created_at' => "ADD COLUMN `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'",
            'updated_at' => "ADD COLUMN `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'"
        ];
        
        // 检查并添加缺失的字段
        $alterStatements = [];
        foreach ($requiredFields as $fieldName => $alterSQL) {
            if ($fieldName === 'id') continue; // ID字段通常已存在
            
            if (!isset($existingFields[$fieldName])) {
                $alterStatements[] = $alterSQL;
                echo "<p style='color: orange;'>⚠️ 缺失字段: $fieldName</p>";
            }
        }
        
        // 执行字段添加
        if (!empty($alterStatements)) {
            foreach ($alterStatements as $alterSQL) {
                try {
                    $pdo->exec("ALTER TABLE outbound_records $alterSQL");
                    echo "<p style='color: green;'>✅ 字段添加成功</p>";
                } catch (Exception $e) {
                    echo "<p style='color: red;'>❌ 字段添加失败: " . $e->getMessage() . "</p>";
                }
            }
        } else {
            echo "<p style='color: green;'>✅ 表结构已是最新</p>";
        }
        
        // 检查并添加索引
        $indexStatements = [
            "ADD UNIQUE KEY `batch_number` (`batch_number`)" => "batch_number",
            "ADD KEY `ingredient_id` (`ingredient_id`)" => "ingredient_id",
            "ADD KEY `meal_date` (`meal_date`)" => "meal_date",
            "ADD KEY `meal_type` (`meal_type`)" => "meal_type",
            "ADD KEY `created_at` (`created_at`)" => "created_at"
        ];
        
        // 获取现有索引
        $stmt = $pdo->query("SHOW INDEX FROM outbound_records");
        $existingIndexes = [];
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $existingIndexes[] = $row['Key_name'];
        }
        
        foreach ($indexStatements as $indexSQL => $indexName) {
            if (!in_array($indexName, $existingIndexes)) {
                try {
                    $pdo->exec("ALTER TABLE outbound_records $indexSQL");
                    echo "<p style='color: green;'>✅ 索引 $indexName 添加成功</p>";
                } catch (Exception $e) {
                    echo "<p style='color: orange;'>⚠️ 索引 $indexName 添加失败(可能已存在): " . $e->getMessage() . "</p>";
                }
            }
        }
    }
    
    // 插入示例数据
    echo "<h3>插入示例数据</h3>";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM outbound_records WHERE status = 1");
    $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    if ($count == 0) {
        echo "<p style='color: blue;'>ℹ️ 插入示例出库记录...</p>";
        
        // 获取食材ID
        $stmt = $pdo->query("SELECT id FROM ingredients WHERE status = 1 LIMIT 5");
        $ingredients = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (!empty($ingredients)) {
            $sampleRecords = [
                [
                    'ingredient_id' => $ingredients[0],
                    'quantity' => 20.5,
                    'unit_price' => 2.50,
                    'total_amount' => 51.25,
                    'batch_number' => 'OUT' . date('YmdHis') . '001',
                    'meal_type' => 'lunch',
                    'meal_date' => date('Y-m-d'),
                    'purpose' => '午餐炒白菜',
                    'operator_name' => '张三',
                    'notes' => '示例出库记录'
                ],
                [
                    'ingredient_id' => $ingredients[1] ?? $ingredients[0],
                    'quantity' => 15.0,
                    'unit_price' => 28.00,
                    'total_amount' => 420.00,
                    'batch_number' => 'OUT' . date('YmdHis') . '002',
                    'meal_type' => 'dinner',
                    'meal_date' => date('Y-m-d'),
                    'purpose' => '晚餐红烧肉',
                    'operator_name' => '李四',
                    'notes' => '示例出库记录'
                ]
            ];
            
            foreach ($sampleRecords as $record) {
                $placeholders = ':' . implode(', :', array_keys($record));
                $columns = implode(', ', array_keys($record));
                
                $sql = "INSERT INTO outbound_records ($columns) VALUES ($placeholders)";
                $stmt = $pdo->prepare($sql);
                
                try {
                    $stmt->execute($record);
                    echo "<p style='color: green;'>✅ 示例记录插入成功: {$record['batch_number']}</p>";
                } catch (Exception $e) {
                    echo "<p style='color: orange;'>⚠️ 示例记录插入失败: " . $e->getMessage() . "</p>";
                }
            }
        } else {
            echo "<p style='color: orange;'>⚠️ 没有找到食材数据，跳过示例数据插入</p>";
        }
    } else {
        echo "<p style='color: blue;'>ℹ️ 已存在 $count 条出库记录，跳过示例数据插入</p>";
    }
    
    echo "<hr>";
    echo "<h3>升级完成！</h3>";
    echo "<p style='color: green;'>✅ 出库记录表已准备就绪</p>";
    echo "<p><a href='../modules/outbound/index.php'>👉 进入出库管理</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ 错误: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='../index.php'>返回首页</a> | <a href='../test/'>测试中心</a></p>";
?>