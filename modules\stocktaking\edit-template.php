<?php require_once dirname(__DIR__, 2) . '/includes/header-modular.php'; ?>

<div class="main-content">
    <?php require_once dirname(__DIR__, 2) . '/includes/sidebar-modular.php'; ?>
    
    <div class="content">
        <div class="content-header">
            <h1>
                <i class="fas fa-edit"></i>
                编辑盘点任务
            </h1>
            <div class="header-actions">
                <a href="index.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i>
                    返回列表
                </a>
                <a href="index.php?action=view&id=<?= $task['id'] ?>" class="btn btn-info">
                    <i class="fas fa-eye"></i>
                    查看详情
                </a>
            </div>
        </div>

        <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle"></i>
            <?= htmlspecialchars($error_message) ?>
        </div>
        <?php endif; ?>

        <div class="form-container">
            <form method="POST" action="index.php?action=edit&id=<?= $task['id'] ?>" class="stocktaking-form">
                <div class="form-section">
                    <h3><i class="fas fa-info-circle"></i> 基本信息</h3>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="task_name">
                                <i class="fas fa-tag"></i>
                                任务名称 <span class="required">*</span>
                            </label>
                            <input type="text" name="task_name" id="task_name" class="form-control" 
                                   value="<?= htmlspecialchars($task['task_name']) ?>" required>
                        </div>
                        <div class="form-group">
                            <label for="task_type">
                                <i class="fas fa-list"></i>
                                盘点类型 <span class="required">*</span>
                            </label>
                            <select name="task_type" id="task_type" class="form-control" required>
                                <option value="">请选择盘点类型</option>
                                <option value="full" <?= $task['task_type'] === 'full' ? 'selected' : '' ?>>全盘</option>
                                <option value="partial" <?= $task['task_type'] === 'partial' ? 'selected' : '' ?>>抽盘</option>
                                <option value="category" <?= $task['task_type'] === 'category' ? 'selected' : '' ?>>分类盘点</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="start_date">
                                <i class="fas fa-calendar-alt"></i>
                                开始日期 <span class="required">*</span>
                            </label>
                            <input type="date" name="start_date" id="start_date" class="form-control" 
                                   value="<?= $task['start_date'] ?>" required>
                        </div>
                        <div class="form-group">
                            <label for="end_date">
                                <i class="fas fa-calendar-check"></i>
                                结束日期 <span class="required">*</span>
                            </label>
                            <input type="date" name="end_date" id="end_date" class="form-control" 
                                   value="<?= $task['end_date'] ?>" required>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="executor_id">
                                <i class="fas fa-user"></i>
                                执行人 <span class="required">*</span>
                            </label>
                            <select name="executor_id" id="executor_id" class="form-control" required>
                                <option value="">请选择执行人</option>
                                <?php if (!empty($data['users'])): ?>
                                    <?php foreach ($data['users'] as $user): ?>
                                        <option value="<?= $user['id'] ?>" <?= $task['executor_id'] == $user['id'] ? 'selected' : '' ?>>
                                            <?php 
                                            $displayName = $user['real_name'] ?? $user['name'];
                                            $username = $user['name'];
                                            if ($user['real_name'] && $user['real_name'] !== $user['name']) {
                                                echo htmlspecialchars($displayName . ' (' . $username . ')');
                                            } else {
                                                echo htmlspecialchars($displayName);
                                            }
                                            ?>
                                        </option>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <option value="" disabled>暂无可用用户</option>
                                <?php endif; ?>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group full-width">
                            <label for="description">
                                <i class="fas fa-comment"></i>
                                任务描述
                            </label>
                            <textarea name="description" id="description" class="form-control" rows="3" 
                                      placeholder="请输入任务描述..."><?= htmlspecialchars($task['description']) ?></textarea>
                        </div>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        保存修改
                    </button>
                    <a href="index.php?action=view&id=<?= $task['id'] ?>" class="btn btn-secondary">
                        <i class="fas fa-times"></i>
                        取消
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.form-container {
    max-width: 800px;
    margin: 0 auto;
}

.stocktaking-form {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
}

.form-section {
    margin-bottom: 32px;
}

.form-section h3 {
    margin: 0 0 20px 0;
    color: #1f2937;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    padding-bottom: 12px;
    border-bottom: 2px solid #e5e7eb;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-group label {
    font-weight: 500;
    color: #374151;
    display: flex;
    align-items: center;
    gap: 6px;
}

.required {
    color: #dc2626;
}

.form-control {
    padding: 10px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.form-control:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-actions {
    display: flex;
    gap: 12px;
    padding-top: 24px;
    border-top: 1px solid #e5e7eb;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #f3f4f6;
    color: #6b7280;
    border: 1px solid #d1d5db;
}

.btn-secondary:hover {
    background: #e5e7eb;
}

.btn-info {
    background: #3b82f6;
    color: white;
}

.btn-info:hover {
    background: #2563eb;
}

.alert {
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.alert-danger {
    background: #fee2e2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
    }
}
</style>

<?php require_once dirname(__DIR__, 2) . '/includes/footer-modular.php'; ?>
