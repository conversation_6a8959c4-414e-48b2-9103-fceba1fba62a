<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($page_title) ?></title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../../includes/styles.css?v=<?= time() ?>" rel="stylesheet">
    <link href="../../assets/css/common.css?v=<?= time() ?>" rel="stylesheet">
    <link href="style.css?v=<?= time() ?>" rel="stylesheet">
</head>
<body>

<?php include 'sidebar.php'; ?>

<!-- 主内容区 -->
<div class="main-content">
    <!-- 顶部栏 -->
    <div class="topbar">
        <div class="topbar-left">
            <h1><i class="fas fa-edit"></i> 编辑供应商</h1>
        </div>
        <div class="topbar-right">
            <a href="index.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> 返回列表
            </a>
        </div>
    </div>

    <div class="content">
        <?php if (isset($error_message)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle"></i>
            <?= htmlspecialchars($error_message) ?>
        </div>
        <?php endif; ?>

        <!-- 编辑供应商表单 -->
        <div class="form-container">
            <div class="form-header">
                <div class="form-icon">
                    <i class="fas fa-truck"></i>
                </div>
                <div class="form-title">
                    <h2>编辑供应商信息</h2>
                    <p>修改供应商的基本信息</p>
                </div>
            </div>

            <form method="POST" class="supplier-form">
                <div class="form-grid">
                    <!-- 基本信息 -->
                    <div class="form-section">
                        <h3><i class="fas fa-info-circle"></i> 基本信息</h3>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label required">供应商名称</label>
                                <input type="text" name="name" class="form-control" required 
                                       placeholder="请输入供应商名称" value="<?= htmlspecialchars($supplier['name'] ?? '') ?>">
                            </div>
                            <div class="form-group">
                                <label class="form-label required">联系人</label>
                                <input type="text" name="contact_person" class="form-control" required 
                                       placeholder="请输入联系人姓名" value="<?= htmlspecialchars($supplier['contact_person'] ?? '') ?>">
                            </div>
                        </div>
                    </div>

                    <!-- 联系方式 -->
                    <div class="form-section">
                        <h3><i class="fas fa-phone"></i> 联系方式</h3>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label required">联系电话</label>
                                <input type="tel" name="phone" class="form-control" required 
                                       placeholder="请输入联系电话" value="<?= htmlspecialchars($supplier['phone'] ?? '') ?>">
                            </div>
                            <div class="form-group">
                                <label class="form-label">邮箱地址</label>
                                <input type="email" name="email" class="form-control" 
                                       placeholder="请输入邮箱地址" value="<?= htmlspecialchars($supplier['email'] ?? '') ?>">
                            </div>
                        </div>
                    </div>

                    <!-- 地址信息 -->
                    <div class="form-section">
                        <h3><i class="fas fa-map-marker-alt"></i> 地址信息</h3>
                        
                        <div class="form-group">
                            <label class="form-label">详细地址</label>
                            <textarea name="address" class="form-control" rows="3" 
                                      placeholder="请输入详细地址"><?= htmlspecialchars($supplier['address'] ?? '') ?></textarea>
                        </div>
                    </div>

                    <!-- 备注信息 -->
                    <div class="form-section">
                        <h3><i class="fas fa-sticky-note"></i> 备注信息</h3>
                        
                        <div class="form-group">
                            <label class="form-label">备注</label>
                            <textarea name="notes" class="form-control" rows="4" 
                                      placeholder="请输入备注信息（可选）"><?= htmlspecialchars($supplier['notes'] ?? '') ?></textarea>
                        </div>
                    </div>
                </div>

                <!-- 表单操作 -->
                <div class="form-actions">
                    <a href="index.php" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i> 取消
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> 保存修改
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// 表单验证
document.querySelector('.supplier-form').addEventListener('submit', function(e) {
    // 验证必填字段
    const requiredFields = ['name', 'contact_person', 'phone'];
    for (let field of requiredFields) {
        const input = document.querySelector(`input[name="${field}"]`);
        if (!input.value.trim()) {
            e.preventDefault();
            alert(`请填写${input.previousElementSibling.textContent.replace('*', '')}`);
            input.focus();
            return false;
        }
    }
    
    // 验证电话号码格式
    const phone = document.querySelector('input[name="phone"]').value.trim();
    const phoneRegex = /^1[3-9]\d{9}$|^0\d{2,3}-?\d{7,8}$|^400-?\d{3}-?\d{4}$/;
    if (!phoneRegex.test(phone)) {
        e.preventDefault();
        alert('请输入正确的电话号码格式');
        return false;
    }
    
    // 验证邮箱格式（如果填写了）
    const email = document.querySelector('input[name="email"]').value.trim();
    if (email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            e.preventDefault();
            alert('请输入正确的邮箱格式');
            return false;
        }
    }
});
</script>

</body>
</html>
