<?php require_once dirname(__DIR__, 2) . '/includes/header-modular.php'; ?>

<div class="main-content">
    <?php require_once dirname(__DIR__, 2) . '/includes/sidebar-modular.php'; ?>
    
    <div class="content">
        <div class="content-header">
            <h1>
                <i class="fas fa-exclamation-triangle"></i>
                食材报损
            </h1>
            <div class="header-actions">
                <a href="index.php?action=damage&operation=create" class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    新增报损
                </a>
            </div>
        </div>

        <!-- 统计卡片 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon bg-red">
                    <i class="fas fa-trash-alt"></i>
                </div>
                <div class="stat-content">
                    <h3><?= number_format($data['stats']['total_damage_records'] ?? 0) ?></h3>
                    <p>报损记录</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon bg-orange">
                    <i class="fas fa-weight"></i>
                </div>
                <div class="stat-content">
                    <h3><?= number_format($data['stats']['total_damage_quantity'] ?? 0, 1) ?></h3>
                    <p>报损总量</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon bg-yellow">
                    <i class="fas fa-money-bill-wave"></i>
                </div>
                <div class="stat-content">
                    <h3>¥<?= number_format($data['stats']['total_damage_value'] ?? 0, 2) ?></h3>
                    <p>报损价值</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon bg-purple">
                    <i class="fas fa-calendar-alt"></i>
                </div>
                <div class="stat-content">
                    <h3><?= number_format($data['stats']['this_month_records'] ?? 0) ?></h3>
                    <p>本月报损</p>
                </div>
            </div>
        </div>

        <!-- 搜索筛选 -->
        <div class="search-bar" style="padding: 15px !important;">
            <form method="GET" action="index.php" class="search-bar-form" style="display: flex !important; flex-wrap: nowrap !important; gap: 10px !important; align-items: center !important; padding: 0 !important;">
                <input type="hidden" name="action" value="damage">
                <div class="form-field text-input" style="display: flex !important; align-items: center !important; gap: 8px !important; flex: 0 0 auto !important; margin: 0 !important;">
                    <label style="font-size: 14px !important; font-weight: 500 !important; color: #374151 !important; white-space: nowrap !important; margin: 0 !important;">食材名称</label>
                    <input type="text" name="search" placeholder="搜索食材名称..." 
                           value="<?= htmlspecialchars($data['search'] ?? '') ?>" class="form-control" style="height: 36px !important; width: 180px !important; padding: 6px 10px !important; border: 1px solid #d1d5db !important; border-radius: 6px !important; font-size: 14px !important; box-sizing: border-box !important;">
                </div>
                
                <div class="form-field select-input" style="display: flex !important; align-items: center !important; gap: 8px !important; flex: 0 0 auto !important; margin: 0 !important;">
                    <label style="font-size: 14px !important; font-weight: 500 !important; color: #374151 !important; white-space: nowrap !important; margin: 0 !important;">报损类型</label>
                    <select name="damage_type" class="form-control" style="height: 36px !important; width: 140px !important; padding: 6px 10px !important; border: 1px solid #d1d5db !important; border-radius: 6px !important; font-size: 14px !important; box-sizing: border-box !important; background: white !important;">
                        <option value="">全部类型</option>
                        <option value="expired" <?= ($data['damage_type'] ?? '') === 'expired' ? 'selected' : '' ?>>过期报损</option>
                        <option value="damaged" <?= ($data['damage_type'] ?? '') === 'damaged' ? 'selected' : '' ?>>破损报损</option>
                        <option value="quality" <?= ($data['damage_type'] ?? '') === 'quality' ? 'selected' : '' ?>>质量问题</option>
                        <option value="other" <?= ($data['damage_type'] ?? '') === 'other' ? 'selected' : '' ?>>其他原因</option>
                    </select>
                </div>
                
                <div class="form-field date-input" style="display: flex !important; align-items: center !important; gap: 8px !important; flex: 0 0 auto !important; margin: 0 !important;">
                    <label style="font-size: 14px !important; font-weight: 500 !important; color: #374151 !important; white-space: nowrap !important; margin: 0 !important;">开始日期</label>
                    <input type="date" name="date_from" 
                           value="<?= htmlspecialchars($data['date_from'] ?? '') ?>" class="form-control" style="height: 36px !important; width: 150px !important; padding: 6px 10px !important; border: 1px solid #d1d5db !important; border-radius: 6px !important; font-size: 14px !important; box-sizing: border-box !important;">
                </div>
                
                <div class="form-field date-input" style="display: flex !important; align-items: center !important; gap: 8px !important; flex: 0 0 auto !important; margin: 0 !important;">
                    <label style="font-size: 14px !important; font-weight: 500 !important; color: #374151 !important; white-space: nowrap !important; margin: 0 !important;">结束日期</label>
                    <input type="date" name="date_to" 
                           value="<?= htmlspecialchars($data['date_to'] ?? '') ?>" class="form-control" style="height: 36px !important; width: 150px !important; padding: 6px 10px !important; border: 1px solid #d1d5db !important; border-radius: 6px !important; font-size: 14px !important; box-sizing: border-box !important;">
                </div>
                
                <button type="submit" class="btn btn-primary" style="flex: 0 0 auto !important; height: 36px !important; padding: 0 14px !important; white-space: nowrap !important; margin: 0 !important; border: none !important; border-radius: 6px !important; background: #3b82f6 !important; color: white !important; font-size: 14px !important; font-weight: 500 !important; cursor: pointer !important; display: inline-flex !important; align-items: center !important; gap: 6px !important; text-decoration: none !important;">
                    <i class="fas fa-search"></i>
                    搜索
                </button>
                
                <a href="index.php?action=damage" class="btn btn-secondary" style="flex: 0 0 auto !important; height: 36px !important; padding: 0 14px !important; white-space: nowrap !important; margin: 0 !important; border: none !important; border-radius: 6px !important; background: #6b7280 !important; color: white !important; font-size: 14px !important; font-weight: 500 !important; cursor: pointer !important; display: inline-flex !important; align-items: center !important; gap: 6px !important; text-decoration: none !important;">
                    <i class="fas fa-refresh"></i>
                    重置
                </a>
            </form>
        </div>

        <!-- 报损记录列表 -->
        <div class="table-container">
            <table class="table">
                <thead>
                    <tr>
                        <th>食材信息</th>
                        <th>报损类型</th>
                        <th>报损数量</th>
                        <th>单价</th>
                        <th>报损价值</th>
                        <th>报损原因</th>
                        <th>操作员</th>
                        <th>报损时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($data['damage_records'])): ?>
                        <?php foreach ($data['damage_records'] as $record): ?>
                            <tr>
                                <td>
                                    <div class="ingredient-info">
                                        <div class="ingredient-name"><?= htmlspecialchars($record['ingredient_name']) ?></div>
                                        <div class="ingredient-unit">单位: <?= htmlspecialchars($record['unit']) ?></div>
                                        <?php if (!empty($record['batch_number'])): ?>
                                        <div class="batch-number">批次: <?= htmlspecialchars($record['batch_number']) ?></div>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <?php
                                    $damageTypes = [
                                        'expired' => '<span class="damage-type expired">过期报损</span>',
                                        'damaged' => '<span class="damage-type damaged">破损报损</span>',
                                        'quality' => '<span class="damage-type quality">质量问题</span>',
                                        'other' => '<span class="damage-type other">其他原因</span>'
                                    ];
                                    echo $damageTypes[$record['damage_type']] ?? $record['damage_type'];
                                    ?>
                                </td>
                                <td>
                                    <div class="quantity-info">
                                        <div class="quantity-value"><?= number_format($record['damage_quantity'], 1) ?></div>
                                        <div class="quantity-unit"><?= htmlspecialchars($record['unit']) ?></div>
                                    </div>
                                </td>
                                <td>
                                    ¥<?= number_format($record['unit_price'], 2) ?>
                                </td>
                                <td>
                                    <strong class="damage-value">¥<?= number_format($record['damage_value'], 2) ?></strong>
                                </td>
                                <td>
                                    <div class="damage-reason"><?= htmlspecialchars($record['damage_reason']) ?></div>
                                </td>
                                <td>
                                    <div class="operator-info"><?= htmlspecialchars($record['operator_name'] ?? '系统') ?></div>
                                </td>
                                <td>
                                    <div class="time-info"><?= date('m-d H:i', strtotime($record['damage_date'])) ?></div>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <a href="index.php?action=damage&operation=view&id=<?= $record['id'] ?>" 
                                           class="btn btn-sm btn-info" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="9" class="text-center text-muted">
                                <i class="fas fa-inbox"></i>
                                暂无报损记录
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <!-- 汇总模块移除，根据需求再开启 -->
    </div>
</div>

<style>
/* 食材报损模块特有样式 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 24px;
    color: white;
}

.stat-icon.bg-red { background: #f56565; }
.stat-icon.bg-orange { background: #ed8936; }
.stat-icon.bg-yellow { background: #f6e05e; }
.stat-icon.bg-purple { background: #9f7aea; }

.stat-content h3 {
    margin: 0;
    font-size: 26px;
    font-weight: bold;
    color: #2d3748;
}

.stat-content p {
    margin: 5px 0 0 0;
    color: #718096;
    font-size: 16px;
}

.search-section {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.search-form {
    display: flex;
    gap: 15px;
    align-items: end;
    flex-wrap: wrap;
}

.search-form .form-group {
    flex: 1;
    min-width: 150px;
}

.ingredient-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.ingredient-name {
    font-weight: 600;
    color: #2d3748;
    font-size: 15px;
}

.ingredient-unit {
    font-size: 12px;
    color: #718096;
    background: #f7fafc;
    padding: 2px 6px;
    border-radius: 4px;
    display: inline-block;
    width: fit-content;
}

.batch-number {
    font-size: 12px;
    color: #718096;
    background: #f7fafc;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    display: inline-block;
    width: fit-content;
}

.damage-type {
    padding: 6px 10px;
    border-radius: 12px;
    font-size: 13px;
    font-weight: 500;
}

.damage-type.expired { background: #fed7d7; color: #c53030; }
.damage-type.damaged { background: #fef5e7; color: #c05621; }
.damage-type.quality { background: #fff5d6; color: #975a16; }
.damage-type.other { background: #e6fffa; color: #234e52; }

.quantity-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
}

.quantity-value {
    font-weight: bold;
    color: #2d3748;
    font-size: 16px;
}

.quantity-unit {
    font-size: 12px;
    color: #718096;
}

.damage-value {
    color: #c53030;
    font-size: 16px;
}

.damage-reason {
    font-size: 14px;
    color: #4a5568;
    max-width: 200px;
    word-wrap: break-word;
}

.operator-info, .time-info {
    font-size: 14px;
    color: #4a5568;
}

.action-buttons {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

.table-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
}

.summary-section {
    margin-top: 30px;
}

.summary-card {
    background: white;
    border-radius: 8px;
    padding: 25px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    align-items: flex-start;
    gap: 20px;
    max-width: 600px;
    margin: 0 auto;
}

.summary-icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    background: linear-gradient(135deg, #f56565 0%, #c53030 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28px;
    color: white;
    flex-shrink: 0;
}

.summary-content {
    flex: 1;
}

.summary-content h4 {
    margin: 0 0 15px 0;
    color: #2d3748;
    font-size: 18px;
}

.summary-details {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e2e8f0;
}

.summary-item:last-child {
    border-bottom: none;
}

.summary-item .label {
    color: #4a5568;
    font-size: 14px;
}

.summary-item .value {
    color: #c53030;
    font-weight: bold;
    font-size: 16px;
}

@media (max-width: 768px) {
    .search-form {
        flex-direction: column;
    }
    
    .search-form .form-group {
        min-width: 100%;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .table-container {
        overflow-x: auto;
    }
    
    .table {
        min-width: 1000px;
    }
    
    .summary-card {
        flex-direction: column;
        text-align: center;
    }
    
    .summary-icon {
        margin: 0 auto;
    }
}
</style>

<script>
function exportData() {
    // 导出功能实现
    alert('导出功能开发中...');
}

function printDamageReport(recordId) {
    // 打印报损单功能
    const printUrl = `index.php?action=damage&operation=print&id=${recordId}`;
    window.open(printUrl, '_blank', 'width=800,height=600');
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 可以添加页面初始化代码
    console.log('食材报损模块已加载');
});
</script>

<?php require_once dirname(__DIR__, 2) . '/includes/footer-modular.php'; ?>