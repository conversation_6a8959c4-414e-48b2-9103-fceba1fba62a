<?php
/**
 * 用户权限管理表升级脚本
 * 创建完整的用户角色权限管理体系
 */

// 数据库配置
$host = '************';
$port = 3306;
$dbname = 'sc';
$username = 'sc';
$password = 'pw5K4SsM7kZsjdxy';

echo "<h2>用户权限管理表升级脚本</h2>";

try {
    $pdo = new PDO("mysql:host=$host;port=$port;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p style='color: green;'>✅ 数据库连接成功</p>";
    
    // 升级用户表结构
    echo "<h3>1. 升级用户表</h3>";
    
    $userTableUpgrades = [
        "ADD COLUMN `real_name` varchar(50) AFTER `name`" => "real_name",
        "ADD COLUMN `avatar` varchar(255) AFTER `phone`" => "avatar", 
        "ADD COLUMN `last_login_at` timestamp NULL AFTER `status`" => "last_login_at",
        "MODIFY COLUMN `email` varchar(100) NOT NULL UNIQUE" => false,
        "MODIFY COLUMN `name` varchar(50) NOT NULL" => false
    ];
    
    foreach ($userTableUpgrades as $sql => $checkField) {
        if ($checkField) {
            // 检查字段是否存在
            $stmt = $pdo->query("SHOW COLUMNS FROM users LIKE '$checkField'");
            if ($stmt->rowCount() == 0) {
                try {
                    $pdo->exec("ALTER TABLE users $sql");
                    echo "<p style='color: green;'>✅ 用户表字段 $checkField 添加成功</p>";
                } catch (Exception $e) {
                    echo "<p style='color: orange;'>⚠️ 用户表字段 $checkField 添加失败: " . $e->getMessage() . "</p>";
                }
            } else {
                echo "<p style='color: blue;'>ℹ️ 用户表字段 $checkField 已存在</p>";
            }
        }
    }
    
    // 创建角色表
    echo "<h3>2. 创建角色表</h3>";
    $rolesTableSQL = "
    CREATE TABLE IF NOT EXISTS `roles` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `name` varchar(50) NOT NULL COMMENT '角色名称（英文）',
        `display_name` varchar(50) NOT NULL COMMENT '角色显示名称（中文）',
        `description` text DEFAULT NULL COMMENT '角色描述',
        `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
        `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1启用 0禁用',
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `name` (`name`),
        KEY `status` (`status`),
        KEY `sort_order` (`sort_order`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='角色表';
    ";
    
    $pdo->exec($rolesTableSQL);
    echo "<p style='color: green;'>✅ 角色表创建成功</p>";
    
    // 创建权限表
    echo "<h3>3. 创建权限表</h3>";
    $permissionsTableSQL = "
    CREATE TABLE IF NOT EXISTS `permissions` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `name` varchar(50) NOT NULL COMMENT '权限名称（英文）',
        `display_name` varchar(50) NOT NULL COMMENT '权限显示名称（中文）',
        `module` varchar(50) NOT NULL COMMENT '所属模块',
        `description` text DEFAULT NULL COMMENT '权限描述',
        `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
        `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1启用 0禁用',
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `name` (`name`),
        KEY `module` (`module`),
        KEY `status` (`status`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='权限表';
    ";
    
    $pdo->exec($permissionsTableSQL);
    echo "<p style='color: green;'>✅ 权限表创建成功</p>";
    
    // 创建用户角色关联表
    echo "<h3>4. 创建用户角色关联表</h3>";
    $userRolesTableSQL = "
    CREATE TABLE IF NOT EXISTS `user_roles` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `user_id` bigint(20) NOT NULL COMMENT '用户ID',
        `role_id` int(11) NOT NULL COMMENT '角色ID',
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `user_role` (`user_id`, `role_id`),
        KEY `user_id` (`user_id`),
        KEY `role_id` (`role_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='用户角色关联表';
    ";
    
    $pdo->exec($userRolesTableSQL);
    echo "<p style='color: green;'>✅ 用户角色关联表创建成功</p>";
    
    // 创建角色权限关联表
    echo "<h3>5. 创建角色权限关联表</h3>";
    $rolePermissionsTableSQL = "
    CREATE TABLE IF NOT EXISTS `role_permissions` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `role_id` int(11) NOT NULL COMMENT '角色ID',
        `permission_id` int(11) NOT NULL COMMENT '权限ID',
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `role_permission` (`role_id`, `permission_id`),
        KEY `role_id` (`role_id`),
        KEY `permission_id` (`permission_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='角色权限关联表';
    ";
    
    $pdo->exec($rolePermissionsTableSQL);
    echo "<p style='color: green;'>✅ 角色权限关联表创建成功</p>";
    
    // 创建用户操作日志表
    echo "<h3>6. 创建用户操作日志表</h3>";
    $userLogsTableSQL = "
    CREATE TABLE IF NOT EXISTS `user_logs` (
        `id` bigint(20) NOT NULL AUTO_INCREMENT,
        `user_id` bigint(20) NOT NULL COMMENT '用户ID',
        `action` varchar(50) NOT NULL COMMENT '操作动作',
        `module` varchar(50) NOT NULL COMMENT '操作模块',
        `description` text NOT NULL COMMENT '操作描述',
        `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
        `user_agent` text DEFAULT NULL COMMENT '用户代理',
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `user_id` (`user_id`),
        KEY `action` (`action`),
        KEY `module` (`module`),
        KEY `created_at` (`created_at`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='用户操作日志表';
    ";
    
    $pdo->exec($userLogsTableSQL);
    echo "<p style='color: green;'>✅ 用户操作日志表创建成功</p>";
    
    // 插入初始角色数据
    echo "<h3>7. 插入初始角色数据</h3>";
    $initialRoles = [
        ['name' => 'admin', 'display_name' => '系统管理员', 'description' => '拥有系统所有权限', 'sort_order' => 1],
        ['name' => 'warehouse_manager', 'display_name' => '仓库管理员', 'description' => '负责库存管理、入库出库操作', 'sort_order' => 2],
        ['name' => 'buyer', 'display_name' => '采购员', 'description' => '负责采购计划、供应商管理', 'sort_order' => 3],
        ['name' => 'chef', 'display_name' => '厨师长', 'description' => '负责出库申请、菜谱管理', 'sort_order' => 4],
        ['name' => 'auditor', 'display_name' => '审核员', 'description' => '负责审核各类业务流程', 'sort_order' => 5],
        ['name' => 'viewer', 'display_name' => '查看员', 'description' => '只能查看数据，无操作权限', 'sort_order' => 6]
    ];
    
    foreach ($initialRoles as $role) {
        $stmt = $pdo->prepare("SELECT id FROM roles WHERE name = ?");
        $stmt->execute([$role['name']]);
        
        if ($stmt->rowCount() == 0) {
            $stmt = $pdo->prepare("INSERT INTO roles (name, display_name, description, sort_order) VALUES (?, ?, ?, ?)");
            $stmt->execute([$role['name'], $role['display_name'], $role['description'], $role['sort_order']]);
            echo "<p style='color: green;'>✅ 角色 {$role['display_name']} 创建成功</p>";
        } else {
            echo "<p style='color: blue;'>ℹ️ 角色 {$role['display_name']} 已存在</p>";
        }
    }
    
    // 插入初始权限数据
    echo "<h3>8. 插入初始权限数据</h3>";
    $initialPermissions = [
        // 系统管理
        ['name' => 'system.view', 'display_name' => '系统查看', 'module' => 'system'],
        ['name' => 'system.manage', 'display_name' => '系统管理', 'module' => 'system'],
        
        // 用户管理
        ['name' => 'user.view', 'display_name' => '用户查看', 'module' => 'user'],
        ['name' => 'user.create', 'display_name' => '用户创建', 'module' => 'user'],
        ['name' => 'user.edit', 'display_name' => '用户编辑', 'module' => 'user'],
        ['name' => 'user.delete', 'display_name' => '用户删除', 'module' => 'user'],
        
        // 食材管理
        ['name' => 'ingredient.view', 'display_name' => '食材查看', 'module' => 'ingredient'],
        ['name' => 'ingredient.create', 'display_name' => '食材创建', 'module' => 'ingredient'],
        ['name' => 'ingredient.edit', 'display_name' => '食材编辑', 'module' => 'ingredient'],
        ['name' => 'ingredient.delete', 'display_name' => '食材删除', 'module' => 'ingredient'],
        
        // 供应商管理
        ['name' => 'supplier.view', 'display_name' => '供应商查看', 'module' => 'supplier'],
        ['name' => 'supplier.create', 'display_name' => '供应商创建', 'module' => 'supplier'],
        ['name' => 'supplier.edit', 'display_name' => '供应商编辑', 'module' => 'supplier'],
        ['name' => 'supplier.delete', 'display_name' => '供应商删除', 'module' => 'supplier'],
        
        // 采购管理
        ['name' => 'purchase.view', 'display_name' => '采购查看', 'module' => 'purchase'],
        ['name' => 'purchase.create', 'display_name' => '采购创建', 'module' => 'purchase'],
        ['name' => 'purchase.edit', 'display_name' => '采购编辑', 'module' => 'purchase'],
        ['name' => 'purchase.approve', 'display_name' => '采购审批', 'module' => 'purchase'],
        
        // 入库管理
        ['name' => 'inbound.view', 'display_name' => '入库查看', 'module' => 'inbound'],
        ['name' => 'inbound.create', 'display_name' => '入库操作', 'module' => 'inbound'],
        ['name' => 'inbound.edit', 'display_name' => '入库编辑', 'module' => 'inbound'],
        
        // 出库管理
        ['name' => 'outbound.view', 'display_name' => '出库查看', 'module' => 'outbound'],
        ['name' => 'outbound.create', 'display_name' => '出库操作', 'module' => 'outbound'],
        ['name' => 'outbound.edit', 'display_name' => '出库编辑', 'module' => 'outbound'],
        
        // 库存管理
        ['name' => 'inventory.view', 'display_name' => '库存查看', 'module' => 'inventory'],
        ['name' => 'inventory.stocktaking', 'display_name' => '库存盘点', 'module' => 'inventory'],
        
        // 报表分析
        ['name' => 'report.view', 'display_name' => '报表查看', 'module' => 'report'],
        ['name' => 'report.export', 'display_name' => '报表导出', 'module' => 'report']
    ];
    
    foreach ($initialPermissions as $index => $permission) {
        $stmt = $pdo->prepare("SELECT id FROM permissions WHERE name = ?");
        $stmt->execute([$permission['name']]);
        
        if ($stmt->rowCount() == 0) {
            $stmt = $pdo->prepare("INSERT INTO permissions (name, display_name, module, sort_order) VALUES (?, ?, ?, ?)");
            $stmt->execute([$permission['name'], $permission['display_name'], $permission['module'], $index + 1]);
            echo "<p style='color: green;'>✅ 权限 {$permission['display_name']} 创建成功</p>";
        } else {
            echo "<p style='color: blue;'>ℹ️ 权限 {$permission['display_name']} 已存在</p>";
        }
    }
    
    // 为管理员角色分配所有权限
    echo "<h3>9. 配置角色权限</h3>";
    $stmt = $pdo->query("SELECT id FROM roles WHERE name = 'admin'");
    $adminRole = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($adminRole) {
        $stmt = $pdo->query("SELECT id FROM permissions");
        $permissions = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        foreach ($permissions as $permissionId) {
            $stmt = $pdo->prepare("SELECT id FROM role_permissions WHERE role_id = ? AND permission_id = ?");
            $stmt->execute([$adminRole['id'], $permissionId]);
            
            if ($stmt->rowCount() == 0) {
                $stmt = $pdo->prepare("INSERT INTO role_permissions (role_id, permission_id) VALUES (?, ?)");
                $stmt->execute([$adminRole['id'], $permissionId]);
            }
        }
        echo "<p style='color: green;'>✅ 管理员角色权限配置完成</p>";
    }
    
    // 更新现有用户
    echo "<h3>10. 更新现有用户数据</h3>";
    $stmt = $pdo->query("SELECT id, name FROM users WHERE status = 1");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($users as $user) {
        // 更新real_name字段
        if (empty($user['real_name'])) {
            $stmt = $pdo->prepare("UPDATE users SET real_name = ? WHERE id = ?");
            $stmt->execute([$user['name'], $user['id']]);
        }
        
        // 为admin用户分配管理员角色
        if ($user['name'] === 'admin' && $adminRole) {
            $stmt = $pdo->prepare("SELECT id FROM user_roles WHERE user_id = ? AND role_id = ?");
            $stmt->execute([$user['id'], $adminRole['id']]);
            
            if ($stmt->rowCount() == 0) {
                $stmt = $pdo->prepare("INSERT INTO user_roles (user_id, role_id) VALUES (?, ?)");
                $stmt->execute([$user['id'], $adminRole['id']]);
                echo "<p style='color: green;'>✅ 用户 {$user['name']} 分配管理员角色</p>";
            }
        }
    }
    
    echo "<hr>";
    echo "<h3>升级完成！</h3>";
    echo "<p style='color: green;'>✅ 用户权限管理系统已准备就绪</p>";
    echo "<p><a href='../modules/users/index.php'>👉 进入用户管理</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ 错误: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='../index.php'>返回首页</a> | <a href='../test/'>测试中心</a></p>";
?>