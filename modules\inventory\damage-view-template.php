<?php require_once dirname(__DIR__, 2) . '/includes/header-modular.php'; ?>

<div class="main-content">
    <?php require_once dirname(__DIR__, 2) . '/includes/sidebar-modular.php'; ?>

    <div class="content">
        <div class="content-header">
            <h1>
                <i class="fas fa-file-alt"></i>
                报损详情
            </h1>
            <div class="header-actions">
                <a href="index.php?action=damage" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i>
                    返回列表
                </a>
            </div>
        </div>

        <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger"><?= htmlspecialchars($error_message) ?></div>
        <?php endif; ?>

        <div class="detail-container">
            <div class="detail-card">
                <h3 class="detail-title">基础信息</h3>
                <div class="detail-content">
                    <div class="detail-row"><span class="detail-label">食材名称</span><span class="detail-value"><?= htmlspecialchars($record['ingredient_name'] ?? '') ?></span></div>
                    <div class="detail-row"><span class="detail-label">报损类型</span><span class="detail-value"><?php
                        $map = [
                            'expired' => '过期报损',
                            'damaged' => '破损报损',
                            'quality' => '质量问题',
                            'other' => '其他原因',
                        ];
                        $t = $record['damage_type'] ?? '';
                        echo htmlspecialchars($map[$t] ?? $t);
                    ?></span></div>
                    <div class="detail-row"><span class="detail-label">报损数量</span><span class="detail-value"><?= number_format($record['damage_quantity'] ?? 0, 2) ?> <?= htmlspecialchars($record['unit'] ?? '') ?></span></div>
                    <div class="detail-row"><span class="detail-label">单价</span><span class="detail-value">¥<?= number_format($record['unit_price'] ?? 0, 2) ?></span></div>
                    <div class="detail-row"><span class="detail-label">报损价值</span><span class="detail-value">¥<?= number_format($record['damage_value'] ?? 0, 2) ?></span></div>
                </div>
            </div>

            <div class="detail-card">
                <h3 class="detail-title">其他信息</h3>
                <div class="detail-content">
                    <div class="detail-row"><span class="detail-label">批次号</span><span class="detail-value"><?= htmlspecialchars($record['batch_number'] ?? '-') ?></span></div>
                    <div class="detail-row"><span class="detail-label">操作员</span><span class="detail-value"><?= htmlspecialchars($record['operator_name'] ?? '系统') ?></span></div>
                    <div class="detail-row"><span class="detail-label">报损时间</span><span class="detail-value"><?= !empty($record['damage_date']) ? date('Y-m-d H:i', strtotime($record['damage_date'])) : '' ?></span></div>
                </div>
            </div>

            <?php if (!empty($record['damage_reason'])): ?>
            <div class="detail-card detail-full">
                <h3 class="detail-title">报损原因</h3>
                <div class="detail-content">
                    <div class="detail-row"><span class="detail-value"><?= nl2br(htmlspecialchars($record['damage_reason'])) ?></span></div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
.detail-container { display: grid; grid-template-columns: repeat(auto-fit, minmax(320px,1fr)); gap: 20px; }
.detail-card { background: #fff; border-radius: 10px; box-shadow: 0 2px 4px rgba(0,0,0,.08); padding: 20px; }
.detail-title { font-size: 18px; font-weight: 700; color: #1f2937; margin: 0 0 12px; }
.detail-content { display: flex; flex-direction: column; gap: 10px; }
.detail-row { display: grid; grid-template-columns: 120px 1fr; gap: 10px; align-items: center; }
.detail-label { color: #6b7280; font-weight: 600; }
.detail-value { color: #111827; font-weight: 600; }
.detail-full .detail-row { grid-template-columns: 1fr; }
</style>

<?php require_once dirname(__DIR__, 2) . '/includes/footer-modular.php'; ?>

