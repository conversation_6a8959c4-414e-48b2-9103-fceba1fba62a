<?php
/**
 * 检查分类数据
 */

require_once '../includes/Database.php';

echo "<h2>检查食材分类数据</h2>";

try {
    // 创建数据库实例
    $db = Database::getInstance();
    echo "<p>✅ Database类实例化成功</p>";
    
    // 检查分类表是否存在
    echo "<h3>检查ingredient_categories表：</h3>";
    try {
        $tables = $db->fetchAll("SHOW TABLES LIKE 'ingredient_categories'");
        if (empty($tables)) {
            echo "<p style='color: red;'>❌ ingredient_categories表不存在</p>";
            echo "<p>请运行 <a href='../create-tables.php'>create-tables.php</a> 创建数据表</p>";
        } else {
            echo "<p style='color: green;'>✅ ingredient_categories表存在</p>";
            
            // 检查表结构
            echo "<h4>表结构：</h4>";
            $columns = $db->fetchAll("DESCRIBE ingredient_categories");
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>字段</th><th>类型</th><th>空值</th><th>键</th><th>默认值</th></tr>";
            foreach ($columns as $column) {
                echo "<tr>";
                echo "<td>" . $column['Field'] . "</td>";
                echo "<td>" . $column['Type'] . "</td>";
                echo "<td>" . $column['Null'] . "</td>";
                echo "<td>" . $column['Key'] . "</td>";
                echo "<td>" . ($column['Default'] ?? 'NULL') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            // 检查数据
            echo "<h4>数据统计：</h4>";
            $count = $db->fetchOne("SELECT COUNT(*) as count FROM ingredient_categories");
            echo "<p>总记录数: " . $count['count'] . "</p>";
            
            if ($count['count'] > 0) {
                $categories = $db->fetchAll("SELECT * FROM ingredient_categories ORDER BY id ASC LIMIT 10");
                echo "<h4>前10条记录：</h4>";
                echo "<table border='1' style='border-collapse: collapse;'>";
                echo "<tr><th>ID</th><th>名称</th><th>代码</th><th>状态</th><th>创建时间</th></tr>";
                foreach ($categories as $category) {
                    echo "<tr>";
                    echo "<td>" . $category['id'] . "</td>";
                    echo "<td>" . htmlspecialchars($category['name']) . "</td>";
                    echo "<td>" . htmlspecialchars($category['code'] ?? '') . "</td>";
                    echo "<td>" . ($category['status'] ?? 'NULL') . "</td>";
                    echo "<td>" . ($category['created_at'] ?? 'NULL') . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ 查询失败: " . $e->getMessage() . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ 数据库连接失败: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='../modules/ingredients/index.php?action=create'>测试食材创建</a> | <a href='../modules/categories/index.php'>分类管理</a> | <a href='../index.php'>返回首页</a></p>";
?>
