<?php require_once dirname(__DIR__, 2) . '/includes/header-modular.php'; ?>

<div class="main-content">
    <?php require_once dirname(__DIR__, 2) . '/includes/sidebar-modular.php'; ?>
    
    <div class="content">
        <div class="content-header">
            <h1>
                <i class="fas fa-info-circle"></i>
                关于系统
            </h1>
            <div class="header-actions">
                <a href="index.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i>
                    返回设置
                </a>
            </div>
        </div>

        <div class="about-container">
            <!-- 系统信息 -->
            <div class="about-section">
                <div class="section-header">
                    <h2><i class="fas fa-desktop"></i> 系统信息</h2>
                </div>
                <div class="info-grid">
                    <div class="info-card">
                        <div class="info-icon">
                            <i class="fas fa-code-branch"></i>
                        </div>
                        <div class="info-content">
                            <h3>系统版本</h3>
                            <p><?= htmlspecialchars($system_info['version'] ?? '1.0.0') ?></p>
                        </div>
                    </div>
                    <div class="info-card">
                        <div class="info-icon">
                            <i class="fab fa-php"></i>
                        </div>
                        <div class="info-content">
                            <h3>PHP 版本</h3>
                            <p><?= htmlspecialchars($system_info['php_version'] ?? 'Unknown') ?></p>
                        </div>
                    </div>
                    <div class="info-card">
                        <div class="info-icon">
                            <i class="fas fa-server"></i>
                        </div>
                        <div class="info-content">
                            <h3>Web 服务器</h3>
                            <p><?= htmlspecialchars($system_info['server_software'] ?? 'Unknown') ?></p>
                        </div>
                    </div>
                    <div class="info-card">
                        <div class="info-icon">
                            <i class="fas fa-database"></i>
                        </div>
                        <div class="info-content">
                            <h3>数据库版本</h3>
                            <p><?= htmlspecialchars($system_info['database_version'] ?? 'Unknown') ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 服务器配置 -->
            <div class="about-section">
                <div class="section-header">
                    <h2><i class="fas fa-cogs"></i> 服务器配置</h2>
                </div>
                <div class="config-table">
                    <div class="config-row">
                        <div class="config-label">最大上传文件大小</div>
                        <div class="config-value"><?= htmlspecialchars($system_info['upload_max_filesize'] ?? 'Unknown') ?></div>
                    </div>
                    <div class="config-row">
                        <div class="config-label">POST 最大大小</div>
                        <div class="config-value"><?= htmlspecialchars($system_info['post_max_size'] ?? 'Unknown') ?></div>
                    </div>
                    <div class="config-row">
                        <div class="config-label">内存限制</div>
                        <div class="config-value"><?= htmlspecialchars($system_info['memory_limit'] ?? 'Unknown') ?></div>
                    </div>
                    <div class="config-row">
                        <div class="config-label">最大执行时间</div>
                        <div class="config-value"><?= htmlspecialchars($system_info['max_execution_time'] ?? 'Unknown') ?> 秒</div>
                    </div>
                </div>
            </div>

            <!-- 系统介绍 -->
            <div class="about-section">
                <div class="section-header">
                    <h2><i class="fas fa-book"></i> 系统介绍</h2>
                </div>
                <div class="system-intro">
                    <div class="intro-content">
                        <h3>学校食堂管理系统</h3>
                        <p>这是一个专为学校食堂设计的综合管理系统，提供食材管理、库存控制、采购管理、用户权限管理等功能。系统采用现代化的Web技术开发，界面简洁美观，操作便捷高效。</p>
                        
                        <h4>主要功能模块：</h4>
                        <ul>
                            <li><strong>食材管理</strong> - 食材信息的录入、编辑和查询</li>
                            <li><strong>库存管理</strong> - 实时库存监控、入库出库记录</li>
                            <li><strong>采购管理</strong> - 采购计划制定、供应商管理</li>
                            <li><strong>用户管理</strong> - 用户账户管理、角色权限分配</li>
                            <li><strong>系统设置</strong> - 系统配置、安全设置、数据备份</li>
                        </ul>

                        <h4>技术特点：</h4>
                        <ul>
                            <li>响应式设计，支持多种设备访问</li>
                            <li>模块化架构，便于功能扩展</li>
                            <li>完善的权限控制系统</li>
                            <li>数据安全保护和备份机制</li>
                        </ul>
                    </div>
                    <div class="intro-image">
                        <div class="feature-showcase">
                            <div class="feature-item">
                                <i class="fas fa-utensils"></i>
                                <span>食材管理</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-warehouse"></i>
                                <span>库存控制</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-shopping-cart"></i>
                                <span>采购管理</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-users"></i>
                                <span>用户管理</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 技术支持 -->
            <div class="about-section">
                <div class="section-header">
                    <h2><i class="fas fa-life-ring"></i> 技术支持</h2>
                </div>
                <div class="support-grid">
                    <div class="support-card">
                        <div class="support-icon">
                            <i class="fas fa-question-circle"></i>
                        </div>
                        <div class="support-content">
                            <h3>使用帮助</h3>
                            <p>查看系统使用手册和常见问题解答</p>
                            <a href="#" class="btn btn-outline" onclick="alert('帮助文档开发中'); return false;">
                                <i class="fas fa-book"></i>
                                查看文档
                            </a>
                        </div>
                    </div>
                    <div class="support-card">
                        <div class="support-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="support-content">
                            <h3>联系我们</h3>
                            <p>遇到问题？联系技术支持团队</p>
                            <a href="mailto:<EMAIL>" class="btn btn-outline">
                                <i class="fas fa-envelope"></i>
                                发送邮件
                            </a>
                        </div>
                    </div>
                    <div class="support-card">
                        <div class="support-icon">
                            <i class="fas fa-download"></i>
                        </div>
                        <div class="support-content">
                            <h3>系统更新</h3>
                            <p>检查并下载最新版本</p>
                            <a href="#" class="btn btn-outline" onclick="alert('更新功能开发中'); return false;">
                                <i class="fas fa-sync-alt"></i>
                                检查更新
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 版权信息 -->
            <div class="about-section">
                <div class="copyright">
                    <p>&copy; 2024 学校食堂管理系统. 保留所有权利.</p>
                    <p>本系统仅供学校内部使用，未经授权不得复制或传播。</p>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.about-container {
    max-width: 900px;
}

.about-section {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    margin-bottom: 24px;
}

.section-header {
    margin-bottom: 20px;
}

.section-header h2 {
    margin: 0;
    color: #1f2937;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.info-card {
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
}

.info-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 12px;
    color: white;
    font-size: 20px;
}

.info-content h3 {
    margin: 0 0 8px 0;
    color: #1f2937;
    font-size: 16px;
    font-weight: 600;
}

.info-content p {
    margin: 0;
    color: #6b7280;
    font-size: 14px;
}

.config-table {
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    overflow: hidden;
}

.config-row {
    display: flex;
    padding: 12px 16px;
    border-bottom: 1px solid #e5e7eb;
}

.config-row:last-child {
    border-bottom: none;
}

.config-label {
    flex: 1;
    font-weight: 500;
    color: #374151;
}

.config-value {
    flex: 1;
    color: #6b7280;
    text-align: right;
}

.system-intro {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 24px;
    align-items: start;
}

.intro-content h3 {
    margin: 0 0 12px 0;
    color: #1f2937;
    font-size: 20px;
    font-weight: 600;
}

.intro-content h4 {
    margin: 20px 0 8px 0;
    color: #374151;
    font-size: 16px;
    font-weight: 600;
}

.intro-content p {
    margin: 0 0 16px 0;
    color: #6b7280;
    line-height: 1.6;
}

.intro-content ul {
    margin: 0 0 16px 0;
    padding-left: 20px;
    color: #6b7280;
}

.intro-content li {
    margin-bottom: 8px;
    line-height: 1.5;
}

.feature-showcase {
    background: #f0f9ff;
    border: 1px solid #bae6fd;
    border-radius: 8px;
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 12px;
    color: #0369a1;
    font-weight: 500;
}

.feature-item i {
    width: 32px;
    height: 32px;
    background: #0369a1;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
}

.support-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.support-card {
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
}

.support-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 16px;
    color: white;
    font-size: 20px;
}

.support-content h3 {
    margin: 0 0 8px 0;
    color: #1f2937;
    font-size: 16px;
    font-weight: 600;
}

.support-content p {
    margin: 0 0 16px 0;
    color: #6b7280;
    font-size: 14px;
    line-height: 1.5;
}

.copyright {
    text-align: center;
    padding: 20px;
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    color: #6b7280;
}

.copyright p {
    margin: 4px 0;
    font-size: 14px;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
}

.btn-secondary {
    background: #f3f4f6;
    color: #6b7280;
    border: 1px solid #d1d5db;
}

.btn-secondary:hover {
    background: #e5e7eb;
}

.btn-outline {
    background: transparent;
    color: #667eea;
    border: 1px solid #667eea;
}

.btn-outline:hover {
    background: #667eea;
    color: white;
}

@media (max-width: 768px) {
    .system-intro {
        grid-template-columns: 1fr;
    }
    
    .support-grid {
        grid-template-columns: 1fr;
    }
    
    .config-row {
        flex-direction: column;
        gap: 4px;
    }
    
    .config-value {
        text-align: left;
    }
}
</style>

<?php require_once dirname(__DIR__, 2) . '/includes/footer-modular.php'; ?>
