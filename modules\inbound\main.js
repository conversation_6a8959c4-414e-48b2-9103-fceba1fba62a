/**
 * 入库管理模块 JavaScript
 */

document.addEventListener('DOMContentLoaded', function() {
    initializePage();
});

function initializePage() {
    initializeSearch();
    initializeAnimations();
    initializeExpiryWarnings();
}

function initializeSearch() {
    const searchForm = document.getElementById('searchForm');
    if (searchForm) {
        searchForm.addEventListener('submit', function(e) {
            showLoading();
        });
    }
}

function initializeAnimations() {
    // 统计项动画
    const statItems = document.querySelectorAll('.stat-item');
    statItems.forEach((item, index) => {
        item.style.animationDelay = (index * 0.1) + 's';
    });
    
    // 数字计数动画
    animateNumbers();
}

function initializeExpiryWarnings() {
    const expiryDates = document.querySelectorAll('.expiry-date');
    expiryDates.forEach(element => {
        const daysLeftElement = element.querySelector('.days-left');
        if (daysLeftElement) {
            const daysLeft = parseInt(daysLeftElement.textContent);
            if (daysLeft <= 1) {
                element.classList.add('urgent-warning');
            }
        }
    });
}

function animateNumbers() {
    const statNumbers = document.querySelectorAll('.stat-number');
    
    statNumbers.forEach(element => {
        const finalValue = element.textContent.replace(/[^\d.]/g, '');
        if (finalValue && !isNaN(finalValue)) {
            animateNumber(element, 0, parseFloat(finalValue), 1000);
        }
    });
}

function animateNumber(element, start, end, duration) {
    const startTime = Date.now();
    const isDecimal = element.textContent.includes('¥');
    
    function update() {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        const current = start + (end - start) * easeOutQuart(progress);
        
        if (isDecimal) {
            element.textContent = '¥' + current.toLocaleString('zh-CN', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
        } else {
            element.textContent = Math.round(current).toLocaleString();
        }
        
        if (progress < 1) {
            requestAnimationFrame(update);
        }
    }
    
    requestAnimationFrame(update);
}

function easeOutQuart(t) {
    return 1 - (--t) * t * t * t;
}

function showLoading() {
    const form = document.getElementById('searchForm');
    if (form) {
        form.classList.add('loading');
    }
}

function exportData() {
    alert('导出功能开发中');
}

// 全局错误处理
window.addEventListener('error', function(e) {
    console.error('页面错误:', e.error);
});
