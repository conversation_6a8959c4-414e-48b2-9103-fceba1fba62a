<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>检查表结构</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .data-table { width: 100%; border-collapse: collapse; margin: 10px 0; font-size: 12px; }
        .data-table th, .data-table td { border: 1px solid #ddd; padding: 6px; text-align: left; }
        .data-table th { background: #f5f5f5; }
    </style>
</head>
<body>
    <h1>🔍 检查数据库表结构</h1>
    
    <?php
    require_once '../includes/Database.php';
    
    try {
        $db = Database::getInstance();
        
        echo "<div class='test-section'>";
        echo "<h2>📊 purchase_orders 表结构</h2>";
        
        $columns = $db->fetchAll("DESCRIBE purchase_orders");
        echo "<table class='data-table'>";
        echo "<tr><th>字段名</th><th>类型</th><th>是否为空</th><th>键</th><th>默认值</th><th>额外</th></tr>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td><strong>{$column['Field']}</strong></td>";
            echo "<td>{$column['Type']}</td>";
            echo "<td>{$column['Null']}</td>";
            echo "<td>{$column['Key']}</td>";
            echo "<td>{$column['Default']}</td>";
            echo "<td>{$column['Extra']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "</div>";
        
        echo "<div class='test-section'>";
        echo "<h2>📦 purchase_order_items 表结构</h2>";
        
        $itemColumns = $db->fetchAll("DESCRIBE purchase_order_items");
        echo "<table class='data-table'>";
        echo "<tr><th>字段名</th><th>类型</th><th>是否为空</th><th>键</th><th>默认值</th><th>额外</th></tr>";
        foreach ($itemColumns as $column) {
            echo "<tr>";
            echo "<td><strong>{$column['Field']}</strong></td>";
            echo "<td>{$column['Type']}</td>";
            echo "<td>{$column['Null']}</td>";
            echo "<td>{$column['Key']}</td>";
            echo "<td>{$column['Default']}</td>";
            echo "<td>{$column['Extra']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "</div>";
        
        echo "<div class='test-section'>";
        echo "<h2>🔍 suppliers 表结构</h2>";
        
        $supplierColumns = $db->fetchAll("DESCRIBE suppliers");
        echo "<table class='data-table'>";
        echo "<tr><th>字段名</th><th>类型</th><th>是否为空</th><th>键</th><th>默认值</th><th>额外</th></tr>";
        foreach ($supplierColumns as $column) {
            echo "<tr>";
            echo "<td><strong>{$column['Field']}</strong></td>";
            echo "<td>{$column['Type']}</td>";
            echo "<td>{$column['Null']}</td>";
            echo "<td>{$column['Key']}</td>";
            echo "<td>{$column['Default']}</td>";
            echo "<td>{$column['Extra']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "</div>";
        
        echo "<div class='test-section'>";
        echo "<h2>🌿 ingredients 表结构</h2>";
        
        $ingredientColumns = $db->fetchAll("DESCRIBE ingredients");
        echo "<table class='data-table'>";
        echo "<tr><th>字段名</th><th>类型</th><th>是否为空</th><th>键</th><th>默认值</th><th>额外</th></tr>";
        foreach ($ingredientColumns as $column) {
            echo "<tr>";
            echo "<td><strong>{$column['Field']}</strong></td>";
            echo "<td>{$column['Type']}</td>";
            echo "<td>{$column['Null']}</td>";
            echo "<td>{$column['Key']}</td>";
            echo "<td>{$column['Default']}</td>";
            echo "<td>{$column['Extra']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "</div>";
        
        echo "<div class='test-section'>";
        echo "<h2>📋 测试查询</h2>";
        
        // 测试简单查询
        echo "<h4>1. 测试基础采购单查询：</h4>";
        $basicOrders = $db->fetchAll("SELECT id, order_number, status FROM purchase_orders LIMIT 5");
        if (!empty($basicOrders)) {
            echo "<table class='data-table'>";
            echo "<tr><th>ID</th><th>订单号</th><th>状态</th></tr>";
            foreach ($basicOrders as $order) {
                echo "<tr>";
                echo "<td>{$order['id']}</td>";
                echo "<td>{$order['order_number']}</td>";
                echo "<td>{$order['status']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p class='error'>❌ 没有采购单数据</p>";
        }
        
        // 测试联合查询
        echo "<h4>2. 测试联合查询：</h4>";
        $joinQuery = "
            SELECT 
                po.id,
                po.order_number,
                po.status,
                s.name as supplier_name,
                COUNT(poi.id) as item_count
            FROM purchase_orders po
            LEFT JOIN suppliers s ON po.supplier_id = s.id
            LEFT JOIN purchase_order_items poi ON po.id = poi.order_id
            GROUP BY po.id
            LIMIT 5
        ";
        
        $joinResults = $db->fetchAll($joinQuery);
        if (!empty($joinResults)) {
            echo "<table class='data-table'>";
            echo "<tr><th>ID</th><th>订单号</th><th>状态</th><th>供应商</th><th>商品数</th></tr>";
            foreach ($joinResults as $result) {
                echo "<tr>";
                echo "<td>{$result['id']}</td>";
                echo "<td>{$result['order_number']}</td>";
                echo "<td>{$result['status']}</td>";
                echo "<td>{$result['supplier_name']}</td>";
                echo "<td>{$result['item_count']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p class='error'>❌ 联合查询失败</p>";
        }
        
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='test-section'>";
        echo "<h2 class='error'>❌ 数据库连接失败</h2>";
        echo "<p class='error'>错误信息: " . $e->getMessage() . "</p>";
        echo "</div>";
    }
    ?>
    
    <p><a href="../modules/inbound/index.php">返回入库管理</a></p>
</body>
</html>
