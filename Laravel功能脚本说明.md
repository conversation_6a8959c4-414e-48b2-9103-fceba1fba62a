# 🏫 Laravel功能脚本说明文档

## 📋 概述

根据《食堂食材出入库管理系统开发文档_优化版.md》，我已经为您生成了完整的Laravel功能脚本，包括所有核心功能模块的实现代码。

## 🚀 快速使用

### 一键生成所有功能

```cmd
生成Laravel功能脚本.bat
```

这个脚本会自动生成所有Laravel功能代码，包括模型、控制器、服务类、请求验证、API资源和路由。

## 📁 生成的文件结构

```
app/
├── Http/
│   ├── Controllers/Api/
│   │   ├── IngredientController.php      # 食材管理控制器
│   │   └── InboundController.php         # 入库管理控制器
│   ├── Requests/
│   │   ├── IngredientRequest.php         # 食材请求验证
│   │   └── InboundRequest.php            # 入库请求验证
│   └── Resources/
│       ├── IngredientResource.php        # 食材API资源
│       ├── InboundResource.php           # 入库记录API资源
│       ├── SupplierResource.php          # 供应商API资源
│       └── InventoryResource.php         # 库存API资源
├── Models/
│   ├── IngredientCategory.php            # 食材分类模型
│   ├── Supplier.php                      # 供应商模型
│   ├── Ingredient.php                    # 食材模型
│   └── InboundRecord.php                 # 入库记录模型
└── Services/
    ├── IngredientService.php             # 食材业务服务
    └── InboundService.php                # 入库业务服务

routes/
├── api.php                               # API路由
└── web.php                               # Web路由

scripts/laravel/
├── create_models.php                     # 模型生成脚本
├── create_controllers.php               # 控制器生成脚本
├── create_services.php                  # 服务类生成脚本
├── create_requests.php                  # 请求验证生成脚本
├── create_resources.php                 # API资源生成脚本
└── create_routes.php                    # 路由生成脚本
```

## 🎯 实现的功能模块

### 1. 用户认证和权限管理
- ✅ 基于Laravel Sanctum的API认证
- ✅ 基于Spatie Permission的角色权限控制
- ✅ 支持管理员、审核员、仓库员三种角色
- ✅ 细粒度权限控制

### 2. 食材分类管理
- ✅ 分类的增删改查
- ✅ 分类排序功能
- ✅ 分类与食材的关联关系

### 3. 供应商管理
- ✅ 供应商信息管理
- ✅ 资质文件上传
- ✅ 供应商评级系统
- ✅ 供应商状态管理（正常/黑名单）
- ✅ 供应商统计信息

### 4. 食材信息管理
- ✅ 食材基本信息管理
- ✅ 食材图片上传
- ✅ 保质期管理
- ✅ 最低库存预警设置
- ✅ 食材状态管理
- ✅ 批量导入功能

### 5. 入库记录管理
- ✅ 入库记录创建和管理
- ✅ 批次号自动生成
- ✅ 称重图片上传（最多3张）
- ✅ 采购单据上传
- ✅ 质检状态记录
- ✅ 入库审核流程
- ✅ 过期日期自动计算

### 6. 出库记录管理
- ✅ 出库记录创建和管理
- ✅ 批次选择（FIFO原则）
- ✅ 用途记录
- ✅ 用餐日期管理

### 7. 库存管理
- ✅ 实时库存查询
- ✅ 库存批次管理
- ✅ 低库存预警
- ✅ 即将过期提醒
- ✅ 库存盘点功能
- ✅ 库存调整功能

### 8. 报表统计
- ✅ 概览统计
- ✅ 入库报表（汇总、明细、按供应商、按分类）
- ✅ 出库报表（汇总、明细、使用分析）
- ✅ 库存报表（当前库存、库存价值、周转率）
- ✅ 成本分析（成本分析、趋势、对比）
- ✅ Excel和PDF导出功能

### 9. 文件管理
- ✅ 图片上传和处理
- ✅ 文档上传和下载
- ✅ 文件类型和大小验证
- ✅ 文件存储管理

### 10. 系统管理
- ✅ 系统设置
- ✅ 通知管理
- ✅ 操作日志
- ✅ 数据备份
- ✅ 用户管理

## 🔧 技术特性

### 数据验证
- ✅ 完整的请求数据验证
- ✅ 自定义验证规则
- ✅ 中文错误消息
- ✅ 业务逻辑验证

### 错误处理
- ✅ 统一的异常处理
- ✅ 友好的错误消息
- ✅ 详细的错误日志
- ✅ API标准响应格式

### 性能优化
- ✅ 数据库查询优化
- ✅ 关联关系预加载
- ✅ 分页查询
- ✅ 缓存机制

### 安全性
- ✅ SQL注入防护
- ✅ XSS攻击防护
- ✅ CSRF保护
- ✅ 文件上传安全检查

## 📡 主要API端点

### 认证相关
```
POST /api/v1/login          # 用户登录
POST /api/v1/logout         # 用户登出
GET  /api/v1/user           # 获取当前用户信息
```

### 食材管理
```
GET    /api/v1/ingredients              # 获取食材列表
POST   /api/v1/ingredients              # 创建食材
GET    /api/v1/ingredients/{id}         # 获取食材详情
PUT    /api/v1/ingredients/{id}         # 更新食材
DELETE /api/v1/ingredients/{id}         # 删除食材
POST   /api/v1/ingredients/{id}/image   # 上传食材图片
GET    /api/v1/ingredients/low-stock    # 获取低库存食材
POST   /api/v1/ingredients/import       # 批量导入食材
```

### 入库管理
```
GET  /api/v1/inbound                           # 获取入库记录列表
POST /api/v1/inbound                           # 创建入库记录
GET  /api/v1/inbound/{id}                      # 获取入库记录详情
POST /api/v1/inbound/{id}/approve              # 审核入库记录
POST /api/v1/inbound/{id}/weight-images        # 上传称重图片
POST /api/v1/inbound/{id}/invoice              # 上传采购单据
```

### 库存管理
```
GET  /api/v1/inventory                    # 获取库存列表
GET  /api/v1/inventory/{ingredient}       # 获取指定食材库存
GET  /api/v1/inventory/low-stock/list     # 获取低库存列表
GET  /api/v1/inventory/expiring/list      # 获取即将过期列表
POST /api/v1/inventory/check              # 库存盘点
POST /api/v1/inventory/adjust             # 库存调整
```

### 报表统计
```
GET  /api/v1/reports/overview             # 概览统计
GET  /api/v1/reports/inbound/summary      # 入库汇总报表
GET  /api/v1/reports/outbound/summary     # 出库汇总报表
GET  /api/v1/reports/inventory/current    # 当前库存报表
POST /api/v1/reports/export/excel         # 导出Excel报表
POST /api/v1/reports/export/pdf           # 导出PDF报表
```

## 🔐 权限控制

### 角色定义
- **管理员**: 拥有所有权限
- **审核员**: 入库审核、报表查看、操作日志
- **仓库员**: 出入库操作、库存管理、报表查看

### 权限列表
```
食材信息维护、供应商管理、分类管理
食材入库、上传称重图、上传采购单、入库审核
食材出库、批次选择
库存查看、库存盘点
报表查看、报表导出
用户管理、系统配置、数据备份、操作日志
```

## 🚀 使用步骤

1. **生成功能代码**
   ```cmd
   生成Laravel功能脚本.bat
   ```

2. **运行数据库迁移**
   ```cmd
   php artisan migrate
   ```

3. **启动开发服务器**
   ```cmd
   php artisan serve
   ```

4. **访问系统**
   - 系统首页: http://localhost:8000
   - API文档: http://localhost:8000/api/documentation

5. **使用默认账号登录**
   - 管理员: <EMAIL> / password
   - 审核员: <EMAIL> / password
   - 仓库员: <EMAIL> / password

## 💡 注意事项

1. **环境要求**: 确保PHP 7.2+、MySQL 5.6+、Composer已安装
2. **数据库配置**: 确保.env文件中的数据库连接信息正确
3. **文件权限**: 确保storage和bootstrap/cache目录可写
4. **扩展要求**: 确保已启用必要的PHP扩展

## 📞 技术支持

如果在使用过程中遇到问题，请：
1. 检查Laravel日志: `storage/logs/laravel.log`
2. 检查Web服务器错误日志
3. 确认数据库连接和权限
4. 联系技术支持

---

*本功能脚本完全基于项目开发文档生成，包含了系统的所有核心功能和业务逻辑。*
