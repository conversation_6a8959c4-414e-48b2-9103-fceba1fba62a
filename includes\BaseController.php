<?php
/**
 * 基础控制器类
 */
require_once 'Database.php';
require_once 'helpers.php';
require_once 'CSRFProtection.php';
require_once 'SessionSecurity.php';

abstract class BaseController
{
    protected $db;
    protected $config;
    protected $request;
    protected $templateData = [];

    public function __construct()
    {
        $this->db = Database::getInstance();
        $this->config = require dirname(__DIR__) . '/config/app.php';
        $this->request = $this->parseRequest();
        $this->init();
    }

    /**
     * 初始化方法，子类可重写
     */
    protected function init() {}

    /**
     * 权限校验：严格模式
     */
    protected function requirePermission($permission)
    {
        // 使用安全会话管理
        SessionSecurity::secureStart();

        $userId = SessionSecurity::getUserId();
        if (!$userId) {
            // 未登录
            if (function_exists('isAjax') && isAjax()) {
                $this->json(['success' => false, 'message' => '未登录，无法操作'], 401);
            }
            $this->redirect('../users/index.php?action=login', '请先登录', 'error');
            return;
        }
        // 严格校验：默认拒绝
        $perms = function_exists('getUserPermissions') ? getUserPermissions($userId) : [];
        if (!in_array($permission, $perms)) {
            if (function_exists('isAjax') && isAjax()) {
                $this->json(['success' => false, 'message' => '无权限执行该操作'], 403);
            }
            // 非Ajax -> 403 文本提示或跳转
            http_response_code(403);
            echo '<div style="padding:24px;font-family:sans-serif;color:#b91c1c;">'
                . '<h3>403 无权限</h3>'
                . '<p>缺少权限：<code>' . htmlspecialchars($permission) . '</code></p>'
                . '</div>';
            exit;
        }
    }

    /**
     * 解析请求数据
     */
    private function parseRequest()
    {
        return [
            'method' => $_SERVER['REQUEST_METHOD'],
            'get' => $_GET,
            'post' => $_POST,
            'files' => $_FILES,
            'uri' => $_SERVER['REQUEST_URI'],
            'action' => $_GET['action'] ?? $_POST['action'] ?? 'index',
        ];
    }

    /**
     * 设置模板数据
     */
    protected function setTemplateData($key, $value = null)
    {
        if (is_array($key)) {
            $this->templateData = array_merge($this->templateData, $key);
        } else {
            $this->templateData[$key] = $value;
        }
    }

    /**
     * 获取模板数据
     */
    protected function getTemplateData($key = null)
    {
        if ($key === null) {
            return $this->templateData;
        }
        return $this->templateData[$key] ?? null;
    }

    /**
     * 验证CSRF令牌
     */
    protected function validateCSRF()
    {
        if ($this->request['method'] === 'POST') {
            CSRFProtection::validateRequest($this->request['post']);
        }
    }

    /**
     * 获取CSRF令牌
     */
    protected function getCSRFToken()
    {
        return CSRFProtection::generateToken();
    }

    /**
     * 渲染模板
     */
    protected function render($template, $data = [])
    {
        // 合并数据
        $templateData = array_merge($this->templateData, $data);
        
        // 提取变量到当前作用域
        extract($templateData);
        
        // 包含模板文件
        $templateFile = $this->getTemplatePath($template);
        if (file_exists($templateFile)) {
            include $templateFile;
        } else {
            throw new Exception("模板文件不存在: {$templateFile}");
        }
    }

    /**
     * 获取模板文件路径
     */
    private function getTemplatePath($template)
    {
        $modulePath = dirname($_SERVER['SCRIPT_FILENAME']);
        return $modulePath . '/' . $template;
    }

    /**
     * 重定向
     */
    protected function redirect($url, $message = '', $type = 'info')
    {
        if ($message) {
            $url .= (strpos($url, '?') !== false ? '&' : '?') . 
                    'message=' . urlencode($message) . '&type=' . $type;
        }
        header("Location: {$url}");
        exit;
    }

    /**
     * 返回JSON响应
     */
    protected function json($data, $status = 200)
    {
        http_response_code($status);
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }

    /**
     * 验证必填字段
     */
    protected function validateRequired($data, $fields)
    {
        $errors = [];
        foreach ($fields as $field) {
            if (empty($data[$field])) {
                $errors[] = "字段 {$field} 为必填项";
            }
        }
        return $errors;
    }

    /**
     * 获取分页参数
     */
    protected function getPaginationParams()
    {
        $page = max(1, intval($this->request['get']['page'] ?? 1));
        $perPage = min(
            $this->config['pagination']['max_per_page'],
            max(1, intval($this->request['get']['per_page'] ?? $this->config['pagination']['per_page']))
        );
        $offset = ($page - 1) * $perPage;

        return compact('page', 'perPage', 'offset');
    }

    /**
     * 构建分页SQL
     */
    protected function buildPaginationSql($baseSql, $params = [])
    {
        $pagination = $this->getPaginationParams();
        $sql = $baseSql . " LIMIT {$pagination['offset']}, {$pagination['perPage']}";
        return [$sql, $params, $pagination];
    }

    /**
     * 处理文件上传
     */
    protected function handleFileUpload($fileKey, $allowedTypes = null)
    {
        if (!isset($_FILES[$fileKey]) || $_FILES[$fileKey]['error'] !== UPLOAD_ERR_OK) {
            return false;
        }

        $file = $_FILES[$fileKey];
        $allowedTypes = $allowedTypes ?? $this->config['upload']['allowed_types'];
        $maxSize = $this->config['upload']['max_size'];
        $uploadPath = $this->config['paths']['uploads'];

        // 验证文件大小
        if ($file['size'] > $maxSize) {
            throw new Exception('文件大小超过限制');
        }

        // 验证文件类型
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($extension, $allowedTypes)) {
            throw new Exception('不支持的文件类型');
        }

        // 生成唯一文件名
        $fileName = uniqid() . '.' . $extension;
        $filePath = $uploadPath . '/' . $fileName;

        // 确保上传目录存在
        if (!is_dir($uploadPath)) {
            mkdir($uploadPath, 0755, true);
        }

        // 移动文件
        if (move_uploaded_file($file['tmp_name'], $filePath)) {
            return [
                'original_name' => $file['name'],
                'file_name' => $fileName,
                'file_path' => $filePath,
                'file_size' => $file['size'],
                'file_type' => $file['type'],
            ];
        }

        throw new Exception('文件上传失败');
    }

    /**
     * 记录日志
     */
    protected function log($message, $level = 'info')
    {
        $logFile = $this->config['paths']['root'] . '/logs/' . date('Y-m-d') . '.log';
        $logDir = dirname($logFile);
        
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[{$timestamp}] [{$level}] {$message}" . PHP_EOL;
        
        file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
    }

    /**
     * 抽象方法：处理请求
     */
    abstract public function handleRequest();
}
?>
