<!-- 供应商趋势分析 -->

<!-- 趋势概览 -->
<div class="analytics-stats-grid">
    <div class="analytics-stat-card">
        <div class="stat-icon">
            <i class="fas fa-truck"></i>
        </div>
        <div class="stat-content">
            <div class="stat-title">活跃供应商</div>
            <div class="stat-value"><?= number_format($data['trends']['summary']['active_suppliers'] ?? 28) ?></div>
            <div class="stat-change positive">
                <i class="fas fa-arrow-up"></i>
                +2
            </div>
        </div>
    </div>

    <div class="analytics-stat-card">
        <div class="stat-icon">
            <i class="fas fa-star"></i>
        </div>
        <div class="stat-content">
            <div class="stat-title">平均评分</div>
            <div class="stat-value"><?= number_format($data['trends']['summary']['avg_rating'] ?? 4.3, 1) ?></div>
            <div class="stat-change positive">
                <i class="fas fa-arrow-up"></i>
                +0.2
            </div>
        </div>
    </div>

    <div class="analytics-stat-card">
        <div class="stat-icon">
            <i class="fas fa-clock"></i>
        </div>
        <div class="stat-content">
            <div class="stat-title">平均交期</div>
            <div class="stat-value"><?= number_format($data['trends']['summary']['avg_delivery_time'] ?? 2.8, 1) ?>天</div>
            <div class="stat-change positive">
                <i class="fas fa-arrow-down"></i>
                -0.3天
            </div>
        </div>
    </div>

    <div class="analytics-stat-card">
        <div class="stat-icon">
            <i class="fas fa-handshake"></i>
        </div>
        <div class="stat-content">
            <div class="stat-title">合作稳定性</div>
            <div class="stat-value"><?= number_format($data['trends']['summary']['cooperation_stability'] ?? 89.5, 1) ?>%</div>
            <div class="stat-change positive">
                <i class="fas fa-arrow-up"></i>
                +1.8%
            </div>
        </div>
    </div>
</div>

<!-- 主要趋势图表 -->
<div class="trend-chart-container">
    <div class="trend-chart-header">
        <div class="trend-chart-title">
            <i class="fas fa-chart-area"></i>
            供应商表现趋势分析
        </div>
        <div class="chart-controls">
            <button class="chart-toggle active" onclick="toggleSupplierChart('performance')">
                <i class="fas fa-chart-line"></i>
                表现趋势
            </button>
            <button class="chart-toggle" onclick="toggleSupplierChart('ranking')">
                <i class="fas fa-trophy"></i>
                排名对比
            </button>
        </div>
    </div>
    <div class="trend-chart-content">
        <canvas id="supplierTrendChart"></canvas>
    </div>
</div>

<!-- 图表网格 -->
<div class="chart-grid">
    <!-- 供应商分布 -->
    <div class="chart-card">
        <div class="chart-header">
            <div class="chart-title">
                <i class="fas fa-chart-pie"></i>
                供应商分布
            </div>
        </div>
        <div class="chart-container">
            <canvas id="supplierDistributionChart"></canvas>
        </div>
    </div>

    <!-- 交期表现 -->
    <div class="chart-card">
        <div class="chart-header">
            <div class="chart-title">
                <i class="fas fa-shipping-fast"></i>
                交期表现
            </div>
        </div>
        <div class="chart-container">
            <canvas id="deliveryPerformanceChart"></canvas>
        </div>
    </div>

    <!-- 质量评分 -->
    <div class="chart-card">
        <div class="chart-header">
            <div class="chart-title">
                <i class="fas fa-award"></i>
                质量评分
            </div>
        </div>
        <div class="chart-container">
            <canvas id="qualityRatingChart"></canvas>
        </div>
    </div>

    <!-- 价格竞争力 -->
    <div class="chart-card">
        <div class="chart-header">
            <div class="chart-title">
                <i class="fas fa-tags"></i>
                价格竞争力
            </div>
        </div>
        <div class="chart-container">
            <canvas id="priceCompetitivenessChart"></canvas>
        </div>
    </div>

    <!-- 合作频次 -->
    <div class="chart-card">
        <div class="chart-header">
            <div class="chart-title">
                <i class="fas fa-repeat"></i>
                合作频次
            </div>
        </div>
        <div class="chart-container">
            <canvas id="cooperationFrequencyChart"></canvas>
        </div>
    </div>

    <!-- 风险评估 -->
    <div class="chart-card">
        <div class="chart-header">
            <div class="chart-title">
                <i class="fas fa-shield-alt"></i>
                风险评估
            </div>
        </div>
        <div class="chart-container">
            <canvas id="riskAssessmentChart"></canvas>
        </div>
    </div>
</div>

<script>
// 供应商趋势数据
const supplierData = {
    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
    datasets: [{
        label: '综合评分',
        data: [4.1, 4.2, 4.0, 4.3, 4.2, 4.3],
        borderColor: '#10b981',
        backgroundColor: 'rgba(16, 185, 129, 0.1)',
        borderWidth: 3,
        fill: true,
        tension: 0.4
    }]
};

let supplierTrendChart;

// 初始化供应商趋势图表
function initSupplierTrendChart() {
    const canvas = document.getElementById('supplierTrendChart');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    
    supplierTrendChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: supplierData.labels,
            datasets: [{
                label: '综合评分',
                data: supplierData.datasets[0].data,
                borderColor: '#10b981',
                backgroundColor: 'rgba(16, 185, 129, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                yAxisID: 'y'
            }, {
                label: '平均交期 (天)',
                data: [3.2, 3.0, 3.1, 2.9, 2.8, 2.8],
                borderColor: '#3b82f6',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                borderWidth: 3,
                fill: false,
                tension: 0.4,
                yAxisID: 'y1'
            }, {
                label: '价格指数',
                data: [102, 98, 101, 97, 99, 96],
                borderColor: '#f59e0b',
                backgroundColor: 'rgba(245, 158, 11, 0.1)',
                borderWidth: 3,
                fill: false,
                tension: 0.4,
                yAxisID: 'y2'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                intersect: false,
                mode: 'index'
            },
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            if (context.datasetIndex === 0) {
                                return '综合评分: ' + context.parsed.y + ' 分';
                            } else if (context.datasetIndex === 1) {
                                return '平均交期: ' + context.parsed.y + ' 天';
                            } else {
                                return '价格指数: ' + context.parsed.y;
                            }
                        }
                    }
                }
            },
            scales: {
                x: {
                    display: true,
                    title: {
                        display: true,
                        text: '时间'
                    }
                },
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: '评分'
                    },
                    min: 0,
                    max: 5
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: '交期 (天)'
                    },
                    grid: {
                        drawOnChartArea: false,
                    }
                },
                y2: {
                    type: 'linear',
                    display: false,
                    position: 'right'
                }
            }
        }
    });
}

// 切换图表类型
function toggleSupplierChart(type) {
    // 更新按钮状态
    document.querySelectorAll('.chart-toggle').forEach(btn => btn.classList.remove('active'));
    event.target.closest('.chart-toggle').classList.add('active');
    
    if (type === 'ranking') {
        // 切换为排名对比图
        supplierTrendChart.data.labels = ['绿源农场', '新鲜配送', '优质食材', '农家直供', '品质保证'];
        supplierTrendChart.data.datasets = [{
            label: '综合评分',
            data: [4.8, 4.5, 4.3, 4.1, 3.9],
            backgroundColor: ['#10b981', '#3b82f6', '#f59e0b', '#ef4444', '#8b5cf6']
        }];
        supplierTrendChart.config.type = 'bar';
    } else {
        // 切换回趋势图
        supplierTrendChart.data.labels = supplierData.labels;
        supplierTrendChart.data.datasets = supplierData.datasets;
        supplierTrendChart.config.type = 'line';
    }
    
    supplierTrendChart.update();
}

// 初始化所有供应商图表
function initSupplierCharts() {
    initSupplierDistributionChart();
    initDeliveryPerformanceChart();
    initQualityRatingChart();
    initPriceCompetitivenessChart();
    initCooperationFrequencyChart();
    initRiskAssessmentChart();
}

// 供应商分布
function initSupplierDistributionChart() {
    const canvas = document.getElementById('supplierDistributionChart');
    if (!canvas) return;

    const ctx = canvas.getContext('2d');

    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['蔬菜供应商', '肉类供应商', '粮食供应商', '调料供应商', '综合供应商'],
            datasets: [{
                data: [12, 8, 6, 4, 8],
                backgroundColor: [
                    '#10b981',
                    '#ef4444',
                    '#f59e0b',
                    '#3b82f6',
                    '#8b5cf6'
                ],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 15,
                        usePointStyle: true
                    }
                }
            }
        }
    });
}

// 交期表现
function initDeliveryPerformanceChart() {
    const canvas = document.getElementById('deliveryPerformanceChart');
    if (!canvas) return;

    const ctx = canvas.getContext('2d');

    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['绿源农场', '新鲜配送', '优质食材', '农家直供', '品质保证'],
            datasets: [{
                label: '平均交期 (天)',
                data: [2.1, 2.5, 2.8, 3.2, 3.5],
                backgroundColor: ['#10b981', '#3b82f6', '#f59e0b', '#ef4444', '#8b5cf6'],
                borderRadius: 4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: '交期 (天)'
                    }
                }
            }
        }
    });
}

// 质量评分
function initQualityRatingChart() {
    const canvas = document.getElementById('qualityRatingChart');
    if (!canvas) return;

    const ctx = canvas.getContext('2d');

    new Chart(ctx, {
        type: 'radar',
        data: {
            labels: ['产品质量', '服务态度', '交期准时', '价格合理', '沟通配合'],
            datasets: [{
                label: '绿源农场',
                data: [95, 88, 92, 85, 90],
                borderColor: '#10b981',
                backgroundColor: 'rgba(16, 185, 129, 0.2)',
                borderWidth: 2
            }, {
                label: '新鲜配送',
                data: [88, 92, 85, 90, 87],
                borderColor: '#3b82f6',
                backgroundColor: 'rgba(59, 130, 246, 0.2)',
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top'
                }
            },
            scales: {
                r: {
                    beginAtZero: true,
                    max: 100,
                    ticks: {
                        stepSize: 20
                    }
                }
            }
        }
    });
}

// 价格竞争力
function initPriceCompetitivenessChart() {
    const canvas = document.getElementById('priceCompetitivenessChart');
    if (!canvas) return;

    const ctx = canvas.getContext('2d');

    new Chart(ctx, {
        type: 'scatter',
        data: {
            datasets: [{
                label: '供应商',
                data: [
                    {x: 95, y: 4.8, r: 15}, // 质量评分 vs 综合评分，气泡大小表示合作频次
                    {x: 88, y: 4.5, r: 12},
                    {x: 82, y: 4.3, r: 10},
                    {x: 78, y: 4.1, r: 8},
                    {x: 75, y: 3.9, r: 6}
                ],
                backgroundColor: 'rgba(59, 130, 246, 0.6)',
                borderColor: '#3b82f6'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                x: {
                    title: {
                        display: true,
                        text: '价格竞争力指数'
                    }
                },
                y: {
                    title: {
                        display: true,
                        text: '综合评分'
                    },
                    min: 0,
                    max: 5
                }
            }
        }
    });
}

// 合作频次
function initCooperationFrequencyChart() {
    const canvas = document.getElementById('cooperationFrequencyChart');
    if (!canvas) return;

    const ctx = canvas.getContext('2d');

    new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
            datasets: [{
                label: '绿源农场',
                data: [15, 18, 16, 20, 19, 22],
                borderColor: '#10b981',
                backgroundColor: 'rgba(16, 185, 129, 0.1)',
                borderWidth: 2,
                fill: false,
                tension: 0.4
            }, {
                label: '新鲜配送',
                data: [12, 14, 13, 16, 15, 18],
                borderColor: '#3b82f6',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                borderWidth: 2,
                fill: false,
                tension: 0.4
            }, {
                label: '优质食材',
                data: [8, 10, 9, 12, 11, 14],
                borderColor: '#f59e0b',
                backgroundColor: 'rgba(245, 158, 11, 0.1)',
                borderWidth: 2,
                fill: false,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: '合作次数'
                    }
                }
            }
        }
    });
}

// 风险评估
function initRiskAssessmentChart() {
    const canvas = document.getElementById('riskAssessmentChart');
    if (!canvas) return;

    const ctx = canvas.getContext('2d');

    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['低风险', '中低风险', '中风险', '中高风险', '高风险'],
            datasets: [{
                label: '供应商数量',
                data: [15, 8, 3, 1, 1],
                backgroundColor: ['#10b981', '#3b82f6', '#f59e0b', '#ef4444', '#dc2626'],
                borderRadius: 4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: '供应商数量'
                    }
                }
            }
        }
    });
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    initSupplierTrendChart();
    initSupplierCharts();
});
</script>
