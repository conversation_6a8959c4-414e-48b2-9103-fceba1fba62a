<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>留样标签打印</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
            background: #f5f5f5;
            padding: 20px;
        }

        .print-container {
            max-width: 800px;
            margin: 0 auto;
        }

        .no-print {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }

        .label-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .label-item {
            background: white;
            border: 2px solid #333;
            border-radius: 8px;
            padding: 15px;
            page-break-inside: avoid;
            break-inside: avoid;
            position: relative;
        }

        .label-header {
            text-align: center;
            border-bottom: 2px solid #333;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }

        .label-title {
            font-size: 20px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .label-subtitle {
            font-size: 12px;
            color: #666;
        }

        .label-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 15px;
        }

        .label-field {
            display: flex;
            flex-direction: column;
            gap: 3px;
        }

        .label-field.full-width {
            grid-column: 1 / -1;
        }

        .field-label {
            font-size: 11px;
            color: #666;
            font-weight: 500;
        }

        .field-value {
            font-size: 14px;
            color: #333;
            font-weight: 600;
            border-bottom: 1px solid #ddd;
            padding-bottom: 2px;
            min-height: 20px;
        }

        .qr-section {
            text-align: center;
            margin-top: 10px;
            padding-top: 10px;
            border-top: 1px dashed #ccc;
        }

        .qr-placeholder {
            width: 60px;
            height: 60px;
            border: 1px solid #ccc;
            margin: 0 auto 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            color: #999;
            background: #f9f9f9;
        }

        .expiry-warning {
            background: #fff3cd;
            color: #856404;
            padding: 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            text-align: center;
            margin-top: 10px;
        }

        .expiry-expired {
            background: #f8d7da;
            color: #721c24;
        }

        .signature-section {
            margin-top: 15px;
            padding-top: 10px;
            border-top: 1px dashed #ccc;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            font-size: 11px;
        }

        .signature-field {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .signature-line {
            flex: 1;
            height: 1px;
            background: #333;
            margin-left: 5px;
        }

        /* 打印样式 */
        @media print {
            body {
                background: white;
                padding: 0;
            }

            .no-print {
                display: none;
            }

            .print-container {
                max-width: none;
                margin: 0;
            }

            .label-grid {
                gap: 15px;
            }

            .label-item {
                box-shadow: none;
                border: 2px solid #000;
                margin-bottom: 15px;
            }

            .label-header {
                border-bottom: 2px solid #000;
            }
        }

        /* 单标签打印优化 */
        @media print and (max-width: 400px) {
            .label-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="print-container">
        <div class="no-print">
            <h2>留样标签打印预览</h2>
            <p>请检查标签信息，确认无误后点击打印</p>
            <div style="margin-top: 15px;">
                <button onclick="window.print()" class="btn btn-primary">
                    <i class="fas fa-print"></i> 打印标签
                </button>
                <button onclick="window.close()" class="btn btn-secondary" style="margin-left: 10px;">
                    关闭窗口
                </button>
            </div>
        </div>

        <?php 
            $mode = isset($print_mode) ? $print_mode : (isset($data['print_mode']) ? $data['print_mode'] : '');
            $single = isset($sample) ? $sample : (isset($data['sample']) ? $data['sample'] : null);
            $batch = isset($samples) ? $samples : (isset($data['samples']) ? $data['samples'] : []);
        ?>
        <div class="label-grid">
            <?php if ($mode === 'single' && !empty($single)): ?>
                <?php $sample = $single; ?>
                <div class="label-item">
                    <div class="label-header">
                        <div class="label-title">食品留样标签</div>
                        <div class="label-subtitle">Food Sample Label</div>
                    </div>

                    <div class="label-content">
                        <div class="label-field">
                            <div class="field-label">留样编号</div>
                            <div class="field-value"><?= htmlspecialchars($sample['sample_code']) ?></div>
                        </div>

                        <div class="label-field">
                            <div class="field-label">批次号</div>
                            <div class="field-value"><?= htmlspecialchars($sample['batch_number']) ?></div>
                        </div>

                        <div class="label-field full-width">
                            <div class="field-label">食材名称</div>
                            <div class="field-value"><?= htmlspecialchars($sample['ingredient_name'] ?? '未知食材') ?></div>
                        </div>

                        <div class="label-field">
                            <div class="field-label">留样数量</div>
                            <div class="field-value"><?= $sample['sample_quantity'] ?> <?= htmlspecialchars($sample['sample_unit']) ?></div>
                        </div>

                        <div class="label-field">
                            <div class="field-label">餐次</div>
                            <div class="field-value">
                                <?php
                                $mealTypes = [
                                    'breakfast' => '早餐',
                                    'lunch' => '午餐',
                                    'dinner' => '晚餐',
                                    'other' => '其他'
                                ];
                                echo $mealTypes[$sample['meal_type']] ?? $sample['meal_type'];
                                ?>
                            </div>
                        </div>

                        <div class="label-field">
                            <div class="field-label">用餐日期</div>
                            <div class="field-value"><?= date('Y-m-d', strtotime($sample['meal_date'])) ?></div>
                        </div>

                        <div class="label-field">
                            <div class="field-label">留样时间</div>
                            <div class="field-value"><?= date('m-d H:i', strtotime($sample['sample_time'])) ?></div>
                        </div>

                        <div class="label-field">
                            <div class="field-label">过期日期</div>
                            <div class="field-value"><?= date('Y-m-d', strtotime($sample['expiration_date'])) ?></div>
                        </div>

                        <div class="label-field">
                            <div class="field-label">存储温度</div>
                            <div class="field-value"><?= htmlspecialchars($sample['storage_temperature'] ?? '') ?></div>
                        </div>

                        <?php if (!empty($sample['storage_location'])): ?>
                        <div class="label-field full-width">
                            <div class="field-label">存储位置</div>
                            <div class="field-value"><?= htmlspecialchars($sample['storage_location']) ?></div>
                        </div>
                        <?php endif; ?>
                    </div>

                    <div class="qr-section">
                        <div class="qr-placeholder">二维码</div>
                        <div style="font-size: 10px; color: #666;">扫码查看详情</div>
                    </div>

                    <?php 
                    $isExpired = strtotime($sample['expiration_date']) < time();
                    $isExpiringSoon = strtotime($sample['expiration_date']) < time() + 48*3600;
                    ?>
                    <?php if ($isExpired): ?>
                    <div class="expiry-warning expiry-expired">
                        ⚠️ 已过期 - 请立即处理
                    </div>
                    <?php elseif ($isExpiringSoon): ?>
                    <div class="expiry-warning">
                        ⚠️ 即将过期 - 请注意检查
                    </div>
                    <?php endif; ?>

                    <div class="signature-section">
                        <div class="signature-field">
                            <span>责任人:</span>
                            <span style="font-weight: 600;"><?= htmlspecialchars($sample['responsible_person']) ?></span>
                        </div>
                        <div class="signature-field">
                            <span>检查:</span>
                            <div class="signature-line"></div>
                        </div>
                    </div>
                </div>

            <?php elseif ($mode === 'batch' && !empty($batch)): ?>
                <?php foreach ($batch as $sample): ?>
                <div class="label-item">
                    <div class="label-header">
                        <div class="label-title">食品留样标签</div>
                        <div class="label-subtitle">Food Sample Label</div>
                    </div>

                    <div class="label-content">
                        <div class="label-field">
                            <div class="field-label">留样编号</div>
                            <div class="field-value"><?= htmlspecialchars($sample['sample_code']) ?></div>
                        </div>

                        <div class="label-field">
                            <div class="field-label">批次号</div>
                            <div class="field-value"><?= htmlspecialchars($sample['batch_number']) ?></div>
                        </div>

                        <div class="label-field full-width">
                            <div class="field-label">食材名称</div>
                            <div class="field-value"><?= htmlspecialchars($sample['ingredient_name'] ?? '未知食材') ?></div>
                        </div>

                        <div class="label-field">
                            <div class="field-label">留样数量</div>
                            <div class="field-value"><?= $sample['sample_quantity'] ?> <?= htmlspecialchars($sample['sample_unit']) ?></div>
                        </div>

                        <div class="label-field">
                            <div class="field-label">餐次</div>
                            <div class="field-value">
                                <?php
                                $mealTypes = [
                                    'breakfast' => '早餐',
                                    'lunch' => '午餐',
                                    'dinner' => '晚餐',
                                    'other' => '其他'
                                ];
                                echo $mealTypes[$sample['meal_type']] ?? $sample['meal_type'];
                                ?>
                            </div>
                        </div>

                        <div class="label-field">
                            <div class="field-label">用餐日期</div>
                            <div class="field-value"><?= date('Y-m-d', strtotime($sample['meal_date'])) ?></div>
                        </div>

                        <div class="label-field">
                            <div class="field-label">留样时间</div>
                            <div class="field-value"><?= date('m-d H:i', strtotime($sample['sample_time'])) ?></div>
                        </div>

                        <div class="label-field">
                            <div class="field-label">过期日期</div>
                            <div class="field-value"><?= date('Y-m-d', strtotime($sample['expiration_date'])) ?></div>
                        </div>

                        <div class="label-field">
                            <div class="field-label">存储温度</div>
                            <div class="field-value"><?= htmlspecialchars($sample['storage_temperature'] ?? '') ?></div>
                        </div>

                        <?php if (!empty($sample['storage_location'])): ?>
                        <div class="label-field full-width">
                            <div class="field-label">存储位置</div>
                            <div class="field-value"><?= htmlspecialchars($sample['storage_location']) ?></div>
                        </div>
                        <?php endif; ?>
                    </div>

                    <div class="qr-section">
                        <div class="qr-placeholder">二维码</div>
                        <div style="font-size: 10px; color: #666;">扫码查看详情</div>
                    </div>

                    <?php 
                    $isExpired = strtotime($sample['expiration_date']) < time();
                    $isExpiringSoon = strtotime($sample['expiration_date']) < time() + 48*3600;
                    ?>
                    <?php if ($isExpired): ?>
                    <div class="expiry-warning expiry-expired">
                        ⚠️ 已过期 - 请立即处理
                    </div>
                    <?php elseif ($isExpiringSoon): ?>
                    <div class="expiry-warning">
                        ⚠️ 即将过期 - 请注意检查
                    </div>
                    <?php endif; ?>

                    <div class="signature-section">
                        <div class="signature-field">
                            <span>责任人:</span>
                            <span style="font-weight: 600;"><?= htmlspecialchars($sample['responsible_person']) ?></span>
                        </div>
                        <div class="signature-field">
                            <span>检查:</span>
                            <div class="signature-line"></div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>

            <?php else: ?>
                <div class="label-item">
                    <div style="text-align: center; padding: 40px; color: #999;">
                        <i class="fas fa-exclamation-triangle" style="font-size: 48px; margin-bottom: 15px;"></i>
                        <p>没有找到要打印的标签数据</p>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script>
        // 自动打印（可选）
        // window.onload = function() {
        //     setTimeout(function() {
        //         window.print();
        //     }, 1000);
        // };
    </script>
</body>
</html>