<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试采购单直接操作按钮</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .comparison-table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        .comparison-table th, .comparison-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        .comparison-table th { background: #f5f5f5; }
        .status-demo { padding: 10px; margin: 5px 0; border-radius: 5px; background: #f8f9fa; }
    </style>
</head>
<body>
    <h1>测试采购单直接操作按钮修改</h1>
    
    <?php
    require_once '../includes/Database.php';
    
    try {
        $db = Database::getInstance();
        echo "<p class='success'>✅ 数据库连接成功</p>";
        
        // 检查采购单数据
        echo "<div class='test-section'>";
        echo "<h2>1. 检查测试数据</h2>";
        
        $orders = $db->fetchAll("SELECT id, order_number, status FROM purchase_orders LIMIT 5");
        if (empty($orders)) {
            echo "<p class='warning'>⚠️ 没有找到采购单数据，创建不同状态的测试数据...</p>";
            
            // 创建不同状态的测试采购单
            $testOrders = [
                [
                    'order_number' => 'DIRECT_TEST_1_' . date('YmdHis'),
                    'status' => 1, // 待确认
                    'notes' => '测试待确认状态的操作按钮'
                ],
                [
                    'order_number' => 'DIRECT_TEST_2_' . date('YmdHis'),
                    'status' => 2, // 已确认
                    'notes' => '测试已确认状态的操作按钮'
                ],
                [
                    'order_number' => 'DIRECT_TEST_3_' . date('YmdHis'),
                    'status' => 3, // 已发货
                    'notes' => '测试已发货状态的操作按钮'
                ],
                [
                    'order_number' => 'DIRECT_TEST_4_' . date('YmdHis'),
                    'status' => 4, // 已完成
                    'notes' => '测试已完成状态的操作按钮'
                ]
            ];
            
            foreach ($testOrders as $testOrder) {
                $orderData = array_merge([
                    'supplier_id' => 1,
                    'order_date' => date('Y-m-d'),
                    'canteen_id' => 1,
                    'contact_person' => '测试联系人',
                    'delivery_address' => '测试地址',
                    'contact_phone' => '13800138000',
                    'order_amount' => 100.00,
                    'actual_amount' => 100.00,
                    'payment_status' => 'unpaid',
                    'created_by' => 1
                ], $testOrder);
                
                $orderId = $db->insert('purchase_orders', $orderData);
                echo "<p class='success'>✅ 创建测试采购单成功，ID: {$orderId}，状态: {$testOrder['status']}</p>";
            }
            
            $orders = $db->fetchAll("SELECT id, order_number, status FROM purchase_orders ORDER BY id DESC LIMIT 5");
        }
        
        echo "<p class='info'>找到 " . count($orders) . " 个采购单用于测试</p>";
        foreach ($orders as $order) {
            $statusText = [1 => '待确认', 2 => '已确认', 3 => '已发货', 4 => '已完成', 5 => '已取消'];
            echo "<p>- 订单 #{$order['id']}: {$order['order_number']} (状态: {$statusText[$order['status']]})</p>";
        }
        echo "</div>";
        
        // 检查模板文件修改
        echo "<div class='test-section'>";
        echo "<h2>2. 检查模板文件修改</h2>";
        
        $templateFile = '../modules/purchase/template.php';
        if (file_exists($templateFile)) {
            $content = file_get_contents($templateFile);
            
            // 检查关键修改
            $checks = [
                'dropdown' => '下拉菜单（应该被移除）',
                'btn-outline-success' => '确认按钮样式',
                'btn-outline-danger' => '取消/删除按钮样式',
                'btn-outline-warning' => '发货按钮样式',
                'updateStatus' => '状态更新函数',
                'flex-wrap: wrap' => '按钮组换行样式'
            ];
            
            echo "<table class='comparison-table'>";
            echo "<tr><th>检查项</th><th>状态</th><th>说明</th></tr>";
            
            foreach ($checks as $keyword => $description) {
                $found = strpos($content, $keyword) !== false;
                if ($keyword === 'dropdown') {
                    // 下拉菜单应该被移除
                    $status = !$found ? '✅ 已移除' : '❌ 仍存在';
                    $class = !$found ? 'success' : 'error';
                } else {
                    $status = $found ? '✅ 存在' : '❌ 未找到';
                    $class = $found ? 'success' : 'error';
                }
                echo "<tr><td>{$description}</td><td class='{$class}'>{$status}</td><td>{$keyword}</td></tr>";
            }
            echo "</table>";
            
            // 检查操作列宽度
            if (strpos($content, 'width: 200px') !== false) {
                echo "<p class='success'>✅ 操作列宽度已优化</p>";
            } else {
                echo "<p class='warning'>⚠️ 操作列宽度可能需要调整</p>";
            }
            
        } else {
            echo "<p class='error'>❌ template.php 文件不存在</p>";
        }
        echo "</div>";
        
        // 检查CSS样式修改
        echo "<div class='test-section'>";
        echo "<h2>3. 检查CSS样式修改</h2>";
        
        $cssFile = '../modules/purchase/style.css';
        if (file_exists($cssFile)) {
            $content = file_get_contents($cssFile);
            
            $cssChecks = [
                'flex-wrap: wrap' => '按钮组换行支持',
                'btn-outline-success:hover' => '确认按钮悬停效果',
                'btn-outline-warning:hover' => '发货按钮悬停效果',
                'btn-outline-danger:hover' => '删除按钮悬停效果',
                'white-space: nowrap' => '操作列不换行',
                'min-width: 32px' => '按钮最小宽度'
            ];
            
            echo "<table class='comparison-table'>";
            echo "<tr><th>CSS检查项</th><th>状态</th></tr>";
            
            foreach ($cssChecks as $keyword => $description) {
                $found = strpos($content, $keyword) !== false;
                $status = $found ? '✅ 存在' : '❌ 未找到';
                $class = $found ? 'success' : 'error';
                echo "<tr><td>{$description}</td><td class='{$class}'>{$status}</td></tr>";
            }
            echo "</table>";
            
        } else {
            echo "<p class='error'>❌ style.css 文件不存在</p>";
        }
        echo "</div>";
        
        // 显示不同状态的按钮组合
        echo "<div class='test-section'>";
        echo "<h2>4. 不同状态的按钮组合预览</h2>";
        
        $statusButtons = [
            1 => ['查看', '编辑', '确认', '取消', '删除'],
            2 => ['查看', '发货', '删除'],
            3 => ['查看', '完成', '删除'],
            4 => ['查看', '删除'],
            5 => ['查看', '删除']
        ];
        
        $statusNames = [1 => '待确认', 2 => '已确认', 3 => '已发货', 4 => '已完成', 5 => '已取消'];
        
        foreach ($statusButtons as $status => $buttons) {
            echo "<div class='status-demo'>";
            echo "<strong>{$statusNames[$status]}状态：</strong> ";
            echo implode(' | ', $buttons);
            echo "</div>";
        }
        echo "</div>";
        
        // 生成测试链接
        echo "<div class='test-section'>";
        echo "<h2>5. 测试链接</h2>";
        
        echo "<p class='info'>测试链接：</p>";
        echo "<ul>";
        echo "<li><a href='../modules/purchase/index.php' target='_blank'>采购管理首页（查看直接操作按钮）</a></li>";
        if (!empty($orders)) {
            $firstOrder = $orders[0];
            echo "<li><a href='../modules/purchase/index.php?action=view&id={$firstOrder['id']}' target='_blank'>查看采购单详情</a></li>";
        }
        echo "</ul>";
        
        echo "<p class='info'>测试步骤：</p>";
        echo "<ol>";
        echo "<li>打开采购管理首页，查看操作列</li>";
        echo "<li>确认不再有下拉菜单的三个点按钮</li>";
        echo "<li>检查不同状态订单显示的按钮是否正确</li>";
        echo "<li>测试按钮的悬停效果和点击功能</li>";
        echo "<li>在不同屏幕尺寸下测试按钮布局</li>";
        echo "</ol>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ 测试失败: " . $e->getMessage() . "</p>";
    }
    ?>
    
    <div class="test-section">
        <h2>6. 修改总结</h2>
        <p><strong>已完成的修改：</strong></p>
        <ul>
            <li>✅ 移除了下拉菜单的"更多操作"按钮</li>
            <li>✅ 将所有操作按钮直接展示在操作列中</li>
            <li>✅ 根据订单状态显示相应的操作按钮</li>
            <li>✅ 优化了按钮样式和颜色</li>
            <li>✅ 增加了操作列的宽度</li>
            <li>✅ 添加了响应式设计支持</li>
            <li>✅ 优化了移动端的按钮布局</li>
        </ul>
        
        <p><strong>按钮状态逻辑：</strong></p>
        <table class="comparison-table">
            <tr>
                <th>订单状态</th>
                <th>显示的操作按钮</th>
                <th>说明</th>
            </tr>
            <tr>
                <td>待确认</td>
                <td>查看 | 编辑 | 确认 | 取消 | 删除</td>
                <td>可以进行所有操作</td>
            </tr>
            <tr>
                <td>已确认</td>
                <td>查看 | 发货 | 删除</td>
                <td>可以标记发货</td>
            </tr>
            <tr>
                <td>已发货</td>
                <td>查看 | 完成 | 删除</td>
                <td>可以标记完成</td>
            </tr>
            <tr>
                <td>已完成</td>
                <td>查看 | 删除</td>
                <td>只能查看和删除</td>
            </tr>
            <tr>
                <td>已取消</td>
                <td>查看 | 删除</td>
                <td>只能查看和删除</td>
            </tr>
        </table>
        
        <p><strong>用户体验改进：</strong></p>
        <ul>
            <li>🎯 操作更直观，无需点击下拉菜单</li>
            <li>🎨 按钮颜色区分不同操作类型</li>
            <li>📱 移动端友好的按钮布局</li>
            <li>⚡ 减少了操作步骤，提高效率</li>
            <li>🔧 状态相关的智能按钮显示</li>
        </ul>
    </div>
    
    <p><a href="../modules/purchase/index.php">返回采购管理</a></p>
</body>
</html>
