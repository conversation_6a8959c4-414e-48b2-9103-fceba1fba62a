<?php
// 页面头部组件
// 使用方法：在页面开头 include 'includes/header.php';
// 需要设置 $page_title 变量

// 引入辅助函数
require_once dirname(__DIR__) . '/includes/helpers.php';

if (!isset($page_title)) {
    $page_title = config('name', '学校食堂管理系统');
}

// 获取基础路径
$currentScript = $_SERVER['SCRIPT_NAME'];
$scriptDir = dirname($currentScript);

// 计算相对于项目根目录的深度
$pathParts = explode('/', trim($scriptDir, '/'));
$depth = count($pathParts) - 1; // 减1因为第一个部分是项目名

// 如果在根目录，深度为0
if ($scriptDir === '/' || $scriptDir === '') {
    $depth = 0;
}

$relativePath = $depth > 0 ? str_repeat('../', $depth) : '';
?>
<!DOCTYPE html>
<html lang="<?= config('locale', 'zh-CN') ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= e($page_title) ?></title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="<?= $relativePath ?>includes/styles.css?v=<?= time() ?>" rel="stylesheet">
    <link href="<?= $relativePath ?>assets/css/common.css?v=<?= time() ?>" rel="stylesheet">
    <link href="<?= $relativePath ?>assets/css/table-enhanced.css?v=<?= time() ?>" rel="stylesheet">
    <link href="<?= $relativePath ?>assets/css/theme.css?v=<?= time() ?>" rel="stylesheet">
    <?php if (isset($module_css)): ?>
    <link href="<?= $module_css ?>?v=<?= time() ?>" rel="stylesheet">
    <?php endif; ?>
    <?php if (isset($additional_css)): ?>
    <style><?= $additional_css ?></style>
    <?php endif; ?>
</head>
<body>
