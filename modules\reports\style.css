/* 数据报表样式 */

/* 日期选择器 */
.date-range-picker {
    display: flex;
    align-items: center;
    gap: 15px;
}

.date-form {
    display: flex;
    align-items: center;
    gap: 10px;
}

.date-inputs {
    display: flex;
    align-items: center;
    gap: 8px;
}

.date-inputs input {
    width: 140px;
    font-size: 14px;
}

.date-inputs span {
    color: #6b7280;
    font-size: 14px;
}

/* 报表标签导航 */
.report-tabs {
    display: flex;
    background: white;
    border-radius: 8px;
    padding: 8px;
    margin-bottom: 25px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    gap: 4px;
}

.tab-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border-radius: 6px;
    text-decoration: none;
    color: #6b7280;
    font-weight: 500;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.tab-item:hover {
    background: #f3f4f6;
    color: #374151;
    text-decoration: none;
}

.tab-item.active {
    background: #3b82f6;
    color: white;
}

.tab-item i {
    font-size: 16px;
}

/* 报表内容区域 */
.report-content {
    min-height: 600px;
}

/* 统计卡片网格 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    border-radius: 8px;
    padding: 25px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.stat-card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
}

.stat-card-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 600;
    color: #6b7280;
}

.stat-card-value {
    font-size: 28px;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 8px;
}

.stat-card-change {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 14px;
}

.stat-card-change.positive {
    color: #10b981;
}

.stat-card-change.negative {
    color: #ef4444;
}

/* 图表容器 */
.chart-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 25px;
    margin-bottom: 30px;
}

.chart-card {
    background: white;
    border-radius: 8px;
    padding: 25px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.chart-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e5e7eb;
}

.chart-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
}

.chart-container {
    position: relative;
    height: 300px;
}

/* 数据表格 */
.data-table {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 25px;
}

.table-header {
    background: #dbeafe;
    color: #1e40af;
    padding: 20px 25px;
    border-bottom: 2px solid #bfdbfe;
}

.table-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 600;
    margin: 0;
}

.data-table table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th,
.data-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
}

.data-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #374151;
    font-size: 14px;
}

.data-table td {
    color: #6b7280;
    font-size: 14px;
}

.data-table tr:hover {
    background: #f9fafb;
}

/* 进度条 */
.progress-bar {
    width: 100%;
    height: 8px;
    background: #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
    border-radius: 4px;
    transition: width 0.3s ease;
}

/* 状态标签 */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.status-badge.success {
    background: #dcfce7;
    color: #166534;
}

.status-badge.warning {
    background: #fef3c7;
    color: #92400e;
}

.status-badge.danger {
    background: #fee2e2;
    color: #991b1b;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .report-tabs {
        flex-wrap: wrap;
        gap: 8px;
    }
    
    .tab-item {
        flex: 1;
        min-width: 120px;
        justify-content: center;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .chart-grid {
        grid-template-columns: 1fr;
    }
    
    .date-form {
        flex-direction: column;
        gap: 8px;
    }
    
    .date-inputs {
        flex-direction: column;
        gap: 8px;
    }
    
    .date-inputs input {
        width: 100%;
    }
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6b7280;
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 16px;
    color: #d1d5db;
}

.empty-state h3 {
    font-size: 18px;
    margin-bottom: 8px;
    color: #374151;
}

.empty-state p {
    font-size: 14px;
    margin-bottom: 0;
}

/* 导出按钮 */
.btn-success {
    background: #10b981;
    border-color: #10b981;
    color: white;
}

.btn-success:hover {
    background: #059669;
    border-color: #059669;
}
