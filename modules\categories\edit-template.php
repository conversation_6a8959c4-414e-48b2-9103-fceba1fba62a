<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($page_title) ?></title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../../includes/styles.css?v=<?= time() ?>" rel="stylesheet">
    <link href="../../assets/css/common.css?v=<?= time() ?>" rel="stylesheet">
    <link href="style.css?v=<?= time() ?>" rel="stylesheet">
</head>
<body>

<?php include 'sidebar.php'; ?>

<!-- 主内容区 -->
<div class="main-content">
    <!-- 顶部栏 -->
    <div class="topbar">
        <div class="topbar-left">
            <h1><i class="fas fa-edit"></i> 编辑分类</h1>
        </div>
        <div class="topbar-right">
            <a href="index.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> 返回列表
            </a>
        </div>
    </div>

    <div class="content">
        <?php if (isset($error_message)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle"></i>
            <?= htmlspecialchars($error_message) ?>
        </div>
        <?php endif; ?>

        <!-- 主要内容区域 -->
        <div class="content-layout">
            <!-- 左侧表单区域 -->
            <div class="form-main">
                <div class="form-container">
            <div class="form-header">
                <div class="form-icon">
                    <i class="fas fa-tags"></i>
                </div>
                <div class="form-title">
                    <h2>编辑分类</h2>
                    <p>修改分类的基本信息</p>
                </div>
            </div>

            <form method="POST" class="category-form">
                <div class="form-grid">
                    <!-- 基本信息 -->
                    <div class="form-section">
                        <h3><i class="fas fa-info-circle"></i> 基本信息</h3>

                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">父分类</label>
                                <select name="parent_id" class="form-control" onchange="updateCategoryLevel()">
                                    <option value="">选择父分类（留空为一级分类）</option>
                                    <?php if (!empty($parent_categories)): ?>
                                        <?php foreach ($parent_categories as $parent): ?>
                                            <option value="<?= $parent['id'] ?>" <?= ($category['parent_id'] ?? '') == $parent['id'] ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($parent['name']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </select>
                                <small class="form-text">选择父分类将设为二级分类，留空将设为一级分类</small>
                            </div>
                            <div class="form-group">
                                <label class="form-label">分类级别</label>
                                <div class="level-display">
                                    <span id="levelBadge" class="level-badge level-<?= $category['level'] ?? 1 ?>">
                                        <?= ($category['level'] ?? 1) == 1 ? '一级分类' : '二级分类' ?>
                                    </span>
                                </div>
                                <small class="form-text">根据是否选择父分类自动确定</small>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label required">分类名称</label>
                                <input type="text" name="name" class="form-control" required
                                       placeholder="请输入分类名称" value="<?= htmlspecialchars($category['name'] ?? '') ?>"
                                       oninput="checkNameAvailability(this.value, <?= $category['id'] ?? 0 ?>)">
                                <div id="nameCheckResult" class="name-check-result"></div>
                            </div>
                            <div class="form-group">
                                <label class="form-label">排序权重</label>
                                <input type="number" name="sort_order" class="form-control" min="0"
                                       placeholder="请输入排序权重" value="<?= htmlspecialchars($category['sort_order'] ?? '0') ?>">
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">分类描述</label>
                                <textarea name="description" class="form-control" rows="4"
                                          placeholder="请输入分类描述（可选）"><?= htmlspecialchars($category['description'] ?? '') ?></textarea>
                                <small class="form-text">详细描述该分类包含的食材类型</small>
                            </div>
                        </div>
                    </div>

                    <!-- 统计信息 -->
                    <?php if (isset($category['ingredient_count'])): ?>
                    <div class="form-section">
                        <h3><i class="fas fa-chart-bar"></i> 统计信息</h3>
                        
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-icon">
                                    <i class="fas fa-carrot"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-number"><?= $category['ingredient_count'] ?? 0 ?></div>
                                    <div class="stat-label">食材数量</div>
                                </div>
                            </div>
                            
                            <div class="stat-item">
                                <div class="stat-icon">
                                    <i class="fas fa-yen-sign"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-number">¥<?= number_format($category['total_value'] ?? 0, 2) ?></div>
                                    <div class="stat-label">总价值</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- 提交按钮 -->
                <div class="form-actions">
                    <button type="button" onclick="history.back()" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i> 取消
                    </button>
                    <button type="submit" class="btn btn-primary btn-submit">
                        <i class="fas fa-save"></i> 保存修改
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- 右侧边栏 -->
    <div class="form-sidebar">
        <!-- 分类信息 -->
        <div class="sidebar-card">
            <div class="sidebar-card-header">
                <h4><i class="fas fa-info-circle"></i> 分类信息</h4>
            </div>
            <div class="sidebar-card-body">
                <div class="info-item">
                    <span class="info-label">分类ID</span>
                    <span class="info-value"><?= htmlspecialchars($category['id'] ?? 'N/A') ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">分类代码</span>
                    <span class="info-value"><?= htmlspecialchars($category['code'] ?? 'N/A') ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">创建时间</span>
                    <span class="info-value"><?= isset($category['created_at']) ? date('Y-m-d H:i', strtotime($category['created_at'])) : 'N/A' ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">更新时间</span>
                    <span class="info-value"><?= isset($category['updated_at']) ? date('Y-m-d H:i', strtotime($category['updated_at'])) : 'N/A' ?></span>
                </div>
            </div>
        </div>

        <!-- 相关链接 -->
        <div class="sidebar-card">
            <div class="sidebar-card-header">
                <h4><i class="fas fa-link"></i> 相关功能</h4>
            </div>
            <div class="sidebar-card-body">
                <div class="related-links">
                    <a href="index.php" class="related-link">
                        <i class="fas fa-list"></i>
                        <span>分类列表</span>
                    </a>
                    <a href="../ingredients/index.php?category=<?= $category['id'] ?? '' ?>" class="related-link">
                        <i class="fas fa-apple-alt"></i>
                        <span>查看该分类下的食材</span>
                    </a>
                    <a href="index.php?action=create" class="related-link">
                        <i class="fas fa-plus"></i>
                        <span>添加新分类</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- 操作提示 -->
        <div class="sidebar-card">
            <div class="sidebar-card-header">
                <h4><i class="fas fa-lightbulb"></i> 操作提示</h4>
            </div>
            <div class="sidebar-card-body">
                <div class="tips-list">
                    <div class="tip-item">
                        <i class="fas fa-info-circle"></i>
                        <span>修改分类名称会影响所有使用该分类的食材</span>
                    </div>
                    <div class="tip-item">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span>删除分类前请确保没有食材使用该分类</span>
                    </div>
                    <div class="tip-item">
                        <i class="fas fa-sort"></i>
                        <span>调整排序权重可以改变分类在列表中的显示顺序</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* 内容布局 */
.content-layout {
    display: grid;
    grid-template-columns: 1fr 350px;
    gap: 30px;
    max-width: 1400px;
    margin: 0 auto;
}

.form-main {
    min-width: 0;
}

.form-sidebar {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* 边栏卡片 */
.sidebar-card {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.3s ease;
}

.sidebar-card:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.sidebar-card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px 20px;
    border-bottom: 1px solid #e2e8f0;
}

.sidebar-card-header h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.sidebar-card-body {
    padding: 20px;
}

/* 信息项 */
.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f1f5f9;
}

.info-item:last-child {
    border-bottom: none;
}

.info-label {
    font-weight: 500;
    color: #64748b;
    font-size: 14px;
}

.info-value {
    font-weight: 600;
    color: #1e293b;
    font-size: 14px;
}

/* 相关链接 */
.related-links {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.related-link {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 15px;
    background: #f7fafc;
    border-radius: 8px;
    text-decoration: none;
    color: #4a5568;
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.related-link:hover {
    background: #edf2f7;
    color: #2b6cb0;
    border-color: #4299e1;
    text-decoration: none;
}

.related-link i {
    color: #4299e1;
    width: 16px;
    text-align: center;
}

.related-link span {
    font-weight: 500;
}

/* 提示列表 */
.tips-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.tip-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 12px;
    background: #f8fafc;
    border-radius: 8px;
    border-left: 4px solid #4299e1;
}

.tip-item i {
    color: #4299e1;
    margin-top: 2px;
    flex-shrink: 0;
}

.tip-item span {
    font-size: 13px;
    color: #4a5568;
    line-height: 1.4;
}

/* 统计网格 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 15px;
}

.stat-item {
    display: flex;
    align-items: center;
    padding: 20px;
    background: #f8fafc;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
}

.stat-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #4299e1, #48bb78);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    margin-right: 15px;
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 24px;
    font-weight: 600;
    color: #2d3748;
    line-height: 1;
}

.stat-label {
    font-size: 14px;
    color: #718096;
    margin-top: 4px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .content-layout {
        grid-template-columns: 1fr 300px;
        gap: 20px;
    }
}

@media (max-width: 992px) {
    .content-layout {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .form-sidebar {
        order: -1;
    }
}

/* 名称检查结果 */
.name-check-result {
    margin-top: 8px;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.3s ease;
    min-height: 20px;
}

.name-check-result.checking {
    background: #e2e8f0;
    color: #64748b;
    border: 1px solid #cbd5e0;
}

.name-check-result.available {
    background: #dcfce7;
    color: #166534;
    border: 1px solid #bbf7d0;
}

.name-check-result.unavailable {
    background: #fef2f2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

.name-check-result.error {
    background: #fef3c7;
    color: #d97706;
    border: 1px solid #fed7aa;
}
</style>

<script src="../../assets/js/common.js"></script>
<script>
// 更新分类级别显示
function updateCategoryLevel() {
    const parentSelect = document.querySelector('select[name="parent_id"]');
    const levelBadge = document.getElementById('levelBadge');

    if (parentSelect.value) {
        levelBadge.textContent = '二级分类';
        levelBadge.className = 'level-badge level-2';
    } else {
        levelBadge.textContent = '一级分类';
        levelBadge.className = 'level-badge level-1';
    }
}

// 检查名称可用性
let nameCheckTimeout;
function checkNameAvailability(name, excludeId = 0) {
    const resultDiv = document.getElementById('nameCheckResult');

    // 清除之前的定时器
    if (nameCheckTimeout) {
        clearTimeout(nameCheckTimeout);
    }

    // 如果名称为空，清除结果
    if (!name.trim()) {
        resultDiv.textContent = '';
        resultDiv.className = 'name-check-result';
        return;
    }

    // 显示检查中状态
    resultDiv.textContent = '正在检查名称可用性...';
    resultDiv.className = 'name-check-result checking';

    // 延迟检查，避免频繁请求
    nameCheckTimeout = setTimeout(() => {
        fetch('check-category-name.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'name=' + encodeURIComponent(name.trim()) + '&exclude_id=' + excludeId
        })
        .then(response => response.json())
        .then(data => {
            if (data.available) {
                resultDiv.textContent = '✅ 分类名称可用';
                resultDiv.className = 'name-check-result available';
            } else {
                resultDiv.textContent = '❌ 分类名称已存在，请使用其他名称';
                resultDiv.className = 'name-check-result unavailable';
            }
        })
        .catch(error => {
            resultDiv.textContent = '⚠️ 检查失败，请稍后重试';
            resultDiv.className = 'name-check-result error';
        });
    }, 500);
}

// 表单验证
document.querySelector('.category-form').addEventListener('submit', function(e) {
    const name = document.querySelector('input[name="name"]').value.trim();

    if (!name) {
        e.preventDefault();
        alert('请填写所有必填字段');
        return false;
    }

    // 验证名称长度
    if (name.length > 50) {
        e.preventDefault();
        alert('分类名称不能超过50个字符');
        return false;
    }

    // 验证描述长度
    const description = document.querySelector('textarea[name="description"]').value.trim();
    if (description.length > 500) {
        e.preventDefault();
        alert('分类描述不能超过500个字符');
        return false;
    }

    // 检查名称是否可用
    const nameCheckResult = document.getElementById('nameCheckResult');
    if (nameCheckResult.classList.contains('unavailable')) {
        e.preventDefault();
        alert('分类名称已存在，请修改后再提交');
        return false;
    }
});
</script>

</body>
</html>
