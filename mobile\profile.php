<?php
/**
 * 移动端个人中心页面 - 增强版
 */
require_once '../includes/Database.php';

try {
    $db = Database::getInstance();
    
    // 获取用户活动统计
    $userStats = $db->fetchOne("
        SELECT 
            (SELECT COUNT(*) FROM inbound_records WHERE DATE(created_at) = CURDATE()) as today_activities,
            (SELECT COUNT(*) FROM inbound_records WHERE WEEK(created_at) = WEEK(CURDATE()) AND YEAR(created_at) = YEAR(CURDATE())) as week_activities,
            (SELECT COUNT(*) FROM inbound_records WHERE MONTH(created_at) = MONTH(CURDATE()) AND YEAR(created_at) = YEAR(CURDATE())) as month_activities,
            (SELECT SUM(quantity) FROM inbound_records WHERE DATE(created_at) = CURDATE()) as today_quantity,
            (SELECT COUNT(DISTINCT ingredient_id) FROM inbound_records WHERE DATE(created_at) = CURDATE()) as today_ingredients
    ") ?? [
        'today_activities' => 0,
        'week_activities' => 0,
        'month_activities' => 0,
        'today_quantity' => 0,
        'today_ingredients' => 0
    ];
    
    // 获取系统概览数据
    $systemStats = $db->fetchOne("
        SELECT 
            (SELECT COUNT(*) FROM ingredients WHERE status = 1) as total_ingredients,
            (SELECT COUNT(*) FROM suppliers WHERE status = 1) as total_suppliers,
            (SELECT COUNT(*) FROM purchase_orders WHERE status IN ('pending', 'approved')) as pending_orders,
            (SELECT COUNT(*) FROM ingredients WHERE current_stock <= min_stock AND status = 1) as low_stock_items
    ") ?? [
        'total_ingredients' => 0,
        'total_suppliers' => 0,
        'pending_orders' => 0,
        'low_stock_items' => 0
    ];
    
    // 获取最近活动记录
    $recentActivities = $db->fetchAll("
        SELECT 
            ir.created_at,
            i.name as ingredient_name,
            ir.quantity,
            ir.unit_price,
            s.name as supplier_name
        FROM inbound_records ir
        LEFT JOIN ingredients i ON ir.ingredient_id = i.id
        LEFT JOIN suppliers s ON ir.supplier_id = s.id
        WHERE ir.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        ORDER BY ir.created_at DESC
        LIMIT 10
    ");
    
    // 获取库存预警信息
    $lowStockAlerts = $db->fetchAll("
        SELECT 
            i.name,
            i.current_stock,
            i.min_stock,
            i.unit,
            c.name as category_name
        FROM ingredients i
        LEFT JOIN ingredient_categories c ON i.category_id = c.id
        WHERE i.current_stock <= i.min_stock AND i.status = 1
        ORDER BY (i.current_stock / NULLIF(i.min_stock, 0)) ASC
        LIMIT 5
    ");
    
    // 获取今日天气（模拟数据）
    $weather = [
        'temperature' => rand(15, 30),
        'condition' => ['晴天', '多云', '阴天'][rand(0, 2)],
        'humidity' => rand(40, 80)
    ];
    
} catch (Exception $e) {
    // 设置默认值
    $userStats = ['today_activities' => 0, 'week_activities' => 0, 'month_activities' => 0, 'today_quantity' => 0, 'today_ingredients' => 0];
    $systemStats = ['total_ingredients' => 0, 'total_suppliers' => 0, 'pending_orders' => 0, 'low_stock_items' => 0];
    $recentActivities = [];
    $lowStockAlerts = [];
    $weather = ['temperature' => 20, 'condition' => '晴天', 'humidity' => 60];
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>个人中心 - 移动端</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="style.css" rel="stylesheet">
    <style>
        /* 个人中心增强样式 */
        .profile-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 20px 7px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .profile-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: pulse 4s ease-in-out infinite;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1) rotate(0deg); opacity: 0.5; }
            50% { transform: scale(1.1) rotate(180deg); opacity: 0.8; }
        }
        
        .user-avatar {
            width: 27px;
            height: 27px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 5px;
            font-size: 12px;
            position: relative;
            z-index: 1;
            border: 1px solid rgba(255,255,255,0.3);
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }
        
        .user-info {
            position: relative;
            z-index: 1;
        }
        
        .user-name {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 2px;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        }

        .user-role {
            font-size: 12px;
            opacity: 0.9;
            margin-bottom: 3px;
        }
        
        .user-status {
            display: inline-block;
            padding: 2px 8px;
            background: rgba(255,255,255,0.2);
            border-radius: 8px;
            font-size: 10px;
            backdrop-filter: blur(10px);
        }
        
        .weather-info {
            position: absolute;
            top: 10px;
            right: 15px;
            background: rgba(255,255,255,0.15);
            padding: 4px 8px;
            border-radius: 8px;
            font-size: 10px;
            backdrop-filter: blur(10px);
            z-index: 2;
        }
        
        .weather-temp {
            font-weight: bold;
            margin-bottom: 2px;
        }
        
        .today-summary {
            background: white;
            margin: -15px 15px 15px;
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .summary-title {
            font-size: 16px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 16px;
        }
        
        .summary-item {
            text-align: center;
            padding: 12px;
            background: linear-gradient(135deg, #f7fafc, #edf2f7);
            border-radius: 12px;
            border: 1px solid #e2e8f0;
        }
        
        .summary-value {
            font-size: 20px;
            font-weight: bold;
            color: #4299e1;
            margin-bottom: 4px;
        }
        
        .summary-label {
            font-size: 11px;
            color: #718096;
            font-weight: 500;
        }
        
        .alerts-section {
            background: white;
            margin: 0 15px 15px;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .alerts-header {
            padding: 16px 20px 12px;
            background: linear-gradient(135deg, #fed7d7, #feb2b2);
            border-bottom: 1px solid #fc8181;
        }
        
        .alerts-title {
            font-size: 14px;
            font-weight: 600;
            color: #c53030;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .alert-item {
            padding: 12px 20px;
            border-bottom: 1px solid #f7fafc;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .alert-item:last-child {
            border-bottom: none;
        }
        
        .alert-info {
            flex: 1;
        }
        
        .alert-name {
            font-size: 14px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 2px;
        }
        
        .alert-category {
            font-size: 11px;
            color: #718096;
        }
        
        .alert-stock {
            text-align: right;
            font-size: 12px;
        }
        
        .current-stock {
            font-weight: bold;
            color: #f56565;
        }
        
        .min-stock {
            color: #718096;
        }
        
        .activity-section {
            background: white;
            margin: 0 15px 15px;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .activity-header {
            padding: 16px 20px 12px;
            background: linear-gradient(135deg, #e6fffa, #b2f5ea);
            border-bottom: 1px solid #81e6d9;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .activity-title {
            font-size: 14px;
            font-weight: 600;
            color: #234e52;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .view-all-btn {
            font-size: 12px;
            color: #319795;
            text-decoration: none;
            font-weight: 500;
        }
        
        .activity-item {
            padding: 12px 20px;
            border-bottom: 1px solid #f7fafc;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .activity-item:last-child {
            border-bottom: none;
        }
        
        .activity-icon {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            background: #4299e1;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
        }
        
        .activity-content {
            flex: 1;
        }
        
        .activity-desc {
            font-size: 13px;
            color: #2d3748;
            font-weight: 500;
            margin-bottom: 2px;
        }
        
        .activity-meta {
            font-size: 11px;
            color: #718096;
            display: flex;
            gap: 8px;
        }
        
        .activity-time {
            font-size: 11px;
            color: #a0aec0;
        }
        
        .quick-actions {
            margin: 0 15px 15px;
        }
        
        .actions-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 12px;
        }
        
        .action-btn {
            background: white;
            border: none;
            border-radius: 16px;
            padding: 16px 8px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.2s;
            text-decoration: none;
            color: inherit;
        }
        
        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.15);
            text-decoration: none;
            color: inherit;
        }
        
        .action-icon {
            width: 36px;
            height: 36px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 8px;
            font-size: 16px;
            color: white;
        }
        
        .action-icon.blue { background: #4299e1; }
        .action-icon.green { background: #48bb78; }
        .action-icon.orange { background: #ed8936; }
        .action-icon.purple { background: #9f7aea; }
        
        .action-label {
            font-size: 11px;
            color: #2d3748;
            font-weight: 500;
        }
        
        .system-info {
            background: white;
            margin: 0 15px 15px;
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .system-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
        }
        
        .system-item {
            text-align: center;
            padding: 12px;
            background: linear-gradient(135deg, #f0fff4, #c6f6d5);
            border-radius: 12px;
            border: 1px solid #9ae6b4;
        }
        
        .system-value {
            font-size: 18px;
            font-weight: bold;
            color: #22543d;
            margin-bottom: 4px;
        }
        
        .system-label {
            font-size: 11px;
            color: #2f855a;
            font-weight: 500;
        }
        
        .settings-section {
            background: white;
            margin: 0 15px 15px;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .settings-item {
            display: flex;
            align-items: center;
            padding: 16px 20px;
            color: #2d3748;
            text-decoration: none;
            border-bottom: 1px solid #f7fafc;
            transition: background 0.2s;
        }
        
        .settings-item:last-child {
            border-bottom: none;
        }
        
        .settings-item:hover {
            background: #f7fafc;
            text-decoration: none;
            color: #2d3748;
        }
        
        .settings-icon {
            width: 36px;
            height: 36px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            font-size: 16px;
            color: white;
        }
        
        .settings-content {
            flex: 1;
        }
        
        .settings-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 2px;
        }
        
        .settings-desc {
            font-size: 11px;
            color: #718096;
        }
        
        .settings-arrow {
            color: #cbd5e0;
            font-size: 12px;
        }

        /* 设置图标颜色 */
        .settings-icon.blue { background: #4299e1; }
        .settings-icon.green { background: #48bb78; }
        .settings-icon.orange { background: #ed8936; }
        .settings-icon.purple { background: #9f7aea; }
        
        .version-info {
            text-align: center;
            color: #a0aec0;
            font-size: 11px;
            margin: 20px 0;
            padding: 0 20px;
        }
        
        .empty-state {
            text-align: center;
            padding: 20px;
            color: #718096;
        }
        
        .empty-state i {
            font-size: 24px;
            margin-bottom: 8px;
            opacity: 0.5;
        }
    </style>
</head>
<body>
    <!-- 个人信息头部 -->
    <div class="profile-header">
        <div class="weather-info">
            <div class="weather-temp"><?= $weather['temperature'] ?>°C</div>
            <div><?= $weather['condition'] ?></div>
        </div>
        
        <div class="user-avatar">
            <i class="fas fa-user"></i>
        </div>
        <div class="user-info">
            <div class="user-name">管理员</div>
            <div class="user-role">系统管理员</div>
            <span class="user-status">
                <i class="fas fa-circle" style="color: #48bb78; font-size: 8px;"></i>
                在线
            </span>
        </div>
    </div>

    <!-- 主要内容 -->
    <main class="mobile-main" style="padding-top: 0;">
        <!-- 今日工作概览 -->
        <div class="today-summary">
            <div class="summary-title">
                <i class="fas fa-chart-line"></i>
                <span>今日工作概览</span>
            </div>
            <div class="summary-grid">
                <div class="summary-item">
                    <div class="summary-value"><?= number_format($userStats['today_activities']) ?></div>
                    <div class="summary-label">入库操作</div>
                </div>
                <div class="summary-item">
                    <div class="summary-value"><?= number_format($userStats['today_quantity'] ?? 0, 1) ?></div>
                    <div class="summary-label">入库数量</div>
                </div>
                <div class="summary-item">
                    <div class="summary-value"><?= number_format($userStats['today_ingredients']) ?></div>
                    <div class="summary-label">涉及品种</div>
                </div>
            </div>
        </div>

        <!-- 库存预警 -->
        <?php if (!empty($lowStockAlerts)): ?>
        <div class="alerts-section">
            <div class="alerts-header">
                <div class="alerts-title">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>库存预警 (<?= count($lowStockAlerts) ?>)</span>
                </div>
            </div>
            <?php foreach ($lowStockAlerts as $alert): ?>
            <div class="alert-item">
                <div class="alert-info">
                    <div class="alert-name"><?= htmlspecialchars($alert['name']) ?></div>
                    <div class="alert-category"><?= htmlspecialchars($alert['category_name'] ?? '未分类') ?></div>
                </div>
                <div class="alert-stock">
                    <div class="current-stock"><?= number_format($alert['current_stock'], 1) ?> <?= htmlspecialchars($alert['unit']) ?></div>
                    <div class="min-stock">最低: <?= number_format($alert['min_stock'], 1) ?></div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>

        <!-- 快捷操作 -->
        <div class="quick-actions">
            <div class="actions-grid">
                <a href="inbound.php" class="action-btn">
                    <div class="action-icon blue">
                        <i class="fas fa-box"></i>
                    </div>
                    <div class="action-label">快速入库</div>
                </a>
                <a href="inventory.php" class="action-btn">
                    <div class="action-icon green">
                        <i class="fas fa-search"></i>
                    </div>
                    <div class="action-label">库存查询</div>
                </a>
                <a href="purchase.php" class="action-btn">
                    <div class="action-icon orange">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div class="action-label">采购管理</div>
                </a>
                <a href="reports.php" class="action-btn">
                    <div class="action-icon purple">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <div class="action-label">数据报表</div>
                </a>
            </div>
        </div>

        <!-- 系统概览 -->
        <div class="system-info">
            <div class="summary-title">
                <i class="fas fa-server"></i>
                <span>系统概览</span>
            </div>
            <div class="system-grid">
                <div class="system-item">
                    <div class="system-value"><?= number_format($systemStats['total_ingredients']) ?></div>
                    <div class="system-label">食材品种</div>
                </div>
                <div class="system-item">
                    <div class="system-value"><?= number_format($systemStats['total_suppliers']) ?></div>
                    <div class="system-label">活跃供应商</div>
                </div>
                <div class="system-item">
                    <div class="system-value"><?= number_format($systemStats['pending_orders']) ?></div>
                    <div class="system-label">待处理订单</div>
                </div>
                <div class="system-item">
                    <div class="system-value"><?= number_format($systemStats['low_stock_items']) ?></div>
                    <div class="system-label">库存预警</div>
                </div>
            </div>
        </div>

        <!-- 最近活动 -->
        <?php if (!empty($recentActivities)): ?>
        <div class="activity-section">
            <div class="activity-header">
                <div class="activity-title">
                    <i class="fas fa-history"></i>
                    <span>最近活动</span>
                </div>
                <a href="#" class="view-all-btn" onclick="showAllActivities()">查看全部</a>
            </div>
            <?php foreach (array_slice($recentActivities, 0, 5) as $activity): ?>
            <div class="activity-item">
                <div class="activity-icon">
                    <i class="fas fa-arrow-down"></i>
                </div>
                <div class="activity-content">
                    <div class="activity-desc">入库 <?= htmlspecialchars($activity['ingredient_name']) ?></div>
                    <div class="activity-meta">
                        <span><?= number_format($activity['quantity'], 1) ?> 单位</span>
                        <span>¥<?= number_format($activity['unit_price'], 2) ?></span>
                        <span><?= htmlspecialchars($activity['supplier_name'] ?? '未知供应商') ?></span>
                    </div>
                </div>
                <div class="activity-time"><?= date('m-d H:i', strtotime($activity['created_at'])) ?></div>
            </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>

        <!-- 设置菜单 -->
        <div class="settings-section">
            <a href="../modules/users/index.php" class="settings-item">
                <div class="settings-icon blue">
                    <i class="fas fa-users"></i>
                </div>
                <div class="settings-content">
                    <div class="settings-title">用户管理</div>
                    <div class="settings-desc">管理系统用户和权限</div>
                </div>
                <div class="settings-arrow">
                    <i class="fas fa-chevron-right"></i>
                </div>
            </a>
            
            <a href="../modules/categories/index.php" class="settings-item">
                <div class="settings-icon green">
                    <i class="fas fa-tags"></i>
                </div>
                <div class="settings-content">
                    <div class="settings-title">分类管理</div>
                    <div class="settings-desc">食材分类设置</div>
                </div>
                <div class="settings-arrow">
                    <i class="fas fa-chevron-right"></i>
                </div>
            </a>
            
            <a href="../modules/suppliers/index.php" class="settings-item">
                <div class="settings-icon orange">
                    <i class="fas fa-truck"></i>
                </div>
                <div class="settings-content">
                    <div class="settings-title">供应商管理</div>
                    <div class="settings-desc">供应商信息维护</div>
                </div>
                <div class="settings-arrow">
                    <i class="fas fa-chevron-right"></i>
                </div>
            </a>
            
            <a href="#" class="settings-item" onclick="showSettings()">
                <div class="settings-icon purple">
                    <i class="fas fa-cog"></i>
                </div>
                <div class="settings-content">
                    <div class="settings-title">系统设置</div>
                    <div class="settings-desc">系统参数配置</div>
                </div>
                <div class="settings-arrow">
                    <i class="fas fa-chevron-right"></i>
                </div>
            </a>
            
            <a href="#" class="settings-item" onclick="confirmLogout()">
                <div class="settings-icon" style="background: #f56565;">
                    <i class="fas fa-sign-out-alt"></i>
                </div>
                <div class="settings-content">
                    <div class="settings-title">退出登录</div>
                    <div class="settings-desc">安全退出系统</div>
                </div>
                <div class="settings-arrow">
                    <i class="fas fa-chevron-right"></i>
                </div>
            </a>
        </div>

        <!-- 版本信息 -->
        <div class="version-info">
            学校食材管理系统 v1.0<br>
            Copyright © 2024 | 移动端优化版<br>
            最后更新: <?= date('Y-m-d') ?>
        </div>

        <!-- 底部间距 -->
        <div class="bottom-spacer"></div>
    </main>

    <!-- 底部导航 -->
    <nav class="bottom-nav">
        <a href="index.php" class="nav-item">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </a>
        <a href="inbound.php" class="nav-item">
            <i class="fas fa-box"></i>
            <span>入库</span>
        </a>
        <a href="inventory.php" class="nav-item">
            <i class="fas fa-warehouse"></i>
            <span>库存</span>
        </a>
        <a href="purchase.php" class="nav-item">
            <i class="fas fa-shopping-cart"></i>
            <span>采购</span>
        </a>
        <a href="reports.php" class="nav-item">
            <i class="fas fa-chart-bar"></i>
            <span>报表</span>
        </a>
        <a href="profile.php" class="nav-item active">
            <i class="fas fa-user"></i>
            <span>我的</span>
        </a>
    </nav>

    <script src="main.js"></script>
    <script>
        // 显示所有活动
        function showAllActivities() {
            alert('查看全部活动功能开发中...\n\n将显示完整的操作历史记录');
        }
        
        // 显示系统设置
        function showSettings() {
            const settings = [
                '• 系统参数配置',
                '• 数据备份与恢复', 
                '• 消息通知设置',
                '• 界面主题设置',
                '• 安全设置'
            ];
            alert('系统设置功能:\n\n' + settings.join('\n') + '\n\n功能开发中...');
        }
        
        // 确认退出登录
        function confirmLogout() {
            if (confirm('确定要退出登录吗？\n\n退出后需要重新登录才能使用系统')) {
                alert('退出登录功能开发中...\n\n将跳转到登录页面');
            }
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 添加一些交互效果
            const summaryItems = document.querySelectorAll('.summary-item');
            summaryItems.forEach(item => {
                item.addEventListener('click', function() {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);
                });
            });
            
            // 模拟实时数据更新
            setInterval(function() {
                const weatherTemp = document.querySelector('.weather-temp');
                if (weatherTemp) {
                    const currentTemp = parseInt(weatherTemp.textContent);
                    const newTemp = currentTemp + (Math.random() > 0.5 ? 1 : -1) * Math.random();
                    weatherTemp.textContent = Math.round(newTemp) + '°C';
                }
            }, 30000); // 30秒更新一次
        });
    </script>
</body>
</html>