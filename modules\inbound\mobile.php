<?php
require_once '../../includes/Database.php';
require_once 'InboundController.php';

$controller = new InboundController();
$controller->handleRequest();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>食材入库 - 移动端</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        /* 移动端专用样式 */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            padding: 0;
            margin: 0;
            font-size: 14px;
        }

        /* 顶部导航栏 */
        .mobile-header {
            background: linear-gradient(135deg, #007cba 0%, #0056b3 100%);
            color: white;
            padding: 15px 20px;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .mobile-header h1 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .back-btn {
            color: white;
            text-decoration: none;
            font-size: 20px;
            margin-right: 15px;
        }

        /* 主要内容区域 */
        .mobile-container {
            padding: 20px 15px;
            max-width: 100%;
            margin: 0 auto;
        }

        /* 卡片样式 */
        .mobile-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border: none;
        }

        .mobile-card h3 {
            color: #333;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        /* 表单样式 */
        .mobile-form-group {
            margin-bottom: 20px;
        }

        .mobile-form-label {
            display: block;
            font-weight: 500;
            color: #333;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .mobile-form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
            background: white;
        }

        .mobile-form-control:focus {
            outline: none;
            border-color: #007cba;
            box-shadow: 0 0 0 3px rgba(0, 124, 186, 0.1);
        }

        /* 选择器样式 */
        .mobile-select {
            appearance: none;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 12px center;
            background-repeat: no-repeat;
            background-size: 16px;
            padding-right: 40px;
        }

        /* 拍照区域 */
        .mobile-photo-area {
            border: 2px dashed #007cba;
            border-radius: 12px;
            padding: 30px 20px;
            text-align: center;
            background: #f8f9ff;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 15px 0;
        }

        .mobile-photo-area:hover {
            background: #e6f3ff;
            border-color: #0056b3;
        }

        .mobile-photo-area.has-photo {
            border-color: #28a745;
            background: #f8fff9;
        }

        .mobile-photo-placeholder {
            color: #007cba;
            font-size: 16px;
        }

        .mobile-photo-placeholder i {
            font-size: 32px;
            margin-bottom: 10px;
            display: block;
        }

        .mobile-photo-preview {
            max-width: 100%;
            max-height: 200px;
            border-radius: 8px;
            margin-top: 10px;
        }

        /* 商品列表 */
        .mobile-item-list {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .mobile-item {
            padding: 15px 20px;
            border-bottom: 1px solid #f0f0f0;
            position: relative;
        }

        .mobile-item:last-child {
            border-bottom: none;
        }

        .mobile-item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .mobile-item-name {
            font-weight: 600;
            color: #333;
            font-size: 16px;
        }

        .mobile-item-price {
            color: #007cba;
            font-weight: 600;
            font-size: 14px;
        }

        .mobile-item-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 15px;
        }

        .mobile-item-detail {
            font-size: 13px;
            color: #666;
        }

        .mobile-item-detail strong {
            color: #333;
        }

        /* 数量输入 */
        .mobile-quantity-input {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }

        .mobile-quantity-input label {
            font-weight: 500;
            color: #333;
            min-width: 80px;
        }

        .mobile-quantity-input input {
            flex: 1;
            padding: 10px 12px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            font-size: 16px;
            text-align: center;
        }

        /* 拍照按钮 */
        .mobile-photo-btn {
            width: 100%;
            padding: 12px;
            background: #007cba;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: background 0.3s ease;
        }

        .mobile-photo-btn:hover {
            background: #0056b3;
        }

        .mobile-photo-btn.has-photo {
            background: #28a745;
        }

        /* 底部按钮 */
        .mobile-bottom-actions {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            padding: 15px 20px;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            z-index: 1000;
        }

        .mobile-submit-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            transition: transform 0.2s ease;
        }

        .mobile-submit-btn:hover {
            transform: translateY(-2px);
        }

        .mobile-submit-btn:disabled {
            background: #6c757d;
            transform: none;
            cursor: not-allowed;
        }

        /* 加载状态 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            display: none;
        }

        .loading-spinner {
            background: white;
            padding: 30px;
            border-radius: 12px;
            text-align: center;
        }

        /* 通知样式 */
        .mobile-notification {
            position: fixed;
            top: 80px;
            left: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            z-index: 9998;
            transform: translateY(-100px);
            transition: transform 0.3s ease;
        }

        .mobile-notification.show {
            transform: translateY(0);
        }

        .mobile-notification.success {
            background: #28a745;
        }

        .mobile-notification.error {
            background: #dc3545;
        }

        .mobile-notification.info {
            background: #17a2b8;
        }

        /* 底部间距 */
        .mobile-bottom-spacer {
            height: 100px;
        }

        /* 隐藏桌面端元素 */
        .desktop-only {
            display: none !important;
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    <div class="mobile-header">
        <h1>
            <a href="../dashboard/index.php" class="back-btn">
                <i class="fas fa-arrow-left"></i>
            </a>
            <i class="fas fa-box"></i>
            食材入库
        </h1>
    </div>

    <!-- 主要内容 -->
    <div class="mobile-container">
        <form id="mobileInboundForm" method="POST" enctype="multipart/form-data">
            <!-- 基本信息 -->
            <div class="mobile-card">
                <h3><i class="fas fa-info-circle"></i> 基本信息</h3>
                
                <div class="mobile-form-group">
                    <label class="mobile-form-label">批次号</label>
                    <input type="text" name="batch_number" class="mobile-form-control" 
                           value="<?= 'BATCH' . date('YmdHis') ?>" required>
                </div>

                <div class="mobile-form-group">
                    <label class="mobile-form-label">入库日期</label>
                    <input type="date" name="inbound_date" class="mobile-form-control" 
                           value="<?= date('Y-m-d') ?>" required>
                </div>

                <div class="mobile-form-group">
                    <label class="mobile-form-label">操作员</label>
                    <input type="text" name="operator_name" class="mobile-form-control" 
                           value="管理员" required>
                </div>
            </div>

            <!-- 供应商选择 -->
            <div class="mobile-card">
                <h3><i class="fas fa-truck"></i> 供应商信息</h3>
                
                <div class="mobile-form-group">
                    <label class="mobile-form-label">选择供应商</label>
                    <select name="supplier_id" class="mobile-form-control mobile-select" required>
                        <option value="">请选择供应商</option>
                        <?php
                        try {
                            $db = Database::getInstance();
                            $suppliers = $db->fetchAll("SELECT id, name FROM suppliers WHERE status = 1 ORDER BY name");
                            foreach ($suppliers as $supplier) {
                                echo "<option value='{$supplier['id']}'>{$supplier['name']}</option>";
                            }
                        } catch (Exception $e) {
                            echo "<option value=''>加载失败</option>";
                        }
                        ?>
                    </select>
                </div>
            </div>

            <!-- 食材选择 -->
            <div class="mobile-card">
                <h3><i class="fas fa-apple-alt"></i> 选择食材</h3>
                
                <div class="mobile-form-group">
                    <label class="mobile-form-label">食材</label>
                    <select id="ingredientSelect" class="mobile-form-control mobile-select">
                        <option value="">请选择食材</option>
                        <?php
                        try {
                            $ingredients = $db->fetchAll("SELECT id, name, unit FROM ingredients WHERE status = 1 ORDER BY name");
                            foreach ($ingredients as $ingredient) {
                                echo "<option value='{$ingredient['id']}' data-unit='{$ingredient['unit']}'>{$ingredient['name']} ({$ingredient['unit']})</option>";
                            }
                        } catch (Exception $e) {
                            echo "<option value=''>加载失败</option>";
                        }
                        ?>
                    </select>
                </div>

                <div class="mobile-form-group">
                    <label class="mobile-form-label">数量</label>
                    <input type="number" id="quantityInput" class="mobile-form-control" 
                           step="0.01" min="0" placeholder="请输入数量">
                </div>

                <div class="mobile-form-group">
                    <label class="mobile-form-label">单价</label>
                    <input type="number" id="priceInput" class="mobile-form-control" 
                           step="0.01" min="0" placeholder="请输入单价">
                </div>

                <button type="button" id="addItemBtn" class="mobile-submit-btn" style="position: relative; margin-bottom: 0;">
                    <i class="fas fa-plus"></i> 添加食材
                </button>
            </div>

            <!-- 已添加食材列表 -->
            <div id="itemsList" class="mobile-item-list" style="display: none;">
                <div style="padding: 15px 20px; background: #f8f9fa; font-weight: 600; color: #333;">
                    <i class="fas fa-list"></i> 已添加食材
                </div>
                <div id="itemsContainer">
                    <!-- 动态添加的食材项目 -->
                </div>
            </div>

            <!-- 送货单照片 -->
            <div class="mobile-card">
                <h3><i class="fas fa-camera"></i> 送货单照片</h3>
                
                <input type="file" id="deliveryPhoto" name="delivery_photo" 
                       accept="image/*" capture="environment" style="display: none;">
                
                <div class="mobile-photo-area" id="deliveryPhotoArea" onclick="document.getElementById('deliveryPhoto').click()">
                    <div class="mobile-photo-placeholder">
                        <i class="fas fa-camera"></i>
                        点击拍摄送货单照片
                    </div>
                </div>
            </div>

            <!-- 备注 -->
            <div class="mobile-card">
                <h3><i class="fas fa-sticky-note"></i> 备注信息</h3>
                
                <div class="mobile-form-group">
                    <textarea name="notes" class="mobile-form-control" rows="3" 
                              placeholder="请输入备注信息（可选）"></textarea>
                </div>
            </div>

            <!-- 底部间距 -->
            <div class="mobile-bottom-spacer"></div>
        </form>
    </div>

    <!-- 底部操作按钮 -->
    <div class="mobile-bottom-actions">
        <button type="submit" form="mobileInboundForm" class="mobile-submit-btn" id="submitBtn" disabled>
            <i class="fas fa-check"></i> 完成入库
        </button>
    </div>

    <!-- 加载遮罩 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin fa-2x" style="color: #007cba;"></i>
            <div style="margin-top: 15px; color: #333;">正在处理...</div>
        </div>
    </div>

    <!-- 通知容器 -->
    <div id="notificationContainer"></div>

    <script>
        // 移动端入库功能
        let addedItems = [];
        let itemIndex = 0;

        // 添加食材
        document.getElementById('addItemBtn').addEventListener('click', function() {
            const ingredientSelect = document.getElementById('ingredientSelect');
            const quantityInput = document.getElementById('quantityInput');
            const priceInput = document.getElementById('priceInput');

            const ingredientId = ingredientSelect.value;
            const ingredientName = ingredientSelect.options[ingredientSelect.selectedIndex].text;
            const unit = ingredientSelect.options[ingredientSelect.selectedIndex].dataset.unit;
            const quantity = parseFloat(quantityInput.value) || 0;
            const price = parseFloat(priceInput.value) || 0;

            if (!ingredientId) {
                showNotification('请选择食材', 'error');
                return;
            }

            if (quantity <= 0) {
                showNotification('请输入有效数量', 'error');
                return;
            }

            if (price <= 0) {
                showNotification('请输入有效单价', 'error');
                return;
            }

            // 检查是否已添加
            if (addedItems.find(item => item.ingredient_id === ingredientId)) {
                showNotification('该食材已添加', 'error');
                return;
            }

            const item = {
                index: itemIndex++,
                ingredient_id: ingredientId,
                ingredient_name: ingredientName.split('(')[0].trim(),
                unit: unit,
                quantity: quantity,
                price: price,
                total: quantity * price
            };

            addedItems.push(item);
            renderItems();
            updateSubmitButton();

            // 清空输入
            ingredientSelect.value = '';
            quantityInput.value = '';
            priceInput.value = '';

            showNotification('食材添加成功', 'success');
        });

        // 渲染食材列表
        function renderItems() {
            const container = document.getElementById('itemsContainer');
            const itemsList = document.getElementById('itemsList');

            if (addedItems.length === 0) {
                itemsList.style.display = 'none';
                return;
            }

            itemsList.style.display = 'block';

            container.innerHTML = addedItems.map(item => `
                <div class="mobile-item">
                    <div class="mobile-item-header">
                        <div class="mobile-item-name">${item.ingredient_name}</div>
                        <div class="mobile-item-price">¥${item.total.toFixed(2)}</div>
                    </div>
                    <div class="mobile-item-details">
                        <div class="mobile-item-detail">
                            <strong>数量:</strong> ${item.quantity}${item.unit}
                        </div>
                        <div class="mobile-item-detail">
                            <strong>单价:</strong> ¥${item.price.toFixed(2)}
                        </div>
                    </div>
                    <div class="mobile-quantity-input">
                        <label>实际数量:</label>
                        <input type="number" name="items[${item.index}][actual_quantity]" 
                               value="${item.quantity}" step="0.01" min="0" required>
                        <input type="hidden" name="items[${item.index}][ingredient_id]" value="${item.ingredient_id}">
                        <input type="hidden" name="items[${item.index}][unit_price]" value="${item.price}">
                    </div>
                    <button type="button" class="mobile-photo-btn" onclick="takeWeightPhoto(${item.index})">
                        <i class="fas fa-camera"></i> 拍摄称重照片
                    </button>
                    <input type="file" id="weightPhoto_${item.index}" name="items[${item.index}][weight_photo]" 
                           accept="image/*" capture="environment" style="display: none;" 
                           onchange="handleWeightPhoto(${item.index})">
                    <button type="button" onclick="removeItem(${item.index})" 
                            style="position: absolute; top: 15px; right: 15px; background: #dc3545; color: white; border: none; border-radius: 50%; width: 30px; height: 30px; font-size: 12px;">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `).join('');
        }

        // 拍摄称重照片
        function takeWeightPhoto(index) {
            document.getElementById(`weightPhoto_${index}`).click();
        }

        // 处理称重照片
        function handleWeightPhoto(index) {
            const input = document.getElementById(`weightPhoto_${index}`);
            const btn = input.parentNode.querySelector('.mobile-photo-btn');
            
            if (input.files.length > 0) {
                btn.innerHTML = '<i class="fas fa-check"></i> 已拍摄称重照片';
                btn.classList.add('has-photo');
                showNotification('称重照片上传成功', 'success');
            }
        }

        // 删除食材
        function removeItem(index) {
            addedItems = addedItems.filter(item => item.index !== index);
            renderItems();
            updateSubmitButton();
            showNotification('食材已删除', 'info');
        }

        // 处理送货单照片
        document.getElementById('deliveryPhoto').addEventListener('change', function(e) {
            const file = e.target.files[0];
            const area = document.getElementById('deliveryPhotoArea');
            
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    area.innerHTML = `
                        <img src="${e.target.result}" class="mobile-photo-preview" alt="送货单照片">
                        <div style="margin-top: 10px; color: #28a745; font-size: 14px;">
                            <i class="fas fa-check"></i> 照片已选择，点击可重新拍摄
                        </div>
                    `;
                    area.classList.add('has-photo');
                };
                reader.readAsDataURL(file);
                showNotification('送货单照片上传成功', 'success');
            }
        });

        // 更新提交按钮状态
        function updateSubmitButton() {
            const submitBtn = document.getElementById('submitBtn');
            const supplierSelect = document.querySelector('select[name="supplier_id"]');
            
            const hasSupplier = supplierSelect.value !== '';
            const hasItems = addedItems.length > 0;
            
            submitBtn.disabled = !(hasSupplier && hasItems);
            
            if (!hasSupplier) {
                submitBtn.innerHTML = '<i class="fas fa-exclamation-triangle"></i> 请选择供应商';
            } else if (!hasItems) {
                submitBtn.innerHTML = '<i class="fas fa-exclamation-triangle"></i> 请添加食材';
            } else {
                submitBtn.innerHTML = '<i class="fas fa-check"></i> 完成入库';
            }
        }

        // 监听供应商选择
        document.querySelector('select[name="supplier_id"]').addEventListener('change', updateSubmitButton);

        // 表单提交
        document.getElementById('mobileInboundForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            if (addedItems.length === 0) {
                showNotification('请至少添加一个食材', 'error');
                return;
            }
            
            // 显示加载状态
            document.getElementById('loadingOverlay').style.display = 'flex';
            
            // 提交表单
            const formData = new FormData(this);
            formData.append('batch_type', 'batch');
            
            fetch('index.php?action=create', {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(data => {
                document.getElementById('loadingOverlay').style.display = 'none';
                
                if (data.includes('成功') || data.includes('success')) {
                    showNotification('入库成功！', 'success');
                    setTimeout(() => {
                        window.location.href = '../dashboard/index.php';
                    }, 2000);
                } else {
                    showNotification('入库失败，请重试', 'error');
                }
            })
            .catch(error => {
                document.getElementById('loadingOverlay').style.display = 'none';
                showNotification('网络错误，请重试', 'error');
            });
        });

        // 显示通知
        function showNotification(message, type = 'info') {
            const container = document.getElementById('notificationContainer');
            const notification = document.createElement('div');
            notification.className = `mobile-notification ${type}`;
            notification.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info-circle'}"></i>
                ${message}
            `;
            
            container.appendChild(notification);
            
            setTimeout(() => {
                notification.classList.add('show');
            }, 100);
            
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    container.removeChild(notification);
                }, 300);
            }, 3000);
        }

        // 防止页面缩放
        document.addEventListener('touchstart', function(event) {
            if (event.touches.length > 1) {
                event.preventDefault();
            }
        });

        // 防止双击缩放
        let lastTouchEnd = 0;
        document.addEventListener('touchend', function(event) {
            const now = (new Date()).getTime();
            if (now - lastTouchEnd <= 300) {
                event.preventDefault();
            }
            lastTouchEnd = now;
        }, false);

        // 初始化
        updateSubmitButton();
    </script>
</body>
</html>
