<?php
/**
 * 下载采购单导入模板
 */

// 设置 CSV 文件头
header('Content-Type: text/csv; charset=UTF-8');
header('Content-Disposition: attachment; filename="采购单导入模板.csv"');
header('Cache-Control: no-cache, must-revalidate');

// 输出 BOM 以支持中文
echo "\xEF\xBB\xBF";

// 创建文件句柄
$output = fopen('php://output', 'w');

// 写入标题行
$headers = [
    '供应商名称',
    '订货日期',
    '交货日期',
    '备注',
    '食材名称',
    '数量',
    '单价'
];
fputcsv($output, $headers);

// 写入示例数据
$sampleData = [
    ['绿色蔬菜供应商', '2024-12-01', '2024-12-02', '紧急采购', '白菜', '50', '2.50'],
    ['绿色蔬菜供应商', '2024-12-01', '2024-12-02', '紧急采购', '土豆', '30', '3.00'],
    ['优质肉类供应商', '2024-12-01', '', '', '猪肉', '20', '28.00'],
    ['粮油批发商', '2024-12-01', '2024-12-03', '月度采购', '大米', '100', '4.20'],
    ['粮油批发商', '2024-12-01', '2024-12-03', '月度采购', '食用油', '10', '45.00']
];

foreach ($sampleData as $row) {
    fputcsv($output, $row);
}

fclose($output);
exit;
?>
