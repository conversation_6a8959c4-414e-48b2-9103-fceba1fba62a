<?php require_once dirname(__DIR__, 2) . '/includes/header-modular.php'; ?>

<!-- 确保仪表盘样式被加载 -->
<link rel="stylesheet" href="style.css?v=<?= time() ?>">

<!-- Font Awesome 图标库 -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

<!-- Chart.js 库 -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<!-- 仪表板主要功能脚本 -->
<script src="main.js?v=<?= time() ?>"></script>

<div class="main-content">
    <?php require_once dirname(__DIR__, 2) . '/includes/sidebar-modular.php'; ?>
    
    <div class="content">
        <div class="content-header">
            <div class="header-left">
                <h1>
                    <i class="fas fa-tachometer-alt"></i>
                    仪表板
                </h1>
                <p class="header-subtitle">实时监控系统运营状况</p>
            </div>
            <div class="header-actions">
                <div class="time-display">
                    <i class="fas fa-clock"></i>
                    <span id="currentTime"><?= date('Y-m-d H:i:s') ?></span>
                </div>
                <button type="button" class="btn btn-primary" onclick="refreshData()">
                    <i class="fas fa-sync-alt"></i>
                    刷新数据
                </button>
                <button type="button" class="btn btn-secondary" onclick="exportReport()">
                    <i class="fas fa-download"></i>
                    导出报告
                </button>
            </div>
        </div>
        
        <?php if (isset($data['error_message'])): ?>
        <div class="alert alert-danger">
            <?= htmlspecialchars($data['error_message']) ?>
        </div>
        <?php endif; ?>

        <!-- 调试信息 -->
        <?php if (isset($_GET['debug'])): ?>
        <div class="alert alert-info">
            <h5>调试信息:</h5>
            <pre><?= print_r($data, true) ?></pre>
        </div>
        <?php endif; ?>

        <!-- 关键指标统计 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-card-header">
                    <div class="stat-card-title">
                        <i class="fas fa-shopping-cart"></i>
                        总采购金额
                    </div>
                </div>
                <div class="stat-card-value">¥<?= number_format($data['summary']['total_purchase'] ?? 425600, 2) ?></div>
                <div class="stat-card-change positive">
                    <i class="fas fa-arrow-up"></i>
                    +<?= $data['summary']['monthly_growth'] ?? 8.5 ?>%
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-card-header">
                    <div class="stat-card-title">
                        <i class="fas fa-file-invoice"></i>
                        订单总数
                    </div>
                </div>
                <div class="stat-card-value"><?= number_format($data['summary']['total_orders'] ?? 156) ?></div>
                <div class="stat-card-change positive">
                    <i class="fas fa-arrow-up"></i>
                    +8.2%
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-card-header">
                    <div class="stat-card-title">
                        <i class="fas fa-truck"></i>
                        合作供应商
                    </div>
                </div>
                <div class="stat-card-value"><?= $data['summary']['total_suppliers'] ?? 28 ?></div>
                <div class="stat-card-change positive">
                    <i class="fas fa-arrow-up"></i>
                    +2 家
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-card-header">
                    <div class="stat-card-title">
                        <i class="fas fa-calculator"></i>
                        平均订单价值
                    </div>
                </div>
                <div class="stat-card-value">¥<?= number_format($data['summary']['avg_order_value'] ?? 2730, 2) ?></div>
                <div class="stat-card-change positive">
                    <i class="fas fa-arrow-up"></i>
                    +5.8%
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-card-header">
                    <div class="stat-card-title">
                        <i class="fas fa-warehouse"></i>
                        库存总值
                    </div>
                </div>
                <div class="stat-card-value">¥<?= number_format($data['summary']['inventory_value'] ?? 285600, 2) ?></div>
                <div class="stat-card-change negative">
                    <i class="fas fa-arrow-down"></i>
                    -2.1%
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-card-header">
                    <div class="stat-card-title">
                        <i class="fas fa-piggy-bank"></i>
                        成本节约
                    </div>
                </div>
                <div class="stat-card-value">¥<?= number_format($data['summary']['cost_savings'] ?? 15200, 2) ?></div>
                <div class="stat-card-change positive">
                    <i class="fas fa-arrow-up"></i>
                    +15.3%
                </div>
            </div>
        </div>

        <!-- 图表展示 -->
        <div class="chart-grid">
            <!-- 月度采购趋势 -->
            <div class="chart-card">
                <div class="chart-header">
                    <div class="chart-title">
                        <i class="fas fa-chart-line"></i>
                        月度采购趋势
                    </div>
                    <select class="form-control form-control-sm" style="width: auto;" onchange="updateChart('monthlyChart', this.value)">
                        <option value="amount">采购金额</option>
                        <option value="orders">订单数量</option>
                    </select>
                </div>
                <div class="chart-container">
                    <canvas id="monthlyChart"></canvas>
                </div>
            </div>

            <!-- 分类分布 -->
            <div class="chart-card">
                <div class="chart-header">
                    <div class="chart-title">
                        <i class="fas fa-chart-pie"></i>
                        采购分类分布
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="categoryChart"></canvas>
                </div>
            </div>

            <!-- 供应商排名 -->
            <div class="chart-card">
                <div class="chart-header">
                    <div class="chart-title">
                        <i class="fas fa-chart-bar"></i>
                        供应商采购排名
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="supplierChart"></canvas>
                </div>
            </div>

            <!-- 库存预警 -->
            <div class="chart-card">
                <div class="chart-header">
                    <div class="chart-title">
                        <i class="fas fa-exclamation-triangle"></i>
                        库存状态概览
                    </div>
                </div>
                <div style="padding: 20px;">
                    <div style="margin-bottom: 15px;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>正常库存</span>
                            <span>85%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 85%; background: #10b981;"></div>
                        </div>
                    </div>
                    <div style="margin-bottom: 15px;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>低库存预警</span>
                            <span>12%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 12%; background: #f59e0b;"></div>
                        </div>
                    </div>
                    <div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>缺货</span>
                            <span>3%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 3%; background: #ef4444;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快速洞察 -->
        <div class="data-table">
            <div class="table-header">
                <h3 class="table-title">
                    <i class="fas fa-lightbulb"></i>
                    数据洞察
                </h3>
            </div>
            <div style="padding: 25px;">
                <div class="row">
                    <div class="col-md-6">
                        <h5 style="color: #374151; margin-bottom: 15px;">📈 趋势分析</h5>
                        <ul style="list-style: none; padding: 0; margin: 0;">
                            <li style="margin-bottom: 10px; color: #6b7280;">
                                <i class="fas fa-check-circle" style="color: #10b981; margin-right: 8px;"></i>
                                本月采购金额较上月增长 <?= $data['summary']['monthly_growth'] ?? 8.5 ?>%
                            </li>
                            <li style="margin-bottom: 10px; color: #6b7280;">
                                <i class="fas fa-check-circle" style="color: #10b981; margin-right: 8px;"></i>
                                蔬菜类采购占比最高，达到 35.8%
                            </li>
                            <li style="margin-bottom: 10px; color: #6b7280;">
                                <i class="fas fa-exclamation-triangle" style="color: #f59e0b; margin-right: 8px;"></i>
                                <?= $data['summary']['low_stock_items'] ?? 8 ?> 种食材库存偏低，需要补货
                            </li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h5 style="color: #374151; margin-bottom: 15px;">💡 优化建议</h5>
                        <ul style="list-style: none; padding: 0; margin: 0;">
                            <li style="margin-bottom: 10px; color: #6b7280;">
                                <i class="fas fa-lightbulb" style="color: #3b82f6; margin-right: 8px;"></i>
                                建议与绿色蔬菜供应商协商批量采购优惠
                            </li>
                            <li style="margin-bottom: 10px; color: #6b7280;">
                                <i class="fas fa-lightbulb" style="color: #3b82f6; margin-right: 8px;"></i>
                                可考虑增加水产类供应商以降低采购成本
                            </li>
                            <li style="margin-bottom: 10px; color: #6b7280;">
                                <i class="fas fa-lightbulb" style="color: #3b82f6; margin-right: 8px;"></i>
                                建议设置自动补货提醒，避免缺货情况
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 全局变量
let refreshInterval;
let chartInstance;

// 数字动画函数
function animateNumber(element, start, end, duration = 2000) {
    const startTime = performance.now();
    const isMoneyValue = element.textContent.includes('¥');

    function updateNumber(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);

        // 使用缓动函数
        const easeOutQuart = 1 - Math.pow(1 - progress, 4);
        const current = start + (end - start) * easeOutQuart;

        if (isMoneyValue) {
            element.textContent = '¥' + Math.floor(current).toLocaleString();
        } else {
            element.textContent = Math.floor(current).toLocaleString();
        }

        if (progress < 1) {
            requestAnimationFrame(updateNumber);
        } else {
            if (isMoneyValue) {
                element.textContent = '¥' + end.toLocaleString();
            } else {
                element.textContent = end.toLocaleString();
            }
        }
    }

    requestAnimationFrame(updateNumber);
}

// 实时时间更新
function updateTime() {
    const now = new Date();
    const timeString = now.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });

    const timeElement = document.getElementById('currentTime');
    if (timeElement) {
        timeElement.textContent = timeString;
    }
}

// 刷新数据
function refreshData() {
    const refreshBtn = document.querySelector('[onclick="refreshData()"]');
    const icon = refreshBtn.querySelector('i');

    // 添加加载状态
    refreshBtn.disabled = true;
    icon.style.animation = 'spin 1s linear infinite';

    // 模拟加载延迟
    setTimeout(() => {
        location.reload();
    }, 1000);
}

// 导出报告
function exportReport() {
    const exportBtn = document.querySelector('[onclick="exportReport()"]');
    const icon = exportBtn.querySelector('i');

    // 添加加载状态
    exportBtn.disabled = true;
    icon.style.animation = 'spin 1s linear infinite';

    // 模拟导出过程
    setTimeout(() => {
        alert('报告导出功能正在开发中...');
        exportBtn.disabled = false;
        icon.style.animation = '';
    }, 2000);
}

// 图表数据
const chartData = {
    monthly_purchase: {
        labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
        data: [120000, 135000, 148000, 162000, 155000, 178000]
    },
    category_distribution: {
        labels: ['蔬菜类', '肉类', '粮食类', '调料类', '水产类', '其他'],
        data: [35.8, 28.2, 18.5, 8.3, 6.2, 3.0]
    },
    supplier_ranking: {
        labels: ['绿源农场', '新鲜配送', '优质食材', '农家直供', '品质保证'],
        data: [85000, 72000, 58000, 45000, 38000]
    }
};



// 更新图表
function updateChart(chartId, type) {
    // 这里可以添加图表切换逻辑
    console.log('更新图表:', chartId, type);
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('仪表板已加载');
    console.log('当前环境:', window.location.hostname);

    // 启动实时时间更新
    updateTime();
    setInterval(updateTime, 1000);

    // 初始化图表（多重检查确保兼容性）
    function tryInitializeCharts() {
        console.log('尝试初始化图表...');
        console.log('Chart.js可用:', typeof Chart !== 'undefined');
        console.log('initializeChart函数可用:', typeof initializeChart === 'function');

        // 检查必要的容器是否存在
        const requiredContainers = ['monthlyChart', 'categoryChart', 'supplierChart'];
        const existingContainers = requiredContainers.filter(id => document.getElementById(id));

        console.log('需要的容器:', requiredContainers);
        console.log('存在的容器:', existingContainers);

        if (existingContainers.length === requiredContainers.length) {
            if (typeof initializeChart === 'function') {
                initializeChart();
            } else {
                console.warn('initializeChart函数未找到，尝试直接初始化');
                // 备用初始化方案
                if (typeof initializeMonthlyChart === 'function') initializeMonthlyChart();
                if (typeof initializeCategoryChart === 'function') initializeCategoryChart();
                if (typeof initializeSupplierChart === 'function') initializeSupplierChart();
                if (typeof initializeInventoryChart === 'function') initializeInventoryChart();
            }
        } else {
            console.warn('图表容器未完全加载，稍后重试...');
            setTimeout(tryInitializeCharts, 200);
        }
    }

    // 延迟执行确保DOM完全加载
    setTimeout(tryInitializeCharts, 100);

    // 添加卡片悬停效果
    document.querySelectorAll('.stat-card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-4px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });

    console.log('仪表板初始化完成');
});

// 备用图表初始化方案（如果main.js加载失败）
window.addEventListener('load', function() {
    setTimeout(() => {
        if (typeof initializeChart !== 'function') {
            console.warn('main.js可能未加载，使用备用图表初始化方案');

            // 简单的图表初始化
            if (typeof Chart !== 'undefined') {
                const containers = ['monthlyChart', 'categoryChart', 'supplierChart'];
                containers.forEach(containerId => {
                    const ctx = document.getElementById(containerId);
                    if (ctx) {
                        console.log(`初始化备用图表: ${containerId}`);
                        new Chart(ctx, {
                            type: containerId === 'categoryChart' ? 'doughnut' :
                                  containerId === 'supplierChart' ? 'bar' : 'line',
                            data: {
                                labels: ['数据1', '数据2', '数据3'],
                                datasets: [{
                                    label: '示例数据',
                                    data: [10, 20, 30],
                                    backgroundColor: ['#4299e1', '#38a169', '#d69e2e']
                                }]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false
                            }
                        });
                    }
                });
            }
        }
    }, 1000);
});

// 页面卸载时清理
window.addEventListener('beforeunload', function() {
    if (refreshInterval) {
        clearInterval(refreshInterval);
    }
    // 销毁所有图表实例
    if (typeof destroyExistingCharts === 'function') {
        destroyExistingCharts();
    }
});
</script>

<?php require_once dirname(__DIR__, 2) . '/includes/footer-modular.php'; ?>
