<?php require_once dirname(__DIR__, 2) . '/includes/header-modular.php'; ?>

<div class="main-content">
    <?php require_once dirname(__DIR__, 2) . '/includes/sidebar-modular.php'; ?>
    
    <div class="content">
        <div class="content-header">
            <h1>
                <i class="fas fa-cog"></i>
                系统设置
            </h1>
            <p class="content-description">管理系统的各项配置和设置</p>
        </div>

        <div class="settings-grid">
            <!-- 基本设置 -->
            <div class="setting-card">
                <div class="setting-icon">
                    <i class="fas fa-sliders-h"></i>
                </div>
                <div class="setting-content">
                    <h3>基本设置</h3>
                    <p>网站名称、描述、时区等基础配置</p>
                    <a href="index.php?action=general" class="btn btn-primary">
                        <i class="fas fa-arrow-right"></i>
                        进入设置
                    </a>
                </div>
            </div>

            <!-- 安全设置 -->
            <div class="setting-card">
                <div class="setting-icon">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <div class="setting-content">
                    <h3>安全设置</h3>
                    <p>密码策略、会话超时、登录限制等安全配置</p>
                    <a href="index.php?action=security" class="btn btn-primary">
                        <i class="fas fa-arrow-right"></i>
                        进入设置
                    </a>
                </div>
            </div>

            <!-- 备份管理 -->
            <div class="setting-card">
                <div class="setting-icon">
                    <i class="fas fa-database"></i>
                </div>
                <div class="setting-content">
                    <h3>备份管理</h3>
                    <p>数据库备份、恢复和备份文件管理</p>
                    <a href="index.php?action=backup" class="btn btn-primary">
                        <i class="fas fa-arrow-right"></i>
                        进入管理
                    </a>
                </div>
            </div>

            <!-- 系统日志 -->
            <div class="setting-card">
                <div class="setting-icon">
                    <i class="fas fa-file-alt"></i>
                </div>
                <div class="setting-content">
                    <h3>系统日志</h3>
                    <p>查看系统运行日志和错误记录</p>
                    <a href="index.php?action=logs" class="btn btn-primary">
                        <i class="fas fa-arrow-right"></i>
                        查看日志
                    </a>
                </div>
            </div>

            <!-- 关于系统 -->
            <div class="setting-card">
                <div class="setting-icon">
                    <i class="fas fa-info-circle"></i>
                </div>
                <div class="setting-content">
                    <h3>关于系统</h3>
                    <p>系统版本、服务器信息和技术支持</p>
                    <a href="index.php?action=about" class="btn btn-primary">
                        <i class="fas fa-arrow-right"></i>
                        查看信息
                    </a>
                </div>
            </div>

            <!-- 系统监控 -->
            <div class="setting-card">
                <div class="setting-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="setting-content">
                    <h3>系统监控</h3>
                    <p>服务器性能、存储空间和运行状态监控</p>
                    <a href="#" class="btn btn-secondary" onclick="alert('功能开发中，敬请期待！'); return false;">
                        <i class="fas fa-clock"></i>
                        开发中
                    </a>
                </div>
            </div>
        </div>

        <!-- 快速状态面板 -->
        <div class="status-panel">
            <h2><i class="fas fa-tachometer-alt"></i> 系统状态</h2>
            <div class="status-grid">
                <div class="status-item">
                    <div class="status-icon success">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="status-info">
                        <h4>系统运行</h4>
                        <p>正常</p>
                    </div>
                </div>
                <div class="status-item">
                    <div class="status-icon success">
                        <i class="fas fa-database"></i>
                    </div>
                    <div class="status-info">
                        <h4>数据库连接</h4>
                        <p>正常</p>
                    </div>
                </div>
                <div class="status-item">
                    <div class="status-icon warning">
                        <i class="fas fa-hdd"></i>
                    </div>
                    <div class="status-info">
                        <h4>存储空间</h4>
                        <p>75% 已使用</p>
                    </div>
                </div>
                <div class="status-item">
                    <div class="status-icon success">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="status-info">
                        <h4>在线用户</h4>
                        <p><?= $_SESSION['user_name'] ?? '未知' ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.content-description {
    color: #6b7280;
    margin-top: 8px;
    margin-bottom: 24px;
}

.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 32px;
}

.setting-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
}

.setting-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.setting-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;
}

.setting-icon i {
    font-size: 24px;
    color: white;
}

.setting-content h3 {
    margin: 0 0 8px 0;
    color: #1f2937;
    font-size: 18px;
    font-weight: 600;
}

.setting-content p {
    margin: 0 0 16px 0;
    color: #6b7280;
    line-height: 1.5;
}

.status-panel {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
}

.status-panel h2 {
    margin: 0 0 20px 0;
    color: #1f2937;
    font-size: 20px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    background: #f9fafb;
    border-radius: 8px;
}

.status-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
}

.status-icon.success {
    background: #dcfce7;
    color: #166534;
}

.status-icon.warning {
    background: #fef3c7;
    color: #d97706;
}

.status-icon.error {
    background: #fee2e2;
    color: #dc2626;
}

.status-info h4 {
    margin: 0 0 4px 0;
    font-size: 14px;
    font-weight: 600;
    color: #374151;
}

.status-info p {
    margin: 0;
    font-size: 12px;
    color: #6b7280;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #f3f4f6;
    color: #6b7280;
}

.btn-secondary:hover {
    background: #e5e7eb;
}

@media (max-width: 768px) {
    .settings-grid {
        grid-template-columns: 1fr;
    }
    
    .status-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<?php require_once dirname(__DIR__, 2) . '/includes/footer-modular.php'; ?>
