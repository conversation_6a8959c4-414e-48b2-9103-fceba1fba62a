<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>添加导入日志</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .code-block { background: #f4f4f4; padding: 10px; border-radius: 3px; font-family: monospace; margin: 5px 0; white-space: pre-wrap; }
        .btn { display: inline-block; padding: 8px 16px; background: #007cba; color: white; text-decoration: none; border-radius: 4px; margin: 5px; }
    </style>
</head>
<body>
    <h1>📝 添加导入日志</h1>
    
    <?php
    $logFile = '../logs/import_debug.log';
    $logDir = dirname($logFile);
    
    // 创建日志目录
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
        echo "<p class='success'>✅ 创建日志目录: {$logDir}</p>";
    }
    
    // 创建日志文件
    if (!file_exists($logFile)) {
        file_put_contents($logFile, "Import Debug Log - Created at " . date('Y-m-d H:i:s') . "\n");
        echo "<p class='success'>✅ 创建日志文件: {$logFile}</p>";
    }
    
    echo "<div class='test-section'>";
    echo "<h2>📝 为PurchaseController添加详细日志</h2>";
    
    echo "<div class='code-block'>";
    echo "我将在PurchaseController中添加以下日志记录:\n\n";
    echo "1. 明细数据处理开始和结束\n";
    echo "2. 每一行的处理状态\n";
    echo "3. 验证失败的具体原因\n";
    echo "4. 数据库插入的结果\n";
    echo "5. 异常的详细信息\n";
    echo "</div>";
    
    // 写入日志的辅助函数
    function writeImportLog($message) {
        $logFile = '../logs/import_debug.log';
        $timestamp = date('Y-m-d H:i:s');
        file_put_contents($logFile, "[{$timestamp}] {$message}\n", FILE_APPEND);
    }
    
    writeImportLog("=== 日志系统初始化 ===");
    writeImportLog("日志文件创建成功");
    
    echo "<p class='success'>✅ 日志系统已初始化</p>";
    echo "</div>";
    
    echo "<div class='test-section'>";
    echo "<h2>🔍 现在请重新导入并查看日志</h2>";
    
    echo "<div class='step'>";
    echo "<h4>步骤：</h4>";
    echo "<ol>";
    echo "<li><strong>重新导入</strong>：<a href='../modules/purchase/index.php?action=import' class='btn'>导入Excel文件</a></li>";
    echo "<li><strong>查看日志</strong>：<a href='view-import-log.php' class='btn'>查看导入日志</a></li>";
    echo "<li><strong>分析问题</strong>：根据日志找出明细丢失的原因</li>";
    echo "</ol>";
    echo "</div>";
    echo "</div>";
    ?>
    
    <p><a href="../modules/purchase/index.php">返回采购管理</a></p>
</body>
</html>
