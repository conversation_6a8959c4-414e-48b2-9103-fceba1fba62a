/**
 * Service Worker for 学校食材管理系统
 * 提供离线缓存和后台同步功能
 */

const CACHE_NAME = 'food-management-v1.0.2';
const STATIC_CACHE_NAME = 'food-management-static-v1.0.2';
const DYNAMIC_CACHE_NAME = 'food-management-dynamic-v1.0.2';

// 需要缓存的静态资源
const STATIC_ASSETS = [
    '/mobile/',
    '/mobile/index.php',
    '/mobile/style.css',
    '/mobile/main.js',
    '/mobile/manifest.json',
    '/mobile/inbound.php',
    '/mobile/inventory.php',
    '/mobile/purchase.php',
    '/mobile/reports.php',
    '/mobile/profile.php',
    // 字体和图标
    'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css',
    // 离线页面
    '/mobile/offline.html'
];

// 需要网络优先的资源
const NETWORK_FIRST_URLS = [
    '/api/',
    '/modules/',
    '/includes/'
];

// 需要缓存优先的资源
const CACHE_FIRST_URLS = [
    '/mobile/icons/',
    '/mobile/images/',
    '.css',
    '.js',
    '.png',
    '.jpg',
    '.jpeg',
    '.gif',
    '.svg',
    '.woff',
    '.woff2'
];

// Service Worker 安装事件
self.addEventListener('install', event => {
    console.log('Service Worker 安装中...');
    
    event.waitUntil(
        caches.open(STATIC_CACHE_NAME)
            .then(cache => {
                console.log('缓存静态资源...');
                return cache.addAll(STATIC_ASSETS);
            })
            .then(() => {
                console.log('静态资源缓存完成');
                return self.skipWaiting();
            })
            .catch(error => {
                console.error('静态资源缓存失败:', error);
            })
    );
});

// Service Worker 激活事件
self.addEventListener('activate', event => {
    console.log('Service Worker 激活中...');
    
    event.waitUntil(
        caches.keys()
            .then(cacheNames => {
                return Promise.all(
                    cacheNames.map(cacheName => {
                        if (cacheName !== STATIC_CACHE_NAME && 
                            cacheName !== DYNAMIC_CACHE_NAME) {
                            console.log('删除旧缓存:', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('Service Worker 激活完成');
                return self.clients.claim();
            })
    );
});

// 网络请求拦截
self.addEventListener('fetch', event => {
    const { request } = event;
    const url = new URL(request.url);

    // 跳过非 GET 请求
    if (request.method !== 'GET') {
        return;
    }

    // 跳过 Chrome 扩展请求
    if (url.protocol === 'chrome-extension:') {
        return;
    }

    // 开发环境检测
    const isDevelopment = url.hostname === 'localhost' || url.hostname === '127.0.0.1';

    // 开发环境下，只拦截静态资源，不拦截PHP页面
    if (isDevelopment && url.pathname.endsWith('.php')) {
        return;
    }

    // 跳过同源检查失败的请求
    if (url.origin !== self.location.origin) {
        return;
    }

    // 网络优先策略
    if (NETWORK_FIRST_URLS.some(pattern => url.pathname.includes(pattern))) {
        event.respondWith(networkFirst(request));
        return;
    }

    // 缓存优先策略
    if (CACHE_FIRST_URLS.some(pattern => url.pathname.includes(pattern))) {
        event.respondWith(cacheFirst(request));
        return;
    }

    // 默认策略：仅对静态资源使用网络优先
    if (!isDevelopment || !url.pathname.endsWith('.php')) {
        event.respondWith(networkFirst(request));
    }
});

// 网络优先策略
async function networkFirst(request) {
    try {
        const networkResponse = await fetch(request);

        if (networkResponse.ok) {
            const cache = await caches.open(DYNAMIC_CACHE_NAME);
            cache.put(request, networkResponse.clone());
        }

        return networkResponse;
    } catch (error) {
        console.log('网络请求失败，尝试从缓存获取:', request.url);

        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }

        // 如果是页面请求且没有缓存，返回离线页面
        if (request.destination === 'document') {
            const offlineResponse = await caches.match('/mobile/offline.html');
            if (offlineResponse) {
                return offlineResponse;
            }
            // 如果离线页面也没有缓存，返回一个简单的HTML响应
            return new Response(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>离线状态</title>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <style>
                        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
                        .offline { color: #666; }
                    </style>
                </head>
                <body>
                    <div class="offline">
                        <h1>网络连接失败</h1>
                        <p>请检查网络连接后重试</p>
                        <button onclick="location.reload()">重新加载</button>
                    </div>
                </body>
                </html>
            `, {
                headers: { 'Content-Type': 'text/html; charset=utf-8' }
            });
        }

        // 对于其他类型的请求，返回一个错误响应
        return new Response('Network Error', {
            status: 408,
            statusText: 'Network Error'
        });
    }
}

// 缓存优先策略
async function cacheFirst(request) {
    const cachedResponse = await caches.match(request);

    if (cachedResponse) {
        return cachedResponse;
    }

    try {
        const networkResponse = await fetch(request);

        if (networkResponse.ok) {
            const cache = await caches.open(DYNAMIC_CACHE_NAME);
            cache.put(request, networkResponse.clone());
        }

        return networkResponse;
    } catch (error) {
        console.error('缓存和网络都失败:', request.url, error);

        // 返回一个错误响应而不是抛出异常
        return new Response('Resource not available', {
            status: 503,
            statusText: 'Service Unavailable'
        });
    }
}

// 后台同步
self.addEventListener('sync', event => {
    console.log('后台同步事件:', event.tag);
    
    if (event.tag === 'background-sync') {
        event.waitUntil(doBackgroundSync());
    }
});

// 执行后台同步
async function doBackgroundSync() {
    try {
        // 同步离线数据
        await syncOfflineData();
        console.log('后台同步完成');
    } catch (error) {
        console.error('后台同步失败:', error);
    }
}

// 同步离线数据
async function syncOfflineData() {
    // 获取离线存储的数据
    const offlineData = await getOfflineData();
    
    if (offlineData.length > 0) {
        for (const data of offlineData) {
            try {
                await fetch(data.url, {
                    method: data.method,
                    headers: data.headers,
                    body: data.body
                });
                
                // 同步成功，删除离线数据
                await removeOfflineData(data.id);
            } catch (error) {
                console.error('同步数据失败:', error);
            }
        }
    }
}

// 获取离线数据
async function getOfflineData() {
    // 这里应该从 IndexedDB 或其他存储中获取离线数据
    return [];
}

// 删除离线数据
async function removeOfflineData(id) {
    // 这里应该从存储中删除已同步的数据
    console.log('删除离线数据:', id);
}

// 推送通知
self.addEventListener('push', event => {
    console.log('收到推送消息:', event);
    
    const options = {
        body: event.data ? event.data.text() : '您有新的消息',
        icon: '/mobile/icons/icon-192x192.png',
        badge: '/mobile/icons/badge-72x72.png',
        vibrate: [100, 50, 100],
        data: {
            dateOfArrival: Date.now(),
            primaryKey: 1
        },
        actions: [
            {
                action: 'explore',
                title: '查看详情',
                icon: '/mobile/icons/checkmark.png'
            },
            {
                action: 'close',
                title: '关闭',
                icon: '/mobile/icons/xmark.png'
            }
        ]
    };
    
    event.waitUntil(
        self.registration.showNotification('食材管理系统', options)
    );
});

// 通知点击事件
self.addEventListener('notificationclick', event => {
    console.log('通知被点击:', event);
    
    event.notification.close();
    
    if (event.action === 'explore') {
        event.waitUntil(
            clients.openWindow('/mobile/index.php')
        );
    }
});

// 错误处理
self.addEventListener('error', event => {
    console.error('Service Worker 错误:', event.error);
});

// 未处理的 Promise 拒绝
self.addEventListener('unhandledrejection', event => {
    console.error('未处理的 Promise 拒绝:', event.reason);
});
