<?php
/**
 * 图表数据API接口
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');

// 引入控制器
require_once 'DashboardController.php';

try {
    // 创建控制器实例
    $controller = new DashboardController();
    
    // 处理图表数据请求
    $controller->getChartData();
    
} catch (Exception $e) {
    // 返回错误响应
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
