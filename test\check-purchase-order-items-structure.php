<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>检查采购单商品表结构</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .data-table { width: 100%; border-collapse: collapse; margin: 10px 0; font-size: 12px; }
        .data-table th, .data-table td { border: 1px solid #ddd; padding: 6px; text-align: left; }
        .data-table th { background: #f5f5f5; }
        .btn { display: inline-block; padding: 8px 16px; background: #007cba; color: white; text-decoration: none; border-radius: 4px; margin: 5px; }
    </style>
</head>
<body>
    <h1>🔍 检查采购单商品表结构</h1>
    
    <?php
    require_once '../includes/Database.php';
    
    try {
        $db = Database::getInstance();
        
        echo "<div class='test-section'>";
        echo "<h2>📊 purchase_order_items 表结构</h2>";
        
        $columns = $db->fetchAll("DESCRIBE purchase_order_items");
        echo "<table class='data-table'>";
        echo "<tr><th>字段名</th><th>类型</th><th>是否为空</th><th>键</th><th>默认值</th><th>额外</th></tr>";
        foreach ($columns as $column) {
            $highlight = '';
            if (in_array($column['Field'], ['purpose', 'notes'])) {
                $highlight = 'style="background-color: #ffffcc;"';
            }
            echo "<tr {$highlight}>";
            echo "<td><strong>{$column['Field']}</strong></td>";
            echo "<td>{$column['Type']}</td>";
            echo "<td>{$column['Null']}</td>";
            echo "<td>{$column['Key']}</td>";
            echo "<td>{$column['Default']}</td>";
            echo "<td>{$column['Extra']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // 检查是否有purpose字段
        $hasPurpose = false;
        $hasNotes = false;
        foreach ($columns as $column) {
            if ($column['Field'] === 'purpose') {
                $hasPurpose = true;
            }
            if ($column['Field'] === 'notes') {
                $hasNotes = true;
            }
        }
        
        echo "<h4>字段检查结果：</h4>";
        if ($hasPurpose) {
            echo "<p class='success'>✅ 表中有 'purpose' 字段</p>";
        } else {
            echo "<p class='error'>❌ 表中没有 'purpose' 字段</p>";
        }
        
        if ($hasNotes) {
            echo "<p class='success'>✅ 表中有 'notes' 字段</p>";
        } else {
            echo "<p class='error'>❌ 表中没有 'notes' 字段</p>";
        }
        echo "</div>";
        
        echo "<div class='test-section'>";
        echo "<h2>🔧 修复建议</h2>";
        
        if (!$hasPurpose && $hasNotes) {
            echo "<p class='info'>建议修复：将查询中的 COALESCE(poi.purpose, poi.notes, '') 改为 poi.notes</p>";
        } elseif (!$hasPurpose && !$hasNotes) {
            echo "<p class='warning'>建议修复：将查询中的 COALESCE(poi.purpose, poi.notes, '') 改为 ''</p>";
        } elseif ($hasPurpose && !$hasNotes) {
            echo "<p class='info'>建议修复：将查询中的 COALESCE(poi.purpose, poi.notes, '') 改为 poi.purpose</p>";
        } else {
            echo "<p class='success'>字段正常，查询应该可以工作</p>";
        }
        echo "</div>";
        
        echo "<div class='test-section'>";
        echo "<h2>📋 示例数据</h2>";
        
        $sampleData = $db->fetchAll("SELECT * FROM purchase_order_items LIMIT 3");
        if (!empty($sampleData)) {
            echo "<h4>前3条记录：</h4>";
            echo "<table class='data-table'>";
            echo "<tr>";
            foreach (array_keys($sampleData[0]) as $key) {
                echo "<th>{$key}</th>";
            }
            echo "</tr>";
            foreach ($sampleData as $row) {
                echo "<tr>";
                foreach ($row as $value) {
                    echo "<td>" . htmlspecialchars($value ?: '') . "</td>";
                }
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p class='warning'>表中没有数据</p>";
        }
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='test-section'>";
        echo "<h2 class='error'>❌ 数据库连接失败</h2>";
        echo "<p class='error'>错误信息: " . $e->getMessage() . "</p>";
        echo "</div>";
    }
    ?>
    
    <p><a href="../modules/inbound/index.php?action=create">返回入库页面</a></p>
</body>
</html>
