<?php require_once dirname(__DIR__, 2) . '/includes/header-modular.php'; ?>

<div class="main-content">
    <?php require_once dirname(__DIR__, 2) . '/includes/sidebar-modular.php'; ?>

    <div class="content">
        <div class="content-header">
            <h1>
                <i class="fas fa-file-invoice"></i>
                出库单
            </h1>
            <div class="header-actions">
                <a href="index.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i>
                    返回列表
                </a>
                <button type="button" class="btn btn-primary" onclick="window.print()">
                    <i class="fas fa-print"></i>
                    打印
                </button>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <div class="card-title"><i class="fas fa-info-circle"></i> 单据信息</div>
            </div>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(220px, 1fr)); gap: 12px;">
                <div><strong>出库单号：</strong> <?= htmlspecialchars($summary['doc_no'] ?? '') ?></div>
                <div><strong>用餐日期：</strong> <?= htmlspecialchars($summary['meal_date'] ?? '') ?></div>
                <div><strong>餐次：</strong> <?= htmlspecialchars($summary['meal_type'] ?? '') ?></div>
                <div><strong>经办人：</strong> <?= htmlspecialchars($summary['operator_name'] ?? '') ?></div>
                <div><strong>合计品项：</strong> <?= htmlspecialchars($summary['total_items'] ?? 0) ?></div>
                <div><strong>合计金额：</strong> ¥<?= number_format((float)($summary['total_amount'] ?? 0), 2) ?></div>
            </div>
        </div>

        <div class="table-container">
            <table class="table">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>食材名称</th>
                        <th>分类</th>
                        <th>数量</th>
                        <th>单位</th>
                        <th>单价</th>
                        <th>金额</th>
                        <th>用途/备注</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($items)): ?>
                        <?php foreach ($items as $idx => $row): ?>
                            <tr>
                                <td><?= $idx + 1 ?></td>
                                <td><?= htmlspecialchars($row['ingredient_name'] ?? '') ?></td>
                                <td><?= htmlspecialchars($row['category_name'] ?? '') ?></td>
                                <td><?= number_format((float)($row['quantity'] ?? 0), 2) ?></td>
                                <td><?= htmlspecialchars($row['unit'] ?? '') ?></td>
                                <td>¥<?= number_format((float)($row['unit_price'] ?? 0), 2) ?></td>
                                <td><strong>¥<?= number_format((float)($row['total_amount'] ?? 0), 2) ?></strong></td>
                                <td><?= htmlspecialchars($row['purpose'] ?? $row['notes'] ?? '') ?></td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="8" class="text-center text-muted">
                                <i class="fas fa-inbox"></i> 未找到出库明细
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<style>
@media print {
  .sidebar, .header-actions, .alert { display: none !important; }
  .main-content { margin: 0 !important; }
  .content { padding: 0 !important; }
  .card, .table-container { box-shadow: none !important; border: 1px solid #ddd; }
}
</style>

<?php require_once dirname(__DIR__, 2) . '/includes/footer-modular.php'; ?>



