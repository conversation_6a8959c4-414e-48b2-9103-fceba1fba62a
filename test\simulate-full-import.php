<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模拟完整导入过程</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .debug-log { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; font-family: monospace; font-size: 12px; max-height: 500px; overflow-y: auto; }
        .btn { display: inline-block; padding: 8px 16px; background: #007cba; color: white; text-decoration: none; border-radius: 4px; margin: 5px; }
    </style>
</head>
<body>
    <h1>🔄 模拟完整导入过程</h1>
    
    <?php
    require_once '../includes/Database.php';
    require_once '../includes/ExcelReader.php';
    
    try {
        $db = Database::getInstance();
        $testFile = '../test.xlsx';
        
        echo "<div class='test-section'>";
        echo "<h2>🔄 完整模拟PurchaseController的导入逻辑</h2>";
        
        if (!file_exists($testFile)) {
            echo "<p class='error'>❌ test.xlsx 文件不存在</p>";
            exit;
        }
        
        echo "<div class='debug-log'>";
        
        function debugLog($message, $type = 'info') {
            $icons = ['info' => 'ℹ️', 'success' => '✅', 'error' => '❌', 'warning' => '⚠️'];
            $icon = $icons[$type] ?? 'ℹ️';
            echo "[" . date('H:i:s') . "] {$icon} {$message}\n";
        }
        
        debugLog("开始模拟完整导入过程", 'info');
        
        // 1. 读取Excel文件
        debugLog("=== 1. 读取Excel文件 ===", 'info');
        $reader = new ExcelReader();
        $data = $reader->read($testFile, 'test.xlsx');
        
        if (empty($data)) {
            debugLog("Excel文件为空", 'error');
            exit;
        }
        
        debugLog("文件读取成功，总行数: " . count($data), 'success');
        
        // 2. 格式检测
        debugLog("=== 2. 格式检测 ===", 'info');
        
        $firstRow = $data[0] ?? [];
        $secondRow = $data[1] ?? [];
        
        $firstRowText = implode('', $firstRow);
        $hasOrderTitle = strpos($firstRowText, '订货单') !== false;
        
        debugLog("第1行包含'订货单': " . ($hasOrderTitle ? '是' : '否'), $hasOrderTitle ? 'success' : 'info');
        
        if ($hasOrderTitle) {
            $importType = 'order_form';
            debugLog("格式检测结果: 订货单格式", 'success');
        } else {
            $orderNumber = trim($secondRow[1] ?? '');
            if (!empty($orderNumber) && strlen($orderNumber) > 10) {
                $importType = 'order_form';
                debugLog("通过订单号检测为订货单格式", 'success');
            } else {
                $importType = 'simple_list';
                debugLog("检测为简单列表格式", 'warning');
            }
        }
        
        // 3. 提取头部信息
        debugLog("=== 3. 提取头部信息 ===", 'info');
        
        if ($importType === 'order_form') {
            $orderNumber = trim($data[1][1] ?? '');
            $orderDateStr = trim($data[1][4] ?? '');
            $expectedDateStr = trim($data[1][12] ?? '');
            $customerName = trim($data[2][0] ?? '');
            $contactPerson = trim($data[2][12] ?? '');
            $deliveryAddress = trim($data[3][0] ?? '');
            $contactPhone = trim($data[3][12] ?? '');
            $orderAmount = floatval($data[4][0] ?? 0);
            $actualAmount = floatval($data[4][4] ?? 0);
            
            debugLog("订单号: '{$orderNumber}'", !empty($orderNumber) ? 'success' : 'error');
            debugLog("下单日期: '{$orderDateStr}'", !empty($orderDateStr) ? 'success' : 'warning');
            debugLog("预交货日期: '{$expectedDateStr}'", !empty($expectedDateStr) ? 'success' : 'warning');
            debugLog("客户名称: '{$customerName}'", !empty($customerName) ? 'success' : 'warning');
            debugLog("联系人: '{$contactPerson}'", !empty($contactPerson) ? 'success' : 'warning');
            debugLog("收货地址: '{$deliveryAddress}'", !empty($deliveryAddress) ? 'success' : 'warning');
            debugLog("联系电话: '{$contactPhone}'", !empty($contactPhone) ? 'success' : 'warning');
            debugLog("下单金额: {$orderAmount}", $orderAmount > 0 ? 'success' : 'warning');
            debugLog("实际金额: {$actualAmount}", $actualAmount > 0 ? 'success' : 'warning');
            
            if (empty($orderNumber)) {
                debugLog("❌ 订单号为空，无法继续", 'error');
                exit;
            }
            
            // 4. 创建采购订单（模拟）
            debugLog("=== 4. 创建采购订单（模拟） ===", 'info');
            
            $orderDate = date('Y-m-d');
            if (!empty($orderDateStr)) {
                $timestamp = strtotime($orderDateStr);
                if ($timestamp !== false) {
                    $orderDate = date('Y-m-d', $timestamp);
                }
            }
            
            $expectedDate = date('Y-m-d', strtotime('+1 day'));
            if (!empty($expectedDateStr)) {
                $timestamp = strtotime($expectedDateStr);
                if ($timestamp !== false) {
                    $expectedDate = date('Y-m-d', $timestamp);
                }
            }
            
            // 查找供应商
            $supplierId = 1; // 默认供应商
            if (!empty($contactPhone)) {
                $supplier = $db->fetchOne("SELECT id FROM suppliers WHERE phone = ? OR phone LIKE ?", [$contactPhone, '%' . $contactPhone . '%']);
                if ($supplier) {
                    $supplierId = $supplier['id'];
                    debugLog("找到匹配的供应商: ID={$supplierId}", 'success');
                } else {
                    debugLog("未找到匹配的供应商，使用默认供应商", 'warning');
                }
            }
            
            debugLog("准备创建订单: 订单号={$orderNumber}, 供应商ID={$supplierId}, 订单日期={$orderDate}", 'info');
            
            // 模拟创建订单
            $orderId = 999; // 模拟订单ID
            debugLog("订单创建成功（模拟）: ID={$orderId}", 'success');
            
            // 5. 处理明细数据
            debugLog("=== 5. 处理明细数据 ===", 'info');
            
            // 查找明细数据开始行
            $detailStartRow = -1;
            for ($i = 5; $i < count($data); $i++) {
                $row = $data[$i];
                $firstCell = trim($row[0] ?? '');
                
                if (strpos($firstCell, '序号') !== false || 
                    strpos($firstCell, '商品') !== false ||
                    strpos($firstCell, '编码') !== false) {
                    $detailStartRow = $i;
                    debugLog("找到明细标题行: 第" . ($i + 1) . "行", 'success');
                    break;
                }
            }
            
            if ($detailStartRow === -1) {
                debugLog("❌ 未找到明细数据开始行", 'error');
            } else {
                $successCount = 0;
                $errorCount = 0;
                $errors = [];
                
                debugLog("开始处理明细数据，从第" . ($detailStartRow + 2) . "行开始", 'info');
                
                for ($i = $detailStartRow + 1; $i < count($data); $i++) {
                    $row = $data[$i];
                    $rowNum = $i + 1;
                    
                    // 检查空行
                    $filteredRow = array_filter($row, function($cell) {
                        return !empty(trim($cell));
                    });
                    
                    if (empty($filteredRow)) {
                        debugLog("第{$rowNum}行: 空行，跳过", 'info');
                        continue;
                    }
                    
                    // 检查关键字段
                    $itemCode = trim($row[1] ?? '');    // 第2列：商品编码
                    $itemName = trim($row[2] ?? '');    // 第3列：商品名称
                    $quantity = trim($row[9] ?? '');    // 第10列：数量
                    
                    if (empty($itemCode) && empty($itemName)) {
                        debugLog("第{$rowNum}行: 关键字段为空，跳过", 'info');
                        continue;
                    }
                    
                    debugLog("第{$rowNum}行: 编码='{$itemCode}', 名称='{$itemName}', 数量='{$quantity}'", 'info');
                    
                    try {
                        // 模拟importOrderItem逻辑
                        $seqNo = trim($row[0] ?? '');
                        $specification = trim($row[3] ?? '');
                        $unit = trim($row[4] ?? '');
                        $brand = trim($row[5] ?? '');
                        $origin = trim($row[6] ?? '');
                        $shelfLife = trim($row[7] ?? '');
                        $unitPrice = floatval($row[8] ?? 0);
                        $quantityFloat = floatval($row[9] ?? 0);
                        $totalPrice = floatval($row[10] ?? 0);
                        $receivedQuantity = floatval($row[12] ?? 0);
                        $notes = trim($row[17] ?? '');
                        
                        debugLog("  详细字段: 单价={$unitPrice}, 数量={$quantityFloat}, 小计={$totalPrice}", 'info');
                        
                        // 验证必填字段
                        if (empty($itemCode)) {
                            throw new Exception('商品编码不能为空（第2列）');
                        }
                        
                        if ($quantityFloat <= 0) {
                            throw new Exception('数量必须大于0（第10列）');
                        }
                        
                        if ($unitPrice < 0) {
                            throw new Exception('单价不能为负数（第9列）');
                        }
                        
                        // 查找或创建食材
                        $ingredient = $db->fetchOne("SELECT id FROM ingredients WHERE code = ?", [$itemCode]);
                        if ($ingredient) {
                            $ingredientId = $ingredient['id'];
                            debugLog("  找到现有食材: ID={$ingredientId}", 'success');
                        } else {
                            debugLog("  食材不存在，需要创建: 编码={$itemCode}, 名称={$itemName}", 'warning');
                            
                            // 模拟创建食材
                            $ingredientData = [
                                'code' => $itemCode,
                                'name' => !empty($itemName) ? $itemName : $itemCode,
                                'specification' => $specification,
                                'unit' => !empty($unit) ? $unit : '个',
                                'category_id' => 1,
                                'status' => 1,
                                'created_at' => date('Y-m-d H:i:s')
                            ];
                            
                            // 这里应该插入数据库，但我们只是模拟
                            $ingredientId = 1000 + $successCount; // 模拟ID
                            debugLog("  创建食材成功（模拟）: ID={$ingredientId}", 'success');
                        }
                        
                        // 计算总价
                        if ($totalPrice <= 0) {
                            $totalPrice = $quantityFloat * $unitPrice;
                        }
                        
                        // 模拟插入订单明细
                        $itemData = [
                            'order_id' => $orderId,
                            'ingredient_id' => $ingredientId,
                            'quantity' => $quantityFloat,
                            'unit_price' => $unitPrice,
                            'total_price' => $totalPrice,
                            'received_quantity' => $receivedQuantity,
                            'notes' => $notes
                        ];
                        
                        debugLog("  准备插入明细: " . json_encode($itemData), 'info');
                        
                        // 这里应该插入数据库，但我们只是模拟
                        debugLog("  明细插入成功（模拟）", 'success');
                        
                        $successCount++;
                        
                    } catch (Exception $e) {
                        $errorCount++;
                        $errorMsg = "第{$rowNum}行明细: " . $e->getMessage();
                        $errors[] = $errorMsg;
                        debugLog("  ❌ {$errorMsg}", 'error');
                    }
                    
                    // 只处理前10行明细
                    if (($successCount + $errorCount) >= 10) {
                        debugLog("（只处理前10行明细数据）", 'info');
                        break;
                    }
                }
                
                debugLog("=== 6. 处理结果 ===", 'info');
                debugLog("成功处理明细: {$successCount}", $successCount > 0 ? 'success' : 'error');
                debugLog("失败明细: {$errorCount}", $errorCount > 0 ? 'warning' : 'info');
                
                if (!empty($errors)) {
                    debugLog("错误详情:", 'error');
                    foreach ($errors as $error) {
                        debugLog("  - {$error}", 'error');
                    }
                }
                
                if ($successCount === 0) {
                    debugLog("❌ 这就是明细数据为空的原因！", 'error');
                } else {
                    debugLog("✅ 应该能够导入 {$successCount} 条明细", 'success');
                }
            }
        } else {
            debugLog("❌ 文件未被识别为订货单格式", 'error');
        }
        
        debugLog("模拟导入完成", 'info');
        
        echo "</div>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ 模拟过程出错: " . $e->getMessage() . "</p>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }
    ?>
    
    <div class="test-section">
        <h2>🎯 下一步操作</h2>
        <div class="step">
            <p><strong>基于模拟结果：</strong></p>
            <ol>
                <li>如果模拟显示有有效明细，但实际导入为空，可能是数据库插入失败</li>
                <li>如果模拟显示没有有效明细，需要检查数据格式和验证逻辑</li>
                <li>检查实际的PurchaseController中是否有异常被静默处理</li>
            </ol>
            
            <p>
                <a href="debug-empty-details.php" class="btn">🔍 详细调试</a>
                <a href="../modules/purchase/index.php?action=import" class="btn">🔄 重新导入</a>
            </p>
        </div>
    </div>
    
    <p><a href="../modules/purchase/index.php">返回采购管理</a></p>
</body>
</html>
