<?php

/**
 * 学校食堂食材出入库管理系统 - 路由创建脚本
 * 根据开发文档自动生成Laravel路由文件
 */

// API路由文件
$apiRoutes = '<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\IngredientController;
use App\Http\Controllers\Api\SupplierController;
use App\Http\Controllers\Api\InboundController;
use App\Http\Controllers\Api\OutboundController;
use App\Http\Controllers\Api\InventoryController;
use App\Http\Controllers\Api\ReportController;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\Api\CategoryController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| 学校食堂食材出入库管理系统API路由
| 所有API路由都需要通过身份验证
|
*/

// 公开路由（无需认证）
Route::prefix(\'v1\')->group(function () {
    // 认证相关
    Route::post(\'login\', [AuthController::class, \'login\']);
    Route::post(\'logout\', [AuthController::class, \'logout\']);
    
    // 系统健康检查
    Route::get(\'health\', function () {
        return response()->json([
            \'status\' => \'ok\',
            \'timestamp\' => now(),
            \'version\' => \'2.0\',
            \'system\' => \'学校食堂食材出入库管理系统\'
        ]);
    });
});

// 需要认证的路由
Route::prefix(\'v1\')->middleware([\'auth:sanctum\'])->group(function () {
    
    // 用户信息
    Route::get(\'user\', function (Request $request) {
        return $request->user();
    });
    
    // 用户管理
    Route::apiResource(\'users\', UserController::class);
    Route::post(\'users/{user}/avatar\', [UserController::class, \'uploadAvatar\']);
    Route::put(\'users/{user}/password\', [UserController::class, \'changePassword\']);
    Route::put(\'users/{user}/status\', [UserController::class, \'changeStatus\']);
    
    // 食材分类管理
    Route::apiResource(\'categories\', CategoryController::class);
    Route::put(\'categories/{category}/sort\', [CategoryController::class, \'updateSort\']);
    
    // 供应商管理
    Route::apiResource(\'suppliers\', SupplierController::class);
    Route::post(\'suppliers/{supplier}/qualification\', [SupplierController::class, \'uploadQualification\']);
    Route::put(\'suppliers/{supplier}/rating\', [SupplierController::class, \'updateRating\']);
    Route::put(\'suppliers/{supplier}/status\', [SupplierController::class, \'changeStatus\']);
    Route::get(\'suppliers/{supplier}/statistics\', [SupplierController::class, \'getStatistics\']);
    
    // 食材管理
    Route::apiResource(\'ingredients\', IngredientController::class);
    Route::post(\'ingredients/{ingredient}/image\', [IngredientController::class, \'uploadImage\']);
    Route::get(\'ingredients/low-stock\', [IngredientController::class, \'lowStock\']);
    Route::post(\'ingredients/import\', [IngredientController::class, \'import\']);
    Route::get(\'ingredients/export\', [IngredientController::class, \'export\']);
    
    // 入库管理
    Route::apiResource(\'inbound\', InboundController::class);
    Route::post(\'inbound/{inboundRecord}/approve\', [InboundController::class, \'approve\']);
    Route::post(\'inbound/{inboundRecord}/weight-images\', [InboundController::class, \'uploadWeightImages\']);
    Route::post(\'inbound/{inboundRecord}/invoice\', [InboundController::class, \'uploadInvoice\']);
    Route::get(\'inbound/pending/list\', [InboundController::class, \'pendingList\']);
    Route::post(\'inbound/batch/approve\', [InboundController::class, \'batchApprove\']);
    
    // 出库管理
    Route::apiResource(\'outbound\', OutboundController::class);
    Route::get(\'outbound/batches/{ingredient}\', [OutboundController::class, \'getAvailableBatches\']);
    Route::post(\'outbound/batch/create\', [OutboundController::class, \'createBatchOutbound\']);
    
    // 库存管理
    Route::prefix(\'inventory\')->group(function () {
        Route::get(\'/\', [InventoryController::class, \'index\']);
        Route::get(\'/{ingredient}\', [InventoryController::class, \'show\']);
        Route::get(\'/low-stock/list\', [InventoryController::class, \'lowStock\']);
        Route::get(\'/expiring/list\', [InventoryController::class, \'expiring\']);
        Route::post(\'/check\', [InventoryController::class, \'stockCheck\']);
        Route::post(\'/adjust\', [InventoryController::class, \'adjust\']);
        Route::get(\'/batches/{ingredient}\', [InventoryController::class, \'getBatches\']);
        Route::put(\'/batches/{batch}/status\', [InventoryController::class, \'updateBatchStatus\']);
    });
    
    // 报表统计
    Route::prefix(\'reports\')->group(function () {
        // 概览统计
        Route::get(\'overview\', [ReportController::class, \'overview\']);
        
        // 入库报表
        Route::get(\'inbound/summary\', [ReportController::class, \'inboundSummary\']);
        Route::get(\'inbound/detail\', [ReportController::class, \'inboundDetail\']);
        Route::get(\'inbound/supplier\', [ReportController::class, \'inboundBySupplier\']);
        Route::get(\'inbound/category\', [ReportController::class, \'inboundByCategory\']);
        
        // 出库报表
        Route::get(\'outbound/summary\', [ReportController::class, \'outboundSummary\']);
        Route::get(\'outbound/detail\', [ReportController::class, \'outboundDetail\']);
        Route::get(\'outbound/usage\', [ReportController::class, \'usageAnalysis\']);
        
        // 库存报表
        Route::get(\'inventory/current\', [ReportController::class, \'currentInventory\']);
        Route::get(\'inventory/value\', [ReportController::class, \'inventoryValue\']);
        Route::get(\'inventory/turnover\', [ReportController::class, \'inventoryTurnover\']);
        Route::get(\'inventory/expiring\', [ReportController::class, \'expiringItems\']);
        
        // 成本分析
        Route::get(\'cost/analysis\', [ReportController::class, \'costAnalysis\']);
        Route::get(\'cost/trend\', [ReportController::class, \'costTrend\']);
        Route::get(\'cost/comparison\', [ReportController::class, \'costComparison\']);
        
        // 导出功能
        Route::post(\'export/excel\', [ReportController::class, \'exportExcel\']);
        Route::post(\'export/pdf\', [ReportController::class, \'exportPdf\']);
    });
    
    // 系统设置
    Route::prefix(\'settings\')->group(function () {
        Route::get(\'system\', [SettingsController::class, \'getSystemSettings\']);
        Route::put(\'system\', [SettingsController::class, \'updateSystemSettings\']);
        Route::get(\'notifications\', [SettingsController::class, \'getNotificationSettings\']);
        Route::put(\'notifications\', [SettingsController::class, \'updateNotificationSettings\']);
    });
    
    // 操作日志
    Route::prefix(\'logs\')->group(function () {
        Route::get(\'operations\', [LogController::class, \'operations\']);
        Route::get(\'operations/{log}\', [LogController::class, \'showOperation\']);
        Route::delete(\'operations/clean\', [LogController::class, \'cleanOperations\']);
    });
    
    // 通知管理
    Route::prefix(\'notifications\')->group(function () {
        Route::get(\'/\', [NotificationController::class, \'index\']);
        Route::put(\'/{notification}/read\', [NotificationController::class, \'markAsRead\']);
        Route::put(\'/read-all\', [NotificationController::class, \'markAllAsRead\']);
        Route::delete(\'/{notification}\', [NotificationController::class, \'destroy\']);
    });
    
    // 文件管理
    Route::prefix(\'files\')->group(function () {
        Route::post(\'upload\', [FileController::class, \'upload\']);
        Route::delete(\'{file}\', [FileController::class, \'delete\']);
        Route::get(\'download/{file}\', [FileController::class, \'download\']);
    });
    
    // 数据备份
    Route::prefix(\'backup\')->group(function () {
        Route::post(\'create\', [BackupController::class, \'create\']);
        Route::get(\'list\', [BackupController::class, \'list\']);
        Route::post(\'restore\', [BackupController::class, \'restore\']);
        Route::delete(\'{backup}\', [BackupController::class, \'delete\']);
    });
});

// 权限中间件路由组
Route::prefix(\'v1\')->middleware([\'auth:sanctum\', \'permission\'])->group(function () {
    
    // 管理员专用路由
    Route::middleware([\'permission:系统管理\'])->group(function () {
        Route::get(\'admin/dashboard\', [AdminController::class, \'dashboard\']);
        Route::get(\'admin/statistics\', [AdminController::class, \'statistics\']);
        Route::post(\'admin/system/maintenance\', [AdminController::class, \'maintenance\']);
    });
    
    // 审核员专用路由
    Route::middleware([\'permission:入库审核\'])->group(function () {
        Route::get(\'audit/pending\', [AuditController::class, \'pending\']);
        Route::post(\'audit/batch-approve\', [AuditController::class, \'batchApprove\']);
    });
    
    // 仓库员专用路由
    Route::middleware([\'permission:食材入库|食材出库\'])->group(function () {
        Route::get(\'warehouse/tasks\', [WarehouseController::class, \'tasks\']);
        Route::get(\'warehouse/alerts\', [WarehouseController::class, \'alerts\']);
    });
});';

// Web路由文件
$webRoutes = '<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Web\HomeController;
use App\Http\Controllers\Web\AuthController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| 学校食堂食材出入库管理系统Web路由
| 主要用于前端页面展示和文件下载
|
*/

// 首页和认证页面
Route::get(\'/\', [HomeController::class, \'index\'])->name(\'home\');
Route::get(\'/login\', [AuthController::class, \'showLogin\'])->name(\'login\');
Route::get(\'/dashboard\', [HomeController::class, \'dashboard\'])->name(\'dashboard\')->middleware(\'auth\');

// 管理后台路由
Route::prefix(\'admin\')->middleware([\'auth\', \'permission:系统管理\'])->group(function () {
    Route::get(\'/\', [AdminController::class, \'index\'])->name(\'admin.index\');
    Route::get(\'users\', [AdminController::class, \'users\'])->name(\'admin.users\');
    Route::get(\'settings\', [AdminController::class, \'settings\'])->name(\'admin.settings\');
    Route::get(\'logs\', [AdminController::class, \'logs\'])->name(\'admin.logs\');
});

// 文件下载路由
Route::prefix(\'download\')->middleware(\'auth\')->group(function () {
    Route::get(\'invoice/{file}\', [FileController::class, \'downloadInvoice\'])->name(\'download.invoice\');
    Route::get(\'export/{file}\', [FileController::class, \'downloadExport\'])->name(\'download.export\');
    Route::get(\'template/{type}\', [FileController::class, \'downloadTemplate\'])->name(\'download.template\');
});

// API文档路由
Route::get(\'/api/documentation\', function () {
    return view(\'api.documentation\');
})->name(\'api.docs\');

// 健康检查路由
Route::get(\'/health\', function () {
    return response()->json([
        \'status\' => \'ok\',
        \'timestamp\' => now(),
        \'version\' => \'2.0\'
    ]);
})->name(\'health\');

// 404页面
Route::fallback(function () {
    return response()->view(\'errors.404\', [], 404);
});';

// 创建文件
file_put_contents('routes/api.php', $apiRoutes);
file_put_contents('routes/web.php', $webRoutes);

echo "✅ 路由文件创建完成！\n";
echo "已创建以下路由文件：\n";
echo "- routes/api.php (API路由)\n";
echo "- routes/web.php (Web路由)\n";
echo "\n路由功能包括：\n";
echo "- 用户认证和权限管理\n";
echo "- 食材和分类管理\n";
echo "- 供应商管理\n";
echo "- 入库和出库管理\n";
echo "- 库存管理和监控\n";
echo "- 报表统计和导出\n";
echo "- 系统设置和日志\n";
echo "- 文件上传和下载\n";
echo "\n继续创建其他功能脚本...\n";
