<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试Excel导入问题</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .data-table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        .data-table th, .data-table td { border: 1px solid #ddd; padding: 8px; text-align: left; font-size: 12px; }
        .data-table th { background: #f5f5f5; }
        .code-block { background: #f4f4f4; padding: 10px; border-radius: 3px; font-family: monospace; margin: 5px 0; }
    </style>
</head>
<body>
    <h1>调试Excel导入问题</h1>
    
    <?php
    try {
        // 检查test.xlsx文件
        $testFile = '../test.xlsx';
        echo "<div class='test-section'>";
        echo "<h2>1. 检查test.xlsx文件</h2>";
        
        if (file_exists($testFile)) {
            echo "<p class='success'>✅ test.xlsx 文件存在</p>";
            echo "<p class='info'>文件大小: " . number_format(filesize($testFile)) . " 字节</p>";
            echo "<p class='info'>文件路径: " . realpath($testFile) . "</p>";
        } else {
            echo "<p class='error'>❌ test.xlsx 文件不存在</p>";
            echo "<p class='warning'>请确保文件位于项目根目录下</p>";
        }
        echo "</div>";
        
        // 尝试读取Excel文件
        if (file_exists($testFile)) {
            echo "<div class='test-section'>";
            echo "<h2>2. 读取Excel文件内容</h2>";
            
            require_once '../includes/ExcelReader.php';
            
            try {
                $reader = new ExcelReader();
                $data = $reader->read($testFile, 'test.xlsx');
                
                echo "<p class='success'>✅ Excel文件读取成功</p>";
                echo "<p class='info'>总行数: " . count($data) . "</p>";
                
                if (!empty($data)) {
                    echo "<p class='info'>总列数: " . count($data[0]) . "</p>";
                    
                    echo "<h3>📋 Excel数据内容</h3>";
                    echo "<table class='data-table'>";
                    echo "<tr><th>行号</th>";
                    for ($i = 0; $i < min(20, count($data[0])); $i++) {
                        echo "<th>列" . ($i + 1) . "</th>";
                    }
                    echo "</tr>";
                    
                    foreach ($data as $rowIndex => $row) {
                        if ($rowIndex >= 20) break; // 只显示前20行
                        echo "<tr>";
                        echo "<td>" . ($rowIndex + 1) . "</td>";
                        for ($i = 0; $i < min(20, count($row)); $i++) {
                            $cellValue = isset($row[$i]) ? htmlspecialchars($row[$i]) : '';
                            if (strlen($cellValue) > 30) {
                                $cellValue = substr($cellValue, 0, 30) . '...';
                            }
                            echo "<td>{$cellValue}</td>";
                        }
                        echo "</tr>";
                    }
                    echo "</table>";
                    
                    if (count($data) > 20) {
                        echo "<p class='info'>... 还有 " . (count($data) - 20) . " 行数据</p>";
                    }
                }
                
            } catch (Exception $e) {
                echo "<p class='error'>❌ Excel文件读取失败: " . $e->getMessage() . "</p>";
            }
            echo "</div>";
            
            // 测试格式检测
            if (isset($data) && !empty($data)) {
                echo "<div class='test-section'>";
                echo "<h2>3. 格式检测测试</h2>";
                
                // 模拟格式检测逻辑
                $detectResult = 'simple_list';
                
                if (count($data) >= 4) {
                    $firstRow = $data[0] ?? [];
                    $secondRow = $data[1] ?? [];
                    
                    echo "<p class='info'>第1行列数: " . count($firstRow) . "</p>";
                    echo "<p class='info'>第2行列数: " . count($secondRow) . "</p>";
                    
                    if (count($firstRow) > 4 && count($secondRow) > 11) {
                        $orderNumber = trim($firstRow[1] ?? '');
                        echo "<p class='info'>第1行第2列内容: '" . htmlspecialchars($orderNumber) . "'</p>";
                        echo "<p class='info'>订单号长度: " . strlen($orderNumber) . "</p>";
                        
                        if (!empty($orderNumber) && (strlen($orderNumber) > 10 || preg_match('/^[A-Z0-9]+/', $orderNumber))) {
                            $detectResult = 'order_form';
                            echo "<p class='success'>✅ 检测为订货单格式</p>";
                        } else {
                            echo "<p class='info'>ℹ️ 不符合订货单格式条件</p>";
                        }
                    } else {
                        echo "<p class='info'>ℹ️ 行列数不足，不符合订货单格式</p>";
                    }
                }
                
                echo "<p class='info'>最终检测结果: <strong>{$detectResult}</strong></p>";
                echo "</div>";
                
                // 测试导入逻辑
                echo "<div class='test-section'>";
                echo "<h2>4. 导入逻辑测试</h2>";
                
                if ($detectResult === 'order_form') {
                    echo "<h3>📋 订货单格式处理</h3>";
                    
                    // 测试订单信息提取
                    try {
                        $orderNumber = trim($data[0][1] ?? '');
                        $orderDateStr = trim($data[0][4] ?? '');
                        $contactPerson = trim($data[1][11] ?? '');
                        $deliveryAddress = trim($data[2][1] ?? '');
                        $contactPhone = trim($data[2][11] ?? '');
                        $orderAmount = floatval($data[3][1] ?? 0);
                        $actualAmount = floatval($data[3][5] ?? 0);
                        $expectedDateStr = trim($data[3][11] ?? '');
                        
                        echo "<div class='code-block'>";
                        echo "订单号: {$orderNumber}<br>";
                        echo "订单日期: {$orderDateStr}<br>";
                        echo "联系人: {$contactPerson}<br>";
                        echo "送货地址: {$deliveryAddress}<br>";
                        echo "联系电话: {$contactPhone}<br>";
                        echo "订单金额: {$orderAmount}<br>";
                        echo "实际金额: {$actualAmount}<br>";
                        echo "预期交货日期: {$expectedDateStr}<br>";
                        echo "</div>";
                        
                        // 检查明细数据
                        echo "<h4>📦 明细数据检查</h4>";
                        $itemCount = 0;
                        for ($i = 6; $i < count($data); $i++) {
                            $row = $data[$i];
                            if (!empty($row[0]) && !empty(array_filter($row))) {
                                $itemCount++;
                                if ($itemCount <= 3) { // 只显示前3个明细
                                    echo "<p class='info'>明细 {$itemCount}: ";
                                    echo "编码=" . ($row[0] ?? '') . ", ";
                                    echo "名称=" . ($row[1] ?? '') . ", ";
                                    echo "数量=" . ($row[8] ?? '') . ", ";
                                    echo "单价=" . ($row[7] ?? '');
                                    echo "</p>";
                                }
                            }
                        }
                        echo "<p class='info'>总明细数量: {$itemCount}</p>";
                        
                    } catch (Exception $e) {
                        echo "<p class='error'>❌ 订货单信息提取失败: " . $e->getMessage() . "</p>";
                    }
                    
                } else {
                    echo "<h3>📝 简单列表格式处理</h3>";
                    
                    // 跳过标题行，检查数据行
                    $dataRows = array_slice($data, 1);
                    $validRows = 0;
                    
                    foreach ($dataRows as $index => $row) {
                        if (!empty(array_filter($row))) {
                            $validRows++;
                            if ($validRows <= 3) { // 只显示前3行
                                echo "<p class='info'>数据行 " . ($index + 2) . ": ";
                                for ($i = 0; $i < min(6, count($row)); $i++) {
                                    echo "列" . ($i + 1) . "=" . ($row[$i] ?? '') . " | ";
                                }
                                echo "</p>";
                            }
                        }
                    }
                    
                    echo "<p class='info'>有效数据行数: {$validRows}</p>";
                    
                    if ($validRows === 0) {
                        echo "<p class='warning'>⚠️ 没有找到有效的数据行，这可能是导入0条记录的原因</p>";
                        echo "<p class='info'>建议检查：</p>";
                        echo "<ul>";
                        echo "<li>Excel文件是否包含数据（除了标题行）</li>";
                        echo "<li>数据行是否为空行</li>";
                        echo "<li>数据格式是否正确</li>";
                        echo "</ul>";
                    }
                }
                echo "</div>";
            }
        }
        
        // 提供解决方案
        echo "<div class='test-section'>";
        echo "<h2>5. 问题诊断和解决方案</h2>";
        
        echo "<h3>🔍 可能的问题原因</h3>";
        echo "<ol>";
        echo "<li><strong>Excel文件为空</strong>：文件中没有实际数据</li>";
        echo "<li><strong>格式不匹配</strong>：Excel格式不符合预期的模板格式</li>";
        echo "<li><strong>数据验证失败</strong>：数据不符合业务规则</li>";
        echo "<li><strong>编码问题</strong>：中文字符编码导致读取失败</li>";
        echo "<li><strong>文件损坏</strong>：Excel文件结构有问题</li>";
        echo "</ol>";
        
        echo "<h3>💡 解决方案</h3>";
        echo "<ol>";
        echo "<li><strong>使用标准模板</strong>：";
        echo "<a href='../modules/purchase/download_excel_template.php' target='_blank'>下载简单列表模板</a> | ";
        echo "<a href='../modules/purchase/download_order_form_template.php' target='_blank'>下载订货单模板</a></li>";
        echo "<li><strong>检查数据格式</strong>：确保Excel中有实际数据，不是空行</li>";
        echo "<li><strong>验证必填字段</strong>：确保供应商名称、食材名称、数量、单价等字段不为空</li>";
        echo "<li><strong>检查编码</strong>：使用UTF-8编码保存Excel文件</li>";
        echo "<li><strong>重新创建文件</strong>：如果文件损坏，重新创建Excel文件</li>";
        echo "</ol>";
        
        echo "<h3>🧪 测试建议</h3>";
        echo "<ol>";
        echo "<li>先使用下载的模板文件测试导入</li>";
        echo "<li>在模板中添加1-2行测试数据</li>";
        echo "<li>确保数据符合格式要求</li>";
        echo "<li>逐步增加数据量进行测试</li>";
        echo "</ol>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ 调试过程出错: " . $e->getMessage() . "</p>";
    }
    ?>
    
    <div class="test-section">
        <h2>6. 快速测试</h2>
        <p><strong>立即测试导入功能：</strong></p>
        <ol>
            <li><a href="../modules/purchase/download_excel_template.php" target="_blank">下载简单列表模板</a></li>
            <li>在模板中添加测试数据</li>
            <li><a href="../modules/purchase/index.php?action=import" target="_blank">访问导入页面</a></li>
            <li>上传修改后的模板文件</li>
        </ol>
        
        <p><strong>示例数据格式（简单列表）：</strong></p>
        <div class="code-block">
        供应商名称 | 订单日期 | 食材名称 | 数量 | 单价 | 备注<br>
        新鲜蔬菜供应商 | 2024-01-15 | 白菜 | 50 | 2.50 | 早餐用<br>
        肉类批发商 | 2024-01-15 | 猪肉 | 20 | 25.00 | 午餐用
        </div>
    </div>
    
    <p><a href="../modules/purchase/index.php">返回采购管理</a></p>
</body>
</html>
