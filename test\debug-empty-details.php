<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试明细数据为空问题</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .step { background: #f9f9f9; padding: 10px; margin: 10px 0; border-left: 4px solid #007cba; }
        .data-table { width: 100%; border-collapse: collapse; margin: 10px 0; font-size: 11px; }
        .data-table th, .data-table td { border: 1px solid #ddd; padding: 4px; text-align: left; }
        .data-table th { background: #f5f5f5; }
        .highlight { background: #ffeb3b; padding: 2px 4px; font-weight: bold; }
        .debug-log { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; font-family: monospace; font-size: 12px; max-height: 400px; overflow-y: auto; }
        .btn { display: inline-block; padding: 8px 16px; background: #007cba; color: white; text-decoration: none; border-radius: 4px; margin: 5px; }
    </style>
</head>
<body>
    <h1>🔍 调试明细数据为空问题</h1>
    
    <?php
    require_once '../includes/Database.php';
    require_once '../includes/ExcelReader.php';
    
    try {
        $db = Database::getInstance();
        $testFile = '../test.xlsx';
        
        echo "<div class='test-section'>";
        echo "<h2>🔍 详细分析明细数据处理过程</h2>";
        
        if (!file_exists($testFile)) {
            echo "<p class='error'>❌ test.xlsx 文件不存在</p>";
            exit;
        }
        
        echo "<div class='debug-log'>";
        
        function debugLog($message, $type = 'info') {
            $icons = ['info' => 'ℹ️', 'success' => '✅', 'error' => '❌', 'warning' => '⚠️'];
            $icon = $icons[$type] ?? 'ℹ️';
            echo "[" . date('H:i:s') . "] {$icon} {$message}\n";
        }
        
        debugLog("开始调试明细数据为空问题", 'info');
        
        $reader = new ExcelReader();
        $data = $reader->read($testFile, 'test.xlsx');
        
        if (empty($data)) {
            debugLog("Excel文件为空", 'error');
            exit;
        }
        
        debugLog("文件读取成功，总行数: " . count($data), 'success');
        
        // 1. 查找明细数据开始行
        debugLog("=== 1. 查找明细数据开始行 ===", 'info');
        
        $detailStartRow = -1;
        for ($i = 5; $i < count($data); $i++) {
            $row = $data[$i];
            $firstCell = trim($row[0] ?? '');
            
            debugLog("第" . ($i + 1) . "行第1列: '{$firstCell}'", 'info');
            
            if (strpos($firstCell, '序号') !== false || 
                strpos($firstCell, '商品') !== false ||
                strpos($firstCell, '编码') !== false) {
                $detailStartRow = $i;
                debugLog("找到明细标题行: 第" . ($i + 1) . "行", 'success');
                break;
            }
        }
        
        if ($detailStartRow === -1) {
            debugLog("❌ 未找到明细数据开始行", 'error');
            debugLog("尝试显示第6-15行的内容来分析", 'info');
            
            for ($i = 5; $i < min(15, count($data)); $i++) {
                $row = $data[$i];
                $rowContent = [];
                for ($j = 0; $j < min(10, count($row)); $j++) {
                    $value = trim($row[$j] ?? '');
                    if (!empty($value)) {
                        $rowContent[] = "[{$j}]='{$value}'";
                    }
                }
                debugLog("第" . ($i + 1) . "行: " . implode(', ', $rowContent), 'info');
            }
        } else {
            debugLog("明细数据从第" . ($detailStartRow + 2) . "行开始", 'success');
            
            // 2. 分析明细数据
            debugLog("=== 2. 分析明细数据 ===", 'info');
            
            $validItems = 0;
            $invalidItems = 0;
            $emptyRows = 0;
            
            for ($i = $detailStartRow + 1; $i < count($data); $i++) {
                $row = $data[$i];
                $rowNum = $i + 1;
                
                // 检查是否为空行
                $filteredRow = array_filter($row, function($cell) {
                    return !empty(trim($cell));
                });
                
                if (empty($filteredRow)) {
                    $emptyRows++;
                    if ($emptyRows <= 3) {
                        debugLog("第{$rowNum}行: 完全空行", 'info');
                    }
                    continue;
                }
                
                // 显示行内容
                $rowContent = [];
                for ($j = 0; $j < min(20, count($row)); $j++) {
                    $value = trim($row[$j] ?? '');
                    if (!empty($value)) {
                        $rowContent[] = "[{$j}]='{$value}'";
                    }
                }
                debugLog("第{$rowNum}行内容: " . implode(', ', $rowContent), 'info');
                
                // 使用修正后的列位置提取字段
                $seqNo = trim($row[0] ?? '');              // 第1列：序号
                $itemCode = trim($row[1] ?? '');           // 第2列：商品编码
                $itemName = trim($row[2] ?? '');           // 第3列：商品名称
                $specification = trim($row[3] ?? '');      // 第4列：规格
                $unit = trim($row[4] ?? '');               // 第5列：单位
                $unitPrice = trim($row[8] ?? '');          // 第9列：单价
                $quantity = trim($row[9] ?? '');           // 第10列：数量
                $totalPrice = trim($row[10] ?? '');        // 第11列：小计
                $receivedQuantity = trim($row[12] ?? '');  // 第13列：实收数量
                $notes = trim($row[17] ?? '');             // 第18列：备注
                
                debugLog("  提取字段: 序号='{$seqNo}', 编码='{$itemCode}', 名称='{$itemName}', 数量='{$quantity}', 单价='{$unitPrice}'", 'info');
                
                // 验证字段
                $errors = [];
                
                if (empty($itemCode)) {
                    $errors[] = '商品编码为空';
                }
                
                if (empty($quantity) || !is_numeric($quantity) || floatval($quantity) <= 0) {
                    $errors[] = '数量无效(' . $quantity . ')';
                }
                
                if (!empty($unitPrice) && (!is_numeric($unitPrice) || floatval($unitPrice) < 0)) {
                    $errors[] = '单价无效(' . $unitPrice . ')';
                }
                
                if (empty($errors)) {
                    $validItems++;
                    debugLog("  ✅ 有效明细", 'success');
                    
                    // 模拟查找食材
                    $ingredient = $db->fetchOne("SELECT id, name FROM ingredients WHERE code = ?", [$itemCode]);
                    if ($ingredient) {
                        debugLog("  ✅ 找到食材: ID={$ingredient['id']}, 名称={$ingredient['name']}", 'success');
                    } else {
                        debugLog("  ⚠️ 食材不存在，将创建新食材: 编码={$itemCode}, 名称={$itemName}", 'warning');
                    }
                } else {
                    $invalidItems++;
                    debugLog("  ❌ 无效明细: " . implode(', ', $errors), 'error');
                }
                
                // 只显示前10行明细
                if (($validItems + $invalidItems) >= 10) {
                    debugLog("（只显示前10行明细数据）", 'info');
                    break;
                }
            }
            
            debugLog("=== 3. 统计结果 ===", 'info');
            debugLog("有效明细数量: {$validItems}", $validItems > 0 ? 'success' : 'error');
            debugLog("无效明细数量: {$invalidItems}", $invalidItems > 0 ? 'warning' : 'info');
            debugLog("空行数量: {$emptyRows}", 'info');
            
            if ($validItems === 0) {
                debugLog("❌ 这就是明细数据为空的原因！", 'error');
                debugLog("可能的原因:", 'info');
                debugLog("1. 商品编码列位置不正确", 'info');
                debugLog("2. 数量列位置不正确或数据格式问题", 'info');
                debugLog("3. 明细数据开始行判断错误", 'info');
                debugLog("4. 数据验证逻辑过于严格", 'info');
            } else {
                debugLog("✅ 找到有效明细，应该能够导入", 'success');
            }
        }
        
        // 4. 检查数据库中的实际情况
        debugLog("=== 4. 检查数据库中的实际情况 ===", 'info');
        
        // 查找最近的采购订单
        $recentOrder = $db->fetchOne("SELECT * FROM purchase_orders ORDER BY created_at DESC LIMIT 1");
        if ($recentOrder) {
            debugLog("最近的采购订单: ID={$recentOrder['id']}, 订单号={$recentOrder['order_number']}", 'success');
            
            // 查找该订单的明细
            $orderItems = $db->fetchAll("SELECT * FROM purchase_order_items WHERE order_id = ?", [$recentOrder['id']]);
            debugLog("该订单的明细数量: " . count($orderItems), count($orderItems) > 0 ? 'success' : 'error');
            
            if (count($orderItems) === 0) {
                debugLog("❌ 确认：数据库中没有明细数据", 'error');
            } else {
                foreach ($orderItems as $item) {
                    debugLog("明细: ingredient_id={$item['ingredient_id']}, quantity={$item['quantity']}, unit_price={$item['unit_price']}", 'success');
                }
            }
        } else {
            debugLog("❌ 数据库中没有找到采购订单", 'error');
        }
        
        debugLog("调试完成", 'info');
        
        echo "</div>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ 调试过程出错: " . $e->getMessage() . "</p>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }
    ?>
    
    <div class="test-section">
        <h2>🛠️ 可能的解决方案</h2>
        <div class="step">
            <h4>根据调试结果，可能需要：</h4>
            <ol>
                <li><strong>检查明细数据列位置</strong>：确认商品编码、数量等字段的实际列位置</li>
                <li><strong>调整验证逻辑</strong>：可能验证条件过于严格</li>
                <li><strong>检查明细开始行</strong>：确认明细数据的实际开始位置</li>
                <li><strong>查看错误日志</strong>：检查是否有异常被捕获但未显示</li>
            </ol>
            
            <p><strong>下一步操作：</strong></p>
            <p>
                <a href="debug-detail-data.php" class="btn">📊 查看详细数据结构</a>
                <a href="../modules/purchase/index.php?action=import" class="btn">🔄 重新尝试导入</a>
            </p>
        </div>
    </div>
    
    <p><a href="../modules/purchase/index.php">返回采购管理</a></p>
</body>
</html>
