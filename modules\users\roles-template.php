<?php
require_once dirname(__DIR__, 2) . '/includes/header-modular.php';
require_once dirname(__DIR__, 2) . '/includes/CSRFProtection.php';
?>

<div class="main-content">
    <?php require_once dirname(__DIR__, 2) . '/includes/sidebar-modular.php'; ?>

    <div class="content">
        <div class="content-header">
            <h1>
                <i class="fas fa-id-badge"></i>
                角色管理
            </h1>
            <div class="header-actions" style="display:flex; gap:8px;">
                <form method="POST" action="index.php?action=roles" onsubmit="return confirm('导入默认角色？已存在的不会重复创建。')">
                    <?= CSRFProtection::getHiddenField() ?>
                    <input type="hidden" name="operation" value="seed_default_roles">
                    <button type="submit" class="btn btn-secondary"><i class="fas fa-upload"></i> 导入默认角色</button>
                </form>
                <a href="index.php?action=permissions" class="btn btn-secondary">
                    <i class="fas fa-shield-alt"></i>
                    权限管理
                </a>
            </div>
        </div>

        <?php if (!empty($error)): ?>
        <div class="alert alert-danger"><i class="fas fa-exclamation-triangle"></i> <?= htmlspecialchars($error) ?></div>
        <?php elseif (!empty($_GET['success'])): ?>
        <div class="alert alert-success"><i class="fas fa-check-circle"></i> 操作成功</div>
        <?php endif; ?>

        <div class="grid">
            <!-- 角色列表 -->
            <div class="card">
                <div class="card-header">
                    <h3><i class="fas fa-list"></i> 角色列表</h3>
                </div>
                <div class="card-body">
                    <table class="table">
                        <thead>
                            <tr>
                                <th style="width: 20%">标识</th>
                                <th style="width: 25%">显示名称</th>
                                <th style="width: 10%">状态</th>
                                <th style="width: 15%">排序</th>
                                <th style="width: 30%">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (!empty($roles)): ?>
                                <?php foreach ($roles as $r): ?>
                                <tr>
                                    <td><code><?= htmlspecialchars($r['name']) ?></code></td>
                                    <td><?= htmlspecialchars($r['display_name']) ?></td>
                                    <td><?= intval($r['status']) === 1 ? '<span class="badge badge-success">启用</span>' : '<span class="badge badge-secondary">停用</span>' ?></td>
                                    <td><?= intval($r['sort_order'] ?? 0) ?></td>
                                    <td>
                                        <div class="action-buttons">
                                            <a href="index.php?action=roles&operation=edit&id=<?= intval($r['id']) ?>" class="btn btn-sm btn-warning"><i class="fas fa-edit"></i> 编辑</a>
                                            <a href="index.php?action=permissions&role_id=<?= intval($r['id']) ?>" class="btn btn-sm btn-info"><i class="fas fa-key"></i> 授权</a>
                                            <a href="index.php?action=roles&operation=toggle&id=<?= intval($r['id']) ?>" class="btn btn-sm btn-secondary"><i class="fas fa-toggle-on"></i> 切换启停</a>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr><td colspan="5" class="text-center text-muted"><i class="fas fa-inbox"></i> 暂无角色</td></tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 新增/编辑角色 -->
            <div class="card">
                <div class="card-header">
                    <h3><i class="fas fa-plus-circle"></i> <?= $editingRole ? '编辑角色' : '新增角色' ?></h3>
                </div>
                <div class="card-body">
                    <form method="POST" action="index.php?action=roles">
                        <?= CSRFProtection::getHiddenField() ?>
                        <input type="hidden" name="operation" value="save">
                        <?php if ($editingRole): ?>
                            <input type="hidden" name="id" value="<?= intval($editingRole['id']) ?>">
                        <?php endif; ?>
                        <div class="form-grid">
                            <div class="form-group">
                                <label>角色标识<span class="required">*</span></label>
                                <input type="text" name="name" class="form-control" placeholder="如 admin" value="<?= htmlspecialchars($editingRole['name'] ?? '') ?>" required>
                                <small class="form-text">用于系统内部唯一标识，建议使用英文小写</small>
                            </div>
                            <div class="form-group">
                                <label>显示名称<span class="required">*</span></label>
                                <input type="text" name="display_name" class="form-control" placeholder="如 系统管理员" value="<?= htmlspecialchars($editingRole['display_name'] ?? '') ?>" required>
                            </div>
                            <div class="form-group">
                                <label>状态</label>
                                <select name="status" class="form-control">
                                    <option value="1" <?= (($editingRole['status'] ?? 1) == 1) ? 'selected' : '' ?>>启用</option>
                                    <option value="0" <?= (($editingRole['status'] ?? 1) == 0) ? 'selected' : '' ?>>停用</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>排序</label>
                                <input type="number" name="sort_order" class="form-control" value="<?= intval($editingRole['sort_order'] ?? 0) ?>">
                            </div>
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary"><i class="fas fa-save"></i> 保存</button>
                            <a href="index.php?action=roles" class="btn btn-outline-secondary"><i class="fas fa-undo"></i> 重置</a>
                        </div>
                    </form>
                </div>
            </div>

            <?php if ($editingRole && !empty($permissions)): ?>
            <!-- 角色权限授权（内嵌） -->
            <div class="card">
                <div class="card-header">
                    <h3><i class="fas fa-key"></i> 角色权限授权 - <?= htmlspecialchars($editingRole['display_name'] ?? $editingRole['name']) ?></h3>
                </div>
                <div class="card-body">
                    <form method="POST" action="index.php?action=permissions">
                        <?= CSRFProtection::getHiddenField() ?>
                        <input type="hidden" name="operation" value="assign">
                        <input type="hidden" name="role_id" value="<?= intval($editingRole['id']) ?>">
                        <input type="hidden" name="redirect_to" value="roles">
                        <div class="perm-grid">
                            <?php
                                $grouped = [];
                                foreach ($permissions as $p) {
                                    $module = $p['module'] ?? '通用';
                                    $grouped[$module][] = $p;
                                }
                                ksort($grouped);
                            ?>
                            <?php foreach ($grouped as $module => $rows): $moduleKey = strtolower(preg_replace('/[^a-z0-9_]+/i','-',$module)); ?>
                                <div class="perm-group" data-module="<?= $moduleKey ?>">
                                    <div class="perm-group-title" style="display:flex;align-items:center;justify-content:space-between;gap:8px;">
                                        <span><i class="fas fa-folder"></i> <?= htmlspecialchars($module) ?></span>
                                        <div class="perm-group-actions" style="display:flex;gap:6px;">
                                            <button type="button" class="btn btn-sm btn-light" data-action="select-view" data-module="<?= $moduleKey ?>">查看</button>
                                            <button type="button" class="btn btn-sm btn-light" data-action="select-operate" data-module="<?= $moduleKey ?>">操作</button>
                                            <button type="button" class="btn btn-sm btn-secondary" data-action="select-all" data-module="<?= $moduleKey ?>">全选</button>
                                            <button type="button" class="btn btn-sm btn-outline-secondary" data-action="clear" data-module="<?= $moduleKey ?>">清空</button>
                                        </div>
                                    </div>
                                    <div class="perm-group-items">
                                        <?php foreach ($rows as $p): ?>
                                        <?php $code = $p['name'] ?? ''; $isOperate = preg_match('/\.(create|edit|update|delete|approve|import|export|assign|toggle|save|batch|batchcreate|audit)$/i', $code); $type = $isOperate ? 'operate' : 'view'; ?>
                                        <label class="perm-item" data-module="<?= $moduleKey ?>" data-type="<?= $type ?>">
                                            <input type="checkbox" name="permission_ids[]" value="<?= intval($p['id']) ?>" <?= in_array(intval($p['id']), $assignedPermissionIds ?? []) ? 'checked' : '' ?>>
                                            <span class="name"><?= htmlspecialchars($p['display_name'] ?? $p['name']) ?></span>
                                            <span class="code"><code><?= htmlspecialchars($p['name']) ?></code></span>
                                            <?= intval($p['status'] ?? 1) !== 1 ? '<span class="badge badge-secondary">停用</span>' : '' ?>
                                        </label>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary"><i class="fas fa-save"></i> 保存授权</button>
                            <a href="index.php?action=permissions&role_id=<?= intval($editingRole['id']) ?>" class="btn btn-outline-secondary"><i class="fas fa-external-link-alt"></i> 在权限管理中打开</a>
                        </div>
                    </form>
                </div>
            </div>
            <?php endif; ?>

        </div>
    </div>
</div>

<style>
.grid { display: grid; grid-template-columns: 2fr 1fr; gap: 20px; }
.card { background: white; border-radius: 8px; box-shadow: 0 2px 6px rgba(0,0,0,0.08); overflow: hidden; }
.card-header { padding: 14px 18px; border-bottom: 1px solid #e5e7eb; font-weight: 600; color: #374151; display:flex; align-items:center; gap:8px; }
.card-body { padding: 16px 18px; }
.form-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 12px; }
.form-group { display:flex; flex-direction: column; gap: 6px; }
.required { color: #e53e3e; }
.action-buttons { display: flex; gap: 6px; flex-wrap: wrap; }
.alert { padding: 10px 12px; border-radius: 6px; margin-bottom: 12px; display:flex; align-items:center; gap:8px; }
.alert-danger { background: #fee2e2; color: #b91c1c; }
<script>
(function(){
  function forItems(moduleKey, filterType, cb){
    document.querySelectorAll('.perm-item[data-module="'+moduleKey+'"]').forEach(function(label){
      if(!filterType || label.getAttribute('data-type') === filterType){
        var input = label.querySelector('input[type="checkbox"]');
        if(input) cb(input);
      }
    });
  }
  function onClick(e){
    var btn = e.target.closest('button[data-action]');
    if(!btn) return;
    var moduleKey = btn.getAttribute('data-module');
    var action = btn.getAttribute('data-action');
    if(action === 'select-view'){
      forItems(moduleKey, 'view', function(input){ input.checked = true; });
    } else if(action === 'select-operate'){
      forItems(moduleKey, 'operate', function(input){ input.checked = true; });
    } else if(action === 'select-all'){
      forItems(moduleKey, null, function(input){ input.checked = true; });
    } else if(action === 'clear'){
      forItems(moduleKey, null, function(input){ input.checked = false; });
    }
  }
  document.addEventListener('click', onClick);
})();
</script>

.alert-success { background: #dcfce7; color: #166534; }
.perm-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 12px; }
.perm-group { border: 1px solid #e5e7eb; border-radius: 6px; overflow: hidden; }
.perm-group-title { background:#f9fafb; padding:8px 10px; font-weight:600; color:#374151; }
.perm-group-items { display:flex; flex-direction:column; padding:8px 10px; gap:6px; }
.perm-item { display:flex; gap:8px; align-items:center; }
.perm-item .name { min-width: 140px; }
.perm-item .code code { color:#6b7280; }
@media (max-width: 1024px) { .grid { grid-template-columns: 1fr; } .form-grid { grid-template-columns: 1fr; } .perm-grid { grid-template-columns: 1fr; } }
</style>

<?php require_once dirname(__DIR__, 2) . '/includes/footer-modular.php'; ?>

