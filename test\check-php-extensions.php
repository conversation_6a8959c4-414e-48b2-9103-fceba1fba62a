<?php
/**
 * PHP扩展详细检查脚本
 */

echo "<h2>PHP扩展详细检查</h2>";

// 获取所有已加载的扩展
$loaded_extensions = get_loaded_extensions();
sort($loaded_extensions);

echo "<h3>已加载的扩展 (" . count($loaded_extensions) . "个)</h3>";
echo "<div style='columns: 3; column-gap: 20px;'>";
foreach ($loaded_extensions as $ext) {
    echo "<p>✅ $ext</p>";
}
echo "</div>";

// 检查特定扩展的详细信息
echo "<h3>关键扩展详细信息</h3>";

$key_extensions = ['pdo', 'pdo_mysql', 'mysqli', 'gd', 'curl', 'openssl'];

foreach ($key_extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "<h4>$ext 扩展</h4>";
        
        switch ($ext) {
            case 'pdo':
                echo "<p>PDO驱动: " . implode(', ', PDO::getAvailableDrivers()) . "</p>";
                break;
            case 'gd':
                $gd_info = gd_info();
                echo "<p>GD版本: " . $gd_info['GD Version'] . "</p>";
                echo "<p>支持格式: ";
                $formats = [];
                if ($gd_info['JPEG Support']) $formats[] = 'JPEG';
                if ($gd_info['PNG Support']) $formats[] = 'PNG';
                if ($gd_info['GIF Read Support']) $formats[] = 'GIF';
                echo implode(', ', $formats) . "</p>";
                break;
            case 'curl':
                $curl_info = curl_version();
                echo "<p>cURL版本: " . $curl_info['version'] . "</p>";
                echo "<p>SSL版本: " . $curl_info['ssl_version'] . "</p>";
                break;
            case 'openssl':
                echo "<p>OpenSSL版本: " . OPENSSL_VERSION_TEXT . "</p>";
                break;
        }
    } else {
        echo "<h4 style='color: red;'>❌ $ext 扩展未加载</h4>";
    }
}

// 检查函数可用性
echo "<h3>关键函数检查</h3>";
$functions = [
    'file_get_contents',
    'file_put_contents',
    'json_encode',
    'json_decode',
    'mb_strlen',
    'hash',
    'password_hash',
    'password_verify'
];

foreach ($functions as $func) {
    $status = function_exists($func) ? '✅' : '❌';
    $color = function_exists($func) ? 'green' : 'red';
    echo "<p style='color: $color;'>$status $func()</p>";
}

echo "<hr>";
echo "<p><a href='../index.php'>返回首页</a></p>";
?>
