<?php
/**
 * CSRF保护类
 * 防止跨站请求伪造攻击
 */

class CSRFProtection
{
    /**
     * 生成CSRF令牌
     */
    public static function generateToken()
    {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        
        return $_SESSION['csrf_token'];
    }

    /**
     * 验证CSRF令牌
     */
    public static function validateToken($token)
    {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        if (!isset($_SESSION['csrf_token']) || empty($token)) {
            return false;
        }
        
        return hash_equals($_SESSION['csrf_token'], $token);
    }

    /**
     * 生成CSRF隐藏字段HTML
     */
    public static function getHiddenField()
    {
        $token = self::generateToken();
        return '<input type="hidden" name="csrf_token" value="' . htmlspecialchars($token) . '">';
    }

    /**
     * 验证请求中的CSRF令牌
     */
    public static function validateRequest($request = null)
    {
        if ($request === null) {
            $request = $_POST;
        }
        
        $token = $request['csrf_token'] ?? '';
        
        if (!self::validateToken($token)) {
            throw new Exception('安全验证失败，请刷新页面重试');
        }
        
        return true;
    }

    /**
     * 刷新CSRF令牌
     */
    public static function refreshToken()
    {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        return $_SESSION['csrf_token'];
    }
}
