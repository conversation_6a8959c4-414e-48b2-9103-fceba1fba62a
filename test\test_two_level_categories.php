<?php
/**
 * 测试两级分类功能
 */

echo "=== 两级分类功能测试 ===\n\n";

echo "1. 检查数据库升级脚本:\n";
if (file_exists('scripts/database/upgrade_categories_to_two_level.sql')) {
    $sql_content = file_get_contents('scripts/database/upgrade_categories_to_two_level.sql');
    $sql_size = strlen($sql_content);
    echo "   ✅ 数据库升级脚本存在 ({$sql_size} 字节)\n";
    
    // 检查关键SQL语句
    $key_statements = [
        'ALTER TABLE `ingredient_categories`' => '表结构修改',
        'ADD COLUMN `parent_id`' => '父分类字段',
        'ADD COLUMN `level`' => '分类级别字段',
        'CREATE OR REPLACE VIEW `v_category_tree`' => '分类树视图',
        'CREATE FUNCTION GetCategoryPath' => '路径获取函数'
    ];
    
    echo "   关键SQL语句检查:\n";
    foreach ($key_statements as $statement => $description) {
        if (strpos($sql_content, $statement) !== false) {
            echo "     ✅ {$description}\n";
        } else {
            echo "     ❌ {$description} - 未找到\n";
        }
    }
} else {
    echo "   ❌ 数据库升级脚本不存在\n";
}

echo "\n2. 检查控制器更新:\n";
if (file_exists('modules/categories/CategoriesController.php')) {
    $controller_content = file_get_contents('modules/categories/CategoriesController.php');
    
    // 检查关键功能
    $key_features = [
        'parent_id' => '父分类处理',
        'level' => '分类级别处理',
        'parent_categories' => '父分类列表',
        'subcategory_count' => '子分类统计',
        'hasChildren' => '子分类检查'
    ];
    
    echo "   控制器功能检查:\n";
    foreach ($key_features as $feature => $description) {
        if (strpos($controller_content, $feature) !== false) {
            echo "     ✅ {$description}\n";
        } else {
            echo "     ❌ {$description} - 未找到\n";
        }
    }
} else {
    echo "   ❌ 控制器文件不存在\n";
}

echo "\n3. 检查模板更新:\n";
$templates = [
    'modules/categories/template.php' => '主模板',
    'modules/categories/create-template.php' => '创建模板',
    'modules/categories/edit-template.php' => '编辑模板'
];

foreach ($templates as $template_path => $template_name) {
    if (file_exists($template_path)) {
        $template_content = file_get_contents($template_path);
        echo "   {$template_name}:\n";
        
        // 检查两级分类相关元素
        $elements = [
            'parent_id' => '父分类选择',
            'level' => '分类级别',
            'level-badge' => '级别标识',
            'subcategory' => '子分类样式',
            'parent_path' => '父分类路径'
        ];
        
        foreach ($elements as $element => $description) {
            if (strpos($template_content, $element) !== false) {
                echo "     ✅ {$description}\n";
            } else {
                echo "     ❌ {$description} - 未找到\n";
            }
        }
    } else {
        echo "   ❌ {$template_name} 不存在\n";
    }
}

echo "\n4. 检查CSS样式:\n";
if (file_exists('modules/categories/style.css')) {
    $css_content = file_get_contents('modules/categories/style.css');
    
    // 检查两级分类样式
    $styles = [
        '.level-badge' => '级别标识样式',
        '.level-1' => '一级分类样式',
        '.level-2' => '二级分类样式',
        '.subcategory' => '子分类样式',
        '.parent-path' => '父分类路径样式'
    ];
    
    echo "   CSS样式检查:\n";
    foreach ($styles as $style => $description) {
        if (strpos($css_content, $style) !== false) {
            echo "     ✅ {$description}\n";
        } else {
            echo "     ❌ {$description} - 未找到\n";
        }
    }
} else {
    echo "   ❌ CSS样式文件不存在\n";
}

echo "\n5. 检查JavaScript功能:\n";
$js_functions = [
    'updateCategoryLevel' => '分类级别更新函数'
];

$create_template = file_exists('modules/categories/create-template.php') ? 
    file_get_contents('modules/categories/create-template.php') : '';
$edit_template = file_exists('modules/categories/edit-template.php') ? 
    file_get_contents('modules/categories/edit-template.php') : '';

echo "   JavaScript功能检查:\n";
foreach ($js_functions as $function => $description) {
    $found_in_create = strpos($create_template, $function) !== false;
    $found_in_edit = strpos($edit_template, $function) !== false;
    
    if ($found_in_create && $found_in_edit) {
        echo "     ✅ {$description} (创建和编辑模板)\n";
    } elseif ($found_in_create || $found_in_edit) {
        echo "     ⚠️ {$description} (仅在" . ($found_in_create ? '创建' : '编辑') . "模板中)\n";
    } else {
        echo "     ❌ {$description} - 未找到\n";
    }
}

echo "\n6. 功能特性总结:\n";
echo "   📋 支持的功能:\n";
echo "     • 一级分类管理\n";
echo "     • 二级分类管理\n";
echo "     • 父子分类关系\n";
echo "     • 分类级别显示\n";
echo "     • 子分类统计\n";
echo "     • 层级路径显示\n";
echo "     • 删除保护（有子分类时不能删除父分类）\n";
echo "     • 筛选功能（按级别、父分类筛选）\n";

echo "\n7. 数据库结构变更:\n";
echo "   新增字段:\n";
echo "     • parent_id: 父分类ID\n";
echo "     • level: 分类级别（1=一级，2=二级）\n";
echo "     • description: 分类描述\n";
echo "     • status: 状态字段\n";
echo "     • updated_at: 更新时间\n";

echo "\n8. 访问链接:\n";
echo "   分类管理: http://localhost:8000/modules/categories/index.php\n";
echo "   创建分类: http://localhost:8000/modules/categories/index.php?action=create\n";

echo "\n=== 两级分类功能测试完成 ===\n";
echo "✨ 食材分类已升级为两级分类系统！\n";
echo "🎯 支持一级分类和二级分类的完整管理\n";
echo "📊 提供层级显示和统计功能\n";
echo "🔒 包含完整的数据保护机制\n";
echo "\n⚠️ 注意：请先执行数据库升级脚本后再使用新功能\n";
echo "   执行命令：mysql -u用户名 -p数据库名 < scripts/database/upgrade_categories_to_two_level.sql\n";
?>
