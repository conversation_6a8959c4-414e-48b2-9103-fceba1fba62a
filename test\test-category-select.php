<?php
/**
 * 测试分类选择功能
 */

require_once '../includes/Database.php';

echo "<h2>测试分类选择功能</h2>";

// 模拟样式
echo "<style>
.debug { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; }
.form-group { margin: 15px 0; }
.form-control { padding: 8px; border: 1px solid #ddd; border-radius: 4px; width: 300px; }
.btn { padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
</style>";

try {
    $db = Database::getInstance();
    echo "<div class='debug'>";
    echo "<h3>数据库连接测试</h3>";
    echo "<p style='color: green;'>✅ 数据库连接成功</p>";
    
    // 获取分类数据
    try {
        $categories = $db->fetchAll("SELECT * FROM ingredient_categories WHERE status = 1 ORDER BY name ASC");
        echo "<p>✅ 查询成功，找到 " . count($categories) . " 个分类</p>";
        
        if (empty($categories)) {
            echo "<p style='color: orange;'>⚠️ 分类表为空，使用模拟数据</p>";
            $categories = [
                ['id' => 1, 'name' => '蔬菜类'],
                ['id' => 2, 'name' => '肉类'],
                ['id' => 3, 'name' => '水产类'],
                ['id' => 4, 'name' => '粮油类'],
                ['id' => 5, 'name' => '调料类'],
                ['id' => 6, 'name' => '豆制品']
            ];
        }
        
        echo "<h4>分类数据：</h4>";
        echo "<ul>";
        foreach ($categories as $category) {
            echo "<li>ID: {$category['id']}, 名称: " . htmlspecialchars($category['name']) . "</li>";
        }
        echo "</ul>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ 查询失败: " . $e->getMessage() . "</p>";
        $categories = [];
    }
    echo "</div>";
    
    // 测试表单
    echo "<h3>分类选择测试表单</h3>";
    echo "<form method='POST'>";
    
    echo "<div class='form-group'>";
    echo "<label>选择分类：</label><br>";
    echo "<select name='category_id' class='form-control'>";
    echo "<option value=''>请选择分类</option>";
    foreach ($categories as $category) {
        $selected = (isset($_POST['category_id']) && $_POST['category_id'] == $category['id']) ? 'selected' : '';
        echo "<option value='{$category['id']}' $selected>" . htmlspecialchars($category['name']) . "</option>";
    }
    echo "</select>";
    echo "</div>";
    
    echo "<div class='form-group'>";
    echo "<label>食材名称：</label><br>";
    echo "<input type='text' name='ingredient_name' class='form-control' value='" . htmlspecialchars($_POST['ingredient_name'] ?? '') . "' placeholder='输入食材名称'>";
    echo "</div>";
    
    echo "<button type='submit' class='btn'>测试提交</button>";
    echo "</form>";
    
    // 处理表单提交
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        echo "<div class='debug'>";
        echo "<h3>表单提交结果</h3>";
        
        $categoryId = $_POST['category_id'] ?? '';
        $ingredientName = $_POST['ingredient_name'] ?? '';
        
        echo "<p><strong>选择的分类ID：</strong> " . htmlspecialchars($categoryId) . "</p>";
        echo "<p><strong>食材名称：</strong> " . htmlspecialchars($ingredientName) . "</p>";
        
        if ($categoryId) {
            // 查找分类名称
            $selectedCategory = null;
            foreach ($categories as $category) {
                if ($category['id'] == $categoryId) {
                    $selectedCategory = $category;
                    break;
                }
            }
            
            if ($selectedCategory) {
                echo "<p><strong>分类名称：</strong> " . htmlspecialchars($selectedCategory['name']) . "</p>";
                echo "<p style='color: green;'>✅ 分类选择功能正常</p>";
            } else {
                echo "<p style='color: red;'>❌ 找不到对应的分类</p>";
            }
        } else {
            echo "<p style='color: orange;'>⚠️ 未选择分类</p>";
        }
        
        // 模拟数据库插入测试
        if ($categoryId && $ingredientName) {
            echo "<h4>模拟数据库操作：</h4>";
            $testData = [
                'name' => $ingredientName,
                'category_id' => $categoryId,
                'unit' => '公斤',
                'status' => 1
            ];
            
            echo "<p>准备插入的数据：</p>";
            echo "<pre>" . print_r($testData, true) . "</pre>";
            
            if (isset($_POST['do_insert']) && $_POST['do_insert'] === 'yes') {
                try {
                    $id = $db->insert('ingredients', $testData);
                    echo "<p style='color: green;'>✅ 数据插入成功，ID: $id</p>";
                    
                    // 立即删除测试数据
                    $db->delete('ingredients', 'id = ?', [$id]);
                    echo "<p style='color: blue;'>ℹ️ 测试数据已清理</p>";
                    
                } catch (Exception $e) {
                    echo "<p style='color: red;'>❌ 插入失败: " . $e->getMessage() . "</p>";
                }
            } else {
                echo "<form method='POST' style='display: inline;'>";
                echo "<input type='hidden' name='category_id' value='" . htmlspecialchars($categoryId) . "'>";
                echo "<input type='hidden' name='ingredient_name' value='" . htmlspecialchars($ingredientName) . "'>";
                echo "<input type='hidden' name='do_insert' value='yes'>";
                echo "<button type='submit' class='btn'>执行插入测试</button>";
                echo "</form>";
            }
        }
        
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ 数据库连接失败: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='../modules/ingredients/index.php?action=create'>食材创建页面</a> | <a href='check-categories.php'>检查分类数据</a> | <a href='../index.php'>返回首页</a></p>";
?>
