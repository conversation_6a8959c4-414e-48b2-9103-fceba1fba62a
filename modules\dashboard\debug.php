<?php
/**
 * 仪表板调试脚本
 * 用于诊断服务端环境问题
 */

header('Content-Type: application/json; charset=utf-8');

$debug_info = [
    'server_info' => [
        'php_version' => PHP_VERSION,
        'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
        'document_root' => $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown',
        'script_name' => $_SERVER['SCRIPT_NAME'] ?? 'Unknown',
        'request_uri' => $_SERVER['REQUEST_URI'] ?? 'Unknown',
        'http_host' => $_SERVER['HTTP_HOST'] ?? 'Unknown'
    ],
    'file_checks' => [
        'template_exists' => file_exists(__DIR__ . '/template.php'),
        'main_js_exists' => file_exists(__DIR__ . '/main.js'),
        'style_css_exists' => file_exists(__DIR__ . '/style.css'),
        'controller_exists' => file_exists(__DIR__ . '/DashboardController.php')
    ],
    'directory_info' => [
        'current_dir' => __DIR__,
        'files_in_dir' => scandir(__DIR__)
    ],
    'permissions' => [
        'template_readable' => is_readable(__DIR__ . '/template.php'),
        'main_js_readable' => is_readable(__DIR__ . '/main.js'),
        'style_css_readable' => is_readable(__DIR__ . '/style.css')
    ]
];

// 检查模板内容
if (file_exists(__DIR__ . '/template.php')) {
    $template_content = file_get_contents(__DIR__ . '/template.php');
    $debug_info['template_analysis'] = [
        'contains_chart_containers' => [
            'monthlyChart' => strpos($template_content, 'id="monthlyChart"') !== false,
            'categoryChart' => strpos($template_content, 'id="categoryChart"') !== false,
            'supplierChart' => strpos($template_content, 'id="supplierChart"') !== false
        ],
        'contains_main_js_reference' => strpos($template_content, 'main.js') !== false,
        'contains_chart_js_reference' => strpos($template_content, 'chart.js') !== false,
        'template_size' => strlen($template_content)
    ];
}

// 检查main.js内容
if (file_exists(__DIR__ . '/main.js')) {
    $main_js_content = file_get_contents(__DIR__ . '/main.js');
    $debug_info['main_js_analysis'] = [
        'contains_initialize_chart' => strpos($main_js_content, 'function initializeChart') !== false,
        'contains_chart_instances' => strpos($main_js_content, 'chartInstances') !== false,
        'file_size' => strlen($main_js_content)
    ];
}

echo json_encode($debug_info, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
?>
