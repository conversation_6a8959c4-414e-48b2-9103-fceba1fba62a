<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>采购单入库问题解决方案</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .btn { display: inline-block; padding: 8px 16px; background: #007cba; color: white; text-decoration: none; border-radius: 4px; margin: 5px; }
        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; }
        .solution-box { background: #e6f3ff; padding: 15px; border-left: 4px solid #007cba; margin: 15px 0; border-radius: 5px; }
        .problem-box { background: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 15px 0; border-radius: 5px; }
        .step-box { background: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 15px 0; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>🎯 采购单入库问题完整解决方案</h1>
    
    <div class="test-section">
        <h2>🔍 问题诊断</h2>
        
        <div class="problem-box">
            <h4>❌ 核心问题</h4>
            <p><strong>现象：</strong>采购管理页面显示有已确认的采购单，但在食材入库页面的采购单选择下拉框中没有显示</p>
            
            <h4>🔎 根本原因</h4>
            <p><strong>最可能的原因：</strong>采购单没有关联的商品项目（purchase_order_items表中没有对应记录）</p>
            
            <p>入库模块使用INNER JOIN查询，只有同时满足以下条件的采购单才会显示：</p>
            <ul>
                <li>✅ 采购单状态为1（待确认）或2（已确认）</li>
                <li>❌ 采购单必须有商品项目</li>
                <li>✅ 商品项目关联的食材必须存在</li>
                <li>✅ 采购单关联的供应商必须存在</li>
            </ul>
        </div>
    </div>
    
    <div class="test-section">
        <h2>✅ 解决方案</h2>
        
        <div class="solution-box">
            <h4>方案1：自动添加示例商品（推荐）</h4>
            <p>使用我们提供的修复脚本，自动为采购单添加商品项目</p>
            <p><a href="fix-purchase-order-items.php" class="btn btn-success">🚀 运行修复脚本</a></p>
            
            <h5>修复脚本功能：</h5>
            <ul>
                <li>检测没有商品项目的采购单</li>
                <li>自动添加5个示例商品项目</li>
                <li>设置合理的数量和价格</li>
                <li>更新采购单总金额</li>
            </ul>
        </div>
        
        <div class="solution-box">
            <h4>方案2：手动编辑采购单</h4>
            <p>通过采购管理页面手动为采购单添加商品</p>
            <ol>
                <li><a href="../modules/purchase/index.php" class="btn">前往采购管理</a></li>
                <li>找到已确认的采购单，点击"编辑"</li>
                <li>在采购单编辑页面添加商品项目</li>
                <li>保存后返回测试入库功能</li>
            </ol>
        </div>
        
        <div class="solution-box">
            <h4>方案3：创建新的完整采购单</h4>
            <p>创建一个包含商品项目的新采购单</p>
            <ol>
                <li><a href="../modules/purchase/index.php?action=create" class="btn">创建新采购单</a></li>
                <li>填写采购单基本信息</li>
                <li>添加商品项目（选择食材、数量、价格）</li>
                <li>确认采购单状态为"已确认"</li>
                <li>测试入库功能</li>
            </ol>
        </div>
    </div>
    
    <div class="test-section">
        <h2>🧪 验证步骤</h2>
        
        <div class="step-box">
            <h4>步骤1：检查数据完整性</h4>
            <p><a href="debug-specific-order.php" class="btn">运行数据检查</a></p>
            <p>确认采购单、商品项目、食材、供应商数据都存在</p>
        </div>
        
        <div class="step-box">
            <h4>步骤2：修复缺失的商品项目</h4>
            <p><a href="fix-purchase-order-items.php" class="btn btn-warning">运行修复脚本</a></p>
            <p>为没有商品项目的采购单自动添加商品</p>
        </div>
        
        <div class="step-box">
            <h4>步骤3：测试入库功能</h4>
            <p><a href="../modules/inbound/index.php?action=create" class="btn btn-success">测试入库页面</a></p>
            <p>确认采购单选择下拉框中显示已确认的采购单</p>
        </div>
    </div>
    
    <?php
    require_once '../includes/Database.php';
    
    try {
        $db = Database::getInstance();
        
        echo "<div class='test-section'>";
        echo "<h2>📊 当前状态检查</h2>";
        
        // 检查采购单状态
        $confirmedOrders = $db->fetchAll("
            SELECT po.id, po.order_number, po.status,
                   COUNT(poi.id) as item_count
            FROM purchase_orders po
            LEFT JOIN purchase_order_items poi ON po.id = poi.order_id
            WHERE po.status IN (1, 2)
            GROUP BY po.id
        ");
        
        echo "<h4>状态为1或2的采购单：</h4>";
        if (!empty($confirmedOrders)) {
            foreach ($confirmedOrders as $order) {
                $statusText = $order['status'] == 1 ? '待确认' : '已确认';
                $itemStatus = $order['item_count'] > 0 ? "✅ {$order['item_count']}个商品" : "❌ 无商品";
                $statusClass = $order['item_count'] > 0 ? 'success' : 'error';
                
                echo "<div class='step-box'>";
                echo "<p><strong>{$order['order_number']}</strong> - {$statusText} - <span class='{$statusClass}'>{$itemStatus}</span></p>";
                if ($order['item_count'] == 0) {
                    echo "<p class='warning'>⚠️ 此采购单需要添加商品项目才能在入库页面显示</p>";
                }
                echo "</div>";
            }
        } else {
            echo "<p class='error'>❌ 没有找到状态为1或2的采购单</p>";
        }
        
        // 检查入库查询结果
        $inboundResults = $db->fetchAll("
            SELECT COUNT(*) as count
            FROM purchase_orders po
            INNER JOIN purchase_order_items poi ON po.id = poi.order_id
            LEFT JOIN suppliers s ON po.supplier_id = s.id
            LEFT JOIN ingredients i ON poi.ingredient_id = i.id
            WHERE po.status IN (1, 2) 
            AND poi.ingredient_id IS NOT NULL
            AND i.id IS NOT NULL
        ");
        
        $resultCount = $inboundResults[0]['count'] ?? 0;
        echo "<h4>入库模块查询结果：</h4>";
        if ($resultCount > 0) {
            echo "<p class='success'>✅ 查询到 {$resultCount} 条可用记录，入库页面应该能正常显示采购单</p>";
        } else {
            echo "<p class='error'>❌ 查询结果为空，需要修复采购单商品项目</p>";
        }
        
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='test-section'>";
        echo "<h2 class='error'>❌ 数据库连接失败</h2>";
        echo "<p class='error'>错误信息: " . $e->getMessage() . "</p>";
        echo "</div>";
    }
    ?>
    
    <div class="test-section">
        <h2>🎯 推荐操作流程</h2>
        
        <div class="step-box">
            <h4>🚀 快速解决（推荐）</h4>
            <ol>
                <li><a href="fix-purchase-order-items.php" class="btn btn-success">点击运行修复脚本</a></li>
                <li>在修复页面中，为没有商品的采购单点击"添加商品"按钮</li>
                <li><a href="../modules/inbound/index.php?action=create" class="btn btn-success">测试入库页面</a></li>
                <li>确认采购单选择下拉框中显示采购单</li>
            </ol>
        </div>
        
        <div class="step-box">
            <h4>📝 手动完善（推荐用于生产环境）</h4>
            <ol>
                <li><a href="../modules/purchase/index.php" class="btn">前往采购管理</a></li>
                <li>编辑已确认的采购单，添加真实的商品项目</li>
                <li>确保每个商品项目都关联了正确的食材</li>
                <li><a href="../modules/inbound/index.php?action=create" class="btn btn-success">测试入库功能</a></li>
            </ol>
        </div>
    </div>
    
    <div class="test-section">
        <h2>📞 技术支持</h2>
        <p>如果按照上述步骤操作后仍然有问题，请检查：</p>
        <ul>
            <li>数据库连接是否正常</li>
            <li>相关表（purchase_orders, purchase_order_items, ingredients, suppliers）是否存在</li>
            <li>数据表之间的外键关联是否正确</li>
        </ul>
        
        <p><strong>调试工具：</strong></p>
        <p>
            <a href="debug-specific-order.php" class="btn">数据检查</a>
            <a href="check-table-structure.php" class="btn">表结构检查</a>
            <a href="final-purchase-order-test.php" class="btn">查询测试</a>
        </p>
    </div>
</body>
</html>
