    <!-- JavaScript -->
    <!-- jQ<PERSON>y CDN -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <!-- Bootstrap CDN -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- 模块特定脚本 -->
    <?php if (file_exists(__DIR__ . '/../modules/' . basename(dirname($_SERVER['SCRIPT_NAME'])) . '/main.js')): ?>
    <script src="main.js"></script>
    <?php endif; ?>
    
    <script>
        // 全局JavaScript函数
        
        // 确认删除
        function confirmDelete(message) {
            return confirm(message || '确定要删除吗？此操作不可恢复。');
        }
        
        // 显示加载状态
        function showLoading(button) {
            if (button) {
                button.disabled = true;
                const originalText = button.innerHTML;
                button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 处理中...';
                button.dataset.originalText = originalText;
            }
        }
        
        // 隐藏加载状态
        function hideLoading(button) {
            if (button && button.dataset.originalText) {
                button.disabled = false;
                button.innerHTML = button.dataset.originalText;
                delete button.dataset.originalText;
            }
        }
        
        // AJAX请求封装
        function ajaxRequest(url, data, options) {
            options = options || {};
            
            return fetch(url, {
                method: options.method || 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    ...options.headers
                },
                body: typeof data === 'string' ? data : new URLSearchParams(data)
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            });
        }
        
        // 显示提示消息
        function showMessage(message, type) {
            type = type || 'info';
            
            const alertClass = {
                'success': 'alert-success',
                'error': 'alert-danger',
                'danger': 'alert-danger',
                'warning': 'alert-warning',
                'info': 'alert-info'
            }[type] || 'alert-info';
            
            const icon = {
                'success': 'fas fa-check-circle',
                'error': 'fas fa-exclamation-circle',
                'danger': 'fas fa-exclamation-circle',
                'warning': 'fas fa-exclamation-triangle',
                'info': 'fas fa-info-circle'
            }[type] || 'fas fa-info-circle';
            
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert ${alertClass}`;
            alertDiv.style.cssText = 'position: fixed; top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
            alertDiv.innerHTML = `
                <i class="${icon}"></i>
                ${message}
                <button type="button" style="margin-left: auto; background: none; border: none; font-size: 18px; cursor: pointer; color: inherit;" onclick="this.parentElement.remove()">&times;</button>
            `;
            
            document.body.appendChild(alertDiv);
            
            // 5秒后自动消失
            setTimeout(() => {
                if (alertDiv.parentElement) {
                    alertDiv.remove();
                }
            }, 5000);
        }
        
        // 格式化数字
        function formatNumber(num, decimals) {
            decimals = decimals || 2;
            return parseFloat(num).toFixed(decimals);
        }
        
        // 格式化货币
        function formatCurrency(amount) {
            return '¥' + parseFloat(amount).toFixed(2);
        }
        
        // 防抖函数
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
        
        // 节流函数
        function throttle(func, limit) {
            let inThrottle;
            return function() {
                const args = arguments;
                const context = this;
                if (!inThrottle) {
                    func.apply(context, args);
                    inThrottle = true;
                    setTimeout(() => inThrottle = false, limit);
                }
            };
        }
        
        // DOM加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化所有工具提示
            const tooltips = document.querySelectorAll('[title]');
            tooltips.forEach(element => {
                element.addEventListener('mouseenter', function() {
                    const title = this.getAttribute('title');
                    if (title) {
                        this.setAttribute('data-original-title', title);
                        this.removeAttribute('title');
                        
                        const tooltip = document.createElement('div');
                        tooltip.className = 'tooltip';
                        tooltip.textContent = title;
                        tooltip.style.cssText = `
                            position: absolute;
                            background: #333;
                            color: white;
                            padding: 4px 8px;
                            border-radius: 4px;
                            font-size: 12px;
                            z-index: 1000;
                            pointer-events: none;
                        `;
                        document.body.appendChild(tooltip);
                        
                        const rect = this.getBoundingClientRect();
                        tooltip.style.left = rect.left + 'px';
                        tooltip.style.top = (rect.top - tooltip.offsetHeight - 5) + 'px';
                        
                        this.tooltip = tooltip;
                    }
                });
                
                element.addEventListener('mouseleave', function() {
                    if (this.tooltip) {
                        this.tooltip.remove();
                        this.tooltip = null;
                    }
                    const originalTitle = this.getAttribute('data-original-title');
                    if (originalTitle) {
                        this.setAttribute('title', originalTitle);
                        this.removeAttribute('data-original-title');
                    }
                });
            });
            
            // 自动聚焦第一个输入框
            const firstInput = document.querySelector('input[type="text"], input[type="email"], textarea');
            if (firstInput && !firstInput.value) {
                firstInput.focus();
            }
        });
    </script>
</body>
</html>