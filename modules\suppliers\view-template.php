<?php require_once dirname(__DIR__, 2) . '/includes/header-modular.php'; ?>

<div class="main-content">
    <?php require_once dirname(__DIR__, 2) . '/includes/sidebar-modular.php'; ?>

    <div class="content">
        <div class="content-header">
            <h1>
                <i class="fas fa-eye"></i>
                查看供应商
            </h1>
            <div class="header-actions">
                <a href="index.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i>
                    返回列表
                </a>
                <a href="index.php?action=edit&id=<?= htmlspecialchars($supplier['id'] ?? 0) ?>" class="btn btn-warning">
                    <i class="fas fa-edit"></i>
                    编辑
                </a>
            </div>
        </div>

        <?php if (isset($error_message)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle"></i>
            <?= htmlspecialchars($error_message) ?>
        </div>
        <?php endif; ?>

        <div class="card">
            <div class="card-header">
                <div class="card-title"><i class="fas fa-truck"></i> 基本信息</div>
            </div>
            <div class="row" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(260px, 1fr)); gap: 16px;">
                <div class="col">
                    <div><strong>名称：</strong> <?= htmlspecialchars($supplier['name'] ?? '-') ?></div>
                    <div><strong>联系人：</strong> <?= htmlspecialchars($supplier['contact_person'] ?? '-') ?></div>
                    <div><strong>电话：</strong> <?= htmlspecialchars($supplier['phone'] ?? '-') ?></div>
                </div>
                <div class="col">
                    <div><strong>邮箱：</strong> <?= htmlspecialchars($supplier['email'] ?? '-') ?></div>
                    <div><strong>地址：</strong> <?= htmlspecialchars($supplier['address'] ?? '-') ?></div>
                    <div><strong>状态：</strong> <?= isset($supplier['status']) && (int)$supplier['status'] === 1 ? '启用' : '禁用' ?></div>
                </div>
                <div class="col">
                    <div><strong>最近下单：</strong> <?= htmlspecialchars($supplier['last_order_date'] ?? '-') ?></div>
                    <div><strong>订单数量：</strong> <?= htmlspecialchars($supplier['order_count'] ?? 0) ?></div>
                    <div><strong>累计金额：</strong> ¥<?= number_format((float)($supplier['total_amount'] ?? 0), 2) ?></div>
                </div>
            </div>
        </div>

        <div class="table-container">
            <table class="table">
                <thead>
                    <tr>
                        <th>采购单号</th>
                        <th>下单日期</th>
                        <th>金额</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($orders)): ?>
                        <?php foreach ($orders as $order): ?>
                            <tr>
                                <td><a href="../purchase/index.php?action=view&id=<?= htmlspecialchars($order['id']) ?>" class="text-primary"><?= htmlspecialchars($order['order_number'] ?? '') ?></a></td>
                                <td><?= htmlspecialchars($order['order_date'] ?? '') ?></td>
                                <td><strong>¥<?= number_format((float)($order['total_amount'] ?? $order['order_amount'] ?? 0), 2) ?></strong></td>
                                <td><?= htmlspecialchars((string)($order['status'] ?? '')) ?></td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="4" class="text-center text-muted">
                                <i class="fas fa-inbox"></i> 暂无相关订单
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?php require_once dirname(__DIR__, 2) . '/includes/footer-modular.php'; ?>


