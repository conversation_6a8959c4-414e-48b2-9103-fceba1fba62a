<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最终采购单测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .btn { display: inline-block; padding: 8px 16px; background: #007cba; color: white; text-decoration: none; border-radius: 4px; margin: 5px; }
        .data-table { width: 100%; border-collapse: collapse; margin: 10px 0; font-size: 12px; }
        .data-table th, .data-table td { border: 1px solid #ddd; padding: 6px; text-align: left; }
        .data-table th { background: #f5f5f5; }
        .query-box { background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 12px; margin: 10px 0; }
        .fix-item { background: #e6ffe6; padding: 10px; border-left: 4px solid #28a745; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>✅ 最终采购单数据测试</h1>
    
    <div class="test-section">
        <h2>🔧 已完成的修复</h2>
        
        <div class="fix-item">
            <strong>1. 移除模拟数据回退</strong>
            <p>不再自动使用模拟数据，强制使用真实数据库数据</p>
        </div>
        
        <div class="fix-item">
            <strong>2. 修正查询字段名</strong>
            <p>根据实际表结构调整字段名，使用INNER JOIN确保数据完整性</p>
        </div>
        
        <div class="fix-item">
            <strong>3. 增强数据验证</strong>
            <p>添加更严格的条件确保只获取有效的采购单数据</p>
        </div>
    </div>
    
    <?php
    require_once '../includes/Database.php';
    
    try {
        $db = Database::getInstance();
        
        echo "<div class='test-section'>";
        echo "<h2>🔍 使用修正后的查询</h2>";
        
        echo "<h4>修正后的查询语句：</h4>";
        echo "<div class='query-box'>";
        echo "SELECT <br>";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;po.id,<br>";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;po.order_number,<br>";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;po.supplier_id,<br>";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;s.name as supplier_name,<br>";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;po.order_date,<br>";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;po.order_amount,<br>";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;poi.ingredient_id,<br>";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;i.name as ingredient_name,<br>";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;i.unit,<br>";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;poi.quantity,<br>";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;poi.unit_price,<br>";
        echo "&nbsp;&nbsp;&nbsp;&nbsp;COALESCE(poi.purpose, poi.notes, '') as purpose<br>";
        echo "FROM purchase_orders po<br>";
        echo "<strong>INNER JOIN</strong> purchase_order_items poi ON po.id = poi.order_id<br>";
        echo "LEFT JOIN suppliers s ON po.supplier_id = s.id<br>";
        echo "LEFT JOIN ingredients i ON poi.ingredient_id = i.id<br>";
        echo "WHERE po.status IN (1, 2)<br>";
        echo "AND poi.ingredient_id IS NOT NULL<br>";
        echo "<strong>AND i.id IS NOT NULL</strong><br>";
        echo "ORDER BY po.order_date DESC, po.order_number ASC";
        echo "</div>";
        
        $purchaseOrders = $db->fetchAll("
            SELECT 
                po.id,
                po.order_number,
                po.supplier_id,
                s.name as supplier_name,
                po.order_date,
                po.order_amount,
                poi.ingredient_id,
                i.name as ingredient_name,
                i.unit,
                poi.quantity,
                poi.unit_price,
                COALESCE(poi.purpose, poi.notes, '') as purpose
            FROM purchase_orders po
            INNER JOIN purchase_order_items poi ON po.id = poi.order_id
            LEFT JOIN suppliers s ON po.supplier_id = s.id
            LEFT JOIN ingredients i ON poi.ingredient_id = i.id
            WHERE po.status IN (1, 2) 
            AND poi.ingredient_id IS NOT NULL
            AND i.id IS NOT NULL
            ORDER BY po.order_date DESC, po.order_number ASC
        ");
        
        echo "<p class='info'>查询结果: <strong>" . count($purchaseOrders) . "</strong> 条记录</p>";
        
        if (!empty($purchaseOrders)) {
            echo "<p class='success'>✅ 成功获取到真实的采购单数据！</p>";
            
            echo "<h4>查询结果详情：</h4>";
            echo "<table class='data-table'>";
            echo "<tr><th>订单号</th><th>供应商</th><th>食材</th><th>单位</th><th>数量</th><th>单价</th><th>用途</th></tr>";
            foreach (array_slice($purchaseOrders, 0, 10) as $order) {
                echo "<tr>";
                echo "<td>{$order['order_number']}</td>";
                echo "<td>{$order['supplier_name']}</td>";
                echo "<td>{$order['ingredient_name']}</td>";
                echo "<td>{$order['unit']}</td>";
                echo "<td>{$order['quantity']}</td>";
                echo "<td>" . number_format($order['unit_price'], 2) . "</td>";
                echo "<td>{$order['purpose']}</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            // 按采购单分组
            $groupedOrders = [];
            foreach ($purchaseOrders as $order) {
                $key = $order['id'] . '_' . $order['order_number'];
                if (!isset($groupedOrders[$key])) {
                    $groupedOrders[$key] = [
                        'order_info' => $order,
                        'items' => []
                    ];
                }
                $groupedOrders[$key]['items'][] = $order;
            }
            
            echo "<h4>按采购单分组（共" . count($groupedOrders) . "个采购单）：</h4>";
            foreach ($groupedOrders as $key => $group) {
                $orderInfo = $group['order_info'];
                echo "<div style='background: #e6f3ff; padding: 15px; margin: 10px 0; border-radius: 8px; border-left: 4px solid #007cba;'>";
                echo "<h5><strong>{$orderInfo['order_number']}</strong></h5>";
                echo "<p><strong>供应商：</strong>{$orderInfo['supplier_name']} | ";
                echo "<strong>日期：</strong>{$orderInfo['order_date']} | ";
                echo "<strong>商品数：</strong>" . count($group['items']) . "种</p>";
                echo "<ul>";
                foreach ($group['items'] as $item) {
                    echo "<li>{$item['ingredient_name']} - {$item['quantity']}{$item['unit']} - ¥{$item['unit_price']}/{$item['unit']}";
                    if (!empty($item['purpose'])) {
                        echo " ({$item['purpose']})";
                    }
                    echo "</li>";
                }
                echo "</ul>";
                echo "</div>";
            }
            
        } else {
            echo "<p class='warning'>⚠️ 查询结果为空</p>";
            echo "<h4>可能的原因：</h4>";
            echo "<ul>";
            echo "<li>数据库中没有状态为1或2的采购单</li>";
            echo "<li>采购单没有关联的商品项目</li>";
            echo "<li>食材或供应商数据缺失</li>";
            echo "</ul>";
            
            echo "<h4>解决方案：</h4>";
            echo "<ol>";
            echo "<li><a href='../modules/purchase/index.php?action=create' class='btn'>创建新采购单</a></li>";
            echo "<li>确保采购单状态为1（待确认）或2（已确认）</li>";
            echo "<li>为采购单添加商品项目</li>";
            echo "<li>确保相关的供应商和食材数据存在</li>";
            echo "</ol>";
        }
        echo "</div>";
        
        echo "<div class='test-section'>";
        echo "<h2>🧪 测试入库页面</h2>";
        
        echo "<p>现在请测试入库页面，查看采购单选择是否正常工作：</p>";
        echo "<p><a href='../modules/inbound/index.php?action=create' class='btn'>🚀 测试入库页面</a></p>";
        
        if (!empty($purchaseOrders)) {
            echo "<h4>预期结果：</h4>";
            echo "<ul>";
            echo "<li class='success'>✅ 采购单下拉框显示 " . count($groupedOrders) . " 个采购单选项</li>";
            echo "<li class='success'>✅ 选择采购单后自动加载对应的商品列表</li>";
            echo "<li class='success'>✅ 商品信息完整显示（名称、数量、单价等）</li>";
            echo "<li class='success'>✅ 可以正常输入实际数量并计算总金额</li>";
            echo "</ul>";
        } else {
            echo "<h4>如果仍然没有数据：</h4>";
            echo "<ul>";
            echo "<li>请先创建采购单并确认状态</li>";
            echo "<li>为采购单添加商品项目</li>";
            echo "<li>确保供应商和食材数据完整</li>";
            echo "</ul>";
        }
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='test-section'>";
        echo "<h2 class='error'>❌ 数据库连接失败</h2>";
        echo "<p class='error'>错误信息: " . $e->getMessage() . "</p>";
        echo "</div>";
    }
    ?>
    
    <div class="test-section">
        <h2>📋 修复总结</h2>
        
        <h4>关键修改：</h4>
        <ul>
            <li class="success">✅ <strong>使用INNER JOIN</strong>：确保只获取有商品的采购单</li>
            <li class="success">✅ <strong>添加数据验证</strong>：AND i.id IS NOT NULL 确保食材数据存在</li>
            <li class="success">✅ <strong>移除模拟数据</strong>：强制使用真实数据库数据</li>
            <li class="success">✅ <strong>字段名修正</strong>：使用正确的数据库字段名</li>
        </ul>
        
        <h4>查询优化：</h4>
        <ul>
            <li><strong>INNER JOIN purchase_order_items</strong>：确保采购单有商品</li>
            <li><strong>LEFT JOIN suppliers</strong>：获取供应商信息</li>
            <li><strong>LEFT JOIN ingredients</strong>：获取食材信息</li>
            <li><strong>严格的WHERE条件</strong>：确保数据完整性</li>
        </ul>
    </div>
    
    <p style="text-align: center; margin-top: 30px;">
        <a href="../modules/inbound/index.php?action=create" class="btn" style="font-size: 1.1rem; padding: 12px 24px;">
            🎯 立即测试修复后的入库功能
        </a>
    </p>
</body>
</html>
