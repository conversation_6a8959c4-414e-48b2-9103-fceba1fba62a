<?php
/**
 * 调试采购单数据生成
 */
require_once '../includes/Database.php';

try {
    $db = Database::getInstance();
    
    // 获取待入库的采购单
    $purchase_orders = $db->fetchAll("
        SELECT 
            po.id,
            po.order_number,
            po.supplier_id,
            po.order_date,
            po.order_amount,
            po.notes,
            s.name as supplier_name,
            poi.id as item_id,
            poi.ingredient_id,
            poi.quantity,
            poi.unit_price,
            poi.total_price,
            poi.notes as item_notes,
            i.name as ingredient_name,
            i.unit as ingredient_unit
        FROM purchase_orders po
        LEFT JOIN suppliers s ON po.supplier_id = s.id
        LEFT JOIN purchase_order_items poi ON po.id = poi.order_id
        LEFT JOIN ingredients i ON poi.ingredient_id = i.id
        WHERE po.status IN (1, 2)
        ORDER BY po.created_at DESC, poi.id ASC
        LIMIT 10
    ");
    
    echo "<h1>采购单原始数据</h1>";
    echo "<pre>";
    print_r($purchase_orders);
    echo "</pre>";
    
    // 分组处理
    $grouped_orders = [];
    foreach ($purchase_orders as $order) {
        $key = $order['id'] . '_' . $order['order_number'];
        if (!isset($grouped_orders[$key])) {
            // 清理数据，移除可能导致JSON问题的字段
            $clean_order = [
                'id' => $order['id'],
                'order_number' => $order['order_number'],
                'supplier_id' => $order['supplier_id'],
                'supplier_name' => $order['supplier_name'],
                'order_date' => $order['order_date'],
                'order_amount' => $order['order_amount'],
                'notes' => $order['notes'] ?? ''
            ];
            
            $grouped_orders[$key] = [
                'order_info' => $clean_order,
                'items' => []
            ];
        }
        if ($order['item_id']) {
            // 清理商品数据
            $clean_item = [
                'id' => $order['item_id'],
                'ingredient_id' => $order['ingredient_id'],
                'ingredient_name' => $order['ingredient_name'],
                'ingredient_unit' => $order['ingredient_unit'],
                'quantity' => $order['quantity'],
                'unit_price' => $order['unit_price'],
                'total_price' => $order['total_price'],
                'notes' => $order['item_notes'] ?? ''
            ];
            $grouped_orders[$key]['items'][] = $clean_item;
        }
    }
    
    echo "<h1>分组后的数据</h1>";
    echo "<pre>";
    print_r($grouped_orders);
    echo "</pre>";
    
    echo "<h1>JSON编码测试</h1>";
    foreach ($grouped_orders as $key => $group) {
        echo "<h3>采购单: " . htmlspecialchars($group['order_info']['order_number']) . "</h3>";
        
        $json = json_encode($group);
        if ($json === false) {
            echo "<p style='color: red;'>JSON编码失败: " . json_last_error_msg() . "</p>";
        } else {
            echo "<p style='color: green;'>JSON编码成功</p>";
            echo "<p><strong>JSON长度:</strong> " . strlen($json) . " 字符</p>";
            echo "<p><strong>HTML转义后:</strong></p>";
            echo "<textarea style='width: 100%; height: 100px;'>" . htmlspecialchars($json) . "</textarea>";
            
            echo "<p><strong>带HEX转义:</strong></p>";
            $hex_json = json_encode($group, JSON_HEX_QUOT | JSON_HEX_APOS);
            echo "<textarea style='width: 100%; height: 100px;'>" . htmlspecialchars($hex_json) . "</textarea>";
            
            // 测试解析
            $parsed = json_decode($json, true);
            if ($parsed === null) {
                echo "<p style='color: red;'>JSON解析失败: " . json_last_error_msg() . "</p>";
            } else {
                echo "<p style='color: green;'>JSON解析成功</p>";
            }
        }
        echo "<hr>";
    }
    
} catch (Exception $e) {
    echo "<h1>错误</h1>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>采购单数据调试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        pre { background: #f5f5f5; padding: 15px; border-radius: 5px; overflow-x: auto; }
        h1 { color: #333; border-bottom: 2px solid #007cba; padding-bottom: 10px; }
        h3 { color: #007cba; }
        textarea { font-family: monospace; font-size: 12px; }
    </style>
</head>
<body>
    <h1>JavaScript测试</h1>
    <button onclick="testJsonParsing()">测试JSON解析</button>
    <div id="test-results"></div>
    
    <script>
        function testJsonParsing() {
            const results = document.getElementById('test-results');
            results.innerHTML = '<h3>测试结果:</h3>';
            
            // 获取页面中的所有textarea
            const textareas = document.querySelectorAll('textarea');
            
            textareas.forEach((textarea, index) => {
                const jsonStr = textarea.value;
                const resultDiv = document.createElement('div');
                resultDiv.style.margin = '10px 0';
                resultDiv.style.padding = '10px';
                resultDiv.style.border = '1px solid #ddd';
                resultDiv.style.borderRadius = '5px';
                
                try {
                    const parsed = JSON.parse(jsonStr);
                    resultDiv.innerHTML = `
                        <p><strong>测试 ${index + 1}:</strong> <span style="color: green;">✓ 解析成功</span></p>
                        <p>订单号: ${parsed.order_info.order_number}</p>
                        <p>商品数量: ${parsed.items.length}</p>
                    `;
                    resultDiv.style.backgroundColor = '#d4edda';
                } catch (e) {
                    resultDiv.innerHTML = `
                        <p><strong>测试 ${index + 1}:</strong> <span style="color: red;">✗ 解析失败</span></p>
                        <p>错误: ${e.message}</p>
                    `;
                    resultDiv.style.backgroundColor = '#f8d7da';
                }
                
                results.appendChild(resultDiv);
            });
        }
    </script>
</body>
</html>
