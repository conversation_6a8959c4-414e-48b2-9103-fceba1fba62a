<?php
/**
 * 模块生成工具
 * 用于快速生成新模块的基础文件结构
 */

class ModuleGenerator
{
    private $projectRoot;
    private $modulesPath;
    
    public function __construct()
    {
        $this->projectRoot = dirname(__DIR__);
        $this->modulesPath = $this->projectRoot . '/modules';
    }
    
    /**
     * 生成模块
     */
    public function generateModule($config)
    {
        $moduleName = $config['name'];
        $moduleTitle = $config['title'];
        $moduleIcon = $config['icon'];
        $moduleDescription = $config['description'];
        
        echo "正在生成模块: {$moduleTitle} ({$moduleName})\n";
        
        // 创建模块目录
        $modulePath = $this->modulesPath . '/' . $moduleName;
        if (!is_dir($modulePath)) {
            mkdir($modulePath, 0755, true);
        }
        
        // 生成控制器
        $this->generateController($modulePath, $config);
        
        // 生成模板
        $this->generateTemplate($modulePath, $config);
        
        // 生成样式
        $this->generateStyle($modulePath, $config);
        
        // 生成脚本
        $this->generateScript($modulePath, $config);
        
        // 生成侧边栏
        $this->generateSidebar($modulePath, $config);
        
        // 生成入口文件
        $this->generateIndex($modulePath, $config);
        
        echo "模块生成完成: {$modulePath}\n";
    }
    
    /**
     * 生成控制器文件
     */
    private function generateController($modulePath, $config)
    {
        $className = ucfirst($config['name']) . 'Controller';
        $content = $this->getControllerTemplate($config, $className);
        file_put_contents($modulePath . '/' . $className . '.php', $content);
    }
    
    /**
     * 生成模板文件
     */
    private function generateTemplate($modulePath, $config)
    {
        $content = $this->getTemplateTemplate($config);
        file_put_contents($modulePath . '/template.php', $content);
    }
    
    /**
     * 生成样式文件
     */
    private function generateStyle($modulePath, $config)
    {
        $content = $this->getStyleTemplate($config);
        file_put_contents($modulePath . '/style.css', $content);
    }
    
    /**
     * 生成脚本文件
     */
    private function generateScript($modulePath, $config)
    {
        $content = $this->getScriptTemplate($config);
        file_put_contents($modulePath . '/main.js', $content);
    }
    
    /**
     * 生成侧边栏文件
     */
    private function generateSidebar($modulePath, $config)
    {
        $content = $this->getSidebarTemplate($config);
        file_put_contents($modulePath . '/sidebar.php', $content);
    }
    
    /**
     * 生成入口文件
     */
    private function generateIndex($modulePath, $config)
    {
        $className = ucfirst($config['name']) . 'Controller';
        $content = $this->getIndexTemplate($config, $className);
        file_put_contents($modulePath . '/index.php', $content);
    }
    
    /**
     * 获取控制器模板
     */
    private function getControllerTemplate($config, $className)
    {
        return "<?php
/**
 * {$config['title']}控制器
 */
require_once dirname(__DIR__, 2) . '/includes/BaseController.php';
require_once dirname(__DIR__, 2) . '/includes/helpers.php';

class {$className} extends BaseController
{
    protected function init()
    {
        \$this->setTemplateData([
            'page_title' => '{$config['title']} - ' . \$this->config['name'],
            'current_module' => '{$config['name']}'
        ]);
    }

    public function handleRequest()
    {
        switch (\$this->request['action']) {
            case 'create':
                return \$this->create();
            case 'edit':
                return \$this->edit();
            case 'delete':
                return \$this->delete();
            case 'view':
                return \$this->view();
            case 'index':
            default:
                return \$this->index();
        }
    }

    /**
     * 列表页面
     */
    private function index()
    {
        try {
            // 获取搜索参数
            \$search = \$this->request['get']['search'] ?? '';

            // 构建查询条件
            \$where = ['status = 1'];
            \$params = [];

            if (\$search) {
                \$where[] = 'name LIKE ?';
                \$params[] = '%' . \$search . '%';
            }

            \$whereClause = 'WHERE ' . implode(' AND ', \$where);

            // 获取数据列表
            \$items = \$this->db->fetchAll(\"
                SELECT * FROM {$config['table']}
                \$whereClause
                ORDER BY created_at DESC
            \", \$params);

            \$this->setTemplateData([
                'items' => \$items,
                'search' => \$search
            ]);

        } catch (Exception \$e) {
            // 使用模拟数据
            \$this->setTemplateData([
                'items' => \$this->getMockData(),
                'search' => \$search ?? ''
            ]);
        }

        \$this->render('template.php');
    }

    /**
     * 创建
     */
    private function create()
    {
        if (\$this->request['method'] === 'POST') {
            try {
                \$data = [
                    'name' => trim(\$this->request['post']['name']),
                    // 添加其他字段
                ];

                \$errors = \$this->validateRequired(\$data, ['name']);
                if (!empty(\$errors)) {
                    throw new Exception(implode(', ', \$errors));
                }

                \$this->db->insert('{$config['table']}', \$data);
                \$this->redirect('index.php', '添加成功', 'success');

            } catch (Exception \$e) {
                \$this->setTemplateData('error_message', \$e->getMessage());
            }
        }

        \$this->render('create-template.php');
    }

    /**
     * 编辑
     */
    private function edit()
    {
        \$id = intval(\$this->request['get']['id'] ?? 0);
        if (!\$id) {
            \$this->redirect('index.php', '参数错误', 'error');
            return;
        }

        if (\$this->request['method'] === 'POST') {
            try {
                \$data = [
                    'name' => trim(\$this->request['post']['name']),
                    // 添加其他字段
                ];

                \$this->db->update('{$config['table']}', \$data, 'id = ?', [\$id]);
                \$this->redirect('index.php', '更新成功', 'success');

            } catch (Exception \$e) {
                \$this->setTemplateData('error_message', \$e->getMessage());
            }
        }

        // 获取数据
        try {
            \$item = \$this->db->fetchOne(\"SELECT * FROM {$config['table']} WHERE id = ?\", [\$id]);
        } catch (Exception \$e) {
            \$item = \$this->getMockData()[0];
        }

        \$this->setTemplateData('item', \$item);
        \$this->render('edit-template.php');
    }

    /**
     * 删除
     */
    private function delete()
    {
        \$id = intval(\$this->request['get']['id'] ?? 0);
        if (!\$id) {
            \$this->redirect('index.php', '参数错误', 'error');
            return;
        }

        try {
            \$this->db->update('{$config['table']}', ['status' => 0], 'id = ?', [\$id]);
            \$this->redirect('index.php', '删除成功', 'success');
        } catch (Exception \$e) {
            \$this->redirect('index.php', '删除失败：' . \$e->getMessage(), 'error');
        }
    }

    /**
     * 获取模拟数据
     */
    private function getMockData()
    {
        return [
            [
                'id' => 1,
                'name' => '示例数据1',
                'created_at' => '2024-01-15 10:30:00'
            ],
            [
                'id' => 2,
                'name' => '示例数据2',
                'created_at' => '2024-01-16 11:30:00'
            ]
        ];
    }
}
?>";
    }
    
    /**
     * 获取模板模板
     */
    private function getTemplateTemplate($config)
    {
        return "<!DOCTYPE html>
<html lang=\"zh-CN\">
<head>
    <meta charset=\"UTF-8\">
    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">
    <title><?= htmlspecialchars(\$page_title) ?></title>
    <link href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\" rel=\"stylesheet\">
    <link href=\"../../includes/styles.css?v=<?= time() ?>\" rel=\"stylesheet\">
    <link href=\"../../assets/css/common.css?v=<?= time() ?>\" rel=\"stylesheet\">
    <link href=\"style.css?v=<?= time() ?>\" rel=\"stylesheet\">
</head>
<body>

<?php include 'sidebar.php'; ?>

<!-- 主内容区 -->
<div class=\"main-content\">
    <!-- 顶部栏 -->
    <div class=\"topbar\">
        <div class=\"topbar-left\">
            <h1><i class=\"{$config['icon']}\"></i> {$config['title']}</h1>
        </div>
        <div class=\"topbar-right\">
            <div class=\"btn-group\">
                <a href=\"create.php\" class=\"btn btn-primary\">
                    <i class=\"fas fa-plus\"></i> 添加
                </a>
                <button type=\"button\" class=\"btn btn-outline-secondary\" onclick=\"exportData()\">
                    <i class=\"fas fa-download\"></i> 导出
                </button>
            </div>
        </div>
    </div>

    <div class=\"content\">
        <!-- 搜索框 -->
        <div class=\"search-box\">
            <form method=\"GET\">
                <div class=\"form-row\">
                    <div class=\"form-group\">
                        <label class=\"form-label\">搜索</label>
                        <input type=\"text\" class=\"form-control\" name=\"search\" value=\"<?= htmlspecialchars(\$search) ?>\" placeholder=\"输入关键词\">
                    </div>
                    <div class=\"form-group\">
                        <button type=\"submit\" class=\"btn btn-primary\">
                            <i class=\"fas fa-search\"></i> 搜索
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- 数据列表 -->
        <div class=\"table-container\">
            <div class=\"card-header\">
                <div class=\"card-title\">
                    <i class=\"fas fa-list\"></i> 数据列表
                    <span class=\"badge badge-secondary\"><?= count(\$items) ?> 条记录</span>
                </div>
            </div>
            
            <?php if (!empty(\$items)): ?>
            <table class=\"table\">
                <thead>
                    <tr>
                        <th>名称</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach (\$items as \$item): ?>
                    <tr>
                        <td><?= htmlspecialchars(\$item['name']) ?></td>
                        <td><?= date('Y-m-d H:i', strtotime(\$item['created_at'])) ?></td>
                        <td>
                            <div class=\"btn-group\">
                                <a href=\"edit.php?id=<?= \$item['id'] ?>\" class=\"btn btn-sm btn-outline-primary\" title=\"编辑\">
                                    <i class=\"fas fa-edit\"></i>
                                </a>
                                <a href=\"?action=delete&id=<?= \$item['id'] ?>\" class=\"btn btn-sm btn-outline-danger\" title=\"删除\" 
                                   onclick=\"return confirm('确定要删除吗？')\">
                                    <i class=\"fas fa-trash\"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            <?php else: ?>
            <div class=\"empty-state\">
                <i class=\"{$config['icon']}\"></i>
                <h5>暂无数据</h5>
                <p>点击上方按钮添加第一条记录</p>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script src=\"../../assets/js/common.js\"></script>
<script src=\"main.js\"></script>
</body>
</html>";
    }
    
    /**
     * 获取其他模板方法...
     */
    private function getStyleTemplate($config)
    {
        return "/* {$config['title']}模块样式 */

/* 自定义样式 */
.module-specific {
    /* 添加模块特定样式 */
}";
    }
    
    private function getScriptTemplate($config)
    {
        return "/**
 * {$config['title']}模块 JavaScript
 */

document.addEventListener('DOMContentLoaded', function() {
    initializePage();
});

function initializePage() {
    console.log('{$config['title']}模块已加载');
}

function exportData() {
    alert('导出功能开发中');
}";
    }
    
    private function getSidebarTemplate($config)
    {
        return file_get_contents($this->modulesPath . '/dashboard/sidebar.php');
    }
    
    private function getIndexTemplate($config, $className)
    {
        return "<?php
/**
 * {$config['title']}模块入口文件
 */

// 引入控制器
require_once '{$className}.php';

// 创建控制器实例并处理请求
\$controller = new {$className}();
\$controller->handleRequest();
?>";
    }
}

// 使用示例
if (php_sapi_name() === 'cli') {
    $generator = new ModuleGenerator();
    
    // 定义要生成的模块
    $modules = [
        [
            'name' => 'inbound',
            'title' => '入库管理',
            'icon' => 'fas fa-arrow-down',
            'description' => '食材入库记录管理',
            'table' => 'inbound_records'
        ],
        [
            'name' => 'outbound',
            'title' => '出库管理',
            'icon' => 'fas fa-arrow-up',
            'description' => '食材出库记录管理',
            'table' => 'outbound_records'
        ],
        [
            'name' => 'inventory',
            'title' => '库存查询',
            'icon' => 'fas fa-search',
            'description' => '库存查询和统计',
            'table' => 'inventory'
        ],
        [
            'name' => 'stocktaking',
            'title' => '盘点管理',
            'icon' => 'fas fa-clipboard-check',
            'description' => '库存盘点管理',
            'table' => 'stocktaking_records'
        ],
        [
            'name' => 'damage',
            'title' => '报损管理',
            'icon' => 'fas fa-exclamation-triangle',
            'description' => '食材报损管理',
            'table' => 'damage_records'
        ]
    ];
    
    foreach ($modules as $module) {
        $generator->generateModule($module);
    }
    
    echo "所有模块生成完成！\n";
}
?>
