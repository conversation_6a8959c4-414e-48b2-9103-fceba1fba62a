# 🏫 学校食堂管理系统 (模块化版本)

## 📋 系统简介

基于PHP模块化架构开发的学校食堂食材管理系统，采用MVC设计模式，支持食材管理、采购管理、库存管理、出入库记录、数据统计等完整业务流程。

## 🔧 环境要求

- **PHP 7.2+** (已针对PHP 7.2优化)
- **MySQL 5.6+** (已针对MySQL 5.6优化)
- **Web服务器** (Apache/Nginx/内置服务器)

## 🚀 快速启动

### 一键启动（推荐）

```cmd
start.bat
```

这个脚本会自动：

- ✅ 启动PHP内置服务器
- ✅ 在8000端口运行系统
- ✅ 自动打开浏览器访问系统

### 手动启动

如果一键启动失败，可以手动执行：

```cmd
# 1. 配置数据库连接
# 编辑 config/database.php 文件

# 2. 初始化数据库表
php create-tables.php

# 3. 启动服务器
php -S localhost:8000
```

## 🌐 访问系统

启动成功后访问：<http://localhost:8000>

系统会自动重定向到模块化仪表板。

## 🎯 功能模块

### 📊 仪表板模块
- 实时数据统计
- 库存预警提醒
- 快捷操作入口

### 🥬 基础数据管理
- **食材管理** - 食材信息维护和批量导入
- **分类管理** - 两级分类管理（一级分类 + 二级分类）
- **供应商管理** - 供应商信息和评级管理
- **采购管理** - 采购订单创建和管理，支持批量导入功能

### 📦 库存管理
- **食材入库** - 入库单创建和管理
- **食材出库** - 出库单创建和库存扣减
- **库存盘点** - 定期盘点和差异处理
- **库存查询** - 实时库存查询和预警
- **食材报损** - 报损单创建和库存调整

## 📁 项目结构

```text
学校食材管理系统/
├── modules/                           # 模块化功能目录
│   ├── dashboard/                     # 仪表板模块
│   │   ├── index.php                  # 模块入口
│   │   ├── DashboardController.php    # 控制器
│   │   ├── template.php               # 视图模板
│   │   ├── style.css                  # 模块样式
│   │   └── main.js                    # 模块脚本
│   ├── ingredients/                   # 食材管理模块
│   ├── categories/                    # 分类管理模块
│   ├── suppliers/                     # 供应商管理模块
│   ├── purchase/                      # 采购管理模块
│   ├── inbound/                       # 入库管理模块
│   └── inventory/                     # 库存管理模块
├── includes/                          # 公共文件
│   ├── BaseController.php             # 基础控制器
│   ├── Database.php                   # 数据库类
│   └── sidebar-modular.php            # 模块化侧边栏
├── assets/                            # 静态资源
│   ├── css/                           # 全局样式
│   └── js/                            # 全局脚本
├── config/                            # 配置文件
├── uploads/                           # 上传文件
├── scripts/                           # 工具脚本
├── test/                              # 测试脚本目录
│   ├── index.php                      # 测试脚本中心
│   ├── check-php.php                  # PHP环境检查
│   ├── check-php-extensions.php       # PHP扩展详细检查
│   ├── check-categories.php           # 分类数据检查
│   ├── debug-categories-list.php      # 调试分类列表
│   ├── check-duplicate-categories.php # 检查重复分类
│   ├── test-api-direct.php            # 直接API测试
│   ├── test-category-api.php          # 分类API功能测试
│   ├── test-categories-query.php      # 分类查询测试
│   ├── test-category-select.php       # 分类选择测试
│   ├── debug-ingredient-create.php    # 调试食材创建
│   ├── test-supplier-fields.php       # 测试供应商字段
│   ├── fix-suppliers-table.php        # 修复供应商表
│   ├── debug-supplier-insert.php      # 调试供应商插入
│   └── test-purchase-table-structure.php # 检查采购单表结构
├── update/                            # 数据库升级脚本目录
│   ├── README.md                      # 升级脚本说明文档
│   ├── update-tables.php              # 基础表结构升级
│   ├── update-suppliers-table.php     # 供应商表升级
│   ├── update-purchase-tables.php     # 采购表升级
│   └── upgrade-categories-table.php   # 分类表二级分类升级
├── start.bat                          # 启动脚本
└── index.php                          # 系统入口
```

## 🧪 测试与调试

### 测试脚本中心

系统提供了完整的测试脚本集合，用于调试和验证各项功能：

**访问地址：** `http://localhost:8000/test/`

### 测试脚本分类

#### 🐘 PHP环境测试
- `check-php.php` - PHP环境检查（版本、扩展、配置）
- `check-php-extensions.php` - PHP扩展详细检查

#### 🗄️ 数据库测试
- `check-categories.php` - 检查分类数据完整性
- `debug-categories-list.php` - 调试分类列表显示问题
- `check-duplicate-categories.php` - 检查和修复重复分类

#### 🔌 API接口测试
- `test-api-direct.php` - 直接测试API端点
- `test-category-api.php` - 分类API功能完整测试

#### 🏷️ 分类功能测试
- `test-categories-query.php` - 分类查询性能测试
- `test-category-select.php` - 分类选择功能测试

#### 🥬 食材功能测试
- `debug-ingredient-create.php` - 调试食材创建页面

#### 🚚 供应商功能测试
- `test-supplier-fields.php` - 测试供应商字段完整性
- `fix-suppliers-table.php` - 修复供应商表结构
- `debug-supplier-insert.php` - 调试供应商插入操作

#### 📦 采购功能测试
- `test-purchase-table-structure.php` - 检查采购单表结构和字段完整性
- `test-purchase-view-dropdown.php` - 测试采购单查看详情和下拉菜单修复
- `test-purchase-view-unified-style.php` - 测试采购单详情页面统一风格修复
- `test-purchase-direct-actions.php` - 测试采购单直接操作按钮修改

#### 📊 Excel导入功能测试
- `test-excel-reader-upgrade.php` - 测试ExcelReader升级到类PhpSpreadsheet功能
- `test-purchase-order-form-import.php` - 测试采购订货单格式导入功能
- `debug-excel-import.php` - 调试Excel导入问题
- `debug-import-step-by-step.php` - 逐步调试导入过程
- `test-import-fix.php` - 测试导入修复效果
- `debug-import-detailed.php` - 详细调试导入问题
- `setup-test-data.php` - 设置测试数据解决导入0条记录问题
- `analyze-test-xlsx-format.php` - 分析test.xlsx格式结构
- `test-xlsx-template-solution.php` - 基于test.xlsx的完整模板解决方案
- `analyze-reference-logic.php` - 分析参考PhpSpreadsheet提取逻辑
- `test-reference-logic-implementation.php` - 测试基于参考逻辑的完整实现

### 使用建议

1. **开发调试时** - 使用相应模块的测试脚本快速定位问题
2. **部署前检查** - 运行环境检查脚本确保系统正常
3. **性能优化** - 使用性能测试脚本分析瓶颈
4. **数据维护** - 使用数据检查脚本保持数据完整性

## 📥 批量导入功能

### 采购单批量导入

系统支持通过CSV文件批量导入采购单数据，提高数据录入效率。

#### 使用步骤

简洁直观的单页面设计，左右分栏布局：

1. **查看格式要求** 📋
   - 右侧查看CSV文件格式说明
   - 了解必填字段和注意事项
   - 下载标准模板文件

2. **上传文件** ⬆️
   - 左侧拖拽文件或点击选择
   - 支持实时文件预览
   - 配置导入选项

3. **开始导入** ✅
   - 点击"开始导入"按钮
   - 查看导入结果统计

#### CSV文件格式

文件应包含以下列（按顺序）：

| 列名 | 是否必填 | 格式说明 | 示例 |
|------|----------|----------|------|
| 供应商名称 | ✅ | 必须在系统中已存在 | 绿色蔬菜供应商 |
| 订货日期 | ✅ | YYYY-MM-DD格式 | 2024-12-01 |
| 交货日期 | ❌ | YYYY-MM-DD格式，可为空 | 2024-12-02 |
| 备注 | ❌ | 文本，可为空 | 紧急采购 |
| 食材名称 | ✅ | 必须在系统中已存在 | 白菜 |
| 数量 | ✅ | 数字，大于0 | 50 |
| 单价 | ❌ | 数字，可为0 | 2.50 |

#### 界面特色

- 🎨 **简洁设计** - 左右分栏布局，信息清晰明了
- 📱 **响应式界面** - 完美适配桌面和移动设备
- 🖱️ **拖拽上传** - 支持拖拽文件到上传区域
- 👁️ **实时预览** - 上传后立即预览文件内容
- ✅ **智能验证** - 文件格式和大小自动检查
- 📋 **格式指导** - 右侧提供详细的格式说明和示例

#### 注意事项

- 第一行为标题行，导入时会自动跳过
- 供应商名称和食材名称必须在系统中已存在
- 相同供应商、日期和备注的订单会自动合并
- 导入成功后会显示成功和失败的记录数量
- 测试文件位于：`test/purchase_import_test.csv`

## 🏷️ 两级分类系统

### 分类层级结构

系统支持两级分类管理，提供更精细的食材分类：

#### 📁 一级分类（主分类）
- 蔬菜类、肉类、水产类、粮食类、调料类等
- 可以包含多个二级分类
- 显示子分类统计数量
- 删除前需要先删除所有子分类

#### 🏷️ 二级分类（子分类）
- 叶菜类、根茎类、瓜果类等
- 必须归属于某个一级分类
- 可以直接关联食材
- 支持独立管理和编辑

### 功能特色

- **层级显示** - 清晰的父子关系展示
- **级别标识** - 不同颜色的级别标识
- **智能筛选** - 按级别、父分类筛选
- **路径显示** - 完整的分类路径展示
- **删除保护** - 有子分类时不能删除父分类
- **统计功能** - 子分类数量和食材统计
- **批量导入** - 支持CSV格式的批量导入功能

### 数据库升级

使用两级分类功能前，需要执行数据库升级：

```bash
mysql -u用户名 -p数据库名 < scripts/database/upgrade_categories_to_two_level.sql
```

升级内容：
- 添加 `parent_id` 字段（父分类ID）
- 添加 `level` 字段（分类级别）
- 添加 `description` 字段（分类描述）
- 创建分类树视图和路径函数
- 插入示例二级分类数据

## 📥 分类导入功能

### 导入格式

支持CSV格式的批量导入，文件格式如下：

| 列名 | 说明 | 必填 | 示例 |
|------|------|------|------|
| 分类名称 | 分类的名称 | ✅ | 蔬菜类 |
| 父分类名称 | 父分类名称，留空为一级分类 | ❌ | 蔬菜类 |
| 分类代码 | 分类代码，留空自动生成 | ❌ | VEG |
| 描述 | 分类描述信息 | ❌ | 各种新鲜蔬菜 |
| 排序 | 排序数字，越小越靠前 | ❌ | 1 |

### 导入步骤

1. **下载模板** - 点击「下载模板」获取标准CSV模板
2. **填写数据** - 按照格式要求填写分类信息
3. **上传文件** - 支持拖拽上传或点击选择
4. **选择选项** - 设置跳过重复、更新现有等选项
5. **开始导入** - 执行导入并查看结果报告

### 导入特色

- **智能验证** - 自动验证数据格式和完整性
- **错误处理** - 详细的错误报告和跳过机制
- **兼容性** - 支持数据库升级前后的导入
- **批量处理** - 一次可导入大量分类数据
- **拖拽上传** - 现代化的文件上传界面

## 🔄 数据库升级

### 升级脚本目录

系统提供了专门的数据库升级脚本目录 `update/`，用于在系统更新时对数据库结构进行必要的修改。

**访问地址：** `http://localhost:8000/update/`

### 升级脚本说明

#### 🗄️ update-tables.php
- **功能**: 基础数据表结构升级
- **用途**: 创建和更新系统核心数据表
- **执行时机**: 系统初始化或基础表结构变更时

#### 🚚 update-suppliers-table.php
- **功能**: 供应商表结构升级
- **用途**: 更新供应商管理相关的数据表结构
- **执行时机**: 供应商模块功能更新时

#### 📦 update-purchase-tables.php
- **功能**: 采购相关表结构升级
- **用途**: 更新采购订单、入库等相关数据表
- **执行时机**: 采购管理模块功能更新时

#### 🏷️ upgrade-categories-table.php
- **功能**: 食材分类表升级支持二级分类
- **用途**: 将分类表升级为支持两级分类结构
- **执行时机**: 启用二级分类功能时

### 升级使用方法

1. **备份数据库** 💾 - 在执行任何升级脚本前，请先备份现有数据库
2. **按顺序执行** 📋 - 建议按照文件编号顺序执行升级脚本
3. **检查结果** ✅ - 每次升级后检查数据库结构和数据完整性
4. **测试功能** 🧪 - 升级完成后测试相关功能是否正常

### 升级注意事项

- 升级脚本具有幂等性，可以重复执行而不会造成数据损坏
- 每个脚本都会检查当前数据库状态，只执行必要的升级操作
- 如果升级过程中出现错误，请检查数据库连接和权限设置
- 建议在测试环境中先验证升级脚本的正确性

## 🔧 故障排除

### 常见问题

1. **PHP未找到**
   - 安装XAMPP或WAMP
   - 或单独安装PHP并添加到PATH

2. **Composer未找到**
   - 从 https://getcomposer.org/ 下载安装

3. **数据库连接失败**
   - 检查网络连接
   - 确认数据库服务器运行正常
   - 检查防火墙设置

4. **MySQL客户端未找到**
   - 安装MySQL客户端
   - 或使用XAMPP/WAMP中的MySQL

### 检查命令

```cmd
# 检查PHP
php --version

# 检查Composer
composer --version

# 检查MySQL客户端
mysql --version

# 测试数据库连接
mysql -h 82.156.63.73 -u sc -ppw5K4SsM7kZsjdxy -e "SELECT 1;" sc
```

## 📞 技术支持

如有问题请联系：
- 邮箱：<EMAIL>
- 电话：400-123-4567

## 📅 最新更新

### 2024-12-29
- ✅ 完成移动端入库页面的采购单选择功能
- ✅ 实现移动端与Web端功能一致性
- ✅ 优化移动端界面布局和用户体验
- ✅ 添加分类筛选功能，支持一级和二级分类筛选
- ✅ 优化表格布局，移除徽章图标，改进重量输入和商品名称的水平对齐
- ✅ 添加表格标题头，提升数据可读性
- ✅ 实现商品数量统计显示（总数、已验证、未验证）
- ✅ 通过拍照状态指示验证完成情况
- ✅ **优化拍照按钮布局**：去除按钮文字，只保留图标，避免表格错位
- ✅ **优化统计项布局**：确保商品数量统计始终保持在同一行显示
- ✅ **优化状态栏和拍照列**：简化状态显示，增加拍照列宽度
- ✅ **iOS样式统一优化**：解决苹果手机上表单元素样式不统一问题

### 界面优化详情

#### 📷 拍照按钮优化
- **问题**：原来的拍照按钮包含文字，在表格布局中容易造成错位
- **解决方案**：
  - 去除所有按钮文字，只保留图标显示
  - 简化title属性提示（拍照、查看、重新拍摄）
  - 统一按钮尺寸，确保布局整齐
  - 不同状态使用不同颜色区分（未拍照：蓝紫色，已拍照：绿色，查看：蓝色）
  - 全面覆盖所有场景：手动添加模式、表格模式、模态框等
- **效果**：表格布局更加整齐，界面更简洁，避免了文字导致的错位问题

#### 📊 统计项布局优化
- **问题**：商品数量统计在小屏幕上可能换行显示，影响界面整齐度
- **解决方案**：
  - 使用 flex-wrap: nowrap 禁止换行
  - 减小间距和内边距，优化空间利用
  - 添加响应式样式，小屏幕下进一步压缩尺寸
  - 使用 flex-shrink: 0 防止项目被压缩变形
  - 支持横向滚动处理极端情况
- **效果**：无论屏幕大小，统计项始终保持在同一行，布局更加稳定

#### 📊 状态栏和拍照列优化
- **问题**：状态栏文字冗余，拍照列空间不足影响操作体验
- **解决方案**：
  - 简化状态显示：去掉"已完成"、"待拍照"、"待称重"文字，只保留图标
  - 增加拍照列宽度：从0.8fr增加到1fr，提供更多操作空间
  - 响应式适配：小屏幕下从0.7fr增加到0.8fr
  - 保持图标语义：✅(已完成)、📷(待拍照)、⏰(待称重)
- **效果**：界面更简洁，拍照按钮操作区域更宽敞，用户体验更佳

#### 🍎 iOS样式统一优化
- **问题**：苹果手机上表单元素样式不统一，影响视觉一致性
- **解决方案**：
  - 重置系统样式：使用-webkit-appearance: none移除iOS默认样式
  - 统一视觉风格：所有表单元素使用相同的边框、圆角、内边距
  - 强制颜色显示：使用!important确保文字颜色不被系统覆盖
  - 自定义下拉箭头：使用SVG图标替换系统默认箭头
  - iOS字体优化：使用-apple-system字体栈确保最佳显示
  - 高度统一控制：所有表单元素统一48px高度，使用@supports检测iOS
- **效果**：iOS设备上所有表单元素样式完全统一，高度一致，提供完美的用户体验

---

*本系统专门针对PHP 7.2和MySQL 5.6环境优化，确保在较低版本环境下稳定运行。*
