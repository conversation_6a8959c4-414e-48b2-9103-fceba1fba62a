<?php require_once dirname(__DIR__, 2) . '/includes/header-modular.php'; ?>

<div class="main-content">
    <?php require_once dirname(__DIR__, 2) . '/includes/sidebar-modular.php'; ?>

    <div class="content">
        <div class="content-header">
            <h1>
                <i class="fas fa-cog"></i>
                权限维护
            </h1>
            <div class="header-actions">
                <a href="index.php?action=roles" class="btn btn-secondary">
                    <i class="fas fa-id-badge"></i>
                    角色管理
                </a>
                <a href="index.php?action=permissions" class="btn btn-secondary">
                    <i class="fas fa-key"></i>
                    权限授权
                </a>
            </div>
        </div>

        <?php if (!empty($error)): ?>
        <div class="alert alert-danger"><i class="fas fa-exclamation-triangle"></i> <?= htmlspecialchars($error) ?></div>
        <?php elseif (!empty($_GET['success'])): ?>
        <div class="alert alert-success"><i class="fas fa-check-circle"></i> 操作成功</div>
        <?php endif; ?>

        <!-- 权限导入 -->
        <div class="card">
            <div class="card-header">
                <h3><i class="fas fa-upload"></i> 权限导入</h3>
            </div>
            <div class="card-body">
                <p class="text-muted">快速导入系统预设的基础权限项，已存在的权限不会重复创建。</p>
                <form method="POST" action="index.php?action=permission_manage" onsubmit="return confirm('确定要导入基础权限项吗？')">
                    <input type="hidden" name="operation" value="seed_defaults">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-upload"></i> 导入基础权限
                    </button>
                </form>
            </div>
        </div>

        <!-- 新增权限 -->
        <div class="card">
            <div class="card-header">
                <h3><i class="fas fa-plus-circle"></i> <?= $editingPermission ? '编辑权限' : '新增权限' ?></h3>
            </div>
            <div class="card-body">
                <form method="POST" action="index.php?action=permission_manage">
                    <input type="hidden" name="operation" value="save">
                    <?php if ($editingPermission): ?>
                        <input type="hidden" name="id" value="<?= intval($editingPermission['id']) ?>">
                    <?php endif; ?>
                    <div class="form-grid">
                        <div class="form-group">
                            <label>权限标识<span class="required">*</span></label>
                            <input type="text" name="name" class="form-control" 
                                   placeholder="如 ingredient.create" 
                                   value="<?= htmlspecialchars($editingPermission['name'] ?? '') ?>" 
                                   required>
                            <small class="form-text">建议格式：模块.操作，如 ingredient.create</small>
                        </div>
                        <div class="form-group">
                            <label>显示名称<span class="required">*</span></label>
                            <input type="text" name="display_name" class="form-control" 
                                   placeholder="如 食材-新增" 
                                   value="<?= htmlspecialchars($editingPermission['display_name'] ?? '') ?>" 
                                   required>
                        </div>
                        <div class="form-group">
                            <label>模块分组</label>
                            <input type="text" name="module" class="form-control" 
                                   placeholder="如 ingredients" 
                                   value="<?= htmlspecialchars($editingPermission['module'] ?? '') ?>">
                            <small class="form-text">用于权限分组显示</small>
                        </div>
                        <div class="form-group">
                            <label>排序</label>
                            <input type="number" name="sort_order" class="form-control" 
                                   value="<?= intval($editingPermission['sort_order'] ?? 0) ?>">
                        </div>
                        <div class="form-group">
                            <label>状态</label>
                            <select name="status" class="form-control">
                                <option value="1" <?= (($editingPermission['status'] ?? 1) == 1) ? 'selected' : '' ?>>启用</option>
                                <option value="0" <?= (($editingPermission['status'] ?? 1) == 0) ? 'selected' : '' ?>>停用</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> 保存
                        </button>
                        <a href="index.php?action=permission_manage" class="btn btn-outline-secondary">
                            <i class="fas fa-undo"></i> 重置
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- 权限列表 -->
        <div class="card">
            <div class="card-header">
                <h3><i class="fas fa-list"></i> 权限列表</h3>
            </div>
            <div class="card-body">
                <div class="table-toolbar">
                    <input type="text" class="form-control" id="permissionSearch" 
                           placeholder="搜索权限标识或显示名称..." 
                           style="max-width: 300px;" 
                           oninput="filterPermissions()">
                </div>
                
                <table class="table" id="permissionTable">
                    <thead>
                        <tr>
                            <th style="width: 20%">权限标识</th>
                            <th style="width: 25%">显示名称</th>
                            <th style="width: 15%">模块</th>
                            <th style="width: 10%">状态</th>
                            <th style="width: 10%">排序</th>
                            <th style="width: 20%">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (!empty($permissions)): ?>
                            <?php foreach ($permissions as $p): ?>
                            <tr data-search="<?= strtolower(htmlspecialchars($p['name'] . ' ' . ($p['display_name'] ?? ''))) ?>">
                                <td><code><?= htmlspecialchars($p['name']) ?></code></td>
                                <td><?= htmlspecialchars($p['display_name'] ?? '') ?></td>
                                <td><?= htmlspecialchars($p['module'] ?? '') ?></td>
                                <td>
                                    <?= intval($p['status'] ?? 1) === 1 ? 
                                        '<span class="badge badge-success">启用</span>' : 
                                        '<span class="badge badge-secondary">停用</span>' ?>
                                </td>
                                <td><?= intval($p['sort_order'] ?? 0) ?></td>
                                <td>
                                    <div class="action-buttons">
                                        <a href="index.php?action=permission_manage&operation=edit&id=<?= intval($p['id']) ?>" 
                                           class="btn btn-sm btn-warning">
                                            <i class="fas fa-edit"></i> 编辑
                                        </a>
                                        <a href="index.php?action=permission_manage&operation=toggle&id=<?= intval($p['id']) ?>" 
                                           class="btn btn-sm btn-secondary">
                                            <i class="fas fa-toggle-on"></i> 切换启停
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr><td colspan="6" class="text-center text-muted"><i class="fas fa-inbox"></i> 暂无权限</td></tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<style>
.card { background: white; border-radius: 8px; box-shadow: 0 2px 6px rgba(0,0,0,0.08); overflow: hidden; margin-bottom: 16px; }
.card-header { padding: 14px 18px; border-bottom: 1px solid #e5e7eb; font-weight: 600; color: #374151; display:flex; align-items:center; gap:8px; }
.card-body { padding: 16px 18px; }
.alert { padding: 10px 12px; border-radius: 6px; margin-bottom: 12px; display:flex; align-items:center; gap:8px; }
.alert-danger { background: #fee2e2; color: #b91c1c; }
.alert-success { background: #dcfce7; color: #166534; }
.form-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 12px; margin-bottom: 16px; }
.form-group { display:flex; flex-direction: column; gap: 6px; }
.required { color: #e53e3e; }
.form-text { font-size: 12px; color: #6b7280; }
.action-buttons { display: flex; gap: 6px; flex-wrap: wrap; }
.table-toolbar { margin-bottom: 16px; }
.badge { padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500; }
.badge-success { background: #dcfce7; color: #166534; }
.badge-secondary { background: #f3f4f6; color: #6b7280; }
@media (max-width: 1024px) { .form-grid { grid-template-columns: 1fr; } }
</style>

<script>
function filterPermissions() {
    const query = document.getElementById('permissionSearch').value.toLowerCase();
    const rows = document.querySelectorAll('#permissionTable tbody tr[data-search]');
    
    rows.forEach(row => {
        const searchText = row.getAttribute('data-search') || '';
        row.style.display = searchText.includes(query) ? '' : 'none';
    });
}
</script>

<?php require_once dirname(__DIR__, 2) . '/includes/footer-modular.php'; ?>
