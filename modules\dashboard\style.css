/* 仪表板模块样式 - 数据报表集成版本 v2.0 */
/* 最后更新: 2025-01-27 */

/* 基础动画 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 统计卡片网格 - 响应式布局 */
.stats-grid {
    display: grid !important;
    grid-template-columns: repeat(6, 1fr) !important;
    gap: 16px !important;
    margin-bottom: 24px !important;
}

/* 超大屏幕 (1400px+) - 6列，间距更大 */
@media (min-width: 1400px) {
    .stats-grid {
        grid-template-columns: repeat(6, 1fr) !important;
        gap: 20px !important;
    }
}

/* 大屏幕 (1080px+) - 6列 */
@media (min-width: 1080px) {
    .stats-grid {
        grid-template-columns: repeat(6, 1fr) !important;
        gap: 16px !important;
    }
}

/* 中等屏幕 (992px-1079px) - 3列 */
@media (max-width: 1079px) and (min-width: 992px) {
    .stats-grid {
        grid-template-columns: repeat(3, 1fr) !important;
        gap: 18px !important;
    }
}

/* 平板屏幕 (768px-991px) - 2列 */
@media (max-width: 991px) and (min-width: 768px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 16px !important;
    }
}

/* 手机屏幕 (767px以下) - 1列 */
@media (max-width: 767px) {
    .stats-grid {
        grid-template-columns: 1fr !important;
        gap: 15px !important;
    }
}

.stat-card {
    background: #ffffff !important;
    border-radius: 8px !important;
    padding: 14px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    border: 1px solid #e2e8f0 !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: center !important;
    transition: all 0.2s ease !important;
    position: relative !important;
    min-height: 110px !important;
    text-align: left !important;
    min-width: 0 !important;
    overflow: hidden !important;
}

.stat-card:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* 大屏幕卡片优化 */
@media (min-width: 1400px) {
    .stat-card {
        padding: 18px !important;
        min-height: 130px !important;
    }
}

/* 1080P屏幕卡片优化 */
@media (min-width: 1080px) and (max-width: 1399px) {
    .stat-card {
        padding: 12px !important;
        min-height: 105px !important;
    }
}

/* 手机端卡片优化 */
@media (max-width: 767px) {
    .stat-card {
        padding: 20px !important;
        min-height: 100px !important;
    }
}

.stat-icon {
    width: 40px !important;
    height: 40px !important;
    border-radius: 6px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 18px !important;
    color: #6b7280 !important;
    background: #f3f4f6 !important;
}

/* 数据报表样式 */
.stat-card-header {
    display: flex !important;
    align-items: center !important;
    margin-bottom: 8px !important;
}

.stat-card-title {
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
    font-size: 16px !important;
    font-weight: 500 !important;
    color: #6b7280 !important;
}

.stat-card-value {
    font-size: 24px !important;
    font-weight: 700 !important;
    color: #1f2937 !important;
    margin-bottom: 8px !important;
}

.stat-card-change {
    display: flex !important;
    align-items: center !important;
    gap: 4px !important;
    font-size: 12px !important;
    font-weight: 500 !important;
}

.stat-card-change.positive {
    color: #10b981 !important;
}

.stat-card-change.negative {
    color: #ef4444 !important;
}

/* 图表网格 - 响应式布局 */
.chart-grid {
    display: grid !important;
    gap: 20px !important;
    margin-bottom: 30px !important;
}

/* 大屏幕 (1200px+) - 2x2网格 */
@media (min-width: 1200px) {
    .chart-grid {
        grid-template-columns: repeat(2, 1fr) !important;
        grid-template-rows: repeat(2, 1fr) !important;
    }
}

/* 中等屏幕 (992px-1199px) - 2x2网格 */
@media (max-width: 1199px) and (min-width: 992px) {
    .chart-grid {
        grid-template-columns: repeat(2, 1fr) !important;
        grid-template-rows: repeat(2, 1fr) !important;
    }
}

/* 平板屏幕 (768px-991px) - 1列 */
@media (max-width: 991px) and (min-width: 768px) {
    .chart-grid {
        grid-template-columns: 1fr !important;
        gap: 15px !important;
    }
}

/* 手机屏幕 (767px以下) - 1列 */
@media (max-width: 767px) {
    .chart-grid {
        grid-template-columns: 1fr !important;
        gap: 15px !important;
    }
}

.chart-card {
    background: white !important;
    border-radius: 12px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    border: 1px solid #e2e8f0 !important;
    overflow: hidden !important;
    width: 100% !important;
    min-height: 350px !important;
}

.chart-header {
    padding: 20px 20px 0 20px !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin-bottom: 15px !important;
    flex-wrap: wrap !important;
    gap: 10px !important;
}

.chart-title {
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
    font-size: 16px !important;
    font-weight: 600 !important;
    color: #1f2937 !important;
    flex: 1 !important;
    min-width: 200px !important;
}

.chart-container {
    padding: 0 20px 20px 20px !important;
    height: 280px !important;
    position: relative !important;
    width: 100% !important;
}

/* 手机端图表优化 */
@media (max-width: 767px) {
    .chart-card {
        min-height: 320px !important;
    }

    .chart-header {
        padding: 15px 15px 0 15px !important;
        flex-direction: column !important;
        align-items: flex-start !important;
    }

    .chart-title {
        font-size: 14px !important;
        min-width: auto !important;
    }

    .chart-container {
        padding: 0 15px 15px 15px !important;
        height: 250px !important;
    }
}

/* 平板端图表优化 */
@media (max-width: 991px) and (min-width: 768px) {
    .chart-container {
        height: 300px !important;
    }
}

/* 进度条样式 */
.progress-bar {
    width: 100% !important;
    height: 8px !important;
    background: #f3f4f6 !important;
    border-radius: 4px !important;
    overflow: hidden !important;
}

.progress-fill {
    height: 100% !important;
    border-radius: 4px !important;
    transition: width 0.3s ease !important;
}

/* 数据洞察表格 */
.data-table {
    background: white !important;
    border-radius: 12px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    border: 1px solid #e2e8f0 !important;
    overflow: hidden !important;
    margin-bottom: 30px !important;
}

.table-header {
    padding: 20px !important;
    border-bottom: 1px solid #e2e8f0 !important;
    background: #f8f9fa !important;
}

.table-title {
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
    font-size: 18px !important;
    font-weight: 600 !important;
    color: #1f2937 !important;
    margin: 0 !important;
}

/* 全局响应式优化 */
@media (max-width: 767px) {
    .data-table {
        margin: 0 -10px 20px -10px !important;
        border-radius: 0 !important;
        border-left: none !important;
        border-right: none !important;
    }

    .table-header {
        padding: 15px !important;
    }

    .table-title {
        font-size: 16px !important;
    }

    .data-table .row {
        margin: 0 !important;
    }

    .data-table .col-md-6 {
        padding: 0 !important;
        margin-bottom: 20px !important;
    }
}

/* 确保容器不会溢出 */
.dashboard-container {
    max-width: 100% !important;
    overflow-x: hidden !important;
}

/* 统计卡片内容优化 */
.stat-card-header {
    margin-bottom: 6px !important;
}

.stat-card-title {
    font-size: 16px !important;
    line-height: 1.2 !important;
    font-weight: 500 !important;
    color: #6b7280 !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
}

.stat-card-value {
    font-size: 18px !important;
    line-height: 1.2 !important;
    margin-bottom: 5px !important;
    font-weight: 700 !important;
    color: #1f2937 !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
}

.stat-card-change {
    font-size: 10px !important;
    font-weight: 500 !important;
}

/* 大屏幕内容调整 */
@media (min-width: 1400px) {
    .stat-card-title {
        font-size: 16px !important;
    }

    .stat-card-value {
        font-size: 22px !important;
        margin-bottom: 8px !important;
    }

    .stat-card-change {
        font-size: 12px !important;
    }

    .stat-card-header {
        margin-bottom: 8px !important;
    }
}

/* 1080P屏幕内容调整 */
@media (min-width: 1080px) and (max-width: 1399px) {
    .stat-card-title {
        font-size: 14px !important;
    }

    .stat-card-value {
        font-size: 16px !important;
    }

    .stat-card-change {
        font-size: 9px !important;
    }
}

/* 手机端统计卡片内容调整 */
@media (max-width: 767px) {
    .stat-card-title {
        font-size: 13px !important;
        white-space: normal !important;
    }

    .stat-card-value {
        font-size: 20px !important;
        white-space: normal !important;
    }

    .stat-card-change {
        font-size: 11px !important;
    }
}

/* 库存状态样式 */
.inventory-status {
    padding: 20px !important;
}

.status-item {
    margin-bottom: 15px !important;
}

.status-item:last-child {
    margin-bottom: 0 !important;
}

.status-label {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin-bottom: 8px !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    color: #374151 !important;
}

.status-value {
    font-weight: 600 !important;
    color: #1f2937 !important;
}
    margin-right: 12px !important;
    flex-shrink: 0 !important;
}

/* 自定义图标样式 */
.stat-icon img {
    width: 20px !important;
    height: 20px !important;
    object-fit: contain !important;
    filter: grayscale(1) opacity(0.7) !important;
}

.stat-icon .custom-icon {
    width: 20px !important;
    height: 20px !important;
    background-size: contain !important;
    background-repeat: no-repeat !important;
    background-position: center !important;
}





.text-primary { color: #6b7280 !important; }
.text-success { color: #6b7280 !important; }
.text-warning { color: #6b7280 !important; }
.text-info { color: #6b7280 !important; }
.text-secondary { color: #6b7280 !important; }
.text-danger { color: #6b7280 !important; }

.stat-content {
    flex: 1 !important;
    position: relative !important;
}

.stat-number {
    font-size: 28px !important;
    font-weight: 700 !important;
    color: #1f2937 !important;
    line-height: 1 !important;
}

.stat-label {
    font-size: 14px !important;
    color: #64748b !important;
    margin-top: 6px !important;
    font-weight: 500 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
}

.stat-trend {
    display: flex !important;
    align-items: center !important;
    gap: 4px !important;
    margin-top: 8px !important;
    font-size: 12px !important;
    font-weight: 600 !important;
}

.stat-trend i {
    font-size: 10px !important;
}

/* 头部样式增强 */
.content-header {
    display: flex !important;
    justify-content: space-between !important;
    align-items: flex-start !important;
    margin-bottom: 24px !important;
    padding: 20px !important;
    background: #ffffff !important;
    border-radius: 8px !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
    border: 1px solid #e2e8f0 !important;
}

.header-left h1 {
    font-size: 28px !important;
    font-weight: 700 !important;
    color: #1f2937 !important;
    margin-bottom: 6px !important;
}

.header-left h1 i {
    margin-right: 12px !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
}

.header-subtitle {
    font-size: 16px !important;
    color: #64748b !important;
    font-weight: 500 !important;
    margin: 0 !important;
}

.header-actions {
    display: flex !important;
    align-items: center !important;
    gap: 16px !important;
}

.time-display {
    display: flex !important;
    align-items: center !important;
    gap: 6px !important;
    background: #f8fafc !important;
    padding: 10px 14px !important;
    border-radius: 6px !important;
    border: 1px solid #e2e8f0 !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    color: #64748b !important;
}

.time-display i {
    font-size: 16px !important;
}

/* 按钮样式增强 */
.btn {
    padding: 10px 16px !important;
    border-radius: 6px !important;
    font-weight: 500 !important;
    font-size: 14px !important;
    border: none !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    display: inline-flex !important;
    align-items: center !important;
    gap: 6px !important;
    text-decoration: none !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}



.btn-primary {
    background: #3b82f6 !important;
    color: white !important;
}

.btn-primary:hover {
    background: #2563eb !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3) !important;
}

.btn-secondary {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%) !important;
    color: #4a5568 !important;
    box-shadow: 0 4px 14px 0 rgba(168, 237, 234, 0.3) !important;
}

.btn-secondary:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(168, 237, 234, 0.4) !important;
}

.btn:disabled {
    opacity: 0.6 !important;
    cursor: not-allowed !important;
    transform: none !important;
}

.btn i {
    font-size: 16px !important;
}

/* 仪表板网格布局 */
.dashboard-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    grid-template-rows: auto auto;
    gap: 20px;
    grid-template-areas:
        "chart activity"
        "alert orders";
}

.chart-section {
    grid-area: chart;
}

.activity-section {
    grid-area: activity;
}

.alert-section {
    grid-area: alert;
}

.orders-section {
    grid-area: orders;
}

/* 现代化卡片样式 */
.card {
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    transition: all 0.2s ease;
    position: relative;
}

/* 警告框样式 */
.alert {
    padding: 16px 20px;
    border-radius: 12px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    font-weight: 500;
}

.alert-danger {
    background: linear-gradient(135deg, rgba(255, 154, 158, 0.1) 0%, rgba(254, 207, 239, 0.1) 100%);
    color: #721c24;
    border-color: rgba(255, 154, 158, 0.3);
}

.alert-info {
    background: linear-gradient(135deg, rgba(79, 172, 254, 0.1) 0%, rgba(0, 242, 254, 0.1) 100%);
    color: #0c5460;
    border-color: rgba(79, 172, 254, 0.3);
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #667eea 100%);
    background-size: 200% 100%;
    animation: shimmer 3s ease-in-out infinite;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.card-body {
    padding: 24px;
}

/* 卡片头部 */
.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 24px 24px 0;
    position: relative;
}

.card-title {
    font-size: 18px;
    font-weight: 700;
    color: #1a202c;
    display: flex;
    align-items: center;
    gap: 12px;
}

.card-title i {
    font-size: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.card-actions select {
    min-width: 140px;
    padding: 8px 12px;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    background: white;
    font-size: 14px;
    transition: all 0.3s ease;
}

.card-actions select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 活动列表 */
.activity-list {
    max-height: 420px;
    overflow-y: auto;
    padding-right: 4px;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    padding: 16px 0;
    border-bottom: 1px solid rgba(226, 232, 240, 0.6);
    transition: all 0.3s ease;
    border-radius: 12px;
    margin-bottom: 8px;
    position: relative;
}

.activity-item:hover {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    transform: translateX(4px);
    border-color: rgba(102, 126, 234, 0.2);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    flex-shrink: 0;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    transition: all 0.3s ease;
}

.activity-item:hover .activity-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.activity-content {
    flex: 1;
    min-width: 0;
}

.activity-title {
    font-weight: 700;
    color: #1a202c;
    font-size: 15px;
    margin-bottom: 4px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.activity-description {
    color: #64748b;
    font-size: 13px;
    line-height: 1.4;
    margin-bottom: 8px;
}

.activity-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
}

.activity-amount {
    color: #667eea;
    font-weight: 700;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    padding: 4px 8px;
    border-radius: 6px;
    border: 1px solid rgba(102, 126, 234, 0.2);
}

.activity-time {
    color: #94a3b8;
    font-weight: 500;
}

/* 预警列表 */
.alert-list {
    max-height: 420px;
    overflow-y: auto;
    padding-right: 4px;
}

.alert-item {
    padding: 18px;
    border-bottom: 1px solid rgba(226, 232, 240, 0.6);
    border-radius: 12px;
    margin-bottom: 12px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(248, 250, 252, 0.8) 100%);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.alert-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    transition: width 0.3s ease;
}

.alert-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    background: linear-gradient(135deg, rgba(255, 255, 255, 1) 0%, rgba(248, 250, 252, 1) 100%);
}

.alert-item:hover::before {
    width: 8px;
}

.alert-item:last-child {
    border-bottom: none;
}

.alert-content {
    margin-bottom: 12px;
}

.alert-title {
    font-weight: 700;
    color: #1a202c;
    margin-bottom: 6px;
    font-size: 15px;
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.alert-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: #64748b;
    margin-bottom: 8px;
}

.alert-meta .category {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    color: #667eea;
    padding: 4px 10px;
    border-radius: 8px;
    font-weight: 600;
    border: 1px solid rgba(102, 126, 234, 0.2);
}

.alert-progress {
    margin-top: 12px;
}

.progress {
    height: 8px;
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progress-bar {
    height: 100%;
    transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.progress-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);
    background-size: 20px 20px;
    animation: move 1s linear infinite;
}

@keyframes move {
    0% { background-position: 0 0; }
    100% { background-position: 20px 20px; }
}

.progress-bar.bg-success {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}
.progress-bar.bg-warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}
.progress-bar.bg-danger {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
}

/* 订单列表 */
.orders-list {
    max-height: 420px;
    overflow-y: auto;
    padding-right: 4px;
}

.order-item {
    padding: 18px;
    border-bottom: 1px solid rgba(226, 232, 240, 0.6);
    border-radius: 12px;
    margin-bottom: 12px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(248, 250, 252, 0.8) 100%);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.order-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transition: width 0.3s ease;
}

.order-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    background: linear-gradient(135deg, rgba(255, 255, 255, 1) 0%, rgba(248, 250, 252, 1) 100%);
}

.order-item:hover::before {
    width: 8px;
}

.order-item:last-child {
    border-bottom: none;
}

.order-title a {
    color: #667eea;
    text-decoration: none;
    font-weight: 700;
    font-size: 15px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    transition: all 0.3s ease;
}

.order-title a:hover {
    text-decoration: none;
    transform: translateX(4px);
    text-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
}

.order-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 8px 0;
    font-size: 14px;
}

.order-meta .supplier {
    color: #64748b;
    font-weight: 500;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    padding: 4px 8px;
    border-radius: 6px;
    border: 1px solid rgba(102, 126, 234, 0.2);
}

.order-meta .amount {
    color: #667eea;
    font-weight: 700;
    font-size: 16px;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    padding: 4px 8px;
    border-radius: 6px;
    border: 1px solid rgba(102, 126, 234, 0.2);
}

.order-dates {
    font-size: 12px;
    color: #94a3b8;
    display: flex;
    gap: 16px;
    margin-top: 8px;
}

.order-dates span {
    background: rgba(248, 250, 252, 0.8);
    padding: 4px 8px;
    border-radius: 6px;
    border: 1px solid rgba(226, 232, 240, 0.8);
    font-weight: 500;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #64748b;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    border-radius: 16px;
    border: 2px dashed rgba(102, 126, 234, 0.2);
    transition: all 0.3s ease;
}

.empty-state:hover {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border-color: rgba(102, 126, 234, 0.3);
    transform: scale(1.02);
}

.empty-state i {
    font-size: 64px;
    margin-bottom: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: pulse 2s ease-in-out infinite;
}

.empty-state p {
    font-size: 16px;
    margin: 0;
    font-weight: 600;
    color: #475569;
}

/* 图表容器 */
#mainChart {
    max-height: 350px;
    border-radius: 12px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
    padding: 16px;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* 图表加载状态 */
.loading-chart {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 350px;
    color: #64748b;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    border-radius: 16px;
    border: 2px dashed rgba(102, 126, 234, 0.2);
}

.loading-chart i {
    font-size: 48px;
    margin-bottom: 16px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: spin 1s linear infinite;
}

.loading-chart p {
    font-size: 16px;
    font-weight: 600;
    margin: 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
        grid-template-areas:
            "chart"
            "activity"
            "alert"
            "orders";
    }

    .content-header {
        flex-direction: column;
        align-items: stretch;
        gap: 20px;
    }

    .header-actions {
        justify-content: space-between;
        flex-wrap: wrap;
    }
}

@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(240px, 1fr)) !important;
        gap: 16px !important;
    }

    .stat-card {
        padding: 20px !important;
        flex-direction: column !important;
        text-align: center !important;
        gap: 12px !important;
    }

    .stat-icon {
        width: 64px !important;
        height: 64px !important;
        font-size: 24px !important;
        margin: 0 auto !important;
    }

    .stat-number {
        font-size: 28px !important;
    }

    .stat-label {
        font-size: 13px !important;
    }

    .dashboard-grid {
        gap: 16px;
    }

    .card-body {
        padding: 20px;
    }

    .content-header {
        padding: 20px !important;
        margin-bottom: 24px !important;
    }

    .header-left h1 {
        font-size: 28px !important;
    }

    .header-subtitle {
        font-size: 14px !important;
    }

    .time-display {
        font-size: 12px !important;
        padding: 10px 12px !important;
    }

    .btn {
        padding: 10px 16px !important;
        font-size: 13px !important;
    }

    .header-actions {
        gap: 12px !important;
    }
}

@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr !important;
    }

    .header-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .time-display {
        order: -1;
        justify-content: center;
    }

    .btn {
        justify-content: center;
    }
}

/* 刷新按钮动画 */
.btn .fa-sync-alt {
    transition: transform 0.3s ease;
}

.btn:hover .fa-sync-alt {
    transform: rotate(180deg);
}

/* 旋转动画 */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 浮动动画 */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* 闪烁动画 */
@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.5; }
}

/* 渐变背景动画 */
@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* 特殊效果类 */
.floating {
    animation: float 3s ease-in-out infinite;
}

.blinking {
    animation: blink 2s ease-in-out infinite;
}

.gradient-bg {
    background: linear-gradient(-45deg, #667eea, #764ba2, #f093fb, #f5576c);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
}

/* 卡片特效 */
.card-glow {
    position: relative;
}

.card-glow::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #667eea, #764ba2, #f093fb, #f5576c);
    border-radius: 22px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.card-glow:hover::after {
    opacity: 0.7;
}

/* 数据加载骨架屏 */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* 成功状态动画 */
.success-pulse {
    animation: successPulse 0.6s ease-out;
}

@keyframes successPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* 错误状态动画 */
.error-shake {
    animation: errorShake 0.5s ease-out;
}

@keyframes errorShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* 通知样式 */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 16px 24px;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    z-index: 9999;
    transform: translateX(400px);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.notification.error {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
}

.notification.warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

/* 手机端导航样式 */
.mobile-nav {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    border-left: 4px solid #20c997;
}

.mobile-nav:hover {
    background: linear-gradient(135deg, #138496 0%, #117a8b 100%);
    transform: translateX(8px);
}

.mobile-nav i {
    color: #20c997;
}
