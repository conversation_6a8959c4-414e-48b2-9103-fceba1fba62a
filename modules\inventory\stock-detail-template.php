<?php require_once dirname(__DIR__, 2) . '/includes/header-modular.php'; ?>

<div class="main-content">
    <?php require_once dirname(__DIR__, 2) . '/includes/sidebar-modular.php'; ?>

    <div class="content">
        <div class="content-header">
            <h1>
                <i class="fas fa-chart-line"></i>
                库存情况
            </h1>
            <div class="header-actions">
                <a href="index.php?action=query" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i>
                    返回列表
                </a>
            </div>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon bg-blue"><i class="fas fa-box"></i></div>
                <div class="stat-content">
                    <h3><?= htmlspecialchars($data['ingredient']['name'] ?? ($ingredient['name'] ?? '未知食材')) ?></h3>
                    <p>分类：<?= htmlspecialchars($data['ingredient']['category_name'] ?? ($ingredient['category_name'] ?? '未分类')) ?></p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon bg-green"><i class="fas fa-warehouse"></i></div>
                <div class="stat-content">
                    <?php $ing = $data['ingredient'] ?? $ingredient ?? []; ?>
                    <h3><?= isset($ing['current_stock']) ? number_format($ing['current_stock'], 2) : '0.00' ?> <?= htmlspecialchars($ing['unit'] ?? '') ?></h3>
                    <p>当前库存</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon bg-orange"><i class="fas fa-sign-in-alt"></i></div>
                <div class="stat-content">
                    <h3><?= number_format(($data['summary']['inbound_qty'] ?? 0), 2) ?></h3>
                    <p>累计入库量</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon bg-red"><i class="fas fa-sign-out-alt"></i></div>
                <div class="stat-content">
                    <h3><?= number_format(($data['summary']['outbound_qty'] ?? 0), 2) ?></h3>
                    <p>累计出库量</p>
                </div>
            </div>
        </div>

        <div class="table-container" style="margin-bottom:20px;">
            <div class="content-header" style="padding: 15px 15px 0 15px;">
                <h2 style="font-size:18px; margin:0; display:flex; align-items:center; gap:8px;"><i class="fas fa-download"></i> 入库记录</h2>
            </div>
            <table class="table">
                <thead>
                    <tr>
                        <th>时间</th>
                        <th>批次号</th>
                        <th>供应商</th>
                        <th>数量</th>
                        <th>单价</th>
                        <th>金额</th>
                    </tr>
                </thead>
                <tbody>
                <?php $inbound = $data['inbound'] ?? $inbound ?? []; ?>
                <?php if (!empty($inbound)): foreach ($inbound as $row): ?>
                    <tr>
                        <td><?= htmlspecialchars($row['created_at'] ?? '') ?></td>
                        <td><?= htmlspecialchars($row['batch_number'] ?? '') ?></td>
                        <td><?= htmlspecialchars($row['supplier_name'] ?? '') ?></td>
                        <td><?= number_format(floatval($row['quantity'] ?? 0), 2) ?></td>
                        <td>¥<?= number_format(floatval($row['unit_price'] ?? 0), 2) ?></td>
                        <td><strong>¥<?= number_format(floatval(($row['quantity'] ?? 0) * ($row['unit_price'] ?? 0)), 2) ?></strong></td>
                    </tr>
                <?php endforeach; else: ?>
                    <tr><td colspan="6" class="text-center text-muted"><i class="fas fa-inbox"></i> 暂无入库记录</td></tr>
                <?php endif; ?>
                </tbody>
            </table>
        </div>

        <div class="table-container" style="margin-bottom:20px;">
            <div class="content-header" style="padding: 15px 15px 0 15px;">
                <h2 style="font-size:18px; margin:0; display:flex; align-items:center; gap:8px;"><i class="fas fa-upload"></i> 出库记录</h2>
            </div>
            <table class="table">
                <thead>
                    <tr>
                        <th>时间</th>
                        <th>批次号</th>
                        <th>经办人</th>
                        <th>数量</th>
                        <th>单价</th>
                        <th>金额</th>
                    </tr>
                </thead>
                <tbody>
                <?php $outbound = $data['outbound'] ?? $outbound ?? []; ?>
                <?php if (!empty($outbound)): foreach ($outbound as $row): ?>
                    <tr>
                        <td><?= htmlspecialchars($row['created_at'] ?? '') ?></td>
                        <td><?= htmlspecialchars($row['batch_number'] ?? '') ?></td>
                        <td><?= htmlspecialchars($row['operator_name'] ?? '') ?></td>
                        <td><?= number_format(floatval($row['quantity'] ?? 0), 2) ?></td>
                        <td>¥<?= number_format(floatval($row['unit_price'] ?? 0), 2) ?></td>
                        <td><strong>¥<?= number_format(floatval(($row['quantity'] ?? 0) * ($row['unit_price'] ?? 0)), 2) ?></strong></td>
                    </tr>
                <?php endforeach; else: ?>
                    <tr><td colspan="6" class="text-center text-muted"><i class="fas fa-inbox"></i> 暂无出库记录</td></tr>
                <?php endif; ?>
                </tbody>
            </table>
        </div>

        <div class="table-container">
            <div class="content-header" style="padding: 15px 15px 0 15px;">
                <h2 style="font-size:18px; margin:0; display:flex; align-items:center; gap:8px;"><i class="fas fa-exclamation-triangle"></i> 报损记录</h2>
            </div>
            <table class="table">
                <thead>
                    <tr>
                        <th>时间</th>
                        <th>批次号</th>
                        <th>类型</th>
                        <th>原因</th>
                        <th>数量</th>
                        <th>金额</th>
                    </tr>
                </thead>
                <tbody>
                <?php $damages = $data['damages'] ?? $damages ?? []; ?>
                <?php if (!empty($damages)): foreach ($damages as $row): ?>
                    <tr>
                        <td><?= htmlspecialchars($row['damage_date'] ?? '') ?></td>
                        <td><?= htmlspecialchars($row['batch_number'] ?? '') ?></td>
                        <td>
                            <?php
                            $typeMap = [
                                'expired' => '过期报损',
                                'damaged' => '破损报损',
                                'quality' => '质量问题',
                                'other' => '其他原因',
                            ];
                            $t = $row['damage_type'] ?? '';
                            echo htmlspecialchars($typeMap[$t] ?? $t);
                            ?>
                        </td>
                        <td><?= htmlspecialchars($row['damage_reason'] ?? '') ?></td>
                        <td><?= number_format(floatval($row['damage_quantity'] ?? 0), 2) ?></td>
                        <td><strong>¥<?= number_format(floatval($row['damage_value'] ?? 0), 2) ?></strong></td>
                    </tr>
                <?php endforeach; else: ?>
                    <tr><td colspan="6" class="text-center text-muted"><i class="fas fa-inbox"></i> 暂无报损记录</td></tr>
                <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<style>
.stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(260px, 1fr)); gap: 20px; margin-bottom: 20px; }
.stat-card { background:#fff; border-radius:8px; padding:20px; box-shadow:0 2px 4px rgba(0,0,0,.1); display:flex; align-items:center; }
.stat-icon { width:56px; height:56px; border-radius:50%; display:flex; align-items:center; justify-content:center; margin-right:12px; color:#fff; font-size:22px; }
.stat-icon.bg-blue { background:#4299e1; }
.stat-icon.bg-green { background:#48bb78; }
.stat-icon.bg-orange { background:#ed8936; }
.stat-icon.bg-red { background:#f56565; }
.table-container { background:#fff; border-radius:8px; box-shadow:0 2px 4px rgba(0,0,0,.1); overflow:hidden; }
</style>

<?php require_once dirname(__DIR__, 2) . '/includes/footer-modular.php'; ?>


