<?php
// 数据库表创建脚本

// 数据库配置
$host = '************';
$port = 3306;
$dbname = 'sc';
$username = 'sc';
$password = 'pw5K4SsM7kZsjdxy';

echo "<h2>数据库表创建脚本</h2>";

// 尝试使用mysqli连接（如果PDO不可用）
if (extension_loaded('mysqli')) {
    echo "<p>使用MySQLi连接...</p>";
    
    $mysqli = new mysqli($host, $username, $password, $dbname, $port);
    
    if ($mysqli->connect_error) {
        die("<p style='color: red;'>连接失败: " . $mysqli->connect_error . "</p>");
    }
    
    echo "<p style='color: green;'>数据库连接成功！</p>";
    
    // 设置字符集
    $mysqli->set_charset("utf8");
    
    // 创建表的SQL语句
    $tables = [
        // 1. 食材分类表
        'ingredient_categories' => "
        CREATE TABLE IF NOT EXISTS `ingredient_categories` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(50) NOT NULL,
            `code` varchar(20) NOT NULL,
            `description` text DEFAULT NULL,
            `sort_order` int(11) DEFAULT 0,
            `status` tinyint(1) DEFAULT 1,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `name` (`name`),
            UNIQUE KEY `code` (`code`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='食材分类表'",
        
        // 2. 供应商表
        'suppliers' => "
        CREATE TABLE IF NOT EXISTS `suppliers` (
            `id` bigint(20) NOT NULL AUTO_INCREMENT,
            `name` varchar(100) NOT NULL,
            `contact_person` varchar(50) NOT NULL,
            `phone` varchar(20) NOT NULL,
            `email` varchar(100) DEFAULT NULL,
            `address` varchar(255) DEFAULT NULL,
            `license_number` varchar(50) DEFAULT NULL,
            `notes` text DEFAULT NULL,
            `rating` tinyint(4) DEFAULT 5,
            `status` tinyint(1) DEFAULT 1,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='供应商表'",
        
        // 3. 食材表
        'ingredients' => "
        CREATE TABLE IF NOT EXISTS `ingredients` (
            `id` bigint(20) NOT NULL AUTO_INCREMENT,
            `name` varchar(100) NOT NULL,
            `code` varchar(50) DEFAULT NULL,
            `category_id` int(11) NOT NULL,
            `unit` varchar(20) NOT NULL,
            `specification` varchar(100) DEFAULT NULL,
            `shelf_life_days` int(11) NOT NULL,
            `min_stock` decimal(10,2) DEFAULT 0.00,
            `unit_price` decimal(10,2) DEFAULT 0.00,
            `current_stock` decimal(10,2) DEFAULT 0.00,
            `max_stock` decimal(10,2) DEFAULT NULL,
            `image_path` varchar(255) DEFAULT NULL,
            `description` text DEFAULT NULL,
            `status` tinyint(1) DEFAULT 1,
            `created_by` bigint(20) NOT NULL,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `name` (`name`),
            UNIQUE KEY `code` (`code`),
            KEY `category_id` (`category_id`),
            KEY `status` (`status`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='食材表'",
        
        // 4. 入库记录表
        'inbound_records' => "
        CREATE TABLE IF NOT EXISTS `inbound_records` (
            `id` bigint(20) NOT NULL AUTO_INCREMENT,
            `ingredient_id` bigint(20) NOT NULL,
            `supplier_id` bigint(20) NOT NULL,
            `batch_number` varchar(64) NOT NULL,
            `quantity` decimal(10,2) NOT NULL,
            `unit_price` decimal(10,2) NOT NULL,
            `production_date` date DEFAULT NULL,
            `expired_at` date NOT NULL,
            `purchase_invoice` varchar(255) NOT NULL,
            `quality_check` tinyint(1) DEFAULT 1,
            `notes` text DEFAULT NULL,
            `status` tinyint(1) DEFAULT 1,
            `created_by` bigint(20) NOT NULL,
            `approved_by` bigint(20) DEFAULT NULL,
            `approved_at` timestamp NULL DEFAULT NULL,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `batch_number` (`batch_number`),
            KEY `ingredient_id` (`ingredient_id`),
            KEY `supplier_id` (`supplier_id`),
            KEY `expired_at` (`expired_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='入库记录表'",

        // 5. 入库单表
        'inbound_orders' => "
        CREATE TABLE IF NOT EXISTS `inbound_orders` (
            `id` bigint(20) NOT NULL AUTO_INCREMENT,
            `order_number` varchar(50) NOT NULL,
            `supplier_id` int(11) NOT NULL,
            `operator` varchar(50) NOT NULL,
            `delivery_photo` varchar(255) DEFAULT NULL,
            `total_amount` decimal(12,2) DEFAULT 0.00,
            `status` enum('pending','completed','cancelled') DEFAULT 'pending',
            `notes` text DEFAULT NULL,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `order_number` (`order_number`),
            KEY `supplier_id` (`supplier_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='入库单表'",

        // 6. 入库单明细表
        'inbound_order_items' => "
        CREATE TABLE IF NOT EXISTS `inbound_order_items` (
            `id` bigint(20) NOT NULL AUTO_INCREMENT,
            `order_id` bigint(20) NOT NULL,
            `ingredient_id` bigint(20) NOT NULL,
            `quantity` decimal(10,2) NOT NULL,
            `unit_price` decimal(10,2) NOT NULL,
            `total_price` decimal(12,2) NOT NULL,
            `acceptance_status` enum('pending','accepted','rejected') DEFAULT 'pending',
            `purpose` enum('breakfast','lunch','dinner','other') DEFAULT 'other',
            `weight_photo` varchar(255) DEFAULT NULL,
            `notes` text DEFAULT NULL,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `order_id` (`order_id`),
            KEY `ingredient_id` (`ingredient_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='入库单明细表'",

        // 7. 出库记录表
        'outbound_records' => "
        CREATE TABLE IF NOT EXISTS `outbound_records` (
            `id` bigint(20) NOT NULL AUTO_INCREMENT,
            `ingredient_id` bigint(20) NOT NULL,
            `batch_number` varchar(64) NOT NULL,
            `quantity` decimal(10,2) NOT NULL,
            `unit_price` decimal(10,2) NOT NULL,
            `used_for` varchar(100) NOT NULL,
            `meal_date` date NOT NULL,
            `notes` text DEFAULT NULL,
            `created_by` bigint(20) NOT NULL,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `ingredient_id` (`ingredient_id`),
            KEY `batch_number` (`batch_number`),
            KEY `meal_date` (`meal_date`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='出库记录表'",
        
        // 6. 库存表
        'inventory' => "
        CREATE TABLE IF NOT EXISTS `inventory` (
            `id` bigint(20) NOT NULL AUTO_INCREMENT,
            `ingredient_id` bigint(20) NOT NULL,
            `current_quantity` decimal(10,2) NOT NULL DEFAULT 0.00,
            `total_value` decimal(12,2) NOT NULL DEFAULT 0.00,
            `last_inbound_at` timestamp NULL DEFAULT NULL,
            `last_outbound_at` timestamp NULL DEFAULT NULL,
            `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `ingredient_id` (`ingredient_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='库存表'",
        
        // 7. 库存批次表
        'inventory_batches' => "
        CREATE TABLE IF NOT EXISTS `inventory_batches` (
            `id` bigint(20) NOT NULL AUTO_INCREMENT,
            `ingredient_id` bigint(20) NOT NULL,
            `batch_number` varchar(64) NOT NULL,
            `remaining_quantity` decimal(10,2) NOT NULL,
            `unit_price` decimal(10,2) NOT NULL,
            `expired_at` date NOT NULL,
            `status` tinyint(4) DEFAULT 1,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `batch_number` (`batch_number`),
            KEY `ingredient_id` (`ingredient_id`),
            KEY `expired_at` (`expired_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='库存批次表'",
        
        // 8. 用户表
        'users' => "
        CREATE TABLE IF NOT EXISTS `users` (
            `id` bigint(20) NOT NULL AUTO_INCREMENT,
            `name` varchar(50) NOT NULL,
            `email` varchar(100) NOT NULL,
            `password` varchar(255) NOT NULL,
            `real_name` varchar(50) NOT NULL,
            `phone` varchar(20) DEFAULT NULL,
            `avatar` varchar(255) DEFAULT NULL,
            `status` tinyint(1) DEFAULT 1,
            `last_login_at` timestamp NULL DEFAULT NULL,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `name` (`name`),
            UNIQUE KEY `email` (`email`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='用户表'",
        
        // 9. 角色表
        'roles' => "
        CREATE TABLE IF NOT EXISTS `roles` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(50) NOT NULL,
            `guard_name` varchar(50) NOT NULL DEFAULT 'web',
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `roles_name_guard_name_unique` (`name`,`guard_name`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='角色表'",
        
        // 10. 权限表
        'permissions' => "
        CREATE TABLE IF NOT EXISTS `permissions` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(50) NOT NULL,
            `guard_name` varchar(50) NOT NULL DEFAULT 'web',
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `permissions_name_guard_name_unique` (`name`,`guard_name`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='权限表'"
    ];
    
    // 创建表
    $success_count = 0;
    $error_count = 0;
    
    foreach ($tables as $table_name => $sql) {
        echo "<p>创建表 <strong>$table_name</strong>...</p>";
        
        if ($mysqli->query($sql)) {
            echo "<p style='color: green;'>✅ 表 $table_name 创建成功</p>";
            $success_count++;
        } else {
            echo "<p style='color: red;'>❌ 表 $table_name 创建失败: " . $mysqli->error . "</p>";
            $error_count++;
        }
    }
    
    echo "<hr>";
    echo "<h3>创建结果统计:</h3>";
    echo "<p>成功: <span style='color: green;'>$success_count</span> 个表</p>";
    echo "<p>失败: <span style='color: red;'>$error_count</span> 个表</p>";
    
    // 插入初始数据
    if ($success_count > 0) {
        echo "<h3>插入初始数据...</h3>";
        
        // 插入食材分类
        $categories = [
            ['蔬菜类', 'VEG', 1],
            ['肉类', 'MEAT', 2],
            ['水产类', 'SEAFOOD', 3],
            ['粮食类', 'GRAIN', 4],
            ['调料类', 'SEASONING', 5]
        ];
        
        foreach ($categories as $cat) {
            $sql = "INSERT IGNORE INTO ingredient_categories (name, code, sort_order) VALUES (?, ?, ?)";
            $stmt = $mysqli->prepare($sql);
            $stmt->bind_param("ssi", $cat[0], $cat[1], $cat[2]);
            $stmt->execute();
        }
        
        // 插入供应商
        $suppliers = [
            ['绿色农场有限公司', '张三', '13800138100', '北京市朝阳区农业园区1号', '91110000000000001X', 5, 1],
            ['新鲜蔬菜批发市场', '李四', '13800138101', '北京市海淀区批发市场2号', '91110000000000002X', 4, 1],
            ['优质肉类供应商', '王五', '13800138102', '北京市丰台区肉类加工园3号', '91110000000000003X', 5, 1]
        ];
        
        foreach ($suppliers as $sup) {
            $sql = "INSERT IGNORE INTO suppliers (name, contact_person, phone, address, license_number, rating, status) VALUES (?, ?, ?, ?, ?, ?, ?)";
            $stmt = $mysqli->prepare($sql);
            $stmt->bind_param("ssssiii", $sup[0], $sup[1], $sup[2], $sup[3], $sup[4], $sup[5], $sup[6]);
            $stmt->execute();
        }
        
        // 插入用户
        $password = password_hash('password', PASSWORD_DEFAULT);
        $users = [
            ['admin', '<EMAIL>', $password, '系统管理员', '13800138000', 1],
            ['auditor', '<EMAIL>', $password, '审核员', '13800138001', 1],
            ['warehouse', '<EMAIL>', $password, '仓库员', '13800138002', 1]
        ];
        
        foreach ($users as $user) {
            $sql = "INSERT IGNORE INTO users (name, email, password, real_name, phone, status) VALUES (?, ?, ?, ?, ?, ?)";
            $stmt = $mysqli->prepare($sql);
            $stmt->bind_param("sssssi", $user[0], $user[1], $user[2], $user[3], $user[4], $user[5]);
            $stmt->execute();
        }
        
        // 插入食材
        $ingredients = [
            ['土豆', 1, '斤', 30, 50.00, '新鲜土豆，产地山东', 1, 1],
            ['白菜', 1, '斤', 7, 30.00, '新鲜大白菜，产地河北', 1, 1],
            ['猪肉', 2, '斤', 3, 100.00, '新鲜猪肉，检疫合格', 1, 1],
            ['大米', 4, '袋', 365, 10.00, '优质大米，25kg装', 1, 1]
        ];
        
        foreach ($ingredients as $ing) {
            $sql = "INSERT IGNORE INTO ingredients (name, category_id, unit, shelf_life_days, min_stock, description, status, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
            $stmt = $mysqli->prepare($sql);
            $stmt->bind_param("sisidsii", $ing[0], $ing[1], $ing[2], $ing[3], $ing[4], $ing[5], $ing[6], $ing[7]);
            $stmt->execute();
        }
        
        // 初始化库存
        $sql = "INSERT IGNORE INTO inventory (ingredient_id, current_quantity, total_value) SELECT id, 0, 0 FROM ingredients";
        $mysqli->query($sql);
        
        echo "<p style='color: green;'>✅ 初始数据插入完成</p>";
    }
    
    $mysqli->close();
    
} else {
    echo "<p style='color: red;'>❌ MySQLi扩展未安装，无法连接数据库</p>";
    echo "<p>请安装PHP MySQL扩展或使用XAMPP/WAMP</p>";
}

echo "<hr>";
echo "<p><a href='index.php'>返回首页</a> | <a href='check-php.php'>检查PHP环境</a></p>";
?>
