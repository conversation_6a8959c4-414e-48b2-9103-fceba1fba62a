<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试实际导入过程</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .debug-log { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; font-family: monospace; font-size: 12px; max-height: 500px; overflow-y: auto; }
        .btn { display: inline-block; padding: 8px 16px; background: #007cba; color: white; text-decoration: none; border-radius: 4px; margin: 5px; }
        .data-table { width: 100%; border-collapse: collapse; margin: 10px 0; font-size: 11px; }
        .data-table th, .data-table td { border: 1px solid #ddd; padding: 4px; text-align: left; }
        .data-table th { background: #f5f5f5; }
    </style>
</head>
<body>
    <h1>🔍 调试实际导入过程</h1>
    
    <?php
    require_once '../includes/Database.php';
    require_once '../includes/ExcelReader.php';
    
    try {
        $db = Database::getInstance();
        $testFile = '../test.xlsx';
        
        echo "<div class='test-section'>";
        echo "<h2>🔍 检查实际导入结果</h2>";
        
        echo "<div class='debug-log'>";
        
        function debugLog($message, $type = 'info') {
            $icons = ['info' => 'ℹ️', 'success' => '✅', 'error' => '❌', 'warning' => '⚠️'];
            $icon = $icons[$type] ?? 'ℹ️';
            echo "[" . date('H:i:s') . "] {$icon} {$message}\n";
        }
        
        debugLog("开始检查实际导入结果", 'info');
        
        // 1. 检查最近的采购订单
        debugLog("=== 1. 检查最近的采购订单 ===", 'info');
        
        $recentOrder = $db->fetchOne("SELECT * FROM purchase_orders ORDER BY created_at DESC LIMIT 1");
        if (!$recentOrder) {
            debugLog("❌ 没有找到采购订单", 'error');
            exit;
        }
        
        debugLog("找到最近订单: ID={$recentOrder['id']}, 订单号={$recentOrder['order_number']}", 'success');
        debugLog("订单日期: {$recentOrder['order_date']}", 'info');
        debugLog("订单金额: {$recentOrder['order_amount']}", 'info');
        debugLog("实际金额: {$recentOrder['actual_amount']}", 'info');
        debugLog("供应商ID: {$recentOrder['supplier_id']}", 'info');
        
        // 2. 检查该订单的明细
        debugLog("=== 2. 检查订单明细 ===", 'info');
        
        $orderItems = $db->fetchAll("SELECT * FROM purchase_order_items WHERE order_id = ? ORDER BY id", [$recentOrder['id']]);
        debugLog("该订单的明细数量: " . count($orderItems), count($orderItems) > 0 ? 'success' : 'error');
        
        if (count($orderItems) === 0) {
            debugLog("❌ 确认：数据库中没有明细数据", 'error');
        } else {
            debugLog("明细详情:", 'info');
            foreach ($orderItems as $index => $item) {
                $ingredient = $db->fetchOne("SELECT name, code FROM ingredients WHERE id = ?", [$item['ingredient_id']]);
                $ingredientInfo = $ingredient ? "{$ingredient['name']}({$ingredient['code']})" : "ID:{$item['ingredient_id']}";
                debugLog("  明细" . ($index + 1) . ": {$ingredientInfo}, 数量={$item['quantity']}, 单价={$item['unit_price']}, 总价={$item['total_price']}", 'success');
            }
        }
        
        // 3. 重新分析Excel文件，找出差异
        debugLog("=== 3. 重新分析Excel文件 ===", 'info');
        
        if (!file_exists($testFile)) {
            debugLog("Excel文件不存在", 'error');
            exit;
        }
        
        $reader = new ExcelReader();
        $data = $reader->read($testFile, 'test.xlsx');
        
        // 查找明细数据开始行
        $detailStartRow = -1;
        for ($i = 5; $i < count($data); $i++) {
            $row = $data[$i];
            $firstCell = trim($row[0] ?? '');
            
            if (strpos($firstCell, '序号') !== false || 
                strpos($firstCell, '商品') !== false ||
                strpos($firstCell, '编码') !== false) {
                $detailStartRow = $i;
                debugLog("找到明细标题行: 第" . ($i + 1) . "行", 'success');
                break;
            }
        }
        
        if ($detailStartRow === -1) {
            debugLog("未找到明细数据开始行", 'error');
            exit;
        }
        
        // 分析每一行明细数据
        debugLog("分析Excel中的明细数据:", 'info');
        
        $validExcelItems = 0;
        $invalidExcelItems = 0;
        
        for ($i = $detailStartRow + 1; $i < count($data); $i++) {
            $row = $data[$i];
            $rowNum = $i + 1;
            
            // 检查空行
            $filteredRow = array_filter($row, function($cell) {
                return !empty(trim($cell));
            });
            
            if (empty($filteredRow)) {
                continue;
            }
            
            // 提取字段
            $itemCode = trim($row[1] ?? '');
            $itemName = trim($row[2] ?? '');
            $quantity = trim($row[9] ?? '');
            $unitPrice = trim($row[8] ?? '');
            
            debugLog("第{$rowNum}行: 编码='{$itemCode}', 名称='{$itemName}', 数量='{$quantity}', 单价='{$unitPrice}'", 'info');
            
            // 验证
            $errors = [];
            if (empty($itemCode)) $errors[] = '编码为空';
            if (!is_numeric($quantity) || floatval($quantity) <= 0) $errors[] = '数量无效';
            if (!is_numeric($unitPrice) || floatval($unitPrice) < 0) $errors[] = '单价无效';
            
            if (empty($errors)) {
                $validExcelItems++;
                debugLog("  ✅ 有效明细", 'success');
            } else {
                $invalidExcelItems++;
                debugLog("  ❌ 无效明细: " . implode(', ', $errors), 'error');
            }
            
            // 只分析前15行
            if (($validExcelItems + $invalidExcelItems) >= 15) {
                debugLog("（只分析前15行明细）", 'info');
                break;
            }
        }
        
        debugLog("Excel分析结果: 有效={$validExcelItems}, 无效={$invalidExcelItems}", 'info');
        
        // 4. 对比分析
        debugLog("=== 4. 对比分析 ===", 'info');
        
        $dbItemCount = count($orderItems);
        $excelValidCount = $validExcelItems;
        
        debugLog("数据库中明细数量: {$dbItemCount}", 'info');
        debugLog("Excel中有效明细数量: {$excelValidCount}", 'info');
        debugLog("差异: " . ($excelValidCount - $dbItemCount), $dbItemCount < $excelValidCount ? 'error' : 'success');
        
        if ($dbItemCount < $excelValidCount) {
            debugLog("❌ 有明细数据丢失！", 'error');
            debugLog("可能的原因:", 'info');
            debugLog("1. 导入过程中发生异常但被静默处理", 'info');
            debugLog("2. 数据库插入失败", 'info');
            debugLog("3. 事务回滚", 'info');
            debugLog("4. 验证逻辑过于严格", 'info');
            debugLog("5. 循环提前退出", 'info');
        } else {
            debugLog("✅ 明细数量匹配", 'success');
        }
        
        // 5. 检查最近的错误日志
        debugLog("=== 5. 检查可能的错误 ===", 'info');
        
        // 检查PHP错误日志（如果有的话）
        $errorLogPath = ini_get('error_log');
        if ($errorLogPath && file_exists($errorLogPath)) {
            $errorLog = file_get_contents($errorLogPath);
            $recentErrors = array_slice(explode("\n", $errorLog), -10);
            debugLog("最近的PHP错误:", 'info');
            foreach ($recentErrors as $error) {
                if (!empty(trim($error))) {
                    debugLog("  " . trim($error), 'warning');
                }
            }
        } else {
            debugLog("无法访问PHP错误日志", 'warning');
        }
        
        debugLog("调试完成", 'info');
        
        echo "</div>";
        echo "</div>";
        
        // 6. 显示建议的解决方案
        echo "<div class='test-section'>";
        echo "<h2>🛠️ 建议的解决方案</h2>";
        
        if ($dbItemCount < $excelValidCount) {
            echo "<div class='step'>";
            echo "<h4>❌ 明细数据丢失，建议：</h4>";
            echo "<ol>";
            echo "<li><strong>检查PurchaseController中的异常处理</strong>：可能有异常被静默捕获</li>";
            echo "<li><strong>添加详细日志</strong>：在importOrderItem方法中添加调试日志</li>";
            echo "<li><strong>检查数据库事务</strong>：确认没有事务回滚</li>";
            echo "<li><strong>逐行调试</strong>：单独测试每一行明细的导入</li>";
            echo "</ol>";
            echo "</div>";
        } else {
            echo "<div class='step'>";
            echo "<h4>✅ 明细数量正确，但如果显示有问题：</h4>";
            echo "<ol>";
            echo "<li>检查前端显示逻辑</li>";
            echo "<li>确认查询条件是否正确</li>";
            echo "<li>检查分页或限制条件</li>";
            echo "</ol>";
            echo "</div>";
        }
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ 调试过程出错: " . $e->getMessage() . "</p>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }
    ?>
    
    <div class="test-section">
        <h2>🎯 下一步操作</h2>
        <div class="step">
            <p>
                <a href="../modules/purchase/index.php" class="btn">📊 查看采购订单</a>
                <a href="add-import-logging.php" class="btn">📝 添加导入日志</a>
                <a href="../modules/purchase/index.php?action=import" class="btn">🔄 重新导入测试</a>
            </p>
        </div>
    </div>
    
    <p><a href="../modules/purchase/index.php">返回采购管理</a></p>
</body>
</html>
