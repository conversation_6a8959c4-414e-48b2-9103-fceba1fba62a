<?php
/**
 * 测试数据库兼容性修复
 */

echo "=== 数据库兼容性修复测试 ===\n\n";

echo "1. 测试字段检查方法:\n";
try {
    require_once 'includes/Database.php';
    require_once 'includes/BaseController.php';
    require_once 'modules/categories/CategoriesController.php';
    
    // 创建控制器实例
    $controller = new CategoriesController();
    
    // 使用反射访问私有方法
    $reflection = new ReflectionClass($controller);
    
    // 测试level字段检查
    $levelMethod = $reflection->getMethod('checkLevelFieldExists');
    $levelMethod->setAccessible(true);
    $hasLevel = $levelMethod->invoke($controller);
    
    echo "   ✅ checkLevelFieldExists 方法: " . ($hasLevel ? '字段存在' : '字段不存在') . "\n";
    
    // 测试parent_id字段检查
    $parentMethod = $reflection->getMethod('checkParentIdFieldExists');
    $parentMethod->setAccessible(true);
    $hasParentId = $parentMethod->invoke($controller);
    
    echo "   ✅ checkParentIdFieldExists 方法: " . ($hasParentId ? '字段存在' : '字段不存在') . "\n";
    
} catch (Exception $e) {
    echo "   ❌ 字段检查测试失败: " . $e->getMessage() . "\n";
}

echo "\n2. 测试数据库状态:\n";
try {
    $db = Database::getInstance();
    
    // 检查表结构
    $columns = $db->fetchAll("SHOW COLUMNS FROM ingredient_categories");
    $columnNames = array_column($columns, 'Field');
    
    echo "   当前表字段:\n";
    foreach ($columnNames as $column) {
        echo "     • {$column}\n";
    }
    
    $hasLevel = in_array('level', $columnNames);
    $hasParentId = in_array('parent_id', $columnNames);
    $hasStatus = in_array('status', $columnNames);
    
    echo "\n   字段存在性检查:\n";
    echo "     " . ($hasLevel ? '✅' : '❌') . " level 字段\n";
    echo "     " . ($hasParentId ? '✅' : '❌') . " parent_id 字段\n";
    echo "     " . ($hasStatus ? '✅' : '❌') . " status 字段\n";
    
    if ($hasLevel && $hasParentId) {
        echo "\n   🎉 数据库已升级为两级分类结构\n";
    } else {
        echo "\n   ⚠️ 数据库尚未升级，使用兼容模式\n";
    }
    
} catch (Exception $e) {
    echo "   ❌ 数据库检查失败: " . $e->getMessage() . "\n";
}

echo "\n3. 测试查询兼容性:\n";
try {
    $db = Database::getInstance();
    
    // 测试基础查询
    echo "   基础分类查询:\n";
    $categories = $db->fetchAll("SELECT * FROM ingredient_categories LIMIT 3");
    echo "     ✅ 查询成功，返回 " . count($categories) . " 条记录\n";
    
    if (!empty($categories)) {
        $firstCategory = $categories[0];
        echo "     示例分类: " . $firstCategory['name'] . "\n";
        
        // 检查字段
        $fields = ['level', 'parent_id', 'status'];
        foreach ($fields as $field) {
            if (array_key_exists($field, $firstCategory)) {
                echo "       ✅ {$field}: " . ($firstCategory[$field] ?? 'null') . "\n";
            } else {
                echo "       ❌ {$field}: 字段不存在\n";
            }
        }
    }
    
} catch (Exception $e) {
    echo "   ❌ 查询测试失败: " . $e->getMessage() . "\n";
}

echo "\n4. 测试页面访问:\n";
$pages = [
    'index.php' => '分类列表页面',
    'index.php?action=create' => '创建分类页面',
    'index.php?level=1' => '一级分类筛选',
    'index.php?search=蔬菜' => '搜索功能'
];

foreach ($pages as $url => $description) {
    echo "   📄 {$description}: http://localhost:8000/modules/categories/{$url}\n";
}

echo "\n5. 兼容性功能总结:\n";
echo "   🔧 自动字段检查:\n";
echo "     • checkLevelFieldExists() - 检查level字段\n";
echo "     • checkParentIdFieldExists() - 检查parent_id字段\n";
echo "\n   🔄 查询适配:\n";
echo "     • 数据库已升级 - 使用完整的两级分类查询\n";
echo "     • 数据库未升级 - 使用简化查询并添加默认字段\n";
echo "\n   🛡️ 错误防护:\n";
echo "     • 所有level字段访问都有默认值\n";
echo "     • 所有parent_id字段访问都有默认值\n";
echo "     • 查询失败时使用模拟数据\n";

echo "\n6. 升级建议:\n";
if (!isset($hasLevel) || !$hasLevel) {
    echo "   📋 建议执行数据库升级以获得完整功能:\n";
    echo "   ```bash\n";
    echo "   mysql -u用户名 -p数据库名 < scripts/database/upgrade_categories_to_two_level.sql\n";
    echo "   ```\n";
    echo "\n   升级后将获得:\n";
    echo "     • 完整的两级分类支持\n";
    echo "     • 父子分类关系管理\n";
    echo "     • 分类层级显示\n";
    echo "     • 高级筛选功能\n";
} else {
    echo "   ✅ 数据库已升级，享受完整的两级分类功能！\n";
}

echo "\n=== 数据库兼容性修复测试完成 ===\n";
echo "🎉 分类管理现在可以在任何数据库状态下正常工作！\n";
echo "🔄 支持数据库升级前后的无缝切换\n";
echo "🛡️ 完整的错误防护和兼容性处理\n";
?>
