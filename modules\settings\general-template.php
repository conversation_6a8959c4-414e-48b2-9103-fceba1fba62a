<?php require_once dirname(__DIR__, 2) . '/includes/header-modular.php'; ?>

<div class="main-content">
    <?php require_once dirname(__DIR__, 2) . '/includes/sidebar-modular.php'; ?>
    
    <div class="content">
        <div class="content-header">
            <h1>
                <i class="fas fa-sliders-h"></i>
                基本设置
            </h1>
            <div class="header-actions">
                <a href="index.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i>
                    返回设置
                </a>
            </div>
        </div>

        <?php if (!empty($error)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle"></i>
            <?= htmlspecialchars($error) ?>
        </div>
        <?php elseif (!empty($_GET['success'])): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i>
            设置保存成功
        </div>
        <?php endif; ?>

        <div class="settings-container">
            <form method="POST" action="index.php?action=general" class="settings-form">
                <?= CSRFProtection::getHiddenField() ?>
                <!-- 网站信息 -->
                <div class="setting-section">
                    <h2><i class="fas fa-globe"></i> 网站信息</h2>
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="site_name">网站名称 <span class="required">*</span></label>
                            <input type="text" id="site_name" name="site_name" class="form-control" 
                                   value="<?= htmlspecialchars($settings['site_name'] ?? '') ?>" required>
                            <small class="form-text">显示在页面标题和导航栏中的网站名称</small>
                        </div>
                        <div class="form-group">
                            <label for="admin_email">管理员邮箱 <span class="required">*</span></label>
                            <input type="email" id="admin_email" name="admin_email" class="form-control" 
                                   value="<?= htmlspecialchars($settings['admin_email'] ?? '') ?>" required>
                            <small class="form-text">用于接收系统通知和重要信息</small>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="site_description">网站描述</label>
                        <textarea id="site_description" name="site_description" class="form-control" rows="3"
                                  placeholder="简要描述网站的功能和用途"><?= htmlspecialchars($settings['site_description'] ?? '') ?></textarea>
                    </div>
                </div>

                <!-- 地区和时间 -->
                <div class="setting-section">
                    <h2><i class="fas fa-clock"></i> 地区和时间</h2>
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="timezone">时区</label>
                            <select id="timezone" name="timezone" class="form-control">
                                <option value="Asia/Shanghai" <?= ($settings['timezone'] ?? '') === 'Asia/Shanghai' ? 'selected' : '' ?>>中国标准时间 (UTC+8)</option>
                                <option value="Asia/Hong_Kong" <?= ($settings['timezone'] ?? '') === 'Asia/Hong_Kong' ? 'selected' : '' ?>>香港时间 (UTC+8)</option>
                                <option value="Asia/Taipei" <?= ($settings['timezone'] ?? '') === 'Asia/Taipei' ? 'selected' : '' ?>>台北时间 (UTC+8)</option>
                                <option value="UTC" <?= ($settings['timezone'] ?? '') === 'UTC' ? 'selected' : '' ?>>协调世界时 (UTC)</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="currency_symbol">货币符号</label>
                            <input type="text" id="currency_symbol" name="currency_symbol" class="form-control" 
                                   value="<?= htmlspecialchars($settings['currency_symbol'] ?? '¥') ?>" maxlength="5">
                        </div>
                    </div>
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="date_format">日期格式</label>
                            <select id="date_format" name="date_format" class="form-control">
                                <option value="Y-m-d" <?= ($settings['date_format'] ?? '') === 'Y-m-d' ? 'selected' : '' ?>>2024-01-01</option>
                                <option value="Y/m/d" <?= ($settings['date_format'] ?? '') === 'Y/m/d' ? 'selected' : '' ?>>2024/01/01</option>
                                <option value="d/m/Y" <?= ($settings['date_format'] ?? '') === 'd/m/Y' ? 'selected' : '' ?>>01/01/2024</option>
                                <option value="m/d/Y" <?= ($settings['date_format'] ?? '') === 'm/d/Y' ? 'selected' : '' ?>>01/01/2024</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="time_format">时间格式</label>
                            <select id="time_format" name="time_format" class="form-control">
                                <option value="H:i:s" <?= ($settings['time_format'] ?? '') === 'H:i:s' ? 'selected' : '' ?>>24小时制 (14:30:00)</option>
                                <option value="H:i" <?= ($settings['time_format'] ?? '') === 'H:i' ? 'selected' : '' ?>>24小时制 (14:30)</option>
                                <option value="g:i A" <?= ($settings['time_format'] ?? '') === 'g:i A' ? 'selected' : '' ?>>12小时制 (2:30 PM)</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- 显示设置 -->
                <div class="setting-section">
                    <h2><i class="fas fa-eye"></i> 显示设置</h2>
                    <div class="form-group">
                        <label for="items_per_page">每页显示条数</label>
                        <select id="items_per_page" name="items_per_page" class="form-control">
                            <option value="10" <?= ($settings['items_per_page'] ?? 20) == 10 ? 'selected' : '' ?>>10 条</option>
                            <option value="20" <?= ($settings['items_per_page'] ?? 20) == 20 ? 'selected' : '' ?>>20 条</option>
                            <option value="50" <?= ($settings['items_per_page'] ?? 20) == 50 ? 'selected' : '' ?>>50 条</option>
                            <option value="100" <?= ($settings['items_per_page'] ?? 20) == 100 ? 'selected' : '' ?>>100 条</option>
                        </select>
                        <small class="form-text">列表页面默认显示的记录数量</small>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        保存设置
                    </button>
                    <button type="reset" class="btn btn-secondary">
                        <i class="fas fa-undo"></i>
                        重置
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.settings-container {
    max-width: 800px;
}

.settings-form {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
}

.setting-section {
    margin-bottom: 32px;
    padding-bottom: 24px;
    border-bottom: 1px solid #e5e7eb;
}

.setting-section:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.setting-section h2 {
    margin: 0 0 20px 0;
    color: #1f2937;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    margin-bottom: 16px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.form-group label {
    font-weight: 500;
    color: #374151;
}

.required {
    color: #dc2626;
}

.form-control {
    padding: 10px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.form-control:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-text {
    font-size: 12px;
    color: #6b7280;
}

.form-actions {
    display: flex;
    gap: 12px;
    padding-top: 24px;
    border-top: 1px solid #e5e7eb;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #f3f4f6;
    color: #6b7280;
    border: 1px solid #d1d5db;
}

.btn-secondary:hover {
    background: #e5e7eb;
}

.alert {
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.alert-success {
    background: #dcfce7;
    color: #166534;
    border: 1px solid #bbf7d0;
}

.alert-danger {
    background: #fee2e2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

@media (max-width: 768px) {
    .form-grid {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
    }
}
</style>

<?php require_once dirname(__DIR__, 2) . '/includes/footer-modular.php'; ?>
