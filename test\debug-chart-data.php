<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试图表数据</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .debug-log { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; font-family: monospace; font-size: 12px; max-height: 400px; overflow-y: auto; }
        .btn { display: inline-block; padding: 8px 16px; background: #007cba; color: white; text-decoration: none; border-radius: 4px; margin: 5px; }
        .data-table { width: 100%; border-collapse: collapse; margin: 10px 0; font-size: 12px; }
        .data-table th, .data-table td { border: 1px solid #ddd; padding: 6px; text-align: left; }
        .data-table th { background: #f5f5f5; }
    </style>
</head>
<body>
    <h1>🔍 调试图表数据问题</h1>
    
    <?php
    require_once '../includes/Database.php';
    require_once '../modules/dashboard/DashboardController.php';
    
    try {
        $db = Database::getInstance();
        
        echo "<div class='test-section'>";
        echo "<h2>📊 检查数据库中的采购订单数据</h2>";
        
        // 检查purchase_orders表是否存在
        $tables = $db->fetchAll("SHOW TABLES LIKE 'purchase_orders'");
        if (empty($tables)) {
            echo "<p class='error'>❌ purchase_orders表不存在</p>";
        } else {
            echo "<p class='success'>✅ purchase_orders表存在</p>";
            
            // 检查表结构
            $columns = $db->fetchAll("DESCRIBE purchase_orders");
            $columnNames = array_column($columns, 'Field');
            echo "<p class='info'>表字段: " . implode(', ', $columnNames) . "</p>";
            
            // 检查数据
            $count = $db->fetchOne("SELECT COUNT(*) as count FROM purchase_orders")['count'];
            echo "<p class='info'>订单总数: <strong>{$count}</strong></p>";
            
            if ($count > 0) {
                // 显示最近的订单
                $recentOrders = $db->fetchAll("SELECT * FROM purchase_orders ORDER BY created_at DESC LIMIT 5");
                
                echo "<h4>最近的5个订单：</h4>";
                echo "<table class='data-table'>";
                echo "<tr><th>ID</th><th>订单号</th><th>金额</th><th>状态</th><th>创建时间</th></tr>";
                
                foreach ($recentOrders as $order) {
                    echo "<tr>";
                    echo "<td>{$order['id']}</td>";
                    echo "<td>{$order['order_number']}</td>";
                    echo "<td>" . ($order['actual_amount'] ?? $order['order_amount'] ?? '0') . "</td>";
                    echo "<td>{$order['order_status']}</td>";
                    echo "<td>{$order['created_at']}</td>";
                    echo "</tr>";
                }
                echo "</table>";
                
                // 测试月度采购数据查询
                echo "<h4>月度采购数据查询测试：</h4>";
                
                $monthlyData = $db->fetchAll("
                    SELECT 
                        DATE_FORMAT(created_at, '%Y-%m') as month,
                        COUNT(*) as order_count,
                        SUM(COALESCE(actual_amount, order_amount, 0)) as total_amount
                    FROM purchase_orders
                    WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
                    AND order_status != 'cancelled'
                    GROUP BY DATE_FORMAT(created_at, '%Y-%m')
                    ORDER BY month ASC
                ");
                
                if (empty($monthlyData)) {
                    echo "<p class='warning'>⚠️ 月度数据查询结果为空</p>";
                    
                    // 尝试简化查询
                    $simpleData = $db->fetchAll("
                        SELECT 
                            DATE_FORMAT(created_at, '%Y-%m') as month,
                            COUNT(*) as order_count
                        FROM purchase_orders
                        GROUP BY DATE_FORMAT(created_at, '%Y-%m')
                        ORDER BY month ASC
                    ");
                    
                    echo "<p class='info'>简化查询结果: " . count($simpleData) . " 条记录</p>";
                    
                    if (!empty($simpleData)) {
                        echo "<table class='data-table'>";
                        echo "<tr><th>月份</th><th>订单数</th></tr>";
                        foreach ($simpleData as $item) {
                            echo "<tr><td>{$item['month']}</td><td>{$item['order_count']}</td></tr>";
                        }
                        echo "</table>";
                    }
                } else {
                    echo "<p class='success'>✅ 月度数据查询成功: " . count($monthlyData) . " 条记录</p>";
                    
                    echo "<table class='data-table'>";
                    echo "<tr><th>月份</th><th>订单数</th><th>总金额</th></tr>";
                    foreach ($monthlyData as $item) {
                        echo "<tr>";
                        echo "<td>{$item['month']}</td>";
                        echo "<td>{$item['order_count']}</td>";
                        echo "<td>{$item['total_amount']}</td>";
                        echo "</tr>";
                    }
                    echo "</table>";
                }
            }
        }
        echo "</div>";
        
        echo "<div class='test-section'>";
        echo "<h2>🔧 测试DashboardController</h2>";
        
        // 直接测试控制器方法
        $controller = new DashboardController();
        
        // 使用反射调用私有方法
        $reflection = new ReflectionClass($controller);
        $method = $reflection->getMethod('getMonthlyPurchaseData');
        $method->setAccessible(true);
        
        try {
            $data = $method->invoke($controller);
            
            echo "<p class='success'>✅ 控制器方法调用成功</p>";
            echo "<p class='info'>返回数据: " . count($data) . " 条记录</p>";
            
            if (!empty($data)) {
                echo "<h4>控制器返回的数据：</h4>";
                echo "<div class='debug-log'>";
                echo json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
                echo "</div>";
            } else {
                echo "<p class='warning'>⚠️ 控制器返回空数据</p>";
            }
            
        } catch (Exception $e) {
            echo "<p class='error'>❌ 控制器方法调用失败: " . $e->getMessage() . "</p>";
        }
        echo "</div>";
        
        echo "<div class='test-section'>";
        echo "<h2>🌐 测试AJAX接口</h2>";
        
        echo "<button onclick='testAjaxRequest()' class='btn'>测试AJAX请求</button>";
        echo "<div id='ajaxResult' class='debug-log' style='display: none;'></div>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ 调试过程出错: " . $e->getMessage() . "</p>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }
    ?>
    
    <div class="test-section">
        <h2>🛠️ 可能的解决方案</h2>
        <div class="step">
            <h4>问题分析：</h4>
            <ul>
                <li>检查purchase_orders表是否有数据</li>
                <li>检查字段名是否匹配（total_amount vs actual_amount vs order_amount）</li>
                <li>检查AJAX请求头设置</li>
                <li>检查JavaScript控制台错误</li>
            </ul>
            
            <h4>修复步骤：</h4>
            <ol>
                <li>确保数据库有采购订单数据</li>
                <li>修正字段名映射</li>
                <li>修复AJAX请求头</li>
                <li>添加错误处理</li>
            </ol>
        </div>
    </div>
    
    <script>
    function testAjaxRequest() {
        const resultDiv = document.getElementById('ajaxResult');
        resultDiv.style.display = 'block';
        resultDiv.innerHTML = '正在测试AJAX请求...';
        
        fetch('../modules/dashboard/chart-data.php?type=monthly_purchase', {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            resultDiv.innerHTML = '<h4>AJAX响应:</h4><pre>' + JSON.stringify(data, null, 2) + '</pre>';
        })
        .catch(error => {
            resultDiv.innerHTML = '<h4>AJAX错误:</h4><pre>' + error.message + '</pre>';
        });
    }
    </script>
    
    <p><a href="../modules/dashboard/index.php">返回仪表板</a></p>
</body>
</html>
