/* 桌面端统一主题（轻量覆盖，不破坏现有布局） */

/* 设计令牌 */
:root {
  --color-bg: #f6f8fb;
  --color-surface: #ffffff;
  --color-border: #e5e7eb;
  --color-text: #1f2937;
  --color-muted: #6b7280;

  --color-primary: #667eea; /* 主色：靛蓝 */
  --color-primary-strong: #5a67d8;
  --color-secondary: #718096; /* 次色：灰蓝 */
  --color-success: #38a169;
  --color-warning: #ed8936;
  --color-danger: #e53e3e;
  --color-info: #4299e1;

  --radius-sm: 6px;
  --radius-md: 10px;
  --radius-lg: 12px;
  --shadow-sm: 0 1px 3px rgba(0,0,0,0.08);
  --shadow-md: 0 4px 12px rgba(0,0,0,0.12);
  --shadow-lg: 0 10px 25px rgba(0,0,0,0.15);
}

/* 基础层级与排版 */
html, body {
  background: var(--color-bg) !important;
  color: var(--color-text) !important;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif !important;
}



a { color: var(--color-info); text-decoration: none; }
a:hover { color: #3182ce; text-decoration: underline; }

/* 容器与卡片 */
.content-header,
.card,
.search-bar,
.table-container {
  background: var(--color-surface) !important;
  border: 1px solid var(--color-border) !important;
  border-radius: var(--radius-lg) !important;
  box-shadow: var(--shadow-sm) !important;
}

.content-header {
  padding: 22px !important;
}

.card-hover:hover,
.stat-card:hover {
  transform: translateY(-2px) !important;
  box-shadow: var(--shadow-md) !important;
}

/* 侧边栏（保持深色风格，优化对比） */
.sidebar {
  background: linear-gradient(180deg, #2d3748 0%, #1f2937 100%) !important;
}

.nav-item.active {
  background: linear-gradient(135deg, var(--color-primary) 0%, #764ba2 100%) !important;
  border-left-color: var(--color-primary) !important;
}

/* 按钮（统一圆角与配色） */
.btn {
  border-radius: var(--radius-sm) !important;
}

.btn-primary, .action-buttons .btn-info {
  background: var(--color-primary) !important;
  color: #fff !important;
}
.btn-primary:hover, .action-buttons .btn-info:hover {
  background: var(--color-primary-strong) !important;
}

.btn-success { background: var(--color-success) !important; color: #fff !important; }
.btn-success:hover { background: #2f855a !important; }
.btn-secondary { background: var(--color-secondary) !important; color: #fff !important; }
.btn-secondary:hover { background: #4a5568 !important; }
.btn-warning { background: var(--color-warning) !important; color: #fff !important; }
.btn-warning:hover { background: #dd6b20 !important; }
.btn-danger { background: var(--color-danger) !important; color: #fff !important; }
.btn-danger:hover { background: #c53030 !important; }

.btn-outline-primary {
  color: var(--color-primary) !important;
  border-color: var(--color-primary) !important;
}
.btn-outline-primary:hover {
  background: var(--color-primary) !important;
  color: #fff !important;
}

/* 表格（与 table-enhanced.css 协同） */
.table th {
  background: linear-gradient(135deg, #eef2ff 0%, #e5e7eb 100%) !important;
  color: #1e3a8a !important;
  border-bottom: 1px solid var(--color-border) !important;
}

.table td {
  color: #374151 !important;
}

.table tbody tr:hover {
  background: #f8fafc !important;
}

/* 搜索栏（压缩高度、统一控件尺寸） */
.search-bar .form-control,
.search-bar .btn {
  height: 38px !important;
  border-radius: var(--radius-sm) !important;
}

/* 徽章统一 */
.badge-primary { background: #e0e7ff !important; color: #3730a3 !important; }
.badge-success { background: #bbf7d0 !important; color: #166534 !important; }
.badge-warning { background: #fed7aa !important; color: #9a3412 !important; }
.badge-danger  { background: #fecaca !important; color: #991b1b !important; }
.badge-info    { background: #bfdbfe !important; color: #1e3a8a !important; }

/* 分页统一 */
.pagination .page-link {
  border-radius: var(--radius-sm) !important;
}
.page-item.active .page-link {
  background: var(--color-primary) !important;
  border-color: var(--color-primary) !important;
}

/* 表单统一 */
.form-control {
  border-radius: var(--radius-sm) !important;
  border-color: var(--color-border) !important;
}
.form-control:focus {
  border-color: var(--color-primary) !important;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.15) !important;
}

/* 统计卡片主题色块（与现有类名适配） */
.stat-icon.bg-blue    { background: linear-gradient(135deg, #60a5fa 0%, #3b82f6 100%) !important; }
.stat-icon.bg-green   { background: linear-gradient(135deg, #34d399 0%, #10b981 100%) !important; }
.stat-icon.bg-orange  { background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%) !important; }
.stat-icon.bg-purple  { background: linear-gradient(135deg, #a78bfa 0%, #8b5cf6 100%) !important; }

/* 顶部色条微调（与模块卡片视觉统一） */
.stat-card::before,
.search-bar::before {
  background: linear-gradient(90deg, var(--color-primary) 0%, #764ba2 100%) !important;
}

/* 统一阴影和圆角到图表容器/数据块 */
.chart-card,
.data-table,
.table-container {
  border-radius: var(--radius-lg) !important;
  box-shadow: var(--shadow-sm) !important;
}
/* 居中弹窗提示 */
.center-toast {
  position: fixed;
  left: 50%;
  top: 18%;
  transform: translate(-50%, -50%);
  background: rgba(17,24,39,0.92);
  color: #fff;
  padding: 12px 16px;
  border-radius: 10px;
  display: inline-flex;
  align-items: center;
  gap: 10px;
  box-shadow: var(--shadow-lg);
  z-index: 9999;
  transition: opacity .3s ease, transform .3s ease;
}
.center-toast.hide { opacity: 0; transform: translate(-50%, -30%); }
.center-toast-success { background: rgba(16,185,129,.95); }
.center-toast-error { background: rgba(239,68,68,.95); }
.center-toast .toast-close { background: transparent; border: 0; color: #fff; cursor: pointer; }

/* 操作说明条 */
.ops-help { position: relative; margin-bottom: 16px; }
.ops-help .ops-help-inner {
  display: flex; align-items: center; gap: 14px; background: #eef2ff; color: #3730a3;
  padding: 10px 14px; border: 1px solid #c7d2fe; border-radius: 10px;
}
.ops-help .ops-help-inner span { display: inline-flex; align-items: center; gap: 6px; }
.ops-help .ops-help-close { margin-left: auto; background: transparent; border: 0; color: #4f46e5; cursor: pointer; }

/* 列表操作按钮在小屏幕仅显示图标，大屏幕显示图标+文字 */
.action-buttons .btn .btn-text { display: none; }
@media (min-width: 1024px) {
  .action-buttons .btn .btn-text { display: inline; }
}

/* 侧边栏LOGO副标题改为白色 */
.sidebar-header p { color: #ffffff !important; }

/* 轻微动画统一 */
.btn:hover { transform: translateY(-1px); }
.card, .stat-card, .table-container, .search-bar { transition: box-shadow .2s ease, transform .2s ease; }


