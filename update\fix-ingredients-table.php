<?php
/**
 * 修复ingredients表结构
 */

require_once '../includes/Database.php';

try {
    $db = Database::getInstance();
    
    echo "<h1>修复ingredients表结构</h1>";
    
    // 检查当前表结构
    echo "<h2>1. 检查当前表结构</h2>";
    
    $columns = $db->fetchAll("DESCRIBE ingredients");
    $columnNames = array_column($columns, 'Field');
    
    echo "<p>当前字段: " . implode(', ', $columnNames) . "</p>";
    
    // 需要的字段
    $requiredColumns = [
        'brand' => "VARCHAR(50) COMMENT '品牌'",
        'origin' => "VARCHAR(50) COMMENT '产地'", 
        'shelf_life' => "VARCHAR(50) COMMENT '保质期'"
    ];
    
    echo "<h2>2. 添加缺少的字段</h2>";
    
    foreach ($requiredColumns as $column => $definition) {
        if (!in_array($column, $columnNames)) {
            try {
                $sql = "ALTER TABLE ingredients ADD COLUMN {$column} {$definition}";
                $db->query($sql);
                echo "<p style='color: green;'>✅ 添加字段 {$column} 成功</p>";
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ 添加字段 {$column} 失败: " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<p style='color: blue;'>ℹ️ 字段 {$column} 已存在</p>";
        }
    }
    
    echo "<h2>3. 验证修复结果</h2>";
    
    $newColumns = $db->fetchAll("DESCRIBE ingredients");
    $newColumnNames = array_column($newColumns, 'Field');
    
    echo "<p>修复后字段: " . implode(', ', $newColumnNames) . "</p>";
    
    $missingColumns = array_diff(array_keys($requiredColumns), $newColumnNames);
    
    if (empty($missingColumns)) {
        echo "<p style='color: green;'>✅ 所有必需字段都已存在</p>";
        echo "<h2>✅ 修复完成！</h2>";
        echo "<p><a href='../modules/purchase/index.php?action=import'>现在可以重新测试导入功能</a></p>";
        echo "<p><a href='../test/view-import-log.php?clear_log=1'>清空日志重新测试</a></p>";
    } else {
        echo "<p style='color: red;'>❌ 仍然缺少字段: " . implode(', ', $missingColumns) . "</p>";
    }
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>❌ 修复失败</h2>";
    echo "<p style='color: red;'>错误: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
