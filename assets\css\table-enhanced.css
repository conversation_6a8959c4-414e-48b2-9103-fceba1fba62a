/**
 * 统一表格美化样式
 * 适用于所有列表页面
 */

/* 表格容器 */
.table-container {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e2e8f0;
    overflow: hidden;
    margin-bottom: 20px;
    position: relative;
}

/* 表格基础样式 */
.table {
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
    margin: 0;
    font-size: 14px;
    line-height: 1.5;
    table-layout: auto;
}

/* 表头美化 */
.table thead {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    position: relative;
}

.table thead::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #4299e1, #48bb78, #ed8936);
}

.table th {
    padding: 18px 16px;
    text-align: left;
    font-weight: 600;
    color: #2d3748;
    font-size: 13px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: none;
    position: relative;
    background: transparent;
}

.table th:not(:last-child)::after {
    content: '';
    position: absolute;
    right: 0;
    top: 25%;
    bottom: 25%;
    width: 1px;
    background: #cbd5e0;
}

/* 表格行美化 */
.table tbody tr {
    border-bottom: 1px solid #f1f5f9;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.table tbody tr:hover {
    background: #f8fafc;
    border-color: #e2e8f0;
}

.table tbody tr:last-child {
    border-bottom: none;
}

/* 表格单元格 */
.table td {
    padding: 16px;
    vertical-align: middle;
    border: none;
    color: #4a5568;
    position: relative;
}

.table td:first-child {
    padding-left: 20px;
}

.table td:last-child {
    padding-right: 20px;
}

/* 表格内容样式 */
.table td strong {
    color: #2d3748;
    font-weight: 600;
}

.table td small {
    color: #718096;
    font-size: 12px;
    display: block;
    margin-top: 2px;
}

/* 状态徽章美化 */
.badge {
    display: inline-flex;
    align-items: center;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: 1px solid transparent;
    position: relative;
    overflow: hidden;
}

.badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.badge:hover::before {
    left: 100%;
}

.badge-success {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(72, 187, 120, 0.3);
}

.badge-warning {
    background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(237, 137, 54, 0.3);
}

.badge-danger {
    background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(229, 62, 62, 0.3);
}

.badge-info {
    background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(66, 153, 225, 0.3);
}

.badge-secondary {
    background: linear-gradient(135deg, #718096 0%, #4a5568 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(113, 128, 150, 0.3);
}

/* 操作按钮组美化 */
.btn-group {
    display: flex;
    gap: 6px;
    align-items: center;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid transparent;
    position: relative;
    overflow: hidden;
}

.btn-sm::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-sm:hover::before {
    left: 100%;
}

.btn-outline-primary {
    color: #4299e1;
    border-color: #4299e1;
    background: rgba(66, 153, 225, 0.05);
}

.btn-outline-primary:hover {
    background: #4299e1;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(66, 153, 225, 0.3);
}

.btn-outline-danger {
    color: #e53e3e;
    border-color: #e53e3e;
    background: rgba(229, 62, 62, 0.05);
}

.btn-outline-danger:hover {
    background: #e53e3e;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(229, 62, 62, 0.3);
}

.btn-outline-secondary {
    color: #718096;
    border-color: #cbd5e0;
    background: rgba(113, 128, 150, 0.05);
}

.btn-outline-secondary:hover {
    background: #718096;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(113, 128, 150, 0.3);
}

/* 数值显示美化 */
.text-success {
    color: #48bb78 !important;
    font-weight: 600;
}

.text-danger {
    color: #e53e3e !important;
    font-weight: 600;
}

.text-warning {
    color: #ed8936 !important;
    font-weight: 600;
}

.text-info {
    color: #4299e1 !important;
    font-weight: 600;
}

/* 价格显示 */
.price {
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    font-weight: 600;
    color: #2d3748;
}

/* 联系方式链接 */
.phone-link, .email-link {
    color: #4299e1;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.2s ease;
    border-radius: 4px;
    padding: 2px 4px;
}

.phone-link:hover, .email-link:hover {
    background: rgba(66, 153, 225, 0.1);
    color: #3182ce;
}

/* 空状态美化 */
.table tbody tr td[colspan] {
    text-align: center;
    padding: 60px 20px;
    color: #718096;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.table tbody tr td[colspan] i {
    font-size: 48px;
    color: #cbd5e0;
    margin-bottom: 16px;
    display: block;
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-8px); }
}

/* 响应式表格 */
@media (max-width: 768px) {
    .table-container {
        border-radius: 12px;
        margin: 0 10px 20px 10px;
    }
    
    .table {
        font-size: 13px;
    }
    
    .table th,
    .table td {
        padding: 12px 8px;
    }
    
    .table td:first-child {
        padding-left: 12px;
    }
    
    .table td:last-child {
        padding-right: 12px;
    }
    
    .btn-group {
        flex-direction: column;
        gap: 4px;
    }
    
    .btn-sm {
        width: 100%;
        justify-content: center;
    }
}

/* 表格加载状态 */
.table-loading {
    position: relative;
}

.table-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

/* 表格动画 */
.table tbody tr {
    animation: slideInUp 0.3s ease-out;
    animation-fill-mode: both;
}

.table tbody tr:nth-child(1) { animation-delay: 0.05s; }
.table tbody tr:nth-child(2) { animation-delay: 0.1s; }
.table tbody tr:nth-child(3) { animation-delay: 0.15s; }
.table tbody tr:nth-child(4) { animation-delay: 0.2s; }
.table tbody tr:nth-child(5) { animation-delay: 0.25s; }

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 特殊数据显示美化 */
.supplier-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.supplier-address {
    font-size: 12px;
    color: #718096;
    font-style: italic;
}

.item-count-badge {
    background: linear-gradient(135deg, #e6fffa 0%, #b2f5ea 100%);
    color: #234e52;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    border: 1px solid #81e6d9;
}

/* 进度条美化 */
.progress {
    height: 8px;
    background: #e2e8f0;
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #4299e1, #48bb78);
    border-radius: 4px;
    transition: width 0.6s ease;
    position: relative;
    overflow: hidden;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* 数据编码显示 */
.data-code {
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    background: #f7fafc;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    color: #4a5568;
    border: 1px solid #e2e8f0;
}

/* 日期时间显示 */
.datetime {
    color: #4a5568;
    font-size: 13px;
}

.date-main {
    font-weight: 600;
    color: #2d3748;
}

.time-sub {
    font-size: 11px;
    color: #718096;
    margin-top: 2px;
}

/* 统计数字美化 */
.stat-number {
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    font-weight: 700;
    font-size: 16px;
}

/* 表格行状态 */
.table tbody tr.row-highlight {
    background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
    border-left: 4px solid #e53e3e;
}

.table tbody tr.row-success {
    background: linear-gradient(135deg, #f0fff4 0%, #c6f6d5 100%);
    border-left: 4px solid #48bb78;
}

.table tbody tr.row-warning {
    background: linear-gradient(135deg, #fffaf0 0%, #fbd38d 100%);
    border-left: 4px solid #ed8936;
}

/* 操作按钮特殊样式 */
.btn-action {
    padding: 4px 8px;
    font-size: 11px;
    border-radius: 6px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* 表格内的图标 */
.table td i {
    margin-right: 4px;
    opacity: 0.8;
}

.table td .fas.fa-check-circle {
    color: #48bb78;
}

.table td .fas.fa-exclamation-triangle {
    color: #ed8936;
}

.table td .fas.fa-times-circle {
    color: #e53e3e;
}

.table td .fas.fa-clock {
    color: #4299e1;
}

/* 表格内的链接 */
.table td a {
    color: #4299e1;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.2s ease;
}

.table td a:hover {
    color: #3182ce;
    text-decoration: underline;
}

/* 表格排序指示器 */
.table th.sortable {
    cursor: pointer;
    user-select: none;
    position: relative;
}

.table th.sortable:hover {
    background: rgba(66, 153, 225, 0.1);
}

.table th.sortable::after {
    content: '\f0dc';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    opacity: 0.3;
    font-size: 10px;
}

.table th.sortable.sort-asc::after {
    content: '\f0de';
    opacity: 1;
    color: #4299e1;
}

.table th.sortable.sort-desc::after {
    content: '\f0dd';
    opacity: 1;
    color: #4299e1;
}
