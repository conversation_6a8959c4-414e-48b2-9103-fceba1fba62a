/**
 * 移动端主要JavaScript功能
 */

// 防止页面缩放
document.addEventListener('touchstart', function(event) {
    if (event.touches.length > 1) {
        event.preventDefault();
    }
});

// 防止双击缩放
let lastTouchEnd = 0;
document.addEventListener('touchend', function(event) {
    const now = (new Date()).getTime();
    if (now - lastTouchEnd <= 300) {
        event.preventDefault();
    }
    lastTouchEnd = now;
}, false);

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化通知功能
    initNotifications();

    // 初始化统计数据动画
    initStatsAnimation();

    // 初始化底部导航
    initBottomNav();

    // 检查网络状态
    checkNetworkStatus();

    // 初始化下拉刷新
    initPullToRefresh();

    // 初始化手势操作
    initGestureControls();

    // 初始化页面转场效果
    initPageTransitions();

    // 初始化性能监控
    initPerformanceMonitoring();
});

// 通知功能
function initNotifications() {
    const notificationBtn = document.querySelector('.notification-btn');
    if (notificationBtn) {
        notificationBtn.addEventListener('click', function() {
            showNotificationPanel();
        });
    }
}

// 显示通知面板
function showNotificationPanel() {
    // 创建通知面板
    const panel = document.createElement('div');
    panel.className = 'notification-panel';
    panel.innerHTML = `
        <div class="panel-overlay" onclick="closeNotificationPanel()"></div>
        <div class="panel-content">
            <div class="panel-header">
                <h3>通知中心</h3>
                <button onclick="closeNotificationPanel()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="panel-body">
                <div class="notification-item">
                    <div class="notification-icon warning">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="notification-content">
                        <div class="notification-title">库存预警</div>
                        <div class="notification-message">白菜库存不足，请及时补货</div>
                        <div class="notification-time">2分钟前</div>
                    </div>
                </div>
                <div class="notification-item">
                    <div class="notification-icon success">
                        <i class="fas fa-check"></i>
                    </div>
                    <div class="notification-content">
                        <div class="notification-title">入库完成</div>
                        <div class="notification-message">猪肉入库操作已完成</div>
                        <div class="notification-time">10分钟前</div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(panel);
    
    // 添加样式
    if (!document.getElementById('notification-panel-style')) {
        const style = document.createElement('style');
        style.id = 'notification-panel-style';
        style.textContent = `
            .notification-panel {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                z-index: 9999;
                display: flex;
                align-items: flex-end;
            }
            
            .panel-overlay {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.5);
                backdrop-filter: blur(5px);
            }
            
            .panel-content {
                position: relative;
                background: white;
                width: 100%;
                max-height: 70vh;
                border-radius: 20px 20px 0 0;
                animation: slideUp 0.3s ease-out;
            }
            
            .panel-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 20px;
                border-bottom: 1px solid #f0f0f0;
            }
            
            .panel-header h3 {
                margin: 0;
                font-size: 18px;
                font-weight: 600;
            }
            
            .panel-header button {
                background: none;
                border: none;
                font-size: 18px;
                color: #999;
                cursor: pointer;
                padding: 5px;
                border-radius: 50%;
            }
            
            .panel-body {
                padding: 20px;
                max-height: 50vh;
                overflow-y: auto;
            }
            
            .notification-item {
                display: flex;
                gap: 15px;
                padding: 15px 0;
                border-bottom: 1px solid #f5f5f5;
            }
            
            .notification-item:last-child {
                border-bottom: none;
            }
            
            .notification-icon {
                width: 40px;
                height: 40px;
                border-radius: 10px;
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-size: 16px;
            }
            
            .notification-icon.warning {
                background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            }
            
            .notification-icon.success {
                background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            }
            
            .notification-title {
                font-weight: 600;
                color: #333;
                margin-bottom: 4px;
            }
            
            .notification-message {
                font-size: 14px;
                color: #666;
                margin-bottom: 4px;
            }
            
            .notification-time {
                font-size: 12px;
                color: #999;
            }
            
            @keyframes slideUp {
                from {
                    transform: translateY(100%);
                }
                to {
                    transform: translateY(0);
                }
            }
        `;
        document.head.appendChild(style);
    }
    
    // 动画效果
    setTimeout(() => {
        panel.querySelector('.panel-content').style.transform = 'translateY(0)';
    }, 10);
}

// 关闭通知面板
function closeNotificationPanel() {
    const panel = document.querySelector('.notification-panel');
    if (panel) {
        panel.querySelector('.panel-content').style.transform = 'translateY(100%)';
        setTimeout(() => {
            panel.remove();
        }, 300);
    }
}

// 统计数据动画
function initStatsAnimation() {
    const statNumbers = document.querySelectorAll('.stat-number');
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateNumber(entry.target);
                observer.unobserve(entry.target);
            }
        });
    });
    
    statNumbers.forEach(number => {
        observer.observe(number);
    });
}

// 数字动画
function animateNumber(element) {
    const target = parseInt(element.textContent);
    const duration = 1000;
    const step = target / (duration / 16);
    let current = 0;
    
    const timer = setInterval(() => {
        current += step;
        if (current >= target) {
            current = target;
            clearInterval(timer);
        }
        element.textContent = Math.floor(current);
    }, 16);
}

// 底部导航
function initBottomNav() {
    const navItems = document.querySelectorAll('.nav-item');
    const currentPage = window.location.pathname.split('/').pop() || 'index.php';
    
    navItems.forEach(item => {
        const href = item.getAttribute('href');
        if (href === currentPage || (currentPage === '' && href === 'index.php')) {
            item.classList.add('active');
        }
        
        // 添加触摸反馈
        item.addEventListener('touchstart', function() {
            this.style.transform = 'scale(0.95)';
        });
        
        item.addEventListener('touchend', function() {
            this.style.transform = 'scale(1)';
        });
    });
}

// 网络状态检查
function checkNetworkStatus() {
    function updateNetworkStatus() {
        if (!navigator.onLine) {
            showToast('网络连接已断开', 'warning');
        }
    }
    
    window.addEventListener('online', () => {
        showToast('网络连接已恢复', 'success');
    });
    
    window.addEventListener('offline', updateNetworkStatus);
}

// 下拉刷新
function initPullToRefresh() {
    let startY = 0;
    let currentY = 0;
    let pullDistance = 0;
    let isPulling = false;
    let refreshThreshold = 80;
    
    const main = document.querySelector('.mobile-main');
    
    main.addEventListener('touchstart', (e) => {
        if (window.scrollY === 0) {
            startY = e.touches[0].clientY;
            isPulling = true;
        }
    });
    
    main.addEventListener('touchmove', (e) => {
        if (!isPulling) return;
        
        currentY = e.touches[0].clientY;
        pullDistance = currentY - startY;
        
        if (pullDistance > 0 && window.scrollY === 0) {
            e.preventDefault();
            
            // 添加下拉效果
            const scale = Math.min(pullDistance / refreshThreshold, 1);
            main.style.transform = `translateY(${pullDistance * 0.5}px)`;
            main.style.opacity = 1 - scale * 0.1;
        }
    });
    
    main.addEventListener('touchend', () => {
        if (isPulling && pullDistance > refreshThreshold) {
            // 触发刷新
            refreshPage();
        }
        
        // 重置状态
        main.style.transform = '';
        main.style.opacity = '';
        isPulling = false;
        pullDistance = 0;
    });
}

// 刷新页面
function refreshPage() {
    showToast('正在刷新...', 'info');
    
    setTimeout(() => {
        window.location.reload();
    }, 1000);
}

// 显示提示消息
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;
    
    // 添加样式
    if (!document.getElementById('toast-style')) {
        const style = document.createElement('style');
        style.id = 'toast-style';
        style.textContent = `
            .toast {
                position: fixed;
                top: 100px;
                left: 20px;
                right: 20px;
                padding: 15px 20px;
                border-radius: 12px;
                color: white;
                font-weight: 500;
                z-index: 9999;
                transform: translateY(-100px);
                transition: transform 0.3s ease;
                text-align: center;
            }
            
            .toast-success { background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%); }
            .toast-warning { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
            .toast-info { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
            .toast-error { background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%); }
            
            .toast.show {
                transform: translateY(0);
            }
        `;
        document.head.appendChild(style);
    }
    
    document.body.appendChild(toast);
    
    setTimeout(() => {
        toast.classList.add('show');
    }, 10);
    
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            toast.remove();
        }, 300);
    }, 3000);
}

// 工具函数
const utils = {
    // 格式化数字
    formatNumber: (num) => {
        return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    },
    
    // 格式化日期
    formatDate: (date) => {
        const now = new Date();
        const target = new Date(date);
        const diff = now - target;
        
        if (diff < 60000) return '刚刚';
        if (diff < 3600000) return Math.floor(diff / 60000) + '分钟前';
        if (diff < 86400000) return Math.floor(diff / 3600000) + '小时前';
        return Math.floor(diff / 86400000) + '天前';
    },
    
    // 防抖函数
    debounce: (func, wait) => {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
};

// 导出工具函数
window.utils = utils;

// 手势操作控制
function initGestureControls() {
    let startX, startY, startTime;
    let isScrolling = false;

    document.addEventListener('touchstart', function(e) {
        if (e.touches.length === 1) {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
            startTime = Date.now();
            isScrolling = false;
        }
    }, { passive: true });

    document.addEventListener('touchmove', function(e) {
        if (e.touches.length === 1 && !isScrolling) {
            const deltaX = Math.abs(e.touches[0].clientX - startX);
            const deltaY = Math.abs(e.touches[0].clientY - startY);

            if (deltaY > deltaX) {
                isScrolling = true;
            }
        }
    }, { passive: true });

    document.addEventListener('touchend', function(e) {
        if (e.changedTouches.length === 1 && !isScrolling) {
            const endX = e.changedTouches[0].clientX;
            const endY = e.changedTouches[0].clientY;
            const endTime = Date.now();

            const deltaX = endX - startX;
            const deltaY = endY - startY;
            const deltaTime = endTime - startTime;

            // 检测滑动手势
            if (Math.abs(deltaX) > 50 && Math.abs(deltaY) < 100 && deltaTime < 300) {
                if (deltaX > 0) {
                    handleSwipeRight();
                } else {
                    handleSwipeLeft();
                }
            }
        }
    }, { passive: true });
}

// 处理右滑手势
function handleSwipeRight() {
    // 在非主页时，右滑返回上一页
    if (window.location.pathname !== '/mobile/index.php' &&
        window.location.pathname !== '/mobile/') {
        window.history.back();
    }
}

// 处理左滑手势
function handleSwipeLeft() {
    // 可以添加左滑功能，比如快速操作菜单
    console.log('左滑手势');
}

// 页面转场效果
function initPageTransitions() {
    // 为所有链接添加转场效果
    document.addEventListener('click', function(e) {
        const link = e.target.closest('a[href]');
        if (link && !link.hasAttribute('target') &&
            link.href.includes(window.location.origin)) {

            e.preventDefault();

            // 添加页面离开动画
            document.body.style.transition = 'opacity 0.3s ease-out';
            document.body.style.opacity = '0.7';

            setTimeout(() => {
                window.location.href = link.href;
            }, 150);
        }
    });

    // 页面进入动画
    document.body.style.opacity = '0';
    setTimeout(() => {
        document.body.style.transition = 'opacity 0.3s ease-out';
        document.body.style.opacity = '1';
    }, 50);
}

// 性能监控
function initPerformanceMonitoring() {
    // 监控页面加载性能
    window.addEventListener('load', function() {
        setTimeout(() => {
            const perfData = performance.getEntriesByType('navigation')[0];
            if (perfData) {
                const loadTime = perfData.loadEventEnd - perfData.loadEventStart;
                console.log(`页面加载时间: ${loadTime}ms`);

                // 如果加载时间过长，显示提示
                if (loadTime > 3000) {
                    showToast('网络较慢，建议检查网络连接', 'warning');
                }
            }
        }, 1000);
    });

    // 监控内存使用
    if ('memory' in performance) {
        setInterval(() => {
            const memory = performance.memory;
            const usedMB = Math.round(memory.usedJSHeapSize / 1048576);
            const limitMB = Math.round(memory.jsHeapSizeLimit / 1048576);

            // 如果内存使用超过80%，建议刷新页面
            if (usedMB / limitMB > 0.8) {
                console.warn('内存使用过高，建议刷新页面');
            }
        }, 30000);
    }
}

// 增强的底部导航功能
function initBottomNav() {
    const navItems = document.querySelectorAll('.nav-item');
    const currentPath = window.location.pathname;

    // 设置当前页面的导航状态
    navItems.forEach(item => {
        const href = item.getAttribute('href');
        if (href && currentPath.includes(href.replace('.php', ''))) {
            item.classList.add('active');
        }

        // 添加触摸反馈
        item.addEventListener('touchstart', function() {
            this.style.transform = 'translateY(-4px) scale(1.05)';
        }, { passive: true });

        item.addEventListener('touchend', function() {
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        }, { passive: true });
    });

    // 添加导航切换动画
    navItems.forEach(item => {
        item.addEventListener('click', function(e) {
            if (this.classList.contains('active')) {
                e.preventDefault();
                return;
            }

            // 移除其他项的active状态
            navItems.forEach(nav => nav.classList.remove('active'));

            // 添加当前项的active状态
            this.classList.add('active');

            // 添加点击波纹效果
            const ripple = document.createElement('div');
            ripple.style.cssText = `
                position: absolute;
                border-radius: 50%;
                background: rgba(102, 126, 234, 0.3);
                transform: scale(0);
                animation: ripple 0.6s linear;
                pointer-events: none;
            `;

            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = (rect.width / 2 - size / 2) + 'px';
            ripple.style.top = (rect.height / 2 - size / 2) + 'px';

            this.style.position = 'relative';
            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });

    // 添加波纹动画样式
    if (!document.getElementById('ripple-animation')) {
        const style = document.createElement('style');
        style.id = 'ripple-animation';
        style.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(2);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    }
}

// 网络状态检测增强
function checkNetworkStatus() {
    function updateNetworkStatus() {
        if (navigator.onLine) {
            // 在线状态
            document.body.classList.remove('offline');

            // 检测网络速度
            const startTime = Date.now();
            fetch('/mobile/index.php?ping=1', {
                method: 'HEAD',
                cache: 'no-cache'
            })
            .then(() => {
                const latency = Date.now() - startTime;
                if (latency > 2000) {
                    showToast('网络连接较慢', 'warning');
                }
            })
            .catch(() => {
                showToast('网络连接不稳定', 'error');
            });
        } else {
            // 离线状态
            document.body.classList.add('offline');
            showToast('网络连接已断开', 'error');
        }
    }

    // 初始检测
    updateNetworkStatus();

    // 监听网络状态变化
    window.addEventListener('online', updateNetworkStatus);
    window.addEventListener('offline', updateNetworkStatus);

    // 定期检测网络状态
    setInterval(updateNetworkStatus, 30000);
}

// 增强的下拉刷新功能
function initPullToRefresh() {
    let startY = 0;
    let currentY = 0;
    let isPulling = false;
    let isRefreshing = false;

    const refreshThreshold = 80;
    const maxPullDistance = 120;

    // 创建下拉刷新指示器
    const refreshIndicator = document.createElement('div');
    refreshIndicator.className = 'pull-refresh-indicator';
    refreshIndicator.innerHTML = '<i class="fas fa-arrow-down"></i>';
    refreshIndicator.style.cssText = `
        position: fixed;
        top: -60px;
        left: 50%;
        transform: translateX(-50%);
        width: 40px;
        height: 40px;
        background: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        z-index: 1000;
        color: var(--primary-color);
    `;
    document.body.appendChild(refreshIndicator);

    document.addEventListener('touchstart', function(e) {
        if (window.scrollY === 0) {
            startY = e.touches[0].clientY;
            isPulling = true;
        }
    }, { passive: true });

    document.addEventListener('touchmove', function(e) {
        if (isPulling && !isRefreshing) {
            currentY = e.touches[0].clientY;
            const pullDistance = Math.min(currentY - startY, maxPullDistance);

            if (pullDistance > 0) {
                e.preventDefault();

                const progress = Math.min(pullDistance / refreshThreshold, 1);
                refreshIndicator.style.top = (pullDistance * 0.5 - 60) + 'px';
                refreshIndicator.style.transform = `translateX(-50%) rotate(${progress * 180}deg)`;

                if (pullDistance >= refreshThreshold) {
                    refreshIndicator.innerHTML = '<i class="fas fa-sync-alt"></i>';
                    refreshIndicator.style.background = 'var(--primary-color)';
                    refreshIndicator.style.color = 'white';
                } else {
                    refreshIndicator.innerHTML = '<i class="fas fa-arrow-down"></i>';
                    refreshIndicator.style.background = 'white';
                    refreshIndicator.style.color = 'var(--primary-color)';
                }
            }
        }
    }, { passive: false });

    document.addEventListener('touchend', function(e) {
        if (isPulling && !isRefreshing) {
            const pullDistance = currentY - startY;

            if (pullDistance >= refreshThreshold) {
                // 触发刷新
                isRefreshing = true;
                refreshIndicator.style.top = '20px';
                refreshIndicator.innerHTML = '<i class="fas fa-sync-alt fa-spin"></i>';
                refreshIndicator.style.background = 'var(--primary-color)';
                refreshIndicator.style.color = 'white';

                // 执行刷新操作
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                // 重置状态
                refreshIndicator.style.top = '-60px';
                refreshIndicator.style.transform = 'translateX(-50%) rotate(0deg)';
                refreshIndicator.innerHTML = '<i class="fas fa-arrow-down"></i>';
                refreshIndicator.style.background = 'white';
                refreshIndicator.style.color = 'var(--primary-color)';
            }
        }

        isPulling = false;
        currentY = 0;
    }, { passive: true });
}
