<?php require_once dirname(__DIR__, 2) . '/includes/header-modular.php'; ?>

<div class="main-content">
    <?php require_once dirname(__DIR__, 2) . '/includes/sidebar-modular.php'; ?>
    
    <div class="content">
        <div class="content-header">
            <h1>
                <?php if (isset($action) && $action === 'edit'): ?>
                    <i class="fas fa-edit"></i> 编辑入库记录
                <?php else: ?>
                    <i class="fas fa-plus-circle"></i> 新增入库记录
                <?php endif; ?>
            </h1>
            <div class="header-actions">
                <a href="index.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i>
                    返回列表
                </a>
            </div>
        </div>
        <?php if (isset($error_message)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle"></i>
            <?= htmlspecialchars($error_message) ?>
        </div>
        <?php endif; ?>

        <!-- 调试信息（生产环境隐藏） -->
        <?php if (false && (isset($debug_error) || isset($debug_purchase_orders_count))): ?>
            <!-- debug block disabled -->
        <?php endif; ?>

        <!-- 入库表单 -->
        <div class="form-container">
            <div class="form-header">
                <h2><i class="fas fa-warehouse"></i> 入库信息</h2>
                <p>请填写食材入库的详细信息</p>
            </div>

            <form method="POST" class="inbound-form" enctype="multipart/form-data">
                <!-- 隐藏字段 -->
                <input type="hidden" name="supplier_id" value="">
                <input type="hidden" name="order_id" value="">
                <input type="hidden" name="batch_type" value="batch"> <!-- 标识为批量入库 -->
                <input type="hidden" name="is_new_order" value="0"> <!-- 标识是否为新建采购单 -->
                <input type="hidden" name="new_order_data" value=""> <!-- 新建采购单数据 -->
                <div class="form-grid">
                    <!-- 采购单选择 -->
                    <div class="form-section">
                        <h3><i class="fas fa-shopping-cart"></i> 采购单批量入库</h3>
                        <p class="form-description">选择采购单后将自动加载所有商品，您只需输入实际入库数量即可</p>

                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">选择采购单</label>
                                <select id="purchase_order_select" class="form-control">
                                    <option value="">请选择采购单</option>
                                    <?php if (!empty($purchase_orders)): ?>
                                        <?php
                                        $grouped_orders = [];
                                        foreach ($purchase_orders as $order) {
                                            $key = $order['id'] . '_' . $order['order_number'];
                                            if (!isset($grouped_orders[$key])) {
                                                $grouped_orders[$key] = [
                                                    'order_info' => $order,
                                                    'items' => []
                                                ];
                                            }
                                            $grouped_orders[$key]['items'][] = $order;
                                        }
                                        ?>
                                        <?php foreach ($grouped_orders as $key => $group): ?>
                                            <option value="<?= htmlspecialchars(json_encode($group)) ?>">
                                                <?= htmlspecialchars($group['order_info']['order_number']) ?> -
                                                <?= htmlspecialchars($group['order_info']['supplier_name']) ?>
                                                (<?= $group['order_info']['order_date'] ?>) -
                                                共<?= count($group['items']) ?>种商品
                                            </option>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                    <option value="create_new" style="background-color: #e6f3ff; font-weight: bold;">
                                        + 创建新采购单并入库
                                    </option>
                                </select>
                            </div>
                        </div>

                        <!-- 或者分隔线 -->
                        <div class="divider-section" style="display: none;" id="divider_section">
                            <div class="divider-line">
                                <span class="divider-text">或者</span>
                            </div>
                        </div>

                        <!-- 采购单信息显示 -->
                        <div id="order_info_display" class="order-info-card" style="display: none;">
                            <div class="order-header">
                                <h4 id="order_title"></h4>
                                <div class="order-details">
                                    <span id="order_supplier"></span>
                                    <span id="order_date"></span>
                                    <span id="order_amount"></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 自建采购单表单 -->
                    <div class="form-section" id="create_order_section" style="display: none;">
                        <h3><i class="fas fa-plus-circle"></i> 创建新采购单</h3>
                        <p class="form-description">填写采购单基本信息，然后添加商品项目</p>

                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label required">供应商</label>
                                <select id="new_order_supplier" name="new_order_supplier" class="form-control" required>
                                    <option value="">请选择供应商</option>
                                    <?php if (isset($suppliers) && !empty($suppliers)): ?>
                                        <?php foreach ($suppliers as $supplier): ?>
                                            <option value="<?= $supplier['id'] ?>">
                                                <?= htmlspecialchars($supplier['name']) ?>
                                                <?php if (!empty($supplier['contact_person'])): ?>
                                                    - <?= htmlspecialchars($supplier['contact_person']) ?>
                                                <?php endif; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label required">采购日期</label>
                                <input type="date" id="new_order_date" name="new_order_date" class="form-control" required
                                       value="<?= date('Y-m-d') ?>">
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">采购单号</label>
                                <input type="text" id="new_order_number" name="new_order_number" class="form-control" readonly
                                       placeholder="系统自动生成" value="">
                            </div>
                            <div class="form-group">
                                <label class="form-label">备注</label>
                                <input type="text" id="new_order_notes" name="new_order_notes" class="form-control"
                                       placeholder="可选备注信息">
                            </div>
                        </div>

                        <!-- 商品添加区域 -->
                        <div class="items-add-section">
                            <h4><i class="fas fa-boxes"></i> 添加商品</h4>

                            <div class="add-item-form">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">食材</label>
                                        <select id="add_ingredient" class="form-control">
                                            <option value="">请选择食材</option>
                                            <?php if (isset($ingredients) && !empty($ingredients)): ?>
                                                <?php foreach ($ingredients as $ingredient): ?>
                                                    <option value="<?= $ingredient['id'] ?>" data-unit="<?= htmlspecialchars($ingredient['unit']) ?>">
                                                        <?= htmlspecialchars($ingredient['name']) ?> (<?= htmlspecialchars($ingredient['unit']) ?>)
                                                    </option>
                                                <?php endforeach; ?>
                                            <?php endif; ?>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">数量</label>
                                        <input type="number" id="add_quantity" class="form-control" step="0.01" min="0" placeholder="0.00">
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">单价</label>
                                        <input type="number" id="add_unit_price" class="form-control" step="0.01" min="0" placeholder="0.00">
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">用途</label>
                                        <select id="add_purpose" class="form-control">
                                            <option value="">请选择用途</option>
                                            <option value="早餐">早餐</option>
                                            <option value="午餐">午餐</option>
                                            <option value="晚餐">晚餐</option>
                                            <option value="全天">全天</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">&nbsp;</label>
                                        <button type="button" id="add_item_btn" class="btn btn-primary">
                                            <i class="fas fa-plus"></i> 添加
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- 已添加商品列表 -->
                            <div class="added-items-list">
                                <table class="table table-bordered" id="new_order_items_table">
                                    <thead>
                                        <tr>
                                            <th width="25%">食材名称</th>
                                            <th width="10%">单位</th>
                                            <th width="15%">数量</th>
                                            <th width="15%">单价</th>
                                            <th width="15%">小计</th>
                                            <th width="15%">用途</th>
                                            <th width="5%">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="new_order_items_tbody">
                                        <!-- 动态添加的商品行 -->
                                    </tbody>
                                    <tfoot>
                                        <tr class="table-info">
                                            <td colspan="4" class="text-end"><strong>总计：</strong></td>
                                            <td><strong id="new_order_total">¥0.00</strong></td>
                                            <td colspan="2"></td>
                                        </tr>
                                    </tfoot>
                                </table>

                                <div id="no_items_added" class="alert alert-info">
                                    <i class="fas fa-info-circle"></i>
                                    请添加至少一个商品项目
                                </div>
                            </div>

                            <div class="form-actions">
                                <button type="button" id="confirm_new_order" class="btn btn-success" disabled>
                                    <i class="fas fa-check"></i> 确认采购单并继续入库
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 日期信息 -->
                    <div class="form-section" id="date_section" style="display: none;">
                        <h3><i class="fas fa-calendar"></i> 日期信息</h3>

                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label required">批次号</label>
                                <input type="text" name="batch_number" class="form-control" required
                                       placeholder="请输入批次号" value="<?= htmlspecialchars($_POST['batch_number'] ?? $record['batch_number'] ?? '') ?>">
                            </div>
                            <div class="form-group">
                                <label class="form-label required">入库日期</label>
                                <input type="date" name="inbound_date" class="form-control" required
                                       value="<?= htmlspecialchars($_POST['inbound_date'] ?? $record['inbound_date'] ?? date('Y-m-d')) ?>">
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label required">操作员</label>
                                <input type="text" name="operator_name" class="form-control" required
                                       placeholder="当前操作员" value="<?= htmlspecialchars($_POST['operator_name'] ?? $record['operator_name'] ?? '管理员') ?>">
                            </div>
                        </div>
                    </div>

                    <!-- 拍照记录 -->
                    <div class="form-section" id="photo_section" style="display: none;">
                        <h3><i class="fas fa-camera"></i> 拍照记录</h3>
                        <p class="form-description">拍摄送货单作为入库凭证</p>

                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">送货单照片</label>
                                <div class="photo-upload-container">
                                    <input type="file" id="delivery_photo" name="delivery_photo" accept="image/*" capture="environment" class="photo-input">
                                    <div class="photo-preview" id="delivery_preview">
                                        <div class="photo-placeholder">
                                            <i class="fas fa-camera"></i>
                                            <span>点击拍摄送货单</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 备注信息 -->
                    <div class="form-section" id="notes_section" style="display: none;">
                        <h3><i class="fas fa-sticky-note"></i> 备注信息</h3>

                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">备注</label>
                                <textarea name="notes" class="form-control" rows="4"
                                          placeholder="请输入备注信息（可选）"><?= htmlspecialchars($_POST['notes'] ?? $record['notes'] ?? '') ?></textarea>
                            </div>
                        </div>
                    </div>

                    <!-- 商品入库列表 -->
                    <div class="form-section" id="items_section" style="display: none;">
                        <h3><i class="fas fa-list"></i> 商品入库列表</h3>
                        <p class="form-description">请输入每个商品的实际入库数量，系统将自动计算金额</p>

                        <div id="items_container" style="display: none;">
                            <div class="table-responsive">
                                <table class="table table-bordered items-table">
                                    <thead>
                                        <tr>
                                            <th width="20%">商品名称</th>
                                            <th width="8%">单位</th>
                                            <th width="12%">采购数量</th>
                                            <th width="12%">单价</th>
                                            <th width="12%">实际数量</th>
                                            <th width="12%">小计</th>
                                            <th width="12%">称重照片</th>
                                            <th width="12%">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="items_tbody">
                                        <!-- 动态生成的商品行 -->
                                    </tbody>
                                    <tfoot>
                                        <tr class="table-info">
                                            <td colspan="6" class="text-end"><strong>总计：</strong></td>
                                            <td><strong id="total_amount">¥0.00</strong></td>
                                            <td></td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>

                        <div id="no_items_message" class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            请先选择采购单以加载商品列表
                        </div>
                    </div>






                </div>

                <!-- 提交按钮 -->
                <div class="form-actions">
                    <button type="button" onclick="history.back()" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i> 取消
                    </button>
                    <button type="submit" class="btn btn-primary btn-submit">
                        <i class="fas fa-save"></i> 保存入库记录
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="../../assets/js/common.js"></script>
<script>
// 表单验证和功能增强
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('.inbound-form');
    const submitBtn = document.querySelector('.btn-submit');
    const purchaseOrderSelect = document.getElementById('purchase_order_select');

    // 采购单选择功能
    if (purchaseOrderSelect) {
        purchaseOrderSelect.addEventListener('change', function() {
            const selectedValue = this.value;

            // 隐藏所有部分
            hideAllSections();

            if (selectedValue === 'create_new') {
                // 显示自建采购单表单
                showCreateOrderSection();
                showNotification('请填写采购单信息并添加商品', 'info');
            } else if (selectedValue && selectedValue !== '') {
                try {
                    const orderGroup = JSON.parse(selectedValue);
                    loadPurchaseOrderItems(orderGroup);
                    showNotification('已加载采购单商品，请输入实际入库数量', 'success');
                } catch (e) {
                    console.error('解析采购单数据失败:', e);
                    showNotification('采购单数据格式错误', 'error');
                }
            } else {
                clearItemsList();
            }
        });
    }

    // 拍照功能
    setupPhotoUpload('delivery_photo', 'delivery_preview');

    form.addEventListener('submit', function(e) {
        const batchType = document.querySelector('input[name="batch_type"]').value;

        if (batchType === 'batch') {
            // 批量入库验证
            const itemRows = document.querySelectorAll('#items_tbody tr');
            if (itemRows.length === 0) {
                e.preventDefault();
                showNotification('请先选择采购单并确认商品列表', 'error');
                return false;
            }

            let hasValidItems = false;
            itemRows.forEach(row => {
                const quantityInput = row.querySelector('.actual-quantity');
                if (quantityInput && parseFloat(quantityInput.value) > 0) {
                    hasValidItems = true;
                }
            });

            if (!hasValidItems) {
                e.preventDefault();
                showNotification('请至少输入一个商品的入库数量', 'error');
                return false;
            }

            const supplierId = document.querySelector('input[name="supplier_id"]').value;
            if (!supplierId) {
                e.preventDefault();
                showNotification('请选择采购单', 'error');
                return false;
            }
        }

        // 显示提交状态
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 保存中...';
        submitBtn.disabled = true;
    });
    
    // 自动计算总价
    const quantityInput = document.querySelector('input[name="quantity"]');
    const priceInput = document.querySelector('input[name="unit_price"]');
    
    function updateTotal() {
        const quantity = parseFloat(quantityInput.value) || 0;
        const price = parseFloat(priceInput.value) || 0;
        const total = quantity * price;
        
        // 可以在这里显示总价
        console.log('总价:', total.toFixed(2));
    }
    
    quantityInput.addEventListener('input', updateTotal);
    priceInput.addEventListener('input', updateTotal);
});

// 加载采购单商品
function loadPurchaseOrderItems(orderGroup) {
    const orderInfo = orderGroup.order_info;
    const items = orderGroup.items;

    // 显示采购单信息
    const orderInfoDisplay = document.getElementById('order_info_display');
    const orderTitle = document.getElementById('order_title');
    const orderSupplier = document.getElementById('order_supplier');
    const orderDate = document.getElementById('order_date');
    const orderAmount = document.getElementById('order_amount');

    orderTitle.textContent = orderInfo.order_number;
    orderSupplier.textContent = `供应商: ${orderInfo.supplier_name}`;
    orderDate.textContent = `订单日期: ${orderInfo.order_date}`;
    orderAmount.textContent = `订单金额: ¥${parseFloat(orderInfo.order_amount || 0).toFixed(2)}`;
    orderInfoDisplay.style.display = 'block';

    // 显示各个部分
    const dateSection = document.getElementById('date_section');
    const photoSection = document.getElementById('photo_section');
    const notesSection = document.getElementById('notes_section');
    const itemsSection = document.getElementById('items_section');
    const itemsContainer = document.getElementById('items_container');
    const noItemsMessage = document.getElementById('no_items_message');
    const itemsTbody = document.getElementById('items_tbody');

    // 显示所有部分
    dateSection.style.display = 'block';
    photoSection.style.display = 'block';
    notesSection.style.display = 'block';
    itemsSection.style.display = 'block';
    itemsContainer.style.display = 'block';
    noItemsMessage.style.display = 'none';

    // 清空现有行
    itemsTbody.innerHTML = '';

    // 生成商品行
    items.forEach((item, index) => {
        const row = createItemRow(item, index);
        itemsTbody.appendChild(row);
    });

    // 设置隐藏字段
    document.querySelector('input[name="supplier_id"]').value = orderInfo.supplier_id;
    document.querySelector('input[name="order_id"]').value = orderInfo.id;

    // 自动生成批次号
    const batchNumber = 'IN' + orderInfo.order_number.replace('PO', '') + '_' + Date.now().toString().slice(-6);
    document.querySelector('input[name="batch_number"]').value = batchNumber;

    // 设置备注
    const notesField = document.querySelector('textarea[name="notes"]');
    notesField.value = `批量入库来自采购单: ${orderInfo.order_number}`;

    // 计算总金额
    calculateTotal();
}

// 创建商品行
function createItemRow(item, index) {
    const row = document.createElement('tr');
    row.innerHTML = `
        <td>
            <strong>${item.ingredient_name}</strong>
            ${item.purpose ? `<br><small class="text-muted">用途: ${item.purpose}</small>` : ''}
            <input type="hidden" name="items[${index}][ingredient_id]" value="${item.ingredient_id}">
            <input type="hidden" name="items[${index}][unit_price]" value="${item.unit_price}">
        </td>
        <td class="text-center">${item.unit}</td>
        <td class="text-center">
            <span class="badge bg-info">${item.quantity}</span>
        </td>
        <td class="text-center">¥${parseFloat(item.unit_price).toFixed(2)}</td>
        <td>
            <div class="input-group input-group-sm">
                <input type="number"
                       name="items[${index}][actual_quantity]"
                       class="form-control actual-quantity"
                       step="0.01"
                       min="0"
                       value="${item.quantity}"
                       data-unit-price="${item.unit_price}"
                       onchange="calculateRowTotal(this)"
                       required>
                <span class="input-group-text">${item.unit}</span>
            </div>
        </td>
        <td class="text-center">
            <strong class="row-total">¥${(parseFloat(item.quantity) * parseFloat(item.unit_price)).toFixed(2)}</strong>
        </td>
        <td class="text-center">
            <div class="weight-photo-container">
                <input type="file" id="weight_photo_${index}" name="items[${index}][weight_photo]"
                       accept="image/*" capture="environment" class="weight-photo-input" style="display: none;">
                <div class="weight-photo-preview" id="weight_preview_${index}">
                    <div class="photo-placeholder" onclick="document.getElementById('weight_photo_${index}').click()">
                        <i class="fas fa-camera"></i>
                        <span>称重照片</span>
                    </div>
                </div>
            </div>
        </td>
        <td class="text-center">
            <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeItemRow(this)" title="移除">
                <i class="fas fa-trash"></i>
            </button>
        </td>
    `;

    // 设置称重照片上传事件
    const weightPhotoInput = row.querySelector(`#weight_photo_${index}`);
    if (weightPhotoInput) {
        weightPhotoInput.addEventListener('change', function(e) {
            handleWeightPhotoUpload(e, index);
        });
    }

    return row;
}

// 计算单行总计
function calculateRowTotal(input) {
    const quantity = parseFloat(input.value) || 0;
    const unitPrice = parseFloat(input.dataset.unitPrice) || 0;
    const total = quantity * unitPrice;

    const row = input.closest('tr');
    const totalCell = row.querySelector('.row-total');
    totalCell.textContent = `¥${total.toFixed(2)}`;

    calculateTotal();
}

// 计算总金额
function calculateTotal() {
    const rowTotals = document.querySelectorAll('.row-total');
    let total = 0;

    rowTotals.forEach(cell => {
        const amount = parseFloat(cell.textContent.replace('¥', '')) || 0;
        total += amount;
    });

    document.getElementById('total_amount').textContent = `¥${total.toFixed(2)}`;
}

// 移除商品行
function removeItemRow(button) {
    if (confirm('确定要移除这个商品吗？')) {
        const row = button.closest('tr');
        row.remove();
        calculateTotal();

        // 重新编号
        reindexItems();
    }
}

// 重新编号商品
function reindexItems() {
    const rows = document.querySelectorAll('#items_tbody tr');
    rows.forEach((row, index) => {
        const inputs = row.querySelectorAll('input[name^="items["]');
        inputs.forEach(input => {
            const name = input.name;
            const newName = name.replace(/items\[\d+\]/, `items[${index}]`);
            input.name = newName;
        });
    });
}

// 清空商品列表
function clearItemsList() {
    const orderInfoDisplay = document.getElementById('order_info_display');
    const itemsContainer = document.getElementById('items_container');
    const noItemsMessage = document.getElementById('no_items_message');
    const itemsTbody = document.getElementById('items_tbody');

    orderInfoDisplay.style.display = 'none';
    itemsContainer.style.display = 'none';
    noItemsMessage.style.display = 'block';
    itemsTbody.innerHTML = '';

    // 清空隐藏字段
    const supplierIdField = document.querySelector('input[name="supplier_id"]');
    const orderIdField = document.querySelector('input[name="order_id"]');
    if (supplierIdField) supplierIdField.value = '';
    if (orderIdField) orderIdField.value = '';
}

// 拍照上传功能
function setupPhotoUpload(inputId, previewId) {
    const input = document.getElementById(inputId);
    const preview = document.getElementById(previewId);

    if (!input || !preview) return;

    // 点击预览区域触发文件选择
    preview.addEventListener('click', function() {
        input.click();
    });

    // 文件选择后预览
    input.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                preview.innerHTML = `
                    <img src="${e.target.result}" alt="预览图片" style="max-width: 100%; max-height: 200px; object-fit: cover; border-radius: 8px;">
                    <div class="photo-actions">
                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="clearPhoto('${inputId}', '${previewId}')">
                            <i class="fas fa-trash"></i> 删除
                        </button>
                    </div>
                `;
            };
            reader.readAsDataURL(file);
        }
    });
}

// 清除照片
function clearPhoto(inputId, previewId) {
    const input = document.getElementById(inputId);
    const preview = document.getElementById(previewId);

    input.value = '';
    preview.innerHTML = `
        <div class="photo-placeholder">
            <i class="fas fa-camera"></i>
            <span>点击拍摄照片</span>
        </div>
    `;
}

// 显示通知
function showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
    notification.style.position = 'fixed';
    notification.style.top = '20px';
    notification.style.right = '20px';
    notification.style.zIndex = '9999';
    notification.style.minWidth = '300px';

    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
        ${message}
        <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
    `;

    document.body.appendChild(notification);

    // 3秒后自动消失
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 3000);
}

// 自建采购单相关函数
let newOrderItems = [];
let newOrderItemIndex = 0;

// 隐藏所有部分
function hideAllSections() {
    document.getElementById('order_info_display').style.display = 'none';
    document.getElementById('create_order_section').style.display = 'none';
    document.getElementById('date_section').style.display = 'none';
    document.getElementById('photo_section').style.display = 'none';
    document.getElementById('notes_section').style.display = 'none';
    document.getElementById('items_section').style.display = 'none';
    document.getElementById('items_container').style.display = 'none';
    document.getElementById('no_items_message').style.display = 'block';
}

// 显示自建采购单部分
function showCreateOrderSection() {
    document.getElementById('create_order_section').style.display = 'block';
    generateOrderNumber();
    setupCreateOrderEvents();
}

// 生成采购单号
function generateOrderNumber() {
    const now = new Date();
    const dateStr = now.getFullYear().toString() +
                   (now.getMonth() + 1).toString().padStart(2, '0') +
                   now.getDate().toString().padStart(2, '0');
    const timeStr = now.getHours().toString().padStart(2, '0') +
                   now.getMinutes().toString().padStart(2, '0');
    const orderNumber = 'PO' + dateStr + timeStr;
    document.getElementById('new_order_number').value = orderNumber;
}

// 设置自建采购单事件
function setupCreateOrderEvents() {
    // 添加商品按钮
    const addItemBtn = document.getElementById('add_item_btn');
    if (addItemBtn) {
        addItemBtn.addEventListener('click', addNewOrderItem);
    }

    // 确认采购单按钮
    const confirmBtn = document.getElementById('confirm_new_order');
    if (confirmBtn) {
        confirmBtn.addEventListener('click', confirmNewOrder);
    }

    // 供应商选择变化时更新确认按钮状态
    const supplierSelect = document.getElementById('new_order_supplier');
    if (supplierSelect) {
        supplierSelect.addEventListener('change', updateConfirmButtonState);
    }
}

// 添加商品到新采购单
function addNewOrderItem() {
    const ingredientSelect = document.getElementById('add_ingredient');
    const quantityInput = document.getElementById('add_quantity');
    const priceInput = document.getElementById('add_unit_price');
    const purposeSelect = document.getElementById('add_purpose');

    const ingredientId = ingredientSelect.value;
    const ingredientText = ingredientSelect.options[ingredientSelect.selectedIndex].text;
    const unit = ingredientSelect.options[ingredientSelect.selectedIndex].dataset.unit || '';
    const quantity = parseFloat(quantityInput.value) || 0;
    const unitPrice = parseFloat(priceInput.value) || 0;
    const purpose = purposeSelect.value;

    if (!ingredientId) {
        showNotification('请选择食材', 'error');
        return;
    }

    if (quantity <= 0) {
        showNotification('请输入有效的数量', 'error');
        return;
    }

    if (unitPrice <= 0) {
        showNotification('请输入有效的单价', 'error');
        return;
    }

    // 检查是否已添加该食材
    if (newOrderItems.find(item => item.ingredient_id === ingredientId)) {
        showNotification('该食材已添加，请勿重复添加', 'error');
        return;
    }

    const subtotal = quantity * unitPrice;
    const item = {
        index: newOrderItemIndex++,
        ingredient_id: ingredientId,
        ingredient_name: ingredientText.split('(')[0].trim(),
        unit: unit,
        quantity: quantity,
        unit_price: unitPrice,
        subtotal: subtotal,
        purpose: purpose
    };

    newOrderItems.push(item);
    renderNewOrderItems();
    updateNewOrderTotal();
    updateConfirmButtonState();

    // 清空输入
    ingredientSelect.value = '';
    quantityInput.value = '';
    priceInput.value = '';
    purposeSelect.value = '';

    showNotification('商品添加成功', 'success');
}

// 渲染新采购单商品列表
function renderNewOrderItems() {
    const tbody = document.getElementById('new_order_items_tbody');
    const noItemsDiv = document.getElementById('no_items_added');

    if (newOrderItems.length === 0) {
        tbody.innerHTML = '';
        noItemsDiv.style.display = 'block';
        return;
    }

    noItemsDiv.style.display = 'none';

    tbody.innerHTML = newOrderItems.map(item => `
        <tr>
            <td>${item.ingredient_name}</td>
            <td>${item.unit}</td>
            <td>${item.quantity}</td>
            <td>¥${item.unit_price.toFixed(2)}</td>
            <td>¥${item.subtotal.toFixed(2)}</td>
            <td>${item.purpose || '无'}</td>
            <td>
                <button type="button" class="remove-item-btn" onclick="removeNewOrderItem(${item.index})">
                    <i class="fas fa-times"></i>
                </button>
            </td>
        </tr>
    `).join('');
}

// 删除新采购单商品
function removeNewOrderItem(index) {
    newOrderItems = newOrderItems.filter(item => item.index !== index);
    renderNewOrderItems();
    updateNewOrderTotal();
    updateConfirmButtonState();
    showNotification('商品已删除', 'success');
}

// 更新新采购单总计
function updateNewOrderTotal() {
    const total = newOrderItems.reduce((sum, item) => sum + item.subtotal, 0);
    document.getElementById('new_order_total').textContent = '¥' + total.toFixed(2);
}

// 更新确认按钮状态
function updateConfirmButtonState() {
    const supplierSelect = document.getElementById('new_order_supplier');
    const confirmBtn = document.getElementById('confirm_new_order');

    const hasSupplier = supplierSelect.value !== '';
    const hasItems = newOrderItems.length > 0;

    confirmBtn.disabled = !(hasSupplier && hasItems);
}

// 确认新采购单
function confirmNewOrder() {
    const supplierSelect = document.getElementById('new_order_supplier');
    const orderNumber = document.getElementById('new_order_number').value;
    const orderDate = document.getElementById('new_order_date').value;
    const notes = document.getElementById('new_order_notes').value;

    if (!supplierSelect.value || newOrderItems.length === 0) {
        showNotification('请完善采购单信息', 'error');
        return;
    }

    // 构造采购单数据
    const orderGroup = {
        order_info: {
            id: 'new',
            order_number: orderNumber,
            supplier_id: supplierSelect.value,
            supplier_name: supplierSelect.options[supplierSelect.selectedIndex].text,
            order_date: orderDate,
            order_amount: newOrderItems.reduce((sum, item) => sum + item.subtotal, 0),
            notes: notes
        },
        items: newOrderItems.map(item => ({
            id: item.index,
            ingredient_id: item.ingredient_id,
            ingredient_name: item.ingredient_name,
            unit: item.unit,
            quantity: item.quantity,
            unit_price: item.unit_price,
            purpose: item.purpose
        }))
    };

    // 隐藏自建采购单部分
    document.getElementById('create_order_section').style.display = 'none';

    // 加载到入库表单
    loadPurchaseOrderItems(orderGroup);

    // 设置标记表示这是新建的采购单
    document.querySelector('input[name="is_new_order"]').value = '1';
    document.querySelector('input[name="new_order_data"]').value = JSON.stringify(orderGroup);

    showNotification('采购单创建成功，请继续完成入库操作', 'success');
}

// 处理称重照片上传
function handleWeightPhotoUpload(event, index) {
    const file = event.target.files[0];
    const previewContainer = document.getElementById(`weight_preview_${index}`);

    if (!file) {
        return;
    }

    // 验证文件类型
    if (!file.type.startsWith('image/')) {
        showNotification('请选择图片文件', 'error');
        event.target.value = '';
        return;
    }

    // 验证文件大小（限制为5MB）
    if (file.size > 5 * 1024 * 1024) {
        showNotification('图片文件不能超过5MB', 'error');
        event.target.value = '';
        return;
    }

    // 显示上传状态
    previewContainer.className = 'weight-photo-preview uploading';
    previewContainer.innerHTML = `
        <div class="photo-placeholder">
            <i class="fas fa-spinner fa-spin"></i>
            <span>上传中...</span>
        </div>
    `;

    // 读取文件并显示预览
    const reader = new FileReader();
    reader.onload = function(e) {
        previewContainer.className = 'weight-photo-preview has-photo';
        previewContainer.innerHTML = `
            <img src="${e.target.result}" alt="称重照片" title="点击重新拍摄">
            <div class="photo-actions">
                <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeWeightPhoto(${index})" title="删除照片">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        // 点击图片可以重新选择
        previewContainer.onclick = function() {
            document.getElementById(`weight_photo_${index}`).click();
        };

        showNotification('称重照片上传成功', 'success');
    };

    reader.onerror = function() {
        previewContainer.className = 'weight-photo-preview error';
        previewContainer.innerHTML = `
            <div class="photo-placeholder">
                <i class="fas fa-exclamation-triangle"></i>
                <span>上传失败</span>
            </div>
        `;
        showNotification('照片上传失败，请重试', 'error');
        event.target.value = '';
    };

    reader.readAsDataURL(file);
}

// 删除称重照片
function removeWeightPhoto(index) {
    const fileInput = document.getElementById(`weight_photo_${index}`);
    const previewContainer = document.getElementById(`weight_preview_${index}`);

    // 清空文件输入
    fileInput.value = '';

    // 重置预览容器
    previewContainer.className = 'weight-photo-preview';
    previewContainer.innerHTML = `
        <div class="photo-placeholder" onclick="document.getElementById('weight_photo_${index}').click()">
            <i class="fas fa-camera"></i>
            <span>称重照片</span>
        </div>
    `;

    showNotification('称重照片已删除', 'info');
}

// 移除商品行时也要清理照片
function removeItemRow(button) {
    const row = button.closest('tr');
    const index = Array.from(row.parentNode.children).indexOf(row);

    // 清理对应的照片文件
    const weightPhotoInput = row.querySelector('.weight-photo-input');
    if (weightPhotoInput) {
        weightPhotoInput.value = '';
    }

    // 移除行
    row.remove();

    // 重新计算总计
    calculateTotal();

    showNotification('商品已移除', 'info');
}
</script>

<style>
/* 表单样式 */
.form-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    border: 1px solid #e2e8f0;
    overflow: hidden;
}

/* 拍照上传样式 */
.photo-upload-container {
    position: relative;
    margin-bottom: 15px;
}

.photo-input {
    display: none;
}

.photo-preview {
    width: 100%;
    min-height: 200px;
    border: 2px dashed #cbd5e0;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.photo-preview:hover {
    border-color: #007cba;
    background: #e3f2fd;
}

.photo-placeholder {
    text-align: center;
    color: #6c757d;
    padding: 20px;
}

.photo-placeholder i {
    font-size: 2rem;
    margin-bottom: 10px;
    display: block;
}

.photo-actions {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 4px;
    padding: 5px;
}

.input-group-text {
    background: #e9ecef;
    border: 1px solid #ced4da;
    color: #495057;
    font-weight: 500;
}

.form-description {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 15px;
    font-style: italic;
}

/* 采购单信息卡片 */
.order-info-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
    padding: 20px;
    margin: 15px 0;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.order-header h4 {
    margin: 0 0 10px 0;
    font-size: 1.3rem;
    font-weight: 600;
}

.order-details {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    font-size: 0.9rem;
}

.order-details span {
    background: rgba(255,255,255,0.2);
    padding: 5px 12px;
    border-radius: 20px;
    backdrop-filter: blur(10px);
}

/* 商品表格样式 */
.items-table {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.items-table thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    font-weight: 600;
    text-align: center;
    padding: 15px 8px;
}

.items-table tbody td {
    vertical-align: middle;
    padding: 12px 8px;
    border-color: #e9ecef;
}

.items-table tbody tr:hover {
    background-color: #f8f9fa;
}

.items-table .badge {
    font-size: 0.85rem;
    padding: 6px 10px;
}

.items-table .input-group-sm .form-control {
    font-size: 0.9rem;
    text-align: center;
}

.items-table .row-total {
    color: #28a745;
    font-size: 1.1rem;
}

.items-table tfoot td {
    background: #f8f9fa;
    font-weight: 600;
    border-top: 2px solid #dee2e6;
}

#total_amount {
    color: #28a745;
    font-size: 1.2rem;
}

/* 响应式表格 */
@media (max-width: 768px) {
    .order-details {
        flex-direction: column;
        gap: 10px;
    }

    .items-table {
        font-size: 0.85rem;
    }

    .items-table thead th,
    .items-table tbody td {
        padding: 8px 4px;
    }
}

.form-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    text-align: center;
}

.form-header h2 {
    margin: 0 0 8px 0;
    font-size: 24px;
    font-weight: 600;
}

.form-header p {
    margin: 0;
    opacity: 0.9;
    font-size: 14px;
}

.form-grid {
    padding: 30px;
}

.form-section {
    margin-bottom: 30px;
}

.form-section h3 {
    color: #2c3e50;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e2e8f0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-label {
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
    font-size: 14px;
}

.form-label.required::after {
    content: ' *';
    color: #ef4444;
}

.form-control {
    padding: 12px 16px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.2s ease;
}

.form-control:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-actions {
    background: #f8fafc;
    padding: 20px 30px;
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    border-top: 1px solid #e2e8f0;
}

.btn-submit {
    min-width: 150px;
    padding: 12px 28px;
    font-size: 15px;
    font-weight: 600;
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    border: none;
    border-radius: 8px;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn-submit:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(72, 187, 120, 0.35);
    background: linear-gradient(135deg, #38a169 0%, #2f855a 100%);
}

.btn-submit:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .form-actions {
        flex-direction: column-reverse;
        gap: 10px;
    }
    
    .form-actions .btn {
        width: 100%;
        justify-content: center;
    }
}

/* 表单容器样式 - 与其他页面保持一致 */
.form-container {
    max-width: 2600px;
    margin: 0 auto;
}
</style>

<?php require_once dirname(__DIR__, 2) . '/includes/footer-modular.php'; ?>
