# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a school canteen ingredient management system built with PHP and MySQL. It uses a **custom modular MVC architecture** (not <PERSON><PERSON>, despite misleading references in start.bat) where each module is self-contained with its own controller, views, styles, and JavaScript files. The system is specifically optimized for Chinese language use in educational environments with legacy infrastructure.

## Development Environment

- **PHP 7.2+** (optimized for legacy environments)
- **MySQL 5.6+** (compatible with older MySQL versions)  
- **Web server**: Apache/Nginx or PHP built-in server
- **Character set**: UTF-8 for Chinese content support
- **Database server**: Remote MySQL at ************:3306

## Quick Start Commands

### Start the system
```bash
# Windows - One-click start (recommended) 
start.bat                    # Starts PHP server + opens browser
simple-start.bat            # PHP server only, no browser

# Manual start
php -S localhost:8000       # Serves on http://localhost:8000
```

### Database operations
```bash
# Initialize database tables
php create-tables.php       # Creates all core tables
php create-users-table.php  # Creates user management tables

# Database upgrades (execute in order, idempotent)
php update/update-tables.php              # Core table structures
php update/update-suppliers-table.php     # Supplier enhancements  
php update/update-purchase-tables.php     # Purchase order features
php update/upgrade-categories-table.php   # Two-level categories
```

### Testing and debugging
```bash
# Test center - 96+ automated test scripts
http://localhost:8000/test/

# Environment verification
php test/check-php.php                    # PHP version/extensions
php test/check-table-structure.php       # Database schema verification
php test/check-categories.php            # Data integrity checks

# Module-specific debugging
php test/debug-ingredient-create.php     # Food item creation issues
php test/debug-supplier-insert.php       # Supplier management
php test/test-purchase-table-structure.php # Purchase order debugging
```

## Architecture

### Modular Structure
The system follows a **module-based architecture** where each feature is a self-contained module with consistent structure:

```
modules/
├── dashboard/          # Real-time statistics and system overview
├── ingredients/        # Food item master data management
├── categories/         # Two-level hierarchical food classification  
├── suppliers/          # Vendor management with ratings
├── purchase/           # Purchase order management with batch import
├── inbound/            # Receiving operations with mobile support
├── outbound/           # Inventory disbursement
├── stocktaking/        # Inventory auditing
├── inventory/          # Real-time stock queries
└── users/              # User and permission management
```

**Standard Module Structure:**
- `{Module}Controller.php` - Business logic extending BaseController
- `index.php` - Module entry point with routing
- `template.php` - Main view template
- `create-template.php` - Create form template (if applicable)
- `edit-template.php` - Edit form template (if applicable)
- `style.css` - Module-specific styles
- `main.js` - Module-specific JavaScript
- `sidebar.php` - Module navigation

### Inter-Module Communication
- **URL Routing**: `getModuleUrl($module, $action)` helper function
- **Sidebar Navigation**: Centralized in `includes/sidebar-modular.php`
- **Active State Detection**: Context-aware navigation highlighting
- **Data Sharing**: Through shared Database class singleton

### Core Components

#### BaseController (`includes/BaseController.php`)
**Abstract base class** providing common functionality for all modules:
- **Database connection management** via singleton pattern
- **Request handling and routing** (`handleRequest()` method)
- **Template rendering system** with data extraction (`renderTemplate()`)
- **JSON response handling** for AJAX operations
- **File upload processing** with security validation and size limits
- **Pagination utilities** (`paginate()` method)
- **Comprehensive logging** with daily rotation (`log()` method)

#### Database Class (`includes/Database.php`)
**Singleton PDO wrapper** optimized for MySQL 5.6 compatibility:
- **Connection management** with error handling and reconnection logic
- **Query execution methods** (`fetchAll`, `fetchOne`, `execute`) with prepared statements
- **Transaction support** (`beginTransaction`, `commit`, `rollback`)
- **Character set handling** for Chinese content (UTF-8)
- **Index length optimization** for MySQL 5.6 (191-character limits)

### Key System Features

#### Two-Level Category System
**Hierarchical food classification system:**
- **Level 1**: Main categories (vegetables, meat, seafood, grains, seasonings)
- **Level 2**: Subcategories (leafy vegetables, root vegetables, etc.)
- **Database structure**: Uses `parent_id` and `level` columns for hierarchy
- **Upgrade support**: Idempotent script converts single-level to two-level
- **UI features**: Cascading display, level filtering, path breadcrumbs

#### Batch Import System
**CSV-based import with comprehensive validation:**
- **Supported modules**: Categories, ingredients, purchase orders
- **Real-time validation** and error reporting with Chinese messages
- **Template download** functionality with format examples
- **Drag-and-drop interface** with file preview
- **Progress tracking** and detailed import results
- **Error handling**: Skip invalid rows, continue processing

#### Mobile-Responsive Design
**Dedicated mobile interface** for warehouse operations:
- **Separate mobile directory** (`mobile/`) with touch-optimized UI
- **Photo capture functionality** for inventory verification with weight recording  
- **Simplified workflows** optimized for mobile devices
- **Cross-platform compatibility** (iOS/Android style normalization)

## Database Schema

### Core Tables
**Primary data entities and relationships:**
- `ingredient_categories` - Hierarchical food classification (supports 2-level)
- `ingredients` - Food items with inventory tracking and supplier references  
- `suppliers` - Vendor information with ratings and contact details
- `purchase_orders` + `purchase_order_items` - Header-detail purchase management
- `inbound_records` + `inbound_orders` - Receiving operations with photo verification
- `inventory` + `inventory_batches` - Stock management with batch tracking
- `users` + `roles` + `permissions` - Role-based access control

### Key Database Relationships
**Foreign key constraints and business rules:**
- Ingredients → Categories (many-to-one, with cascade options)
- Purchase Orders → Suppliers (many-to-one)
- Purchase Orders → Purchase Order Items (one-to-many, cascade delete)
- Inbound Records → Ingredients (many-to-one)  
- Inventory → Ingredients (one-to-one, with triggers for stock updates)
- Categories → Categories (self-referencing parent-child for two-level structure)

### Database Schema Upgrade Strategy
**Idempotent upgrade scripts** with safety measures:
- **Check-then-modify pattern**: Scripts verify current state before making changes
- **Data preservation**: Existing data is migrated, not replaced
- **Rollback support**: Each script logs changes for potential reversal
- **Sequential execution**: Scripts are numbered and must run in order
- **Testing integration**: Each upgrade has corresponding test scripts

## Development Patterns and Best Practices

### Request Flow Architecture
**Standard request handling pattern:**
1. **Entry Point**: `modules/{module}/index.php` receives requests
2. **Controller**: Instantiates `{Module}Controller` extending `BaseController`
3. **Routing**: `handleRequest()` method dispatches based on action parameter
4. **Processing**: Business logic with database operations via singleton
5. **Response**: Template rendering or JSON response for AJAX calls

### Template System Architecture
**Consistent templating across modules:**
- **Data extraction**: Controllers populate template variables via `extract()`
- **Template inclusion**: PHP `include` with isolated variable scope
- **Sidebar integration**: Module-specific sidebar includes for navigation
- **Asset loading**: Module-specific CSS/JS loaded via base template

### File Organization Patterns
**Strict separation of concerns:**
- **Module isolation**: All module-specific code stays within `modules/{module}/`
- **Shared components**: Common utilities in `includes/` directory
- **Upload organization**: Files stored in `uploads/` with subdirectories by type
- **Test organization**: All test scripts in `test/` directory with clear naming
- **Configuration**: Database and app config in `config/` directory
- **Upgrades**: Database migration scripts in `update/` directory

### Coding Standards
**PHP and database conventions:**
- **Class naming**: PHP PSR-style (`PascalCase`) for classes
- **Database naming**: `snake_case` for columns and table names
- **Chinese content**: Expected in comments and user-facing text
- **Error handling**: User-friendly Chinese error messages
- **Prepared statements**: Always use via Database class, never raw SQL
- **File uploads**: Validate type, size, and sanitize filenames

### Security Implementation
**Built-in security measures:**
- **SQL injection prevention**: All queries use prepared statements
- **File upload validation**: Type checking, size limits, safe storage
- **Input sanitization**: All user input validated and escaped
- **Path traversal protection**: Upload paths are restricted and validated

### Database Operations Best Practices
**MySQL 5.6 optimization patterns:**
- **Prepared statements**: Always use via Database class singleton
- **Character limits**: Handle 191-character index limits for UTF-8
- **Error handling**: Graceful degradation when tables are missing during development
- **Transaction usage**: Use for multi-table operations to maintain data integrity
- **Connection management**: Database class handles reconnection and connection pooling

### Comprehensive Testing Strategy
**96+ automated test scripts organized by function:**
- **Environment testing**: PHP version, extensions, database connectivity
- **Module testing**: Individual module functionality and integration
- **API testing**: Direct endpoint testing for AJAX operations
- **Import testing**: File upload, validation, and batch processing
- **Performance testing**: Query optimization and load testing
- **Data integrity**: Consistency checks and relationship validation

## Common Development Workflows

### Adding a New Module
**Step-by-step module creation process:**
1. **Create module directory** in `modules/{module_name}/`
2. **Implement controller** extending `BaseController` with `handleRequest()` method
3. **Create entry point** `index.php` that instantiates controller
4. **Add view templates** (template.php, create-template.php, etc.)
5. **Add module assets** (style.css, main.js, sidebar.php)
6. **Update navigation** in `includes/sidebar-modular.php`
7. **Create test scripts** in `test/` directory for the new module
8. **Add database tables** if needed with upgrade script in `update/`

### Database Schema Evolution
**Safe database modification workflow:**
1. **Create upgrade script** in `update/upgrade-{feature}-{table}.php`
2. **Implement idempotent logic** - check current state before modifications
3. **Test with existing data** to ensure compatibility and no data loss
4. **Update test scripts** to verify new schema
5. **Create corresponding test** in `test/` directory
6. **Document changes** in script comments and module README

### Import Feature Development Pattern
**Established workflow for adding CSV import functionality:**
1. **Study existing patterns** in `categories/` and `purchase/` modules
2. **Implement standard flow**: template download → file upload → validation → import
3. **Add real-time progress** reporting with Chinese language messages
4. **Create comprehensive error handling** with skip-and-continue logic
5. **Add debugging tools** in `test/` directory for step-by-step import debugging
6. **Test with edge cases** - empty files, malformed data, encoding issues

## Troubleshooting

### Common Issues and Solutions
**Frequently encountered problems:**
- **Database connection failures**: 
  - Check `config/database.php` settings and network connectivity
  - Verify remote MySQL server (************) is accessible
  - Test connection with `mysql -h ************ -u sc -ppw5K4SsM7kZsjdxy sc`
- **Permission errors**: 
  - Ensure `uploads/` directory is writable by web server
  - Check `logs/` directory permissions for daily log rotation
- **Import failures**: 
  - Use step-by-step test scripts in `test/` to debug import process
  - Check file encoding (must be UTF-8 for Chinese content)
  - Verify CSV format matches template exactly
- **Module not loading**: 
  - Verify controller extends `BaseController` and implements `handleRequest()`
  - Check that `index.php` properly instantiates the controller
  - Ensure sidebar navigation is updated in `includes/sidebar-modular.php`

### Debugging Toolkit
**Comprehensive debugging resources:**
- **Test center**: `http://localhost:8000/test/` - 96+ automated diagnostic scripts
- **Log analysis**: Check `logs/` directory (organized by date, auto-rotation)
- **Database debugging**: Use `test/check-table-structure.php` for schema verification
- **Import debugging**: Real-time debuggers in `test/` for CSV import issues
- **Browser tools**: Developer console for JavaScript debugging
- **PHP error reporting**: Enable in development, disable in production

## Important System Considerations

### Architecture Misconceptions
- **NOT Laravel**: Despite misleading references in `start.bat`, this is a custom PHP framework
- **Module-based MVC**: Each module is self-contained, not following Laravel conventions
- **Custom routing**: Uses query parameters and `BaseController`, not Laravel routes

### Environment Specifics  
- **Legacy optimization**: Specifically optimized for PHP 7.2 and MySQL 5.6
- **Chinese language focus**: All user interfaces and error messages in Chinese
- **Educational environment**: Designed for school canteen operations with specific workflows
- **Mobile warehouse focus**: Mobile interface prioritizes inventory and receiving operations

### Production Considerations
- **Remote database**: Hardcoded credentials for ************ MySQL server
- **File permissions**: Requires writable `uploads/` and `logs/` directories
- **Character encoding**: UTF-8 throughout for Chinese content support
- **Error handling**: Comprehensive logging with daily rotation built-in
- **Security**: Built-in protections against common vulnerabilities (SQL injection, file upload exploits)