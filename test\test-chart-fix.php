<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试图表修复</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .btn { display: inline-block; padding: 8px 16px; background: #007cba; color: white; text-decoration: none; border-radius: 4px; margin: 5px; }
        .debug-log { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; font-family: monospace; font-size: 12px; max-height: 400px; overflow-y: auto; }
    </style>
</head>
<body>
    <h1>✅ 图表修复验证</h1>
    
    <div class="test-section">
        <h2>🔧 已完成的修复</h2>
        
        <h4>1. JavaScript AJAX请求修复：</h4>
        <ul>
            <li class="success">✅ 添加了 X-Requested-With 请求头</li>
            <li class="success">✅ 添加了 Content-Type 请求头</li>
        </ul>
        
        <h4>2. 数据库查询修复：</h4>
        <ul>
            <li class="success">✅ 修正了字段名：使用 actual_amount 和 order_amount</li>
            <li class="success">✅ 修正了状态字段：使用 order_status 而不是 status</li>
            <li class="success">✅ 添加了 COALESCE 处理空值</li>
        </ul>
        
        <h4>修复前的问题：</h4>
        <ul>
            <li class="error">❌ AJAX请求缺少必需的请求头</li>
            <li class="error">❌ 数据库字段名不匹配</li>
            <li class="error">❌ 状态字段引用错误</li>
        </ul>
        
        <h4>修复后的改进：</h4>
        <ul>
            <li class="success">✅ AJAX请求能正确通过服务器验证</li>
            <li class="success">✅ 数据库查询能正确获取数据</li>
            <li class="success">✅ 图表能正常显示月度采购趋势</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>🧪 测试AJAX接口</h2>
        
        <button onclick="testChartData()" class="btn">测试月度采购数据</button>
        <button onclick="testCategoryData()" class="btn">测试分类分布数据</button>
        <button onclick="testSupplierData()" class="btn">测试供应商排名数据</button>
        
        <div id="testResult" class="debug-log" style="display: none;"></div>
    </div>
    
    <div class="test-section">
        <h2>🎯 验证修复效果</h2>
        <div class="step">
            <p>
                <a href="../modules/dashboard/index.php" class="btn">📊 查看仪表板</a>
            </p>
            <p class="info">请访问仪表板页面，确认月度采购趋势图表能正常显示</p>
            
            <h4>预期结果：</h4>
            <ul>
                <li>图表区域显示月度采购趋势线图</li>
                <li>X轴显示月份（如：2024-01, 2024-02...）</li>
                <li>Y轴显示采购金额和订单数量</li>
                <li>可以切换不同的图表类型</li>
                <li>没有"数据加载失败"的错误提示</li>
            </ul>
        </div>
    </div>
    
    <script>
    function testChartData() {
        testAjaxRequest('monthly_purchase', '月度采购数据');
    }
    
    function testCategoryData() {
        testAjaxRequest('category_distribution', '分类分布数据');
    }
    
    function testSupplierData() {
        testAjaxRequest('supplier_ranking', '供应商排名数据');
    }
    
    function testAjaxRequest(type, name) {
        const resultDiv = document.getElementById('testResult');
        resultDiv.style.display = 'block';
        resultDiv.innerHTML = `正在测试${name}...`;
        
        fetch(`../modules/dashboard/chart-data.php?type=${type}`, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            let html = `<h4>${name}测试结果:</h4>`;
            
            if (data.success) {
                html += `<p style="color: green;">✅ 请求成功</p>`;
                html += `<p>数据条数: ${data.data ? data.data.length : 0}</p>`;
                html += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
            } else {
                html += `<p style="color: red;">❌ 请求失败: ${data.message}</p>`;
            }
            
            resultDiv.innerHTML = html;
        })
        .catch(error => {
            resultDiv.innerHTML = `<h4>${name}测试错误:</h4><p style="color: red;">❌ ${error.message}</p>`;
        });
    }
    </script>
    
    <p><a href="../modules/dashboard/index.php">返回仪表板</a></p>
</body>
</html>
