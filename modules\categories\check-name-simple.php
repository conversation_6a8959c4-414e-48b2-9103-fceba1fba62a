<?php
/**
 * 简化版分类名称检查API
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// 错误报告
error_reporting(E_ALL);
ini_set('display_errors', 0); // 不显示错误到输出

// 记录调试信息
function debug_log($message) {
    error_log(date('Y-m-d H:i:s') . ' - ' . $message . PHP_EOL, 3, 'debug.log');
}

debug_log('API调用开始');

try {
    // 检查请求方法
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        http_response_code(405);
        echo json_encode([
            'error' => true,
            'message' => 'Only POST method allowed',
            'method' => $_SERVER['REQUEST_METHOD']
        ]);
        exit;
    }

    debug_log('POST数据: ' . print_r($_POST, true));

    // 获取参数
    $name = trim($_POST['name'] ?? '');
    $excludeId = intval($_POST['exclude_id'] ?? 0);

    debug_log("检查名称: $name, 排除ID: $excludeId");

    // 验证参数
    if (empty($name)) {
        echo json_encode([
            'available' => false,
            'message' => '分类名称不能为空'
        ]);
        exit;
    }

    // 尝试连接数据库
    require_once '../../includes/Database.php';
    debug_log('Database类加载成功');

    $db = Database::getInstance();
    debug_log('数据库连接成功');

    // 构建查询
    if ($excludeId > 0) {
        $sql = "SELECT id, name FROM ingredient_categories WHERE name = ? AND id != ?";
        $params = [$name, $excludeId];
        debug_log("编辑模式查询: $sql, 参数: " . implode(', ', $params));
    } else {
        $sql = "SELECT id, name FROM ingredient_categories WHERE name = ?";
        $params = [$name];
        debug_log("新建模式查询: $sql, 参数: " . implode(', ', $params));
    }

    // 执行查询
    $existing = $db->fetchOne($sql, $params);
    debug_log('查询结果: ' . ($existing ? 'found id=' . $existing['id'] : 'not found'));

    // 返回结果
    if ($existing) {
        echo json_encode([
            'available' => false,
            'message' => '分类名称已存在',
            'existing_id' => $existing['id'],
            'existing_name' => $existing['name']
        ]);
    } else {
        echo json_encode([
            'available' => true,
            'message' => '分类名称可用'
        ]);
    }

    debug_log('API调用成功完成');

} catch (Exception $e) {
    debug_log('异常: ' . $e->getMessage());
    debug_log('堆栈: ' . $e->getTraceAsString());
    
    http_response_code(500);
    echo json_encode([
        'error' => true,
        'message' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ]);
}
?>
