<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试状态栏和拍照列优化</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .btn { display: inline-block; padding: 8px 16px; background: #007cba; color: white; text-decoration: none; border-radius: 4px; margin: 5px; }
        .btn-success { background: #28a745; }
        .btn-mobile { background: #17a2b8; }
        .btn-primary { background: #667eea; }
        .feature-box { background: #e6f3ff; padding: 15px; border-left: 4px solid #007cba; margin: 15px 0; border-radius: 5px; }
        .step-box { background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; border-radius: 5px; margin: 10px 0; }
        .mobile-demo { background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0; text-align: center; }
        .mobile-frame { width: 320px; height: 640px; border: 8px solid #333; border-radius: 25px; margin: 0 auto; background: white; position: relative; overflow: hidden; box-shadow: 0 10px 30px rgba(0,0,0,0.3); }
        .mobile-screen { width: 100%; height: 100%; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; display: flex; flex-direction: column; }
        .mobile-header { background: rgba(255,255,255,0.95); color: #667eea; padding: 15px; display: flex; align-items: center; justify-content: center; font-weight: bold; }
        .mobile-content { flex: 1; padding: 20px; display: flex; flex-direction: column; gap: 8px; overflow-y: auto; }
        
        /* 表格布局样式 */
        .mobile-table-header { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            color: white; 
            padding: 8px; 
            border-radius: 6px; 
            display: grid; 
            grid-template-columns: 2fr 1fr 1.2fr 1fr 0.6fr; 
            gap: 6px; 
            font-size: 10px; 
            font-weight: 600; 
            text-align: center; 
        }
        .mobile-table-row { 
            background: white; 
            padding: 8px; 
            color: #333; 
            border-left: 4px solid #667eea; 
            display: grid; 
            grid-template-columns: 2fr 1fr 1.2fr 1fr 0.6fr; 
            gap: 6px; 
            align-items: center; 
            min-height: 40px; 
            margin-bottom: 1px; 
        }
        
        /* 拍照按钮样式 */
        .photo-btn-demo {
            width: 28px;
            height: 28px;
            border: none;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 11px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .photo-btn-demo.camera {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .photo-btn-demo.success {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            color: white;
        }
        
        .photo-btn-demo.view {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }
        
        .comparison-table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        .comparison-table th, .comparison-table td { border: 1px solid #ddd; padding: 10px; text-align: left; }
        .comparison-table th { background: #f5f5f5; }
        .before-col { background: #fff3cd; }
        .after-col { background: #d4edda; }
        .code-box { background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 12px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>✅ 状态栏和拍照列优化完成！</h1>
    
    <div class="test-section">
        <h2>🎯 优化目标</h2>
        
        <div class="feature-box">
            <h4>📊 界面布局优化</h4>
            <p>优化状态栏显示和拍照列宽度，提升界面的简洁性和可用性。</p>
            
            <h5>主要改进：</h5>
            <ul>
                <li>🎯 <strong>状态栏简化</strong>：去掉"已完成"、"待拍照"、"待称重"文字，只保留图标</li>
                <li>📐 <strong>拍照列加宽</strong>：从0.8fr增加到1fr，提供更多操作空间</li>
                <li>🎨 <strong>视觉优化</strong>：界面更简洁，操作区域更宽敞</li>
                <li>📱 <strong>响应式适配</strong>：小屏幕下也保持合适的比例</li>
            </ul>
        </div>
    </div>
    
    <div class="test-section">
        <h2>📊 布局对比</h2>
        
        <table class="comparison-table">
            <thead>
                <tr>
                    <th>项目</th>
                    <th class="before-col">优化前</th>
                    <th class="after-col">优化后</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>状态栏显示</strong></td>
                    <td class="before-col">✅ 已完成<br>📷 待拍照<br>⏰ 待称重</td>
                    <td class="after-col">✅<br>📷<br>⏰</td>
                </tr>
                <tr>
                    <td><strong>拍照列宽度</strong></td>
                    <td class="before-col">0.8fr（较窄）</td>
                    <td class="after-col">1fr（加宽25%）</td>
                </tr>
                <tr>
                    <td><strong>小屏幕拍照列</strong></td>
                    <td class="before-col">0.7fr</td>
                    <td class="after-col">0.8fr</td>
                </tr>
                <tr>
                    <td><strong>界面简洁度</strong></td>
                    <td class="before-col">文字较多，显得拥挤</td>
                    <td class="after-col">只显示图标，更简洁</td>
                </tr>
                <tr>
                    <td><strong>操作便利性</strong></td>
                    <td class="before-col">拍照按钮空间较小</td>
                    <td class="after-col">拍照按钮空间更宽敞</td>
                </tr>
            </tbody>
        </table>
    </div>
    
    <div class="test-section">
        <h2>📱 界面预览</h2>
        
        <div class="mobile-demo">
            <h4>优化后的表格布局</h4>
            <div class="mobile-frame">
                <div class="mobile-screen">
                    <div class="mobile-header">
                        📊 状态栏和拍照列优化
                    </div>
                    <div class="mobile-content">
                        <!-- 表头 -->
                        <div class="mobile-table-header">
                            <div style="text-align: left; padding-left: 4px;">商品名称</div>
                            <div>采购数量</div>
                            <div>实际重量</div>
                            <div>拍照</div>
                            <div>状态</div>
                        </div>
                        
                        <!-- 已完成商品 -->
                        <div class="mobile-table-row">
                            <div style="font-weight: 600; font-size: 12px; text-align: left; padding-left: 4px;">白菜</div>
                            <div style="font-size: 11px; text-align: center;">50斤</div>
                            <div style="display: flex; justify-content: center;">
                                <div style="display: flex; border: 1px solid #ddd; border-radius: 3px; overflow: hidden; width: 60px; height: 24px;">
                                    <input style="width: 40px; padding: 2px; border: none; font-size: 10px; text-align: center;" value="48.5">
                                    <div style="padding: 2px 4px; background: #f0f0f0; font-size: 9px; line-height: 1.8;">斤</div>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: center; gap: 2px;">
                                <button class="photo-btn-demo success" title="重新拍摄">
                                    ✅
                                </button>
                                <button class="photo-btn-demo view" title="查看">
                                    👁️
                                </button>
                            </div>
                            <div style="display: flex; justify-content: center; font-size: 16px; color: #27ae60;">✅</div>
                        </div>
                        
                        <!-- 待拍照商品 -->
                        <div class="mobile-table-row">
                            <div style="font-weight: 600; font-size: 12px; text-align: left; padding-left: 4px;">猪肉</div>
                            <div style="font-size: 11px; text-align: center;">20斤</div>
                            <div style="display: flex; justify-content: center;">
                                <div style="display: flex; border: 1px solid #ddd; border-radius: 3px; overflow: hidden; width: 60px; height: 24px;">
                                    <input style="width: 40px; padding: 2px; border: none; font-size: 10px; text-align: center;" value="19.5">
                                    <div style="padding: 2px 4px; background: #f0f0f0; font-size: 9px; line-height: 1.8;">斤</div>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: center;">
                                <button class="photo-btn-demo camera" title="拍照">
                                    📷
                                </button>
                            </div>
                            <div style="display: flex; justify-content: center; font-size: 16px; color: #3498db;">📷</div>
                        </div>
                        
                        <!-- 待称重商品 -->
                        <div class="mobile-table-row">
                            <div style="font-weight: 600; font-size: 12px; text-align: left; padding-left: 4px;">胡萝卜</div>
                            <div style="font-size: 11px; text-align: center;">30斤</div>
                            <div style="display: flex; justify-content: center;">
                                <div style="display: flex; border: 1px solid #ddd; border-radius: 3px; overflow: hidden; width: 60px; height: 24px;">
                                    <input style="width: 40px; padding: 2px; border: none; font-size: 10px; text-align: center;" placeholder="0.00">
                                    <div style="padding: 2px 4px; background: #f0f0f0; font-size: 9px; line-height: 1.8;">斤</div>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: center;">
                                <button class="photo-btn-demo camera" title="拍照">
                                    📷
                                </button>
                            </div>
                            <div style="display: flex; justify-content: center; font-size: 16px; color: #f39c12;">⏰</div>
                        </div>
                        
                        <div style="text-align: center; margin-top: 20px; font-size: 12px; color: rgba(255,255,255,0.8);">
                            ✅ 状态栏只显示图标<br>
                            📷 拍照列空间更宽敞<br>
                            🎨 界面更简洁美观
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="test-section">
        <h2>🎨 状态图标说明</h2>
        
        <div class="step-box">
            <h4>状态图标含义</h4>
            <ul>
                <li><span style="color: #27ae60; font-size: 16px;">✅</span> <strong>已完成</strong>：已称重且已拍照</li>
                <li><span style="color: #3498db; font-size: 16px;">📷</span> <strong>待拍照</strong>：已称重但未拍照</li>
                <li><span style="color: #f39c12; font-size: 16px;">⏰</span> <strong>待称重</strong>：未称重</li>
            </ul>
            <p><strong>优势</strong>：图标比文字更直观，节省空间，界面更简洁。</p>
        </div>
    </div>
</body>
</html>
