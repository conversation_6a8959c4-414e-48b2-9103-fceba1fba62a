<?php
/**
 * 简化的Excel读取器
 * 基于PhpSpreadsheet的简化版本，适用于无Composer环境
 */

class ExcelReader
{
    private $supportedFormats = ['xlsx', 'xls', 'csv'];
    
    /**
     * 读取Excel文件
     */
    public function read($filePath, $originalFileName = null)
    {
        if (!file_exists($filePath)) {
            throw new Exception('文件不存在');
        }
        
        // 获取文件扩展名
        $fileName = $originalFileName ?: $filePath;
        $extension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
        
        if (!in_array($extension, $this->supportedFormats)) {
            throw new Exception('不支持的文件格式，支持的格式：' . implode(', ', $this->supportedFormats));
        }
        
        switch ($extension) {
            case 'xlsx':
                return $this->readXlsx($filePath);
            case 'xls':
                return $this->readXls($filePath);
            case 'csv':
                return $this->readCsv($filePath);
            default:
                throw new Exception('未知的文件格式');
        }
    }
    
    /**
     * 读取XLSX文件（改进版）
     */
    private function readXlsx($filePath)
    {
        if (!class_exists('ZipArchive')) {
            throw new Exception('系统不支持ZIP扩展，无法读取XLSX文件');
        }
        
        $tempDir = sys_get_temp_dir() . '/xlsx_' . uniqid();
        
        try {
            // 解压XLSX文件
            $zip = new ZipArchive();
            if ($zip->open($filePath) !== TRUE) {
                throw new Exception('无法打开XLSX文件');
            }
            
            if (!$zip->extractTo($tempDir)) {
                throw new Exception('无法解压XLSX文件');
            }
            $zip->close();
            
            // 读取工作簿信息
            $workbookFile = $tempDir . '/xl/workbook.xml';
            $sheetFile = $tempDir . '/xl/worksheets/sheet1.xml';
            $stringsFile = $tempDir . '/xl/sharedStrings.xml';
            $stylesFile = $tempDir . '/xl/styles.xml';
            
            if (!file_exists($sheetFile)) {
                throw new Exception('XLSX文件格式错误：找不到工作表');
            }
            
            // 读取共享字符串
            $sharedStrings = $this->readSharedStrings($stringsFile);
            
            // 读取样式信息（用于识别日期格式）
            $styles = $this->readStyles($stylesFile);
            
            // 读取工作表数据
            $data = $this->readWorksheet($sheetFile, $sharedStrings, $styles);
            
            return $data;
            
        } catch (Exception $e) {
            throw new Exception('XLSX文件解析失败: ' . $e->getMessage());
        } finally {
            // 清理临时文件
            if (is_dir($tempDir)) {
                $this->removeDirectory($tempDir);
            }
        }
    }
    
    /**
     * 读取共享字符串
     */
    private function readSharedStrings($stringsFile)
    {
        $strings = [];
        
        if (file_exists($stringsFile)) {
            $xml = simplexml_load_file($stringsFile);
            if ($xml) {
                foreach ($xml->si as $si) {
                    if (isset($si->t)) {
                        $strings[] = (string)$si->t;
                    } elseif (isset($si->r)) {
                        // 处理富文本
                        $richText = '';
                        foreach ($si->r as $r) {
                            if (isset($r->t)) {
                                $richText .= (string)$r->t;
                            }
                        }
                        $strings[] = $richText;
                    } else {
                        $strings[] = '';
                    }
                }
            }
        }
        
        return $strings;
    }
    
    /**
     * 读取样式信息
     */
    private function readStyles($stylesFile)
    {
        $styles = [];
        
        if (file_exists($stylesFile)) {
            $xml = simplexml_load_file($stylesFile);
            if ($xml && isset($xml->cellXfs)) {
                foreach ($xml->cellXfs->xf as $index => $xf) {
                    $styles[$index] = [
                        'numFmtId' => isset($xf['numFmtId']) ? (int)$xf['numFmtId'] : 0
                    ];
                }
            }
        }
        
        return $styles;
    }
    
    /**
     * 读取工作表数据
     */
    private function readWorksheet($sheetFile, $sharedStrings, $styles)
    {
        $xml = simplexml_load_file($sheetFile);
        if (!$xml) {
            throw new Exception('无法解析工作表数据');
        }
        
        $rows = [];
        $maxCol = 0;
        
        foreach ($xml->sheetData->row as $row) {
            $rowIndex = (int)$row['r'] - 1;
            $rowData = [];
            
            foreach ($row->c as $cell) {
                $cellRef = (string)$cell['r'];
                $colIndex = $this->columnIndexFromString($cellRef);
                $maxCol = max($maxCol, $colIndex);
                
                $cellValue = $this->getCellValue($cell, $sharedStrings, $styles);
                $rowData[$colIndex] = $cellValue;
            }
            
            // 填充空列
            for ($i = 0; $i <= $maxCol; $i++) {
                if (!isset($rowData[$i])) {
                    $rowData[$i] = '';
                }
            }
            
            ksort($rowData);
            $rows[$rowIndex] = array_values($rowData);
        }
        
        // 填充空行并排序
        if (!empty($rows)) {
            $maxRow = max(array_keys($rows));
            for ($i = 0; $i <= $maxRow; $i++) {
                if (!isset($rows[$i])) {
                    $rows[$i] = array_fill(0, $maxCol + 1, '');
                }
            }
            ksort($rows);
        }
        
        return array_values($rows);
    }
    
    /**
     * 获取单元格值
     */
    private function getCellValue($cell, $sharedStrings, $styles)
    {
        if (!isset($cell->v)) {
            return '';
        }
        
        $value = (string)$cell->v;
        $cellType = isset($cell['t']) ? (string)$cell['t'] : '';
        $styleIndex = isset($cell['s']) ? (int)$cell['s'] : 0;
        
        // 共享字符串
        if ($cellType === 's') {
            $index = (int)$value;
            return isset($sharedStrings[$index]) ? $sharedStrings[$index] : '';
        }
        
        // 布尔值
        if ($cellType === 'b') {
            return $value === '1' ? 'TRUE' : 'FALSE';
        }
        
        // 错误值
        if ($cellType === 'e') {
            return '#ERROR';
        }
        
        // 数字或日期
        if (is_numeric($value)) {
            // 检查是否是日期格式
            if (isset($styles[$styleIndex])) {
                $numFmtId = $styles[$styleIndex]['numFmtId'];
                if ($this->isDateFormat($numFmtId)) {
                    return $this->excelDateToString($value);
                }
            }
            
            // 普通数字
            return $this->formatNumber($value);
        }
        
        return $value;
    }
    
    /**
     * 检查是否是日期格式
     */
    private function isDateFormat($numFmtId)
    {
        // Excel内置日期格式ID
        $dateFormats = [14, 15, 16, 17, 18, 19, 20, 21, 22];
        return in_array($numFmtId, $dateFormats);
    }
    
    /**
     * Excel日期转字符串
     */
    private function excelDateToString($excelDate)
    {
        // Excel日期基准：1900-01-01
        $baseDate = new DateTime('1900-01-01');
        $days = (int)$excelDate - 2; // Excel的日期计算有偏差
        $baseDate->add(new DateInterval('P' . $days . 'D'));
        return $baseDate->format('Y-m-d');
    }
    
    /**
     * 格式化数字
     */
    private function formatNumber($value)
    {
        $num = (float)$value;
        if ($num == (int)$num) {
            return (string)(int)$num;
        }
        return (string)$num;
    }
    
    /**
     * 从单元格引用获取列索引
     */
    private function columnIndexFromString($cellRef)
    {
        preg_match('/([A-Z]+)/', $cellRef, $matches);
        if (!isset($matches[1])) {
            return 0;
        }
        
        $column = $matches[1];
        $index = 0;
        $length = strlen($column);
        
        for ($i = 0; $i < $length; $i++) {
            $index = $index * 26 + (ord($column[$i]) - ord('A') + 1);
        }
        
        return $index - 1;
    }
    
    /**
     * 读取XLS文件（简化处理）
     */
    private function readXls($filePath)
    {
        // XLS格式比较复杂，建议用户转换为XLSX
        throw new Exception('暂不支持.xls格式，请将文件另存为.xlsx格式');
    }
    
    /**
     * 读取CSV文件
     */
    private function readCsv($filePath)
    {
        $data = [];
        $handle = fopen($filePath, 'r');
        
        if ($handle === false) {
            throw new Exception('无法打开CSV文件');
        }
        
        // 检测编码
        $firstLine = fgets($handle);
        rewind($handle);
        
        $encoding = mb_detect_encoding($firstLine, ['UTF-8', 'GBK', 'GB2312'], true);
        
        while (($row = fgetcsv($handle)) !== false) {
            if ($encoding && $encoding !== 'UTF-8') {
                $row = array_map(function($cell) use ($encoding) {
                    return mb_convert_encoding($cell, 'UTF-8', $encoding);
                }, $row);
            }
            $data[] = $row;
        }
        
        fclose($handle);
        return $data;
    }
    
    /**
     * 递归删除目录
     */
    private function removeDirectory($dir)
    {
        if (!is_dir($dir)) {
            return;
        }
        
        $files = array_diff(scandir($dir), ['.', '..']);
        foreach ($files as $file) {
            $path = $dir . '/' . $file;
            if (is_dir($path)) {
                $this->removeDirectory($path);
            } else {
                unlink($path);
            }
        }
        rmdir($dir);
    }
    
    /**
     * 获取支持的格式
     */
    public function getSupportedFormats()
    {
        return $this->supportedFormats;
    }
    
    /**
     * 检查文件格式是否支持
     */
    public function isSupported($fileName)
    {
        $extension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
        return in_array($extension, $this->supportedFormats);
    }
}
