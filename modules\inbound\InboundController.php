<?php
/**
 * 入库管理控制器
 */
require_once dirname(__DIR__, 2) . '/includes/BaseController.php';
require_once dirname(__DIR__, 2) . '/includes/helpers.php';

class InboundController extends BaseController
{
    protected function init()
    {
        $this->setTemplateData([
            'page_title' => '入库管理 - ' . $this->config['name'],
            'current_module' => 'inbound'
        ]);
    }

    public function handleRequest()
    {
        switch ($this->request['action']) {
            case 'create':
                return $this->create();
            case 'edit':
                return $this->edit();
            case 'delete':
                return $this->delete();
            case 'view':
                return $this->view();
            case 'doc':
                return $this->doc();
            case 'index':
            default:
                return $this->index();
        }
    }

    /**
     * 入库记录列表页面
     */
    private function index()
    {
        try {
            // 获取搜索参数
            $search = $this->request['get']['search'] ?? '';
            $supplier = $this->request['get']['supplier'] ?? '';
            $date_from = $this->request['get']['date_from'] ?? '';
            $date_to = $this->request['get']['date_to'] ?? '';

            // 构建查询条件（成单列表，按单号聚合）
            $where = ['ir.status = 1'];
            $params = [];

            if ($search) {
                $where[] = "(SUBSTRING_INDEX(ir.batch_number,'_',1) LIKE ? OR s.name LIKE ? )";
                $params[] = '%' . $search . '%';
                $params[] = '%' . $search . '%';
            }

            if ($supplier) {
                $where[] = 'ir.supplier_id = ?';
                $params[] = $supplier;
            }

            if ($date_from) {
                $where[] = 'DATE(ir.production_date) >= ?';
                $params[] = $date_from;
            }

            if ($date_to) {
                $where[] = 'DATE(ir.production_date) <= ?';
                $params[] = $date_to;
            }

            $whereClause = 'WHERE ' . implode(' AND ', $where);

            // 入库单列表（按单号聚合）
            $records = $this->db->fetchAll("
                SELECT 
                  SUBSTRING_INDEX(ir.batch_number,'_',1) AS doc_no,
                  COUNT(*) AS item_count,
                  SUM(ir.quantity) AS total_quantity,
                  SUM(ir.quantity * ir.unit_price) AS total_amount,
                  MIN(ir.production_date) AS inbound_date,
                  GROUP_CONCAT(DISTINCT s.name SEPARATOR '、') AS supplier_names,
                  MIN(ir.created_at) AS created_at
                FROM inbound_records ir
                LEFT JOIN suppliers s ON ir.supplier_id = s.id
                $whereClause
                GROUP BY SUBSTRING_INDEX(ir.batch_number,'_',1)
                ORDER BY created_at DESC
                LIMIT 50
            ", $params);

            // 无数据时提供示例单据列表
            if (empty($records)) {
                $records = $this->getMockInboundDocList();
            }

            // 获取供应商列表
            $suppliers = $this->db->fetchAll("
                SELECT id, name FROM suppliers 
                WHERE status = 1 
                ORDER BY name ASC
            ");

            $this->setTemplateData([
                'records' => $records,
                'suppliers' => $suppliers,
                'search' => $search,
                'selected_supplier' => $supplier,
                'date_from' => $date_from,
                'date_to' => $date_to
            ]);

        } catch (Exception $e) {
            // 使用示例单据列表
            $this->setTemplateData([
                'records' => $this->getMockInboundDocList(),
                'suppliers' => $this->getMockSuppliers(),
                'search' => $search ?? '',
                'selected_supplier' => $supplier ?? '',
                'date_from' => $date_from ?? '',
                'date_to' => $date_to ?? ''
            ]);
        }

        $this->render('template.php');
    }

    /**
     * 创建入库记录
     */
    private function create()
    {
        $this->requirePermission('inbound.create');
        if ($this->request['method'] === 'POST') {
            try {
                $batchType = $this->request['post']['batch_type'] ?? 'single';

                if ($batchType === 'batch') {
                    // 批量入库处理
                    return $this->processBatchInbound();
                } else {
                    // 单个商品入库处理
                    return $this->processSingleInbound();
                }

            } catch (Exception $e) {
                try {
                    $this->db->rollback();
                } catch (Exception $rollbackException) {
                    // 忽略回滚错误
                }
                
                // 记录详细错误日志
                $errorDetails = [
                    'error_message' => $e->getMessage(),
                    'error_trace' => $e->getTraceAsString(),
                    'request_data' => json_encode($_POST, JSON_UNESCAPED_UNICODE),
                    'timestamp' => date('Y-m-d H:i:s'),
                    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                    'remote_ip' => $_SERVER['REMOTE_ADDR'] ?? ''
                ];
                
                // 写入错误日志
                $logDir = dirname(__DIR__, 2) . '/logs/';
                if (!is_dir($logDir)) {
                    mkdir($logDir, 0755, true);
                }
                $logFile = $logDir . 'inbound_errors_' . date('Y-m-d') . '.log';
                file_put_contents($logFile, 
                    "[" . date('Y-m-d H:i:s') . "] INBOUND ERROR:\n" . 
                    json_encode($errorDetails, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n", 
                    FILE_APPEND | LOCK_EX
                );
                
                // 检查是否是移动端 AJAX 请求
                if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
                    strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
                    header('Content-Type: application/json');
                    echo json_encode([
                        'success' => false, 
                        'message' => $e->getMessage(),
                        'error_code' => 'INBOUND_ERROR',
                        'timestamp' => date('Y-m-d H:i:s')
                    ]);
                    exit;
                }
                
                $this->setTemplateData('error_message', $e->getMessage());
            }
        }

        $this->loadFormData();
    }

    /**
     * 处理批量入库
     */
    private function processBatchInbound()
    {
        // 优先使用 supplier_id，如果为空则使用 supplier_id_hidden
        $supplierId = intval($this->request['post']['supplier_id'] ?? $this->request['post']['supplier_id_hidden'] ?? 0);
        $orderId = intval($this->request['post']['order_id'] ?? 0);
        $items = $this->request['post']['items'] ?? [];
        $batchNumber = trim($this->request['post']['batch_number'] ?? '');

        // 检查是否为新建采购单
        $isNewOrder = intval($this->request['post']['is_new_order'] ?? 0);
        $newOrderData = $this->request['post']['new_order_data'] ?? '';

        // 如果是新建采购单，先创建采购单
        if ($isNewOrder && !empty($newOrderData)) {
            $orderData = json_decode($newOrderData, true);
            if ($orderData) {
                $orderId = $this->createPurchaseOrder($orderData);
                $supplierId = $orderData['order_info']['supplier_id'];

                // 重新构造items数组以匹配批量入库格式
                $items = [];
                foreach ($orderData['items'] as $item) {
                    $items[] = [
                        'ingredient_id' => $item['ingredient_id'],
                        'quantity' => $item['quantity'], // 采购数量
                        'actual_quantity' => $item['quantity'], // 默认实际数量等于采购数量
                        'unit_price' => $item['unit_price'],
                        'purpose' => $item['purpose'] ?? ''
                    ];
                }
            }
        }
        $inboundDate = $this->request['post']['inbound_date'] ?: date('Y-m-d');
        $operatorName = trim($this->request['post']['operator_name'] ?? '操作员');
        $notes = trim($this->request['post']['notes'] ?? '');

        if (!$supplierId || empty($items)) {
            throw new Exception('请选择采购单并确认商品列表');
        }

        // 处理照片上传
        $photoData = $this->handlePhotoUploads();

        // 开始事务
        $this->db->beginTransaction();

        $totalAmount = 0;
        $recordIds = [];

        foreach ($items as $index => $item) {
            $ingredientId = intval($item['ingredient_id']);
            $unitPrice = floatval($item['unit_price']);
            $actualQuantity = floatval($item['actual_quantity']);

            if ($actualQuantity <= 0) {
                continue; // 跳过数量为0的商品
            }

            $itemTotal = $actualQuantity * $unitPrice;
            $totalAmount += $itemTotal;

            // 为每个商品生成唯一的批次号
            $itemBatchNumber = $batchNumber . '_' . ($index + 1);

            $data = [
                'ingredient_id' => $ingredientId,
                'supplier_id' => $supplierId,
                'quantity' => $actualQuantity,
                'unit_price' => $unitPrice,
                'batch_number' => $itemBatchNumber,
                'production_date' => $inboundDate,
                'expired_at' => date('Y-m-d', strtotime($inboundDate . ' +7 days')), // 默认7天保质期
                'purchase_invoice' => '', // 空发票号
                'notes' => $notes,
                'status' => 1,
                'created_by' => 1, // 默认操作员ID
                'created_at' => date('Y-m-d H:i:s')
            ];

            // 注意：当前表结构不支持以下字段：order_id, delivery_photo, weight_photo, inbound_date, operator_name

            // 插入入库记录
            $recordId = $this->db->insert('inbound_records', $data);
            $recordIds[] = $recordId;

            // 更新食材库存
            $this->updateIngredientStock($ingredientId, $actualQuantity, 'add');
        }

        // 如果是基于采购单的入库（包括新建和现有采购单），更新采购单状态为已完成
        if ($orderId > 0) {
            $this->db->update('purchase_orders', [
                'status' => 4, // 已完成
                'updated_at' => date('Y-m-d H:i:s')
            ], 'id = ?', [$orderId]);

            // 记录日志
            error_log("入库完成，采购单ID {$orderId} 状态已更新为已完成");
        } else if (!$isNewOrder) {
            // 如果不是新建采购单但也没有orderId，尝试从采购单数据中查找
            // 这种情况可能发生在前端传递了采购单数据但没有正确设置order_id的情况
            $supplierIdFromPost = intval($this->request['post']['supplier_id'] ?? $this->request['post']['supplier_id_hidden'] ?? 0);
            if ($supplierIdFromPost > 0 && !empty($items)) {
                // 根据供应商和商品信息查找可能的采购单
                $firstItem = reset($items);
                if ($firstItem && isset($firstItem['ingredient_id'])) {
                    $possibleOrder = $this->db->fetchOne("
                        SELECT po.id
                        FROM purchase_orders po
                        INNER JOIN purchase_order_items poi ON po.id = poi.order_id
                        WHERE po.supplier_id = ?
                        AND poi.ingredient_id = ?
                        AND po.status IN (1, 2)
                        ORDER BY po.created_at DESC
                        LIMIT 1
                    ", [$supplierIdFromPost, $firstItem['ingredient_id']]);

                    if ($possibleOrder) {
                        $this->db->update('purchase_orders', [
                            'status' => 4, // 已完成
                            'updated_at' => date('Y-m-d H:i:s')
                        ], 'id = ?', [$possibleOrder['id']]);
                    }
                }
            }
        }

        $this->db->commit();

        // 检查是否是移动端 AJAX 请求
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) &&
            strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'message' => "批量入库成功，共入库 " . count($recordIds) . " 种商品，总金额 ¥" . number_format($totalAmount, 2),
                'data' => [
                    'record_count' => count($recordIds),
                    'total_amount' => $totalAmount
                ]
            ]);
            exit;
        }

        $this->redirect('index.php', "批量入库成功，共入库 " . count($recordIds) . " 种商品，总金额 ¥" . number_format($totalAmount, 2), 'success');
    }

    /**
     * 处理单个商品入库
     */
    private function processSingleInbound()
    {
        try {
            // 生成批次号
            $batch_number = $this->generateBatchNumber();

            // 使用采购数量作为入库数量
            $quantity = floatval($this->request['post']['quantity']);
            $unitPrice = floatval($this->request['post']['unit_price']);

            $data = [
                'ingredient_id' => intval($this->request['post']['ingredient_id']),
                'supplier_id' => intval($this->request['post']['supplier_id']),
                'quantity' => $quantity,
                'unit_price' => $unitPrice,
                'batch_number' => !empty($this->request['post']['batch_number']) ? trim($this->request['post']['batch_number']) : $batch_number,
                'production_date' => $this->request['post']['inbound_date'] ?: date('Y-m-d'),
                'expired_at' => date('Y-m-d', strtotime(($this->request['post']['inbound_date'] ?: date('Y-m-d')) . ' +7 days')),
                'purchase_invoice' => '',
                'notes' => trim($this->request['post']['notes'] ?? ''),
                'status' => 1,
                'created_by' => 1,
                'created_at' => date('Y-m-d H:i:s')
            ];

            // 验证必填字段
            $errors = [];
            if (!$data['ingredient_id']) $errors[] = '请选择食材';
            if (!$data['supplier_id']) $errors[] = '请选择供应商';
            if ($data['quantity'] <= 0) $errors[] = '数量必须大于0';
            if ($data['unit_price'] < 0) $errors[] = '单价不能为负数';
            if (empty($data['batch_number'])) $errors[] = '批次号不能为空';
            if (empty($data['production_date'])) $errors[] = '入库日期不能为空';

            if (!empty($errors)) {
                throw new Exception(implode(', ', $errors));
            }

            // 开始事务
            $this->db->beginTransaction();

            // 插入入库记录
            $recordId = $this->db->insert('inbound_records', $data);

            // 更新食材库存
            $this->updateIngredientStock($data['ingredient_id'], $data['quantity'], 'add');

            $this->db->commit();
            
            // 检查是否是移动端 AJAX 请求
            if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
                strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => true, 
                    'message' => '入库记录添加成功',
                    'data' => [
                        'record_id' => $recordId,
                        'total_amount' => $quantity * $unitPrice
                    ]
                ]);
                exit;
            }
            
            // 跳转入库单
            $this->redirect('index.php?action=doc&doc_no=' . urlencode($data['batch_number']), '入库记录添加成功', 'success');

        } catch (Exception $e) {
            try {
                $this->db->rollback();
            } catch (Exception $rollbackException) {
                // 忽略回滚错误
            }
            throw $e;
        }
    }

    /**
     * 加载表单数据
     */
    private function loadFormData()
    {
        // 获取食材、供应商列表和未完成的采购单
        try {
            $ingredients = $this->db->fetchAll("SELECT id, name, unit FROM ingredients WHERE status = 1 ORDER BY name ASC");
            $suppliers = $this->db->fetchAll("SELECT id, name FROM suppliers WHERE status = 1 ORDER BY name ASC");

            // 获取未完成的采购单（状态为1待确认或2已确认）
            $purchaseOrders = $this->db->fetchAll("
                SELECT
                    po.id,
                    po.order_number,
                    po.supplier_id,
                    s.name as supplier_name,
                    po.order_date,
                    po.order_amount,
                    poi.ingredient_id,
                    i.name as ingredient_name,
                    i.unit,
                    poi.quantity,
                    poi.unit_price,
                    COALESCE(poi.notes, '') as purpose
                FROM purchase_orders po
                INNER JOIN purchase_order_items poi ON po.id = poi.order_id
                LEFT JOIN suppliers s ON po.supplier_id = s.id
                LEFT JOIN ingredients i ON poi.ingredient_id = i.id
                WHERE po.status IN (1, 2)
                AND poi.ingredient_id IS NOT NULL
                AND i.id IS NOT NULL
                ORDER BY po.order_date DESC, po.order_number ASC
            ");
        } catch (Exception $e) {
            $ingredients = $this->getMockIngredients();
            $suppliers = $this->getMockSuppliers();
            $purchaseOrders = [];

            // 记录错误但不使用模拟数据
            error_log("获取采购单数据失败: " . $e->getMessage());

            // 仅记录日志，生产环境不向页面暴露调试信息
        }

        // 添加调试信息
        $this->setTemplateData(['debug_purchase_orders_count' => count($purchaseOrders)]);

        $this->setTemplateData([
            'ingredients' => $ingredients,
            'suppliers' => $suppliers,
            'purchase_orders' => $purchaseOrders
        ]);

        // 检查是否是移动端 AJAX 请求，如果是则不渲染模板
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
            strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            // AJAX 请求在这里不应该渲染模板，直接返回
            return;
        }

        $this->render('create-template.php');
    }

    /**
     * 编辑入库记录
     */
    private function edit()
    {
        $this->requirePermission('inbound.edit');
        $id = intval($this->request['get']['id'] ?? 0);

        if (!$id) {
            $this->redirect('index.php', '参数错误', 'error');
            return;
        }

        if ($this->request['method'] === 'POST') {
            try {
                // 获取表单数据
                $data = [
                    'ingredient_id' => intval($this->request['post']['ingredient_id']),
                    'supplier_id' => intval($this->request['post']['supplier_id']),
                    'quantity' => floatval($this->request['post']['quantity']),
                    'unit_price' => floatval($this->request['post']['unit_price']),
                    'total_amount' => floatval($this->request['post']['quantity']) * floatval($this->request['post']['unit_price']),
                    'batch_number' => trim($this->request['post']['batch_number']),
                    'inbound_date' => $this->request['post']['inbound_date'],
                    'operator_name' => trim($this->request['post']['operator_name']),
                    'notes' => trim($this->request['post']['notes'] ?? ''),
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                // 验证必填字段
                $errors = [];
                if (!$data['ingredient_id']) $errors[] = '请选择食材';
                if (!$data['supplier_id']) $errors[] = '请选择供应商';
                if ($data['quantity'] <= 0) $errors[] = '数量必须大于0';
                if ($data['unit_price'] < 0) $errors[] = '单价不能为负数';
                if (empty($data['batch_number'])) $errors[] = '批次号不能为空';
                if (empty($data['inbound_date'])) $errors[] = '入库日期不能为空';
                if (empty($data['operator_name'])) $errors[] = '操作员不能为空';

                if (!empty($errors)) {
                    throw new Exception(implode(', ', $errors));
                }

                // 更新入库记录
                $this->db->update('inbound_records', $data, 'id = ?', [$id]);
                $this->redirect('index.php', '入库记录更新成功', 'success');

            } catch (Exception $e) {
                $this->setTemplateData(['error' => $e->getMessage()]);
            }
        }

        try {
            // 获取入库记录详情
            $record = $this->db->fetchOne("
                SELECT ir.*, i.name as ingredient_name, i.unit, s.name as supplier_name
                FROM inbound_records ir
                LEFT JOIN ingredients i ON ir.ingredient_id = i.id
                LEFT JOIN suppliers s ON ir.supplier_id = s.id
                WHERE ir.id = ?
            ", [$id]);

            if (!$record) {
                $this->redirect('index.php', '入库记录不存在', 'error');
                return;
            }

            // 获取食材和供应商列表
            $ingredients = $this->db->fetchAll("SELECT id, name, unit FROM ingredients WHERE status = 1 ORDER BY name");
            $suppliers = $this->db->fetchAll("SELECT id, name FROM suppliers WHERE status = 1 ORDER BY name");

        } catch (Exception $e) {
            // 使用模拟数据
            $mockRecords = $this->getMockRecords();
            $record = array_filter($mockRecords, function($r) use ($id) {
                return $r['id'] == $id;
            });
            $record = !empty($record) ? array_values($record)[0] : null;

            if (!$record) {
                $this->redirect('index.php', '入库记录不存在', 'error');
                return;
            }

            $ingredients = $this->getMockIngredients();
            $suppliers = $this->getMockSuppliers();
        }

        $this->setTemplateData([
            'record' => $record,
            'ingredients' => $ingredients,
            'suppliers' => $suppliers,
            'action' => 'edit'
        ]);

        $this->render('create-template.php');
    }

    /**
     * 查看入库记录详情
     */
    private function view()
    {
        $id = intval($this->request['get']['id'] ?? 0);

        if (!$id) {
            $this->redirect('index.php', '参数错误', 'error');
            return;
        }

        try {
            $record = $this->db->fetchOne("
                SELECT ir.*, i.name as ingredient_name, i.unit, s.name as supplier_name
                FROM inbound_records ir
                LEFT JOIN ingredients i ON ir.ingredient_id = i.id
                LEFT JOIN suppliers s ON ir.supplier_id = s.id
                WHERE ir.id = ?
            ", [$id]);

            if (!$record) {
                $this->redirect('index.php', '入库记录不存在', 'error');
                return;
            }

        } catch (Exception $e) {
            // 使用模拟数据
            $mockRecords = $this->getMockRecords();
            $record = array_filter($mockRecords, function($r) use ($id) {
                return $r['id'] == $id;
            });
            $record = !empty($record) ? array_values($record)[0] : null;

            if (!$record) {
                $this->redirect('index.php', '入库记录不存在', 'error');
                return;
            }
        }

        $this->setTemplateData([
            'record' => $record,
            'action' => 'view'
        ]);

        $this->render('view-template.php');
    }

    /**
     * 入库单据视图（按单号聚合明细并展示）
     */
    private function doc()
    {
        $docNo = trim($this->request['get']['doc_no'] ?? '');
        $id = intval($this->request['get']['id'] ?? 0);

        try {
            if ($docNo === '' && $id > 0) {
                $rec = $this->db->fetchOne("SELECT batch_number FROM inbound_records WHERE id = ?", [$id]);
                if ($rec && !empty($rec['batch_number'])) {
                    // 去掉末尾的 _序号 得到单号
                    $docNo = preg_replace('/_\\d+$/', '', $rec['batch_number']);
                }
            }

            if ($docNo === '') {
                $this->redirect('index.php', '缺少入库单号', 'error');
                return;
            }

            // 查询该单号下的全部明细
            $items = $this->db->fetchAll(
                "SELECT ir.*, i.name AS ingredient_name, i.unit, s.name AS supplier_name
                 FROM inbound_records ir
                 LEFT JOIN ingredients i ON ir.ingredient_id = i.id
                 LEFT JOIN suppliers s ON ir.supplier_id = s.id
                 WHERE ir.status = 1 AND SUBSTRING_INDEX(ir.batch_number,'_',1) = ?
                 ORDER BY ir.created_at ASC",
                [$docNo]
            );

            // 汇总信息
            $totalAmount = 0.0;
            $suppliers = [];
            $inboundDate = '';
            foreach ($items as $row) {
                $totalAmount += floatval(($row['quantity'] ?? 0) * ($row['unit_price'] ?? 0));
                if (!empty($row['supplier_name'])) {
                    $suppliers[$row['supplier_name']] = true;
                }
                if (empty($inboundDate) && !empty($row['production_date'])) {
                    $inboundDate = $row['production_date'];
                }
            }

            $summary = [
                'doc_no' => $docNo,
                'inbound_date' => $inboundDate,
                'supplier_name' => implode('、', array_keys($suppliers)),
                'operator_name' => '管理员',
                'total_items' => count($items),
                'total_amount' => $totalAmount,
            ];

            $this->setTemplateData([
                'summary' => $summary,
                'items' => $items,
            ]);

            $this->render('doc-template.php');
        } catch (Exception $e) {
            $this->redirect('index.php', '加载入库单失败：' . $e->getMessage(), 'error');
        }
    }

    /**
     * 删除入库记录
     */
    private function delete()
    {
        $this->requirePermission('inbound.delete');
        $id = intval($this->request['get']['id'] ?? 0);

        if (!$id) {
            $this->redirect('index.php', '参数错误', 'error');
            return;
        }

        try {
            // 获取入库记录信息（用于回滚库存）
            $record = $this->db->fetchOne("SELECT ingredient_id, quantity FROM inbound_records WHERE id = ?", [$id]);

            if ($record) {
                // 开始事务
                $this->db->beginTransaction();

                // 删除入库记录
                $this->db->delete('inbound_records', 'id = ?', [$id]);

                // 回滚库存（减少库存）
                $this->updateIngredientStock($record['ingredient_id'], $record['quantity'], 'subtract');

                // 提交事务
                $this->db->commit();

                $this->redirect('index.php', '入库记录删除成功', 'success');
            } else {
                $this->redirect('index.php', '入库记录不存在', 'error');
            }

        } catch (Exception $e) {
            $this->db->rollback();
            $this->redirect('index.php', '删除失败：' . $e->getMessage(), 'error');
        }
    }

    /**
     * 生成批次号
     */
    private function generateBatchNumber()
    {
        return 'IN' . date('YmdHis') . rand(100, 999);
    }

    /**
     * 处理照片上传
     */
    private function handlePhotoUploads()
    {
        $photoData = [];
        $uploadDir = dirname(__DIR__, 2) . '/uploads/inbound/';

        // 确保上传目录存在
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }

        // 处理送货单照片
        if (isset($_FILES['delivery_photo']) && $_FILES['delivery_photo']['error'] === UPLOAD_ERR_OK) {
            $deliveryPhoto = $this->uploadPhoto($_FILES['delivery_photo'], $uploadDir, 'delivery');
            if ($deliveryPhoto) {
                $photoData['delivery_photo'] = $deliveryPhoto;
            }
        }

        return $photoData;
    }

    /**
     * 处理单个商品的称重照片
     */
    private function handleItemWeightPhoto($index)
    {
        $fieldName = "items[{$index}][weight_photo]";

        if (!isset($_FILES[$fieldName]) || $_FILES[$fieldName]['error'] !== UPLOAD_ERR_OK) {
            return null;
        }

        $uploadDir = dirname(__DIR__, 2) . '/uploads/inbound/';

        // 确保上传目录存在
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }

        return $this->uploadPhoto($_FILES[$fieldName], $uploadDir, "weight_{$index}");
    }

    /**
     * 上传单个照片
     */
    private function uploadPhoto($file, $uploadDir, $prefix)
    {
        // 验证文件类型
        $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        if (!in_array($file['type'], $allowedTypes)) {
            return false;
        }

        // 验证文件大小（最大5MB）
        if ($file['size'] > 5 * 1024 * 1024) {
            return false;
        }

        // 生成唯一文件名
        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $filename = $prefix . '_' . date('YmdHis') . '_' . rand(1000, 9999) . '.' . $extension;
        $filepath = $uploadDir . $filename;

        // 移动文件
        if (move_uploaded_file($file['tmp_name'], $filepath)) {
            return 'uploads/inbound/' . $filename; // 返回相对路径
        }

        return false;
    }

    /**
     * 更新食材库存
     */
    private function updateIngredientStock($ingredientId, $quantity, $operation = 'add')
    {
        try {
            $sql = "UPDATE ingredients SET current_stock = current_stock " .
                   ($operation === 'add' ? '+' : '-') . " ? WHERE id = ?";
            $this->db->query($sql, [$quantity, $ingredientId]);
        } catch (Exception $e) {
            // 如果ingredients表不存在current_stock字段，忽略错误
            error_log("库存更新失败: " . $e->getMessage());
        }
    }

    /**
     * 获取模拟入库记录数据
     */
    private function getMockRecords()
    {
        return [
            [
                'id' => 1,
                'ingredient_name' => '白菜',
                'unit' => '斤',
                'supplier_name' => '绿色蔬菜供应商',
                'quantity' => 50.00,
                'unit_price' => 2.50,
                'total_amount' => 125.00,
                'batch_number' => 'BC20241201001',
                'production_date' => '2024-12-01',
                'expiry_date' => '2024-12-08',
                'inbound_date' => '2024-12-01 09:30:00',
                'operator_name' => '张三',
                'notes' => '新鲜蔬菜，质量良好',
                'created_at' => '2024-12-01 09:30:00'
            ],
            [
                'id' => 2,
                'ingredient_name' => '猪肉',
                'unit' => '斤',
                'supplier_name' => '优质肉类供应商',
                'quantity' => 20.00,
                'unit_price' => 28.00,
                'total_amount' => 560.00,
                'batch_number' => 'ZR20241201001',
                'production_date' => '2024-12-01',
                'expiry_date' => '2024-12-05',
                'inbound_date' => '2024-12-01 10:15:00',
                'operator_name' => '李四',
                'notes' => '冷鲜肉，已检验合格',
                'created_at' => '2024-12-01 10:15:00'
            ],
            [
                'id' => 3,
                'ingredient_name' => '大米',
                'unit' => '斤',
                'supplier_name' => '粮油批发商',
                'quantity' => 100.00,
                'unit_price' => 4.20,
                'total_amount' => 420.00,
                'batch_number' => 'DM20241130001',
                'production_date' => '2024-11-15',
                'expiry_date' => '2025-11-15',
                'inbound_date' => '2024-11-30 14:20:00',
                'operator_name' => '王五',
                'notes' => '优质大米，包装完好',
                'created_at' => '2024-11-30 14:20:00'
            ]
        ];
    }

    /**
     * 示例入库单据列表（按单号聚合）
     */
    private function getMockInboundDocList()
    {
        return [
            [
                'doc_no' => 'IN20241201001',
                'item_count' => 4,
                'total_quantity' => 115.0,
                'total_amount' => 1520.50,
                'inbound_date' => '2024-12-01',
                'supplier_names' => '绿色蔬菜供应商、优质肉类供应商',
                'created_at' => '2024-12-01 10:15:00'
            ],
            [
                'doc_no' => 'IN20241130001',
                'item_count' => 3,
                'total_quantity' => 65.0,
                'total_amount' => 820.00,
                'inbound_date' => '2024-11-30',
                'supplier_names' => '粮油批发商',
                'created_at' => '2024-11-30 14:20:00'
            ]
        ];
    }

    /**
     * 获取模拟食材数据
     */
    private function getMockIngredients()
    {
        return [
            ['id' => 1, 'name' => '白菜', 'unit' => '斤'],
            ['id' => 2, 'name' => '萝卜', 'unit' => '斤'],
            ['id' => 3, 'name' => '猪肉', 'unit' => '斤'],
            ['id' => 4, 'name' => '土豆', 'unit' => '斤'],
            ['id' => 5, 'name' => '牛肉', 'unit' => '斤'],
            ['id' => 6, 'name' => '鲫鱼', 'unit' => '斤'],
            ['id' => 7, 'name' => '带鱼', 'unit' => '斤'],
            ['id' => 8, 'name' => '大米', 'unit' => '袋'],
            ['id' => 9, 'name' => '食用油', 'unit' => '桶'],
            ['id' => 10, 'name' => '面粉', 'unit' => '袋']
        ];
    }

    /**
     * 获取模拟供应商数据
     */
    private function getMockSuppliers()
    {
        return [
            ['id' => 1, 'name' => '绿色蔬菜供应商'],
            ['id' => 2, 'name' => '优质肉类供应商'],
            ['id' => 3, 'name' => '新鲜水产供应商'],
            ['id' => 4, 'name' => '粮油批发商']
        ];
    }

    /**
     * 获取模拟采购单数据
     */
    private function getMockPurchaseOrders()
    {
        return [
            // 采购单1 - 绿色蔬菜供应商
            [
                'id' => 1,
                'order_number' => 'PO20241201001',
                'supplier_id' => 1,
                'supplier_name' => '绿色蔬菜供应商',
                'order_date' => '2024-12-01',
                'order_amount' => 1250.00,
                'ingredient_id' => 1,
                'ingredient_name' => '白菜',
                'unit' => '斤',
                'quantity' => 50.00,
                'unit_price' => 2.50,
                'purpose' => '午餐'
            ],
            [
                'id' => 1,
                'order_number' => 'PO20241201001',
                'supplier_id' => 1,
                'supplier_name' => '绿色蔬菜供应商',
                'order_date' => '2024-12-01',
                'order_amount' => 1250.00,
                'ingredient_id' => 2,
                'ingredient_name' => '萝卜',
                'unit' => '斤',
                'quantity' => 30.00,
                'unit_price' => 3.00,
                'purpose' => '午餐'
            ],
            [
                'id' => 1,
                'order_number' => 'PO20241201001',
                'supplier_id' => 1,
                'supplier_name' => '绿色蔬菜供应商',
                'order_date' => '2024-12-01',
                'order_amount' => 1250.00,
                'ingredient_id' => 4,
                'ingredient_name' => '土豆',
                'unit' => '斤',
                'quantity' => 40.00,
                'unit_price' => 2.00,
                'purpose' => '晚餐'
            ],

            // 采购单2 - 优质肉类供应商
            [
                'id' => 2,
                'order_number' => 'PO20241201002',
                'supplier_id' => 2,
                'supplier_name' => '优质肉类供应商',
                'order_date' => '2024-12-01',
                'order_amount' => 2800.00,
                'ingredient_id' => 3,
                'ingredient_name' => '猪肉',
                'unit' => '斤',
                'quantity' => 20.00,
                'unit_price' => 28.00,
                'purpose' => '晚餐'
            ],
            [
                'id' => 2,
                'order_number' => 'PO20241201002',
                'supplier_id' => 2,
                'supplier_name' => '优质肉类供应商',
                'order_date' => '2024-12-01',
                'order_amount' => 2800.00,
                'ingredient_id' => 5,
                'ingredient_name' => '牛肉',
                'unit' => '斤',
                'quantity' => 15.00,
                'unit_price' => 45.00,
                'purpose' => '午餐'
            ],

            // 采购单3 - 新鲜水产供应商
            [
                'id' => 3,
                'order_number' => 'PO20241202001',
                'supplier_id' => 3,
                'supplier_name' => '新鲜水产供应商',
                'order_date' => '2024-12-02',
                'order_amount' => 1800.00,
                'ingredient_id' => 6,
                'ingredient_name' => '鲫鱼',
                'unit' => '斤',
                'quantity' => 25.00,
                'unit_price' => 18.00,
                'purpose' => '午餐'
            ],
            [
                'id' => 3,
                'order_number' => 'PO20241202001',
                'supplier_id' => 3,
                'supplier_name' => '新鲜水产供应商',
                'order_date' => '2024-12-02',
                'order_amount' => 1800.00,
                'ingredient_id' => 7,
                'ingredient_name' => '带鱼',
                'unit' => '斤',
                'quantity' => 20.00,
                'unit_price' => 22.00,
                'purpose' => '晚餐'
            ],

            // 采购单4 - 粮油批发商
            [
                'id' => 4,
                'order_number' => 'PO20241202002',
                'supplier_id' => 4,
                'supplier_name' => '粮油批发商',
                'order_date' => '2024-12-02',
                'order_amount' => 950.00,
                'ingredient_id' => 8,
                'ingredient_name' => '大米',
                'unit' => '袋',
                'quantity' => 10.00,
                'unit_price' => 45.00,
                'purpose' => '全天'
            ],
            [
                'id' => 4,
                'order_number' => 'PO20241202002',
                'supplier_id' => 4,
                'supplier_name' => '粮油批发商',
                'order_date' => '2024-12-02',
                'order_amount' => 950.00,
                'ingredient_id' => 9,
                'ingredient_name' => '食用油',
                'unit' => '桶',
                'quantity' => 5.00,
                'unit_price' => 85.00,
                'purpose' => '全天'
            ]
        ];
    }

    /**
     * 创建新的采购单
     */
    private function createPurchaseOrder($orderData)
    {
        $orderInfo = $orderData['order_info'];
        $items = $orderData['items'];

        try {
            $this->db->beginTransaction();

            // 插入采购单主表
            $purchaseOrderData = [
                'order_number' => $orderInfo['order_number'],
                'supplier_id' => intval($orderInfo['supplier_id']),
                'order_date' => $orderInfo['order_date'],
                'order_amount' => floatval($orderInfo['order_amount']),
                'actual_amount' => floatval($orderInfo['order_amount']),
                'status' => 2, // 直接设为已确认状态
                'notes' => $orderInfo['notes'] ?? '',
                'created_by' => 1, // 默认操作员ID
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $orderId = $this->db->insert('purchase_orders', $purchaseOrderData);

            // 插入采购单商品明细
            foreach ($items as $item) {
                $itemData = [
                    'order_id' => $orderId,
                    'ingredient_id' => intval($item['ingredient_id']),
                    'quantity' => floatval($item['quantity']),
                    'unit_price' => floatval($item['unit_price']),
                    'total_price' => floatval($item['quantity']) * floatval($item['unit_price']),
                    'notes' => $item['purpose'] ?? '',
                    'created_at' => date('Y-m-d H:i:s')
                ];

                $this->db->insert('purchase_order_items', $itemData);
            }

            $this->db->commit();
            return $orderId;

        } catch (Exception $e) {
            $this->db->rollback();
            throw new Exception('创建采购单失败: ' . $e->getMessage());
        }
    }
}
?>
