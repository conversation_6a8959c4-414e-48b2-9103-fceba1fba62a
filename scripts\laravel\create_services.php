<?php

/**
 * 学校食堂食材出入库管理系统 - 服务类创建脚本
 * 根据开发文档自动生成Laravel服务类文件
 */

// 食材服务类
$ingredientService = '<?php

namespace App\Services;

use App\Models\Ingredient;
use App\Models\Inventory;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use App\Imports\IngredientImport;

class IngredientService
{
    /**
     * 创建食材
     */
    public function create(array $data): Ingredient
    {
        return DB::transaction(function () use ($data) {
            // 设置创建人
            $data[\'created_by\'] = auth()->id();
            
            // 创建食材
            $ingredient = Ingredient::create($data);
            
            // 初始化库存记录
            Inventory::create([
                \'ingredient_id\' => $ingredient->id,
                \'current_quantity\' => 0,
                \'total_value\' => 0
            ]);
            
            return $ingredient->load([\'category\', \'creator\', \'inventory\']);
        });
    }

    /**
     * 更新食材
     */
    public function update(Ingredient $ingredient, array $data): Ingredient
    {
        $ingredient->update($data);
        return $ingredient->load([\'category\', \'creator\', \'inventory\']);
    }

    /**
     * 删除食材
     */
    public function delete(Ingredient $ingredient): bool
    {
        return DB::transaction(function () use ($ingredient) {
            // 检查是否有库存
            if ($ingredient->getCurrentStock() > 0) {
                throw new \Exception(\'该食材还有库存，无法删除\');
            }
            
            // 检查是否有未完成的入库记录
            if ($ingredient->inboundRecords()->where(\'status\', 1)->exists()) {
                throw new \Exception(\'该食材还有未完成的入库记录，无法删除\');
            }
            
            // 删除库存记录
            $ingredient->inventory()->delete();
            
            // 删除食材
            return $ingredient->delete();
        });
    }

    /**
     * 上传食材图片
     */
    public function uploadImage(Ingredient $ingredient, UploadedFile $image): string
    {
        // 删除旧图片
        if ($ingredient->image_path) {
            Storage::disk(\'public\')->delete($ingredient->image_path);
        }
        
        // 上传新图片
        $path = $image->store(\'images/ingredients\', \'public\');
        
        // 更新食材记录
        $ingredient->update([\'image_path\' => $path]);
        
        return $path;
    }

    /**
     * 获取低库存食材
     */
    public function getLowStockIngredients()
    {
        return Ingredient::with([\'category\', \'inventory\'])
            ->active()
            ->whereHas(\'inventory\', function ($query) {
                $query->whereRaw(\'current_quantity <= min_stock\');
            })
            ->get();
    }

    /**
     * 从文件导入食材
     */
    public function importFromFile(UploadedFile $file): array
    {
        try {
            $import = new IngredientImport();
            Excel::import($import, $file);
            
            return [
                \'total\' => $import->getRowCount(),
                \'success\' => $import->getSuccessCount(),
                \'failed\' => $import->getFailedCount(),
                \'errors\' => $import->getErrors()
            ];
        } catch (\Exception $e) {
            throw new \Exception(\'文件导入失败：\' . $e->getMessage());
        }
    }

    /**
     * 检查食材名称是否重复
     */
    public function checkNameExists(string $name, ?int $excludeId = null): bool
    {
        $query = Ingredient::where(\'name\', $name);
        
        if ($excludeId) {
            $query->where(\'id\', \'!=\', $excludeId);
        }
        
        return $query->exists();
    }

    /**
     * 获取食材统计信息
     */
    public function getStatistics(): array
    {
        return [
            \'total_ingredients\' => Ingredient::count(),
            \'active_ingredients\' => Ingredient::active()->count(),
            \'low_stock_count\' => $this->getLowStockIngredients()->count(),
            \'total_categories\' => Ingredient::distinct(\'category_id\')->count(),
            \'total_value\' => Inventory::sum(\'total_value\')
        ];
    }
}';

// 入库服务类
$inboundService = '<?php

namespace App\Services;

use App\Models\InboundRecord;
use App\Models\Ingredient;
use App\Models\Inventory;
use App\Models\InventoryBatch;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class InboundService
{
    /**
     * 创建入库记录
     */
    public function create(array $data): InboundRecord
    {
        return DB::transaction(function () use ($data) {
            // 获取食材信息
            $ingredient = Ingredient::findOrFail($data[\'ingredient_id\']);
            
            // 生成批次号
            $data[\'batch_number\'] = InboundRecord::generateBatchNumber($ingredient->id);
            
            // 计算过期日期
            if (!isset($data[\'expired_at\']) && isset($data[\'production_date\'])) {
                $data[\'expired_at\'] = Carbon::parse($data[\'production_date\'])
                    ->addDays($ingredient->shelf_life_days);
            }
            
            // 设置创建人
            $data[\'created_by\'] = auth()->id();
            
            // 创建入库记录
            $record = InboundRecord::create($data);
            
            // 更新库存
            $this->updateInventory($record);
            
            // 创建库存批次
            $this->createInventoryBatch($record);
            
            return $record->load([\'ingredient\', \'supplier\', \'creator\']);
        });
    }

    /**
     * 审核入库记录
     */
    public function approve(InboundRecord $record, bool $approved, ?string $notes = null): InboundRecord
    {
        return DB::transaction(function () use ($record, $approved, $notes) {
            // 更新审核信息
            $record->update([
                \'approved_by\' => auth()->id(),
                \'approved_at\' => now(),
                \'status\' => $approved ? 1 : 0,
                \'notes\' => $notes ? $record->notes . \'\n审核备注：\' . $notes : $record->notes
            ]);
            
            // 如果审核不通过，需要回滚库存
            if (!$approved) {
                $this->rollbackInventory($record);
            }
            
            return $record->load([\'ingredient\', \'supplier\', \'creator\', \'approver\']);
        });
    }

    /**
     * 上传称重图片
     */
    public function uploadWeightImages(InboundRecord $record, array $images): array
    {
        $imagePaths = [];
        
        foreach ($images as $image) {
            $path = $image->store(\'images/weight\', \'public\');
            $imagePaths[] = $path;
        }
        
        // 更新记录
        $record->update([\'weight_images\' => $imagePaths]);
        
        return $imagePaths;
    }

    /**
     * 上传采购单据
     */
    public function uploadInvoice(InboundRecord $record, UploadedFile $invoice): string
    {
        // 删除旧文件
        if ($record->purchase_invoice) {
            Storage::disk(\'public\')->delete($record->purchase_invoice);
        }
        
        // 上传新文件
        $path = $invoice->store(\'invoices\', \'public\');
        
        // 更新记录
        $record->update([\'purchase_invoice\' => $path]);
        
        return $path;
    }

    /**
     * 更新库存
     */
    protected function updateInventory(InboundRecord $record): void
    {
        $inventory = Inventory::firstOrCreate(
            [\'ingredient_id\' => $record->ingredient_id],
            [\'current_quantity\' => 0, \'total_value\' => 0]
        );
        
        $inventory->increment(\'current_quantity\', $record->quantity);
        $inventory->increment(\'total_value\', $record->getTotalPriceAttribute());
        $inventory->update([\'last_inbound_at\' => now()]);
    }

    /**
     * 创建库存批次
     */
    protected function createInventoryBatch(InboundRecord $record): InventoryBatch
    {
        return InventoryBatch::create([
            \'ingredient_id\' => $record->ingredient_id,
            \'batch_number\' => $record->batch_number,
            \'remaining_quantity\' => $record->quantity,
            \'unit_price\' => $record->unit_price,
            \'expired_at\' => $record->expired_at,
            \'status\' => $this->getBatchStatus($record->expired_at)
        ]);
    }

    /**
     * 回滚库存
     */
    protected function rollbackInventory(InboundRecord $record): void
    {
        $inventory = Inventory::where(\'ingredient_id\', $record->ingredient_id)->first();
        
        if ($inventory) {
            $inventory->decrement(\'current_quantity\', $record->quantity);
            $inventory->decrement(\'total_value\', $record->getTotalPriceAttribute());
        }
        
        // 删除对应的库存批次
        InventoryBatch::where(\'batch_number\', $record->batch_number)->delete();
    }

    /**
     * 获取批次状态
     */
    protected function getBatchStatus(string $expiredAt): int
    {
        $expiredDate = Carbon::parse($expiredAt);
        $now = Carbon::now();
        $daysToExpire = $now->diffInDays($expiredDate, false);
        
        if ($daysToExpire < 0) {
            return 3; // 已过期
        } elseif ($daysToExpire <= 3) {
            return 2; // 即将过期
        } else {
            return 1; // 正常
        }
    }

    /**
     * 获取入库统计
     */
    public function getStatistics(string $startDate, string $endDate): array
    {
        $query = InboundRecord::active()
            ->whereBetween(\'created_at\', [$startDate, $endDate]);
        
        return [
            \'total_records\' => $query->count(),
            \'total_quantity\' => $query->sum(\'quantity\'),
            \'total_value\' => $query->sum(DB::raw(\'quantity * unit_price\')),
            \'approved_records\' => $query->approved()->count(),
            \'pending_records\' => $query->whereNull(\'approved_by\')->count()
        ];
    }

    /**
     * 批量导入入库记录
     */
    public function importFromFile(UploadedFile $file): array
    {
        // 实现批量导入逻辑
        // 这里可以使用 Laravel Excel 包来处理
        throw new \Exception(\'批量导入功能待实现\');
    }
}';

// 创建文件
if (!is_dir('app/Services')) {
    mkdir('app/Services', 0755, true);
}

file_put_contents('app/Services/IngredientService.php', $ingredientService);
file_put_contents('app/Services/InboundService.php', $inboundService);

echo "✅ 服务类文件创建完成！\n";
echo "已创建以下服务类：\n";
echo "- IngredientService (食材服务)\n";
echo "- InboundService (入库服务)\n";
echo "\n继续创建其他服务类...\n";
