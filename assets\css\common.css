/* 通用样式增强 */

/* 动画库 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 通用动画类 */
.fade-in {
    animation: fadeIn 0.3s ease-out;
}

.slide-in-up {
    animation: slideInUp 0.3s ease-out;
}

.slide-in-down {
    animation: slideInDown 0.3s ease-out;
}

.slide-in-right {
    animation: slideInRight 0.3s ease-out;
}

.pulse {
    animation: pulse 2s infinite;
}

/* 加载状态 */
.loading {
    position: relative;
    pointer-events: none;
    opacity: 0.6;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #4299e1;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 1000;
}

/* 通知组件 */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    min-width: 300px;
    max-width: 500px;
    animation: slideInRight 0.3s ease-out;
}

.notification.fade-out {
    animation: slideInRight 0.3s ease-in reverse;
}

/* 模态框增强 */
.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    animation: fadeIn 0.3s ease-out;
}

.modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    z-index: 10000;
    animation: slideInUp 0.3s ease-out;
    max-width: 90vw;
    max-height: 90vh;
    overflow: auto;
}

/* 工具提示增强 */
.tooltip {
    position: absolute;
    background: #2d3748;
    color: white;
    padding: 6px 10px;
    border-radius: 6px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 10000;
    opacity: 0;
    transition: opacity 0.2s ease;
    pointer-events: none;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.tooltip.show {
    opacity: 1;
}

.tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: #2d3748;
}

/* 分页组件 */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 5px;
    margin: 20px 0;
}

.page-item {
    list-style: none;
}

.page-link {
    display: block;
    padding: 8px 12px;
    color: #4299e1;
    text-decoration: none;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.page-link:hover {
    background: #f7fafc;
    border-color: #4299e1;
}

.page-item.active .page-link {
    background: #4299e1;
    color: white;
    border-color: #4299e1;
}

.page-item.disabled .page-link {
    color: #a0aec0;
    pointer-events: none;
    background: #f7fafc;
}

/* 数据表格增强 */
.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

.table-hover tbody tr:hover {
    background-color: rgba(66, 153, 225, 0.05);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

/* 表单增强 */
.form-floating {
    position: relative;
}

.form-floating .form-control {
    padding: 1rem 0.75rem 0.5rem;
}

.form-floating label {
    position: absolute;
    top: 0;
    left: 0.75rem;
    height: 100%;
    padding: 1rem 0;
    pointer-events: none;
    border: 1px solid transparent;
    transform-origin: 0 0;
    transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out;
    color: #6c757d;
}

.form-floating .form-control:focus ~ label,
.form-floating .form-control:not(:placeholder-shown) ~ label {
    opacity: 0.65;
    transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}

/* 状态指示器 */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 6px;
}

.status-indicator.success {
    background: #38a169;
}

.status-indicator.warning {
    background: #d69e2e;
}

.status-indicator.danger {
    background: #e53e3e;
}

.status-indicator.info {
    background: #4299e1;
}

/* 进度条增强 */
.progress {
    position: relative;
    overflow: visible;
}

.progress-bar {
    transition: width 0.6s ease;
    position: relative;
}

.progress-bar::after {
    content: attr(data-label);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 11px;
    font-weight: 600;
    color: white;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

/* 卡片增强 */
.card-hover {
    transition: all 0.3s ease;
}

.card-hover:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* 按钮增强 */
.btn {
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-loading {
    pointer-events: none;
    opacity: 0.6;
}

.btn-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* 响应式工具类 */
.d-none { display: none !important; }
.d-block { display: block !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }
.d-flex { display: flex !important; }

@media (max-width: 768px) {
    .d-md-none { display: none !important; }
    .d-md-block { display: block !important; }
}

@media (max-width: 576px) {
    .d-sm-none { display: none !important; }
    .d-sm-block { display: block !important; }
}

/* 文本工具类 */
.text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.text-break {
    word-wrap: break-word !important;
    word-break: break-word !important;
}

/* 间距工具类 */
.m-0 { margin: 0 !important; }
.mt-0 { margin-top: 0 !important; }
.mb-0 { margin-bottom: 0 !important; }
.ml-0 { margin-left: 0 !important; }
.mr-0 { margin-right: 0 !important; }

.p-0 { padding: 0 !important; }
.pt-0 { padding-top: 0 !important; }
.pb-0 { padding-bottom: 0 !important; }
.pl-0 { padding-left: 0 !important; }
.pr-0 { padding-right: 0 !important; }

/* 颜色工具类 */
.text-primary { color: #4299e1 !important; }
.text-success { color: #38a169 !important; }
.text-warning { color: #d69e2e !important; }
.text-danger { color: #e53e3e !important; }
.text-info { color: #4299e1 !important; }
.text-muted { color: #718096 !important; }

.bg-primary { background-color: #4299e1 !important; }
.bg-success { background-color: #38a169 !important; }
.bg-warning { background-color: #d69e2e !important; }
.bg-danger { background-color: #e53e3e !important; }
.bg-info { background-color: #4299e1 !important; }
.bg-light { background-color: #f7fafc !important; }
