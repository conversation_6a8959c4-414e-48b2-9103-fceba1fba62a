/* 趋势分析样式 */

/* 时间范围选择器 */
.time-range-selector select {
    min-width: 120px;
    font-size: 14px;
}

/* 指标标签导航 */
.metric-tabs {
    display: flex;
    background: white;
    border-radius: 8px;
    padding: 8px;
    margin-bottom: 25px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    gap: 4px;
}

.metric-tab {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    padding: 15px 20px;
    border-radius: 6px;
    text-decoration: none;
    color: #6b7280;
    font-weight: 500;
    transition: all 0.2s ease;
    flex: 1;
    text-align: center;
}

.metric-tab:hover {
    background: #f3f4f6;
    color: #374151;
    text-decoration: none;
}

.metric-tab.active {
    background: #3b82f6;
    color: white;
}

.metric-tab i {
    font-size: 20px;
}

.metric-tab span {
    font-size: 14px;
}

/* 趋势图表 */
.trend-chart-container {
    background: white;
    border-radius: 8px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.trend-chart-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e5e7eb;
}

.trend-chart-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
}

.trend-chart-controls {
    display: flex;
    gap: 10px;
}

.chart-toggle {
    padding: 6px 12px;
    border: 1px solid #d1d5db;
    background: white;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.chart-toggle.active {
    background: #3b82f6;
    color: white;
    border-color: #3b82f6;
}

.trend-chart {
    height: 400px;
    position: relative;
}

/* 洞察卡片 */
.insights-section {
    margin-bottom: 30px;
}

.insight-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
    height: 100%;
}

.insight-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 25px;
}

.insight-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.insight-content {
    padding: 25px;
}

/* 预测项目 */
.prediction-item {
    text-align: center;
    margin-bottom: 25px;
}

.prediction-label {
    font-size: 14px;
    color: #6b7280;
    margin-bottom: 8px;
}

.prediction-value {
    font-size: 32px;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 8px;
}

.prediction-change {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    font-size: 14px;
    font-weight: 600;
}

.prediction-change.positive {
    color: #10b981;
}

.prediction-change.negative {
    color: #ef4444;
}

/* 置信度计量器 */
.confidence-meter {
    margin-bottom: 25px;
}

.confidence-label {
    font-size: 14px;
    color: #6b7280;
    margin-bottom: 8px;
}

.confidence-bar {
    width: 100%;
    height: 12px;
    background: #e5e7eb;
    border-radius: 6px;
    overflow: hidden;
    margin-bottom: 8px;
}

.confidence-fill {
    height: 100%;
    background: linear-gradient(90deg, #10b981, #059669);
    border-radius: 6px;
    transition: width 0.3s ease;
}

.confidence-value {
    text-align: center;
    font-size: 14px;
    font-weight: 600;
    color: #374151;
}

/* 风险因素 */
.risk-factors h5 {
    font-size: 14px;
    color: #374151;
    margin-bottom: 10px;
}

.risk-factors ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.risk-factors li {
    font-size: 13px;
    color: #6b7280;
    margin-bottom: 6px;
    padding-left: 16px;
    position: relative;
}

.risk-factors li:before {
    content: "⚠️";
    position: absolute;
    left: 0;
    top: 0;
}

/* 发现和建议列表 */
.findings-section,
.recommendations-section,
.alerts-section {
    margin-bottom: 20px;
}

.findings-section h5,
.recommendations-section h5,
.alerts-section h5 {
    font-size: 14px;
    color: #374151;
    margin-bottom: 10px;
}

.findings-list,
.recommendations-list,
.alerts-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.findings-list li,
.recommendations-list li,
.alerts-list li {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    font-size: 13px;
    color: #6b7280;
    margin-bottom: 8px;
    line-height: 1.4;
}

.findings-list i {
    color: #10b981;
    margin-top: 2px;
}

.recommendations-list i {
    color: #3b82f6;
    margin-top: 2px;
}

.alerts-list i {
    color: #f59e0b;
    margin-top: 2px;
}

/* 对比分析 */
.comparison-section {
    margin-bottom: 30px;
}

.comparison-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
}

.comparison-header {
    background: #f8f9fa;
    padding: 20px 25px;
    border-bottom: 1px solid #e5e7eb;
}

.comparison-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #374151;
    display: flex;
    align-items: center;
    gap: 8px;
}

.comparison-content {
    padding: 25px;
}

.comparison-content h5 {
    font-size: 14px;
    color: #374151;
    margin-bottom: 15px;
}

.comparison-metrics {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.metric-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    background: #f9fafb;
    border-radius: 6px;
}

.metric-label {
    font-size: 14px;
    color: #6b7280;
}

.metric-value {
    font-size: 16px;
    font-weight: 600;
}

.metric-value.positive {
    color: #10b981;
}

.metric-value.negative {
    color: #ef4444;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .metric-tabs {
        flex-wrap: wrap;
        gap: 8px;
    }
    
    .metric-tab {
        flex: 1;
        min-width: 120px;
    }
    
    .trend-chart-header {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }
    
    .trend-chart-controls {
        width: 100%;
        justify-content: center;
    }
    
    .comparison-content .row {
        margin: 0;
    }
    
    .comparison-content .col-md-6 {
        padding: 0;
        margin-bottom: 20px;
    }

    .chart-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .chart-container {
        height: 250px;
    }

    .chart-card {
        padding: 15px;
    }
}

/* 图表网格布局 */
.chart-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.chart-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
}

.chart-card:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.chart-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e5e7eb;
}

.chart-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
}

.chart-title i {
    color: #6b7280;
    font-size: 14px;
}

.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}

.chart-container canvas {
    max-height: 100%;
    max-width: 100%;
}

/* 图表控制按钮 */
.chart-controls {
    display: flex;
    gap: 8px;
    margin-top: 10px;
}

.chart-control-btn {
    padding: 4px 8px;
    font-size: 12px;
    border: 1px solid #d1d5db;
    background: white;
    color: #6b7280;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.chart-control-btn:hover {
    background: #f3f4f6;
    border-color: #9ca3af;
}

.chart-control-btn.active {
    background: #3b82f6;
    color: white;
    border-color: #3b82f6;
}

/* 新的统计卡片样式 */
.analytics-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.analytics-stat-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    display: flex;
    align-items: center;
    gap: 15px;
    transition: all 0.3s ease;
}

.analytics-stat-card:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 10px;
    background: #f3f4f6;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.stat-icon i {
    font-size: 20px;
    color: #6b7280;
}

.stat-content {
    flex: 1;
}

.stat-title {
    font-size: 14px;
    color: #6b7280;
    margin-bottom: 5px;
    font-weight: 500;
}

.stat-value {
    font-size: 24px;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 5px;
    line-height: 1;
}

.stat-change {
    font-size: 12px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 4px;
}

.stat-change.positive {
    color: #10b981;
}

.stat-change.negative {
    color: #ef4444;
}

.stat-change i {
    font-size: 10px;
}

/* 响应式图表设计 */
@media (max-width: 1200px) {
    .chart-grid {
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    }

    .analytics-stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
    }
}

@media (max-width: 768px) {
    .analytics-stats-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .analytics-stat-card {
        padding: 15px;
    }

    .stat-icon {
        width: 40px;
        height: 40px;
    }

    .stat-icon i {
        font-size: 16px;
    }

    .stat-value {
        font-size: 20px;
    }
}
