<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>检查数据库结构</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .step { background: #f9f9f9; padding: 10px; margin: 10px 0; border-left: 4px solid #007cba; }
        .data-table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        .data-table th, .data-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        .data-table th { background: #f5f5f5; }
        .code-block { background: #f4f4f4; padding: 10px; border-radius: 3px; font-family: monospace; margin: 5px 0; white-space: pre-wrap; }
        .btn { display: inline-block; padding: 8px 16px; background: #007cba; color: white; text-decoration: none; border-radius: 4px; margin: 5px; }
    </style>
</head>
<body>
    <h1>🔍 检查数据库结构</h1>
    
    <?php
    require_once '../includes/Database.php';
    
    try {
        $db = Database::getInstance();
        
        echo "<div class='test-section'>";
        echo "<h2>📊 数据库表结构检查</h2>";
        
        echo "<div class='step'>";
        echo "<h4>1. 检查purchase_orders表：</h4>";
        
        try {
            $tables = $db->fetchAll("SHOW TABLES LIKE 'purchase_orders'");
            if (empty($tables)) {
                echo "<p class='error'>❌ purchase_orders表不存在</p>";
            } else {
                echo "<p class='success'>✅ purchase_orders表存在</p>";
                
                $columns = $db->fetchAll("DESCRIBE purchase_orders");
                echo "<table class='data-table'>";
                echo "<tr><th>字段名</th><th>类型</th><th>是否为空</th><th>键</th><th>默认值</th></tr>";
                foreach ($columns as $column) {
                    echo "<tr>";
                    echo "<td>{$column['Field']}</td>";
                    echo "<td>{$column['Type']}</td>";
                    echo "<td>{$column['Null']}</td>";
                    echo "<td>{$column['Key']}</td>";
                    echo "<td>{$column['Default']}</td>";
                    echo "</tr>";
                }
                echo "</table>";
                
                $count = $db->fetchOne("SELECT COUNT(*) as count FROM purchase_orders")['count'];
                echo "<p class='info'>表中记录数: {$count}</p>";
            }
        } catch (Exception $e) {
            echo "<p class='error'>❌ 检查purchase_orders表失败: " . $e->getMessage() . "</p>";
        }
        echo "</div>";
        
        echo "<div class='step'>";
        echo "<h4>2. 检查purchase_order_items表：</h4>";
        
        try {
            $tables = $db->fetchAll("SHOW TABLES LIKE 'purchase_order_items'");
            if (empty($tables)) {
                echo "<p class='error'>❌ purchase_order_items表不存在</p>";
                echo "<p class='warning'>这可能是明细数据为空的原因！</p>";
                
                echo "<h5>创建purchase_order_items表：</h5>";
                echo "<div class='code-block'>";
                echo "CREATE TABLE purchase_order_items (\n";
                echo "    id INT AUTO_INCREMENT PRIMARY KEY,\n";
                echo "    order_id INT NOT NULL,\n";
                echo "    ingredient_id INT NOT NULL,\n";
                echo "    quantity DECIMAL(10,2) NOT NULL,\n";
                echo "    unit_price DECIMAL(10,2) NOT NULL,\n";
                echo "    total_price DECIMAL(10,2) NOT NULL,\n";
                echo "    received_quantity DECIMAL(10,2) DEFAULT 0,\n";
                echo "    qualified_quantity DECIMAL(10,2) DEFAULT 0,\n";
                echo "    unqualified_quantity DECIMAL(10,2) DEFAULT 0,\n";
                echo "    loss_quantity DECIMAL(10,2) DEFAULT 0,\n";
                echo "    rejected_quantity DECIMAL(10,2) DEFAULT 0,\n";
                echo "    notes TEXT,\n";
                echo "    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n";
                echo "    FOREIGN KEY (order_id) REFERENCES purchase_orders(id) ON DELETE CASCADE,\n";
                echo "    FOREIGN KEY (ingredient_id) REFERENCES ingredients(id)\n";
                echo ");\n";
                echo "</div>";
                
                // 尝试创建表
                try {
                    $createSql = "
                    CREATE TABLE purchase_order_items (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        order_id INT NOT NULL,
                        ingredient_id INT NOT NULL,
                        quantity DECIMAL(10,2) NOT NULL,
                        unit_price DECIMAL(10,2) NOT NULL,
                        total_price DECIMAL(10,2) NOT NULL,
                        received_quantity DECIMAL(10,2) DEFAULT 0,
                        qualified_quantity DECIMAL(10,2) DEFAULT 0,
                        unqualified_quantity DECIMAL(10,2) DEFAULT 0,
                        loss_quantity DECIMAL(10,2) DEFAULT 0,
                        rejected_quantity DECIMAL(10,2) DEFAULT 0,
                        notes TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )";
                    
                    $db->query($createSql);
                    echo "<p class='success'>✅ purchase_order_items表创建成功</p>";
                } catch (Exception $e) {
                    echo "<p class='error'>❌ 创建purchase_order_items表失败: " . $e->getMessage() . "</p>";
                }
                
            } else {
                echo "<p class='success'>✅ purchase_order_items表存在</p>";
                
                $columns = $db->fetchAll("DESCRIBE purchase_order_items");
                echo "<table class='data-table'>";
                echo "<tr><th>字段名</th><th>类型</th><th>是否为空</th><th>键</th><th>默认值</th></tr>";
                foreach ($columns as $column) {
                    echo "<tr>";
                    echo "<td>{$column['Field']}</td>";
                    echo "<td>{$column['Type']}</td>";
                    echo "<td>{$column['Null']}</td>";
                    echo "<td>{$column['Key']}</td>";
                    echo "<td>{$column['Default']}</td>";
                    echo "</tr>";
                }
                echo "</table>";
                
                $count = $db->fetchOne("SELECT COUNT(*) as count FROM purchase_order_items")['count'];
                echo "<p class='info'>表中记录数: {$count}</p>";
                
                if ($count > 0) {
                    echo "<h5>最近的明细记录：</h5>";
                    $recentItems = $db->fetchAll("SELECT * FROM purchase_order_items ORDER BY created_at DESC LIMIT 5");
                    echo "<table class='data-table'>";
                    echo "<tr><th>ID</th><th>订单ID</th><th>食材ID</th><th>数量</th><th>单价</th><th>总价</th><th>创建时间</th></tr>";
                    foreach ($recentItems as $item) {
                        echo "<tr>";
                        echo "<td>{$item['id']}</td>";
                        echo "<td>{$item['order_id']}</td>";
                        echo "<td>{$item['ingredient_id']}</td>";
                        echo "<td>{$item['quantity']}</td>";
                        echo "<td>{$item['unit_price']}</td>";
                        echo "<td>{$item['total_price']}</td>";
                        echo "<td>{$item['created_at']}</td>";
                        echo "</tr>";
                    }
                    echo "</table>";
                }
            }
        } catch (Exception $e) {
            echo "<p class='error'>❌ 检查purchase_order_items表失败: " . $e->getMessage() . "</p>";
        }
        echo "</div>";
        
        echo "<div class='step'>";
        echo "<h4>3. 检查ingredients表：</h4>";
        
        try {
            $tables = $db->fetchAll("SHOW TABLES LIKE 'ingredients'");
            if (empty($tables)) {
                echo "<p class='error'>❌ ingredients表不存在</p>";
            } else {
                echo "<p class='success'>✅ ingredients表存在</p>";
                
                $count = $db->fetchOne("SELECT COUNT(*) as count FROM ingredients")['count'];
                echo "<p class='info'>表中记录数: {$count}</p>";
                
                if ($count > 0) {
                    echo "<h5>前5条食材记录：</h5>";
                    $ingredients = $db->fetchAll("SELECT id, code, name, unit FROM ingredients LIMIT 5");
                    echo "<table class='data-table'>";
                    echo "<tr><th>ID</th><th>编码</th><th>名称</th><th>单位</th></tr>";
                    foreach ($ingredients as $ingredient) {
                        echo "<tr>";
                        echo "<td>{$ingredient['id']}</td>";
                        echo "<td>{$ingredient['code']}</td>";
                        echo "<td>{$ingredient['name']}</td>";
                        echo "<td>{$ingredient['unit']}</td>";
                        echo "</tr>";
                    }
                    echo "</table>";
                }
            }
        } catch (Exception $e) {
            echo "<p class='error'>❌ 检查ingredients表失败: " . $e->getMessage() . "</p>";
        }
        echo "</div>";
        
        echo "<div class='step'>";
        echo "<h4>4. 检查suppliers表：</h4>";
        
        try {
            $tables = $db->fetchAll("SHOW TABLES LIKE 'suppliers'");
            if (empty($tables)) {
                echo "<p class='error'>❌ suppliers表不存在</p>";
            } else {
                echo "<p class='success'>✅ suppliers表存在</p>";
                
                $count = $db->fetchOne("SELECT COUNT(*) as count FROM suppliers")['count'];
                echo "<p class='info'>表中记录数: {$count}</p>";
                
                if ($count === 0) {
                    echo "<p class='warning'>⚠️ suppliers表为空，可能影响导入</p>";
                    
                    // 创建默认供应商
                    try {
                        $db->insert('suppliers', [
                            'name' => '默认供应商',
                            'contact_person' => '联系人',
                            'phone' => '13800138000',
                            'address' => '默认地址',
                            'status' => 1,
                            'created_at' => date('Y-m-d H:i:s')
                        ]);
                        echo "<p class='success'>✅ 创建默认供应商成功</p>";
                    } catch (Exception $e) {
                        echo "<p class='error'>❌ 创建默认供应商失败: " . $e->getMessage() . "</p>";
                    }
                }
            }
        } catch (Exception $e) {
            echo "<p class='error'>❌ 检查suppliers表失败: " . $e->getMessage() . "</p>";
        }
        echo "</div>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ 数据库连接失败: " . $e->getMessage() . "</p>";
    }
    ?>
    
    <div class="test-section">
        <h2>🎯 下一步操作</h2>
        <div class="step">
            <p><strong>基于检查结果：</strong></p>
            <ol>
                <li>如果purchase_order_items表不存在，这就是明细数据为空的原因</li>
                <li>如果表存在但字段不匹配，需要调整表结构</li>
                <li>如果表和字段都正确，需要检查插入逻辑</li>
            </ol>
            
            <p>
                <a href="simulate-full-import.php" class="btn">🔄 重新模拟导入</a>
                <a href="../modules/purchase/index.php?action=import" class="btn">📤 重新测试导入</a>
            </p>
        </div>
    </div>
    
    <p><a href="../modules/purchase/index.php">返回采购管理</a></p>
</body>
</html>
