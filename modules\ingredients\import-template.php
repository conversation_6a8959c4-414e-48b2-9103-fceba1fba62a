<?php
/**
 * 食材导入页面模板
 */

// 引入通用头部
require_once '../../includes/header.php';
?>

<link rel="stylesheet" href="style.css">

<div class="main-content">
    <?php require_once 'sidebar.php'; ?>

    <!-- 页面标题栏 -->
    <div class="topbar">
        <div class="topbar-left">
            <h1><i class="fas fa-upload"></i> 批量导入食材</h1>
        </div>
        <div class="topbar-right">
            <div class="btn-group">
                <a href="index.php" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> 返回列表
                </a>
            </div>
        </div>
    </div>

    <div class="content">
        <!-- 错误信息 -->
        <?php if (isset($error_message)): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i>
                <?= htmlspecialchars($error_message) ?>
            </div>
        <?php endif; ?>

        <!-- 上传区域 -->
        <div class="upload-section">
            <div class="section-header">
                <h3><i class="fas fa-cloud-upload-alt"></i> 选择导入文件</h3>
            </div>

            <div class="upload-area" onclick="document.getElementById('importFile').click()">
                <div class="upload-icon">
                    <i class="fas fa-file-csv"></i>
                </div>
                <div class="upload-text">
                    <h4>点击此处选择文件或拖拽文件到此区域</h4>
                    <p>支持 CSV、Excel 格式，文件大小不超过 5MB</p>
                </div>
                
                <form id="importForm" method="POST" enctype="multipart/form-data" style="display: inline-block;">
                    <input type="file" id="importFile" name="import_file" 
                           accept=".csv,.xlsx,.xls" required style="display: none;">
                    <button type="button" class="btn btn-primary" onclick="document.getElementById('importFile').click(); event.stopPropagation();">
                        <i class="fas fa-folder-open"></i> 选择文件
                    </button>
                    <a href="download_template.php" class="btn btn-success">
                        <i class="fas fa-download"></i> 下载模板
                    </a>
                </form>
            </div>

            <!-- 文件信息显示 -->
            <div id="fileInfo" class="file-info" style="display: none;">
                <div class="file-details">
                    <div class="file-icon">
                        <i class="fas fa-file-check"></i>
                    </div>
                    <div class="file-meta">
                        <div class="file-name" id="fileName"></div>
                        <div class="file-size" id="fileSize"></div>
                    </div>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="clearFile()">
                        <i class="fas fa-times"></i> 清除
                    </button>
                </div>
            </div>

            <!-- 导入选项 -->
            <div class="import-options">
                <h4><i class="fas fa-cog"></i> 导入选项</h4>
                <div class="options-grid">
                    <label class="checkbox-label">
                        <input type="checkbox" id="skipDuplicates" name="skip_duplicates" checked>
                        <span class="checkmark"></span>
                        跳过重复的食材名称
                    </label>
                    <label class="checkbox-label">
                        <input type="checkbox" id="updateExisting" name="update_existing">
                        <span class="checkmark"></span>
                        更新已存在的食材信息
                    </label>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="form-actions">
                <button type="button" id="startImport" class="btn btn-primary" disabled>
                    <i class="fas fa-upload"></i> 开始导入
                </button>
                <a href="index.php" class="btn btn-outline-secondary">
                    <i class="fas fa-times"></i> 取消
                </a>
            </div>

            <!-- 导入进度 -->
            <div id="importProgress" class="import-progress" style="display: none;">
                <div class="progress-header">
                    <h4><i class="fas fa-spinner fa-spin"></i> 正在导入...</h4>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="progress-text" id="progressText">准备中...</div>
            </div>

            <!-- 导入结果 -->
            <div id="importResult" class="import-result" style="display: none;">
                <div class="result-header">
                    <h4 id="resultTitle"></h4>
                </div>
                <div class="result-stats">
                    <div class="stat-item">
                        <span class="stat-label">总计:</span>
                        <span class="stat-value" id="totalCount">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">成功:</span>
                        <span class="stat-value success" id="successCount">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">跳过:</span>
                        <span class="stat-value warning" id="skipCount">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">错误:</span>
                        <span class="stat-value error" id="errorCount">0</span>
                    </div>
                </div>
                <div id="errorDetails" class="error-details" style="display: none;">
                    <h5>错误详情:</h5>
                    <ul id="errorList"></ul>
                </div>
                <div class="result-actions">
                    <a href="index.php" class="btn btn-primary">
                        <i class="fas fa-list"></i> 查看食材列表
                    </a>
                    <button type="button" class="btn btn-outline-secondary" onclick="resetImport()">
                        <i class="fas fa-redo"></i> 重新导入
                    </button>
                </div>
            </div>
        </div>

        <!-- 说明和示例 -->
        <div class="info-section">
            <!-- 格式说明 -->
            <div class="info-card">
                <div class="card-header">
                    <h4><i class="fas fa-info-circle"></i> 文件格式要求</h4>
                </div>
                <div class="card-content">
                    <p>CSV文件应包含以下列（按顺序）：</p>
                    <div class="format-list">
                        <div class="format-item required">食材名称 <span class="required-mark">*</span></div>
                        <div class="format-item">分类ID</div>
                        <div class="format-item required">单位 <span class="required-mark">*</span></div>
                        <div class="format-item">单价</div>
                        <div class="format-item">最低库存</div>
                    </div>
                    <div class="format-notes">
                        <p><strong>注意事项：</strong></p>
                        <ul>
                            <li>食材名称和单位为必填项</li>
                            <li>分类ID可以留空，系统会分配到默认分类</li>
                            <li>单价和最低库存可以为0</li>
                            <li>第一行为标题行，导入时会自动跳过</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 示例数据 -->
            <div class="info-card">
                <div class="card-header">
                    <h4><i class="fas fa-table"></i> 示例数据</h4>
                </div>
                <div class="card-content">
                    <div class="example-table">
                        <table>
                            <thead>
                                <tr>
                                    <th>食材名称</th>
                                    <th>分类ID</th>
                                    <th>单位</th>
                                    <th>单价</th>
                                    <th>最低库存</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>白菜</td>
                                    <td>1</td>
                                    <td>斤</td>
                                    <td>2.50</td>
                                    <td>10</td>
                                </tr>
                                <tr>
                                    <td>土豆</td>
                                    <td>1</td>
                                    <td>斤</td>
                                    <td>3.00</td>
                                    <td>20</td>
                                </tr>
                                <tr>
                                    <td>猪肉</td>
                                    <td>2</td>
                                    <td>斤</td>
                                    <td>25.00</td>
                                    <td>5</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="../../assets/js/common.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const fileInput = document.getElementById('importFile');
    const fileInfo = document.getElementById('fileInfo');
    const startImportBtn = document.getElementById('startImport');
    const importForm = document.getElementById('importForm');
    
    // 文件选择事件
    fileInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            showFileInfo(file);
            startImportBtn.disabled = false;
        } else {
            hideFileInfo();
            startImportBtn.disabled = true;
        }
    });
    
    // 开始导入
    startImportBtn.addEventListener('click', function() {
        if (!fileInput.files.length) {
            alert('请先选择要导入的文件');
            return;
        }
        
        startImport();
    });
    
    // 拖拽上传
    const uploadArea = document.querySelector('.upload-area');
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });
    
    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
    });
    
    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            fileInput.files = files;
            const event = new Event('change', { bubbles: true });
            fileInput.dispatchEvent(event);
        }
    });
    
    function showFileInfo(file) {
        document.getElementById('fileName').textContent = file.name;
        document.getElementById('fileSize').textContent = formatFileSize(file.size);
        fileInfo.style.display = 'block';
    }
    
    function hideFileInfo() {
        fileInfo.style.display = 'none';
    }
    
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    function startImport() {
        // 显示进度
        document.getElementById('importProgress').style.display = 'block';
        document.getElementById('importResult').style.display = 'none';
        startImportBtn.disabled = true;
        
        // 准备表单数据
        const formData = new FormData();
        formData.append('import_file', fileInput.files[0]);
        formData.append('skip_duplicates', document.getElementById('skipDuplicates').checked ? '1' : '0');
        formData.append('update_existing', document.getElementById('updateExisting').checked ? '1' : '0');
        
        // 发送请求
        fetch('index.php?action=import', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            showImportResult(data);
        })
        .catch(error => {
            console.error('导入失败:', error);
            showImportResult({
                success: false,
                message: '导入失败: ' + error.message
            });
        });
    }
    
    function showImportResult(result) {
        document.getElementById('importProgress').style.display = 'none';
        document.getElementById('importResult').style.display = 'block';
        
        if (result.success) {
            document.getElementById('resultTitle').innerHTML = '<i class="fas fa-check-circle"></i> 导入完成';
            document.getElementById('totalCount').textContent = result.total || 0;
            document.getElementById('successCount').textContent = result.imported || 0;
            document.getElementById('skipCount').textContent = result.skipped || 0;
            document.getElementById('errorCount').textContent = result.errors ? result.errors.length : 0;
            
            if (result.errors && result.errors.length > 0) {
                const errorList = document.getElementById('errorList');
                errorList.innerHTML = '';
                result.errors.forEach(error => {
                    const li = document.createElement('li');
                    li.textContent = error;
                    errorList.appendChild(li);
                });
                document.getElementById('errorDetails').style.display = 'block';
            }
        } else {
            document.getElementById('resultTitle').innerHTML = '<i class="fas fa-exclamation-triangle"></i> 导入失败';
            document.getElementById('errorDetails').style.display = 'block';
            document.getElementById('errorList').innerHTML = '<li>' + (result.message || '未知错误') + '</li>';
        }
    }
    
    window.clearFile = function() {
        fileInput.value = '';
        hideFileInfo();
        startImportBtn.disabled = true;
    };
    
    window.resetImport = function() {
        document.getElementById('importResult').style.display = 'none';
        document.getElementById('importProgress').style.display = 'none';
        clearFile();
        startImportBtn.disabled = false;
    };
});
</script>

<?php require_once '../../includes/footer.php'; ?>
