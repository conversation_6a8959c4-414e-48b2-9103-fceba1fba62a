<!-- 成本趋势分析 -->

<!-- 趋势概览 -->
<div class="analytics-stats-grid">
    <div class="analytics-stat-card">
        <div class="stat-icon">
            <i class="fas fa-dollar-sign"></i>
        </div>
        <div class="stat-content">
            <div class="stat-title">总成本支出</div>
            <div class="stat-value">¥<?= number_format($data['trends']['summary']['total_cost'] ?? 425600) ?></div>
            <div class="stat-change positive">
                <i class="fas fa-arrow-up"></i>
                +2.8%
            </div>
        </div>
    </div>

    <div class="analytics-stat-card">
        <div class="stat-icon">
            <i class="fas fa-chart-line"></i>
        </div>
        <div class="stat-content">
            <div class="stat-title">平均单价</div>
            <div class="stat-value">¥<?= number_format($data['trends']['summary']['avg_unit_cost'] ?? 28.5, 1) ?></div>
            <div class="stat-change negative">
                <i class="fas fa-arrow-down"></i>
                -1.2%
            </div>
        </div>
    </div>

    <div class="analytics-stat-card">
        <div class="stat-icon">
            <i class="fas fa-percentage"></i>
        </div>
        <div class="stat-content">
            <div class="stat-title">成本控制率</div>
            <div class="stat-value"><?= number_format($data['trends']['summary']['cost_control_rate'] ?? 92.3, 1) ?>%</div>
            <div class="stat-change positive">
                <i class="fas fa-arrow-up"></i>
                +0.8%
            </div>
        </div>
    </div>

    <div class="analytics-stat-card">
        <div class="stat-icon">
            <i class="fas fa-piggy-bank"></i>
        </div>
        <div class="stat-content">
            <div class="stat-title">节约金额</div>
            <div class="stat-value">¥<?= number_format($data['trends']['summary']['savings'] ?? 15200) ?></div>
            <div class="stat-change positive">
                <i class="fas fa-arrow-up"></i>
                +12.5%
            </div>
        </div>
    </div>
</div>

<!-- 主要趋势图表 -->
<div class="trend-chart-container">
    <div class="trend-chart-header">
        <div class="trend-chart-title">
            <i class="fas fa-chart-area"></i>
            成本变化趋势分析
        </div>
        <div class="chart-controls">
            <button class="chart-toggle active" onclick="toggleCostChart('trend')">
                <i class="fas fa-chart-line"></i>
                趋势图
            </button>
            <button class="chart-toggle" onclick="toggleCostChart('comparison')">
                <i class="fas fa-chart-bar"></i>
                对比分析
            </button>
        </div>
    </div>
    <div class="trend-chart-content">
        <canvas id="costTrendChart"></canvas>
    </div>
</div>

<!-- 图表网格 -->
<div class="chart-grid">
    <!-- 成本构成饼图 -->
    <div class="chart-card">
        <div class="chart-header">
            <div class="chart-title">
                <i class="fas fa-chart-pie"></i>
                成本构成
            </div>
        </div>
        <div class="chart-container">
            <canvas id="costCompositionChart"></canvas>
        </div>
    </div>

    <!-- 单价变化趋势 -->
    <div class="chart-card">
        <div class="chart-header">
            <div class="chart-title">
                <i class="fas fa-money-bill"></i>
                单价变化
            </div>
        </div>
        <div class="chart-container">
            <canvas id="unitPriceChart"></canvas>
        </div>
    </div>

    <!-- 成本效益分析 -->
    <div class="chart-card">
        <div class="chart-header">
            <div class="chart-title">
                <i class="fas fa-balance-scale"></i>
                成本效益
            </div>
        </div>
        <div class="chart-container">
            <canvas id="costEfficiencyChart"></canvas>
        </div>
    </div>

    <!-- 预算执行情况 -->
    <div class="chart-card">
        <div class="chart-header">
            <div class="chart-title">
                <i class="fas fa-clipboard-check"></i>
                预算执行
            </div>
        </div>
        <div class="chart-container">
            <canvas id="budgetExecutionChart"></canvas>
        </div>
    </div>

    <!-- 成本预测 -->
    <div class="chart-card">
        <div class="chart-header">
            <div class="chart-title">
                <i class="fas fa-crystal-ball"></i>
                成本预测
            </div>
        </div>
        <div class="chart-container">
            <canvas id="costForecastChart"></canvas>
        </div>
    </div>

    <!-- 节约分析 -->
    <div class="chart-card">
        <div class="chart-header">
            <div class="chart-title">
                <i class="fas fa-leaf"></i>
                节约分析
            </div>
        </div>
        <div class="chart-container">
            <canvas id="savingsChart"></canvas>
        </div>
    </div>
</div>

<script>
// 成本趋势数据
const costData = {
    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
    datasets: [{
        label: '总成本',
        data: [385000, 392000, 408000, 415000, 420000, 425600],
        borderColor: '#ef4444',
        backgroundColor: 'rgba(239, 68, 68, 0.1)',
        borderWidth: 3,
        fill: true,
        tension: 0.4
    }]
};

let costTrendChart;

// 初始化成本趋势图表
function initCostTrendChart() {
    const canvas = document.getElementById('costTrendChart');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    
    costTrendChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: costData.labels,
            datasets: [{
                label: '总成本 (¥)',
                data: costData.datasets[0].data,
                borderColor: '#ef4444',
                backgroundColor: 'rgba(239, 68, 68, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                yAxisID: 'y'
            }, {
                label: '预算金额 (¥)',
                data: [400000, 400000, 420000, 420000, 430000, 430000],
                borderColor: '#6b7280',
                backgroundColor: 'rgba(107, 114, 128, 0.1)',
                borderWidth: 2,
                borderDash: [5, 5],
                fill: false,
                tension: 0.4,
                yAxisID: 'y'
            }, {
                label: '平均单价 (¥)',
                data: [28.8, 29.2, 28.5, 28.9, 28.2, 28.5],
                borderColor: '#3b82f6',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                borderWidth: 3,
                fill: false,
                tension: 0.4,
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                intersect: false,
                mode: 'index'
            },
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            if (context.datasetIndex === 0) {
                                return '总成本: ¥' + context.parsed.y.toLocaleString();
                            } else if (context.datasetIndex === 1) {
                                return '预算金额: ¥' + context.parsed.y.toLocaleString();
                            } else {
                                return '平均单价: ¥' + context.parsed.y;
                            }
                        }
                    }
                }
            },
            scales: {
                x: {
                    display: true,
                    title: {
                        display: true,
                        text: '时间'
                    }
                },
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: '成本金额 (¥)'
                    },
                    ticks: {
                        callback: function(value) {
                            return '¥' + (value / 1000) + 'K';
                        }
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: '单价 (¥)'
                    },
                    grid: {
                        drawOnChartArea: false,
                    },
                    ticks: {
                        callback: function(value) {
                            return '¥' + value;
                        }
                    }
                }
            }
        }
    });
}

// 切换图表类型
function toggleCostChart(type) {
    // 更新按钮状态
    document.querySelectorAll('.chart-toggle').forEach(btn => btn.classList.remove('active'));
    event.target.closest('.chart-toggle').classList.add('active');
    
    if (type === 'comparison') {
        // 切换为对比分析图
        costTrendChart.data.datasets = [{
            label: '食材成本',
            data: [285000, 292000, 308000, 315000, 320000, 325600],
            backgroundColor: '#ef4444'
        }, {
            label: '人工成本',
            data: [80000, 80000, 80000, 80000, 80000, 80000],
            backgroundColor: '#f59e0b'
        }, {
            label: '其他成本',
            data: [20000, 20000, 20000, 20000, 20000, 20000],
            backgroundColor: '#6b7280'
        }];
        costTrendChart.config.type = 'bar';
    } else {
        // 切换回趋势图
        costTrendChart.data.datasets = costData.datasets;
        costTrendChart.config.type = 'line';
    }
    
    costTrendChart.update();
}

// 初始化所有成本图表
function initCostCharts() {
    initCostCompositionChart();
    initUnitPriceChart();
    initCostEfficiencyChart();
    initBudgetExecutionChart();
    initCostForecastChart();
    initSavingsChart();
}

// 成本构成饼图
function initCostCompositionChart() {
    const canvas = document.getElementById('costCompositionChart');
    if (!canvas) return;

    const ctx = canvas.getContext('2d');

    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['食材成本', '人工成本', '运输成本', '储存成本', '其他成本'],
            datasets: [{
                data: [76, 19, 3, 1.5, 0.5],
                backgroundColor: [
                    '#ef4444',
                    '#f59e0b',
                    '#3b82f6',
                    '#10b981',
                    '#8b5cf6'
                ],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 15,
                        usePointStyle: true
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return context.label + ': ' + context.parsed + '%';
                        }
                    }
                }
            }
        }
    });
}

// 单价变化趋势
function initUnitPriceChart() {
    const canvas = document.getElementById('unitPriceChart');
    if (!canvas) return;

    const ctx = canvas.getContext('2d');

    new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
            datasets: [{
                label: '蔬菜类',
                data: [15.2, 16.8, 14.5, 15.9, 14.2, 15.1],
                borderColor: '#10b981',
                backgroundColor: 'rgba(16, 185, 129, 0.1)',
                borderWidth: 2,
                fill: false,
                tension: 0.4
            }, {
                label: '肉类',
                data: [45.8, 47.2, 46.5, 48.9, 47.2, 48.5],
                borderColor: '#ef4444',
                backgroundColor: 'rgba(239, 68, 68, 0.1)',
                borderWidth: 2,
                fill: false,
                tension: 0.4
            }, {
                label: '粮食类',
                data: [8.2, 8.5, 8.1, 8.8, 8.3, 8.6],
                borderColor: '#f59e0b',
                backgroundColor: 'rgba(245, 158, 11, 0.1)',
                borderWidth: 2,
                fill: false,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: '单价 (¥/kg)'
                    }
                }
            }
        }
    });
}

// 成本效益分析
function initCostEfficiencyChart() {
    const canvas = document.getElementById('costEfficiencyChart');
    if (!canvas) return;

    const ctx = canvas.getContext('2d');

    new Chart(ctx, {
        type: 'scatter',
        data: {
            datasets: [{
                label: '蔬菜类',
                data: [{x: 15.1, y: 4.2}, {x: 14.8, y: 4.5}, {x: 15.5, y: 3.8}],
                backgroundColor: '#10b981',
                borderColor: '#10b981'
            }, {
                label: '肉类',
                data: [{x: 48.5, y: 3.2}, {x: 47.8, y: 3.5}, {x: 49.2, y: 2.9}],
                backgroundColor: '#ef4444',
                borderColor: '#ef4444'
            }, {
                label: '粮食类',
                data: [{x: 8.6, y: 2.8}, {x: 8.3, y: 3.1}, {x: 8.9, y: 2.5}],
                backgroundColor: '#f59e0b',
                borderColor: '#f59e0b'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top'
                }
            },
            scales: {
                x: {
                    title: {
                        display: true,
                        text: '单价 (¥/kg)'
                    }
                },
                y: {
                    title: {
                        display: true,
                        text: '质量评分'
                    }
                }
            }
        }
    });
}

// 预算执行情况
function initBudgetExecutionChart() {
    const canvas = document.getElementById('budgetExecutionChart');
    if (!canvas) return;

    const ctx = canvas.getContext('2d');

    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
            datasets: [{
                label: '预算',
                data: [400000, 400000, 420000, 420000, 430000, 430000],
                backgroundColor: '#6b7280',
                borderRadius: 4
            }, {
                label: '实际支出',
                data: [385000, 392000, 408000, 415000, 420000, 425600],
                backgroundColor: '#ef4444',
                borderRadius: 4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: '金额 (¥)'
                    },
                    ticks: {
                        callback: function(value) {
                            return '¥' + (value / 1000) + 'K';
                        }
                    }
                }
            }
        }
    });
}

// 成本预测
function initCostForecastChart() {
    const canvas = document.getElementById('costForecastChart');
    if (!canvas) return;

    const ctx = canvas.getContext('2d');

    new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['4月', '5月', '6月', '7月(预测)', '8月(预测)', '9月(预测)'],
            datasets: [{
                label: '实际成本',
                data: [415000, 420000, 425600, null, null, null],
                borderColor: '#ef4444',
                backgroundColor: 'rgba(239, 68, 68, 0.1)',
                borderWidth: 3,
                fill: false,
                tension: 0.4
            }, {
                label: '预测成本',
                data: [null, null, 425600, 432000, 438000, 445000],
                borderColor: '#f59e0b',
                backgroundColor: 'rgba(245, 158, 11, 0.1)',
                borderWidth: 3,
                borderDash: [5, 5],
                fill: false,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top'
                }
            },
            scales: {
                y: {
                    beginAtZero: false,
                    title: {
                        display: true,
                        text: '成本 (¥)'
                    },
                    ticks: {
                        callback: function(value) {
                            return '¥' + (value / 1000) + 'K';
                        }
                    }
                }
            }
        }
    });
}

// 节约分析
function initSavingsChart() {
    const canvas = document.getElementById('savingsChart');
    if (!canvas) return;

    const ctx = canvas.getContext('2d');

    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
            datasets: [{
                label: '节约金额',
                data: [15000, 8000, 12000, 5000, 10000, 15200],
                backgroundColor: '#10b981',
                borderRadius: 4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: '节约金额 (¥)'
                    },
                    ticks: {
                        callback: function(value) {
                            return '¥' + value.toLocaleString();
                        }
                    }
                }
            }
        }
    });
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    initCostTrendChart();
    initCostCharts();
});
</script>
