<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>逐步调试导入过程</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .data-table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        .data-table th, .data-table td { border: 1px solid #ddd; padding: 8px; text-align: left; font-size: 12px; }
        .data-table th { background: #f5f5f5; }
        .code-block { background: #f4f4f4; padding: 10px; border-radius: 3px; font-family: monospace; margin: 5px 0; white-space: pre-wrap; }
        .step { background: #f9f9f9; padding: 10px; margin: 10px 0; border-left: 4px solid #007cba; }
    </style>
</head>
<body>
    <h1>逐步调试导入过程</h1>
    
    <?php
    require_once '../includes/Database.php';
    require_once '../includes/ExcelReader.php';
    
    try {
        $db = Database::getInstance();
        echo "<p class='success'>✅ 数据库连接成功</p>";
        
        $testFile = '../test.xlsx';
        
        echo "<div class='test-section'>";
        echo "<h2>步骤1: 文件检查</h2>";
        
        if (file_exists($testFile)) {
            echo "<div class='step'>";
            echo "<p class='success'>✅ test.xlsx 文件存在</p>";
            echo "<p class='info'>文件大小: " . number_format(filesize($testFile)) . " 字节</p>";
            echo "<p class='info'>文件路径: " . realpath($testFile) . "</p>";
            echo "</div>";
        } else {
            echo "<p class='error'>❌ test.xlsx 文件不存在</p>";
            exit;
        }
        echo "</div>";
        
        echo "<div class='test-section'>";
        echo "<h2>步骤2: Excel文件读取</h2>";
        
        try {
            $reader = new ExcelReader();
            $data = $reader->read($testFile, 'test.xlsx');
            
            echo "<div class='step'>";
            echo "<p class='success'>✅ Excel文件读取成功</p>";
            echo "<p class='info'>总行数: " . count($data) . "</p>";
            
            if (!empty($data)) {
                echo "<p class='info'>第一行列数: " . count($data[0]) . "</p>";
                
                // 显示前几行数据
                echo "<h4>前5行数据预览:</h4>";
                echo "<div class='code-block'>";
                for ($i = 0; $i < min(5, count($data)); $i++) {
                    echo "行" . ($i + 1) . ": ";
                    $row = $data[$i];
                    for ($j = 0; $j < min(10, count($row)); $j++) {
                        $value = isset($row[$j]) ? $row[$j] : '';
                        if (strlen($value) > 20) {
                            $value = substr($value, 0, 20) . '...';
                        }
                        echo "[{$j}]='" . htmlspecialchars($value) . "' ";
                    }
                    echo "\n";
                }
                echo "</div>";
            }
            echo "</div>";
            
        } catch (Exception $e) {
            echo "<p class='error'>❌ Excel文件读取失败: " . $e->getMessage() . "</p>";
            exit;
        }
        echo "</div>";
        
        echo "<div class='test-section'>";
        echo "<h2>步骤3: 格式检测</h2>";
        
        // 模拟格式检测逻辑
        $detectResult = 'simple_list';
        
        echo "<div class='step'>";
        echo "<h4>检测条件分析:</h4>";
        
        if (count($data) >= 4) {
            echo "<p class='success'>✅ 数据行数 >= 4: " . count($data) . "</p>";
            
            $firstRow = $data[0] ?? [];
            $secondRow = $data[1] ?? [];
            
            echo "<p class='info'>第1行列数: " . count($firstRow) . "</p>";
            echo "<p class='info'>第2行列数: " . count($secondRow) . "</p>";
            
            if (count($firstRow) > 4 && count($secondRow) > 11) {
                echo "<p class='success'>✅ 列数条件满足</p>";
                
                $orderNumber = trim($firstRow[1] ?? '');
                echo "<p class='info'>第1行第2列内容: '" . htmlspecialchars($orderNumber) . "'</p>";
                echo "<p class='info'>订单号长度: " . strlen($orderNumber) . "</p>";
                
                if (!empty($orderNumber) && (strlen($orderNumber) > 10 || preg_match('/^[A-Z0-9]+/', $orderNumber))) {
                    $detectResult = 'order_form';
                    echo "<p class='success'>✅ 检测为订货单格式</p>";
                } else {
                    echo "<p class='warning'>⚠️ 订单号不符合条件（长度<=10且不匹配模式）</p>";
                }
            } else {
                echo "<p class='warning'>⚠️ 列数不足（需要第1行>4列，第2行>11列）</p>";
            }
        } else {
            echo "<p class='warning'>⚠️ 数据行数不足（需要>=4行）</p>";
        }
        
        echo "<p class='info'><strong>最终检测结果: {$detectResult}</strong></p>";
        echo "</div>";
        echo "</div>";
        
        echo "<div class='test-section'>";
        echo "<h2>步骤4: 数据处理模拟</h2>";
        
        if ($detectResult === 'order_form') {
            echo "<div class='step'>";
            echo "<h4>订货单格式处理:</h4>";
            
            // 模拟订单信息提取
            $orderInfo = [
                'order_number' => trim($data[0][1] ?? ''),
                'order_date' => trim($data[0][4] ?? ''),
                'contact_person' => trim($data[1][11] ?? ''),
                'delivery_address' => trim($data[2][1] ?? ''),
                'contact_phone' => trim($data[2][11] ?? ''),
                'order_amount' => floatval($data[3][1] ?? 0),
                'actual_amount' => floatval($data[3][5] ?? 0),
                'expected_date' => trim($data[3][11] ?? '')
            ];
            
            echo "<div class='code-block'>";
            foreach ($orderInfo as $key => $value) {
                echo "{$key}: " . htmlspecialchars($value) . "\n";
            }
            echo "</div>";
            
            // 检查明细数据
            echo "<h4>明细数据分析:</h4>";
            $validItems = 0;
            $itemDetails = [];
            
            for ($i = 6; $i < count($data); $i++) {
                $row = $data[$i];
                if (!empty($row[0]) && !empty(array_filter($row))) {
                    $validItems++;
                    if ($validItems <= 3) {
                        $itemDetails[] = [
                            'row' => $i + 1,
                            'code' => $row[0] ?? '',
                            'name' => $row[1] ?? '',
                            'quantity' => $row[8] ?? '',
                            'price' => $row[7] ?? ''
                        ];
                    }
                }
            }
            
            echo "<p class='info'>有效明细数量: {$validItems}</p>";
            if (!empty($itemDetails)) {
                echo "<div class='code-block'>";
                foreach ($itemDetails as $item) {
                    echo "行{$item['row']}: 编码={$item['code']}, 名称={$item['name']}, 数量={$item['quantity']}, 单价={$item['price']}\n";
                }
                echo "</div>";
            }
            echo "</div>";
            
        } else {
            echo "<div class='step'>";
            echo "<h4>简单列表格式处理:</h4>";
            
            // 跳过标题行
            $dataRows = array_slice($data, 1);
            $validRows = 0;
            $rowDetails = [];
            
            foreach ($dataRows as $index => $row) {
                // 检查是否为空行
                $filteredRow = array_filter($row, function($cell) {
                    return !empty(trim($cell));
                });
                
                if (!empty($filteredRow)) {
                    $validRows++;
                    if ($validRows <= 3) {
                        $rowDetails[] = [
                            'row' => $index + 2,
                            'supplier' => $row[0] ?? '',
                            'date' => $row[1] ?? '',
                            'ingredient' => $row[2] ?? '',
                            'quantity' => $row[3] ?? '',
                            'price' => $row[4] ?? '',
                            'notes' => $row[5] ?? ''
                        ];
                    }
                }
            }
            
            echo "<p class='info'>有效数据行数: {$validRows}</p>";
            
            if ($validRows === 0) {
                echo "<p class='error'>❌ 没有找到有效的数据行！</p>";
                echo "<p class='warning'>这就是导入0条记录的原因</p>";
                
                echo "<h4>详细分析:</h4>";
                echo "<div class='code-block'>";
                echo "标题行（第1行）: ";
                $headerRow = $data[0] ?? [];
                for ($i = 0; $i < min(6, count($headerRow)); $i++) {
                    echo "[{$i}]='" . htmlspecialchars($headerRow[$i] ?? '') . "' ";
                }
                echo "\n\n";
                
                echo "数据行分析:\n";
                foreach ($dataRows as $index => $row) {
                    echo "第" . ($index + 2) . "行: ";
                    $filteredRow = array_filter($row, function($cell) {
                        return !empty(trim($cell));
                    });
                    echo "非空单元格数量=" . count($filteredRow) . " ";
                    
                    if (empty($filteredRow)) {
                        echo "（空行）";
                    } else {
                        echo "内容=";
                        for ($i = 0; $i < min(6, count($row)); $i++) {
                            $value = trim($row[$i] ?? '');
                            if (!empty($value)) {
                                echo "[{$i}]='" . htmlspecialchars($value) . "' ";
                            }
                        }
                    }
                    echo "\n";
                    
                    if ($index >= 5) {
                        echo "... 还有 " . (count($dataRows) - 6) . " 行\n";
                        break;
                    }
                }
                echo "</div>";
                
            } else {
                echo "<div class='code-block'>";
                foreach ($rowDetails as $detail) {
                    echo "行{$detail['row']}: 供应商={$detail['supplier']}, 日期={$detail['date']}, 食材={$detail['ingredient']}, 数量={$detail['quantity']}, 单价={$detail['price']}\n";
                }
                echo "</div>";
            }
            echo "</div>";
        }
        echo "</div>";
        
        echo "<div class='test-section'>";
        echo "<h2>步骤5: 问题诊断</h2>";
        
        echo "<div class='step'>";
        if ($detectResult === 'simple_list' && isset($validRows) && $validRows === 0) {
            echo "<h4>🔍 问题确认：</h4>";
            echo "<p class='error'>导入0条记录的原因：Excel文件中除了标题行外，没有有效的数据行</p>";
            
            echo "<h4>💡 可能的原因：</h4>";
            echo "<ul>";
            echo "<li>Excel文件只有标题行，没有实际数据</li>";
            echo "<li>数据行都是空行（所有单元格都为空）</li>";
            echo "<li>数据格式不正确（比如数据在错误的列中）</li>";
            echo "<li>Excel文件编码问题导致数据读取失败</li>";
            echo "</ul>";
            
            echo "<h4>🛠️ 解决方案：</h4>";
            echo "<ol>";
            echo "<li><strong>检查Excel文件内容</strong>：确保文件中有实际数据，不只是标题行</li>";
            echo "<li><strong>使用标准模板</strong>：<a href='../modules/purchase/download_excel_template.php' target='_blank'>下载标准模板</a></li>";
            echo "<li><strong>验证数据格式</strong>：确保数据按照模板格式填写</li>";
            echo "<li><strong>检查编码</strong>：使用UTF-8编码保存Excel文件</li>";
            echo "</ol>";
            
        } else {
            echo "<p class='success'>✅ 数据格式检测正常，应该可以正常导入</p>";
        }
        echo "</div>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ 调试过程出错: " . $e->getMessage() . "</p>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }
    ?>
    
    <div class="test-section">
        <h2>快速解决方案</h2>
        <div class="step">
            <h4>🚀 立即测试：</h4>
            <ol>
                <li><a href="../modules/purchase/download_excel_template.php" target="_blank">下载简单列表模板</a></li>
                <li>在模板中添加以下测试数据：</li>
            </ol>
            
            <div class="code-block">供应商名称    订单日期      食材名称    数量    单价    备注
新鲜蔬菜供应商  2024-01-15   白菜       50     2.50   早餐用
肉类批发商     2024-01-15   猪肉       20     25.00  午餐用</div>
            
            <ol start="3">
                <li><a href="../modules/purchase/index.php?action=import" target="_blank">重新测试导入</a></li>
            </ol>
        </div>
    </div>
    
    <p><a href="../modules/purchase/index.php">返回采购管理</a></p>
</body>
</html>
