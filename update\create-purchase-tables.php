<?php
/**
 * 创建采购管理相关数据库表
 */

require_once '../includes/Database.php';

try {
    $db = Database::getInstance();
    
    echo "<h1>创建采购管理数据库表</h1>";
    
    // 1. 检查并创建suppliers表
    echo "<h2>1. 检查suppliers表</h2>";
    
    $tables = $db->fetchAll("SHOW TABLES LIKE 'suppliers'");
    if (empty($tables)) {
        echo "<p>创建suppliers表...</p>";
        
        $suppliersSql = "
        CREATE TABLE suppliers (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL COMMENT '供应商名称',
            contact_person VARCHAR(50) COMMENT '联系人',
            phone VARCHAR(20) COMMENT '联系电话',
            email VARCHAR(100) COMMENT '邮箱',
            address TEXT COMMENT '地址',
            status TINYINT DEFAULT 1 COMMENT '状态：1启用，0禁用',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='供应商表'";
        
        $db->query($suppliersSql);
        echo "<p style='color: green;'>✅ suppliers表创建成功</p>";
        
        // 插入默认供应商
        $db->insert('suppliers', [
            'name' => '默认供应商',
            'contact_person' => '联系人',
            'phone' => '13800138000',
            'address' => '默认地址',
            'status' => 1
        ]);
        echo "<p style='color: green;'>✅ 默认供应商创建成功</p>";
        
    } else {
        echo "<p style='color: green;'>✅ suppliers表已存在</p>";
    }
    
    // 2. 检查并创建purchase_orders表
    echo "<h2>2. 检查purchase_orders表</h2>";
    
    $tables = $db->fetchAll("SHOW TABLES LIKE 'purchase_orders'");
    if (empty($tables)) {
        echo "<p>创建purchase_orders表...</p>";
        
        $ordersSql = "
        CREATE TABLE purchase_orders (
            id INT AUTO_INCREMENT PRIMARY KEY,
            order_number VARCHAR(50) NOT NULL UNIQUE COMMENT '订单号',
            supplier_id INT NOT NULL COMMENT '供应商ID',
            order_date DATE NOT NULL COMMENT '订单日期',
            expected_date DATE COMMENT '预期交货日期',
            delivery_address TEXT COMMENT '送货地址',
            contact_person VARCHAR(50) COMMENT '联系人',
            contact_phone VARCHAR(20) COMMENT '联系电话',
            order_amount DECIMAL(10,2) DEFAULT 0 COMMENT '订单金额',
            actual_amount DECIMAL(10,2) DEFAULT 0 COMMENT '实际金额',
            payment_status VARCHAR(20) DEFAULT 'unpaid' COMMENT '付款状态',
            order_status VARCHAR(20) DEFAULT 'pending' COMMENT '订单状态',
            notes TEXT COMMENT '备注',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (supplier_id) REFERENCES suppliers(id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='采购订单表'";
        
        $db->query($ordersSql);
        echo "<p style='color: green;'>✅ purchase_orders表创建成功</p>";
        
    } else {
        echo "<p style='color: green;'>✅ purchase_orders表已存在</p>";
    }
    
    // 3. 检查并创建purchase_order_items表
    echo "<h2>3. 检查purchase_order_items表</h2>";
    
    $tables = $db->fetchAll("SHOW TABLES LIKE 'purchase_order_items'");
    if (empty($tables)) {
        echo "<p>创建purchase_order_items表...</p>";
        
        $itemsSql = "
        CREATE TABLE purchase_order_items (
            id INT AUTO_INCREMENT PRIMARY KEY,
            order_id INT NOT NULL COMMENT '订单ID',
            ingredient_id INT NOT NULL COMMENT '食材ID',
            quantity DECIMAL(10,2) NOT NULL COMMENT '数量',
            unit_price DECIMAL(10,2) NOT NULL COMMENT '单价',
            total_price DECIMAL(10,2) NOT NULL COMMENT '总价',
            received_quantity DECIMAL(10,2) DEFAULT 0 COMMENT '实收数量',
            qualified_quantity DECIMAL(10,2) DEFAULT 0 COMMENT '合格数量',
            unqualified_quantity DECIMAL(10,2) DEFAULT 0 COMMENT '不合格数量',
            loss_quantity DECIMAL(10,2) DEFAULT 0 COMMENT '损耗数量',
            rejected_quantity DECIMAL(10,2) DEFAULT 0 COMMENT '拒收数量',
            notes TEXT COMMENT '备注',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (order_id) REFERENCES purchase_orders(id) ON DELETE CASCADE,
            FOREIGN KEY (ingredient_id) REFERENCES ingredients(id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='采购订单明细表'";
        
        $db->query($itemsSql);
        echo "<p style='color: green;'>✅ purchase_order_items表创建成功</p>";
        
    } else {
        echo "<p style='color: green;'>✅ purchase_order_items表已存在</p>";
        
        // 检查字段是否完整
        $columns = $db->fetchAll("DESCRIBE purchase_order_items");
        $columnNames = array_column($columns, 'Field');
        
        $requiredColumns = [
            'id', 'order_id', 'ingredient_id', 'quantity', 'unit_price', 'total_price',
            'received_quantity', 'qualified_quantity', 'unqualified_quantity', 
            'loss_quantity', 'rejected_quantity', 'notes', 'created_at'
        ];
        
        $missingColumns = array_diff($requiredColumns, $columnNames);
        
        if (!empty($missingColumns)) {
            echo "<p style='color: orange;'>⚠️ 缺少字段: " . implode(', ', $missingColumns) . "</p>";
            
            // 添加缺少的字段
            foreach ($missingColumns as $column) {
                try {
                    switch ($column) {
                        case 'qualified_quantity':
                        case 'unqualified_quantity':
                        case 'loss_quantity':
                        case 'rejected_quantity':
                            $db->query("ALTER TABLE purchase_order_items ADD COLUMN {$column} DECIMAL(10,2) DEFAULT 0 COMMENT '{$column}'");
                            echo "<p style='color: green;'>✅ 添加字段 {$column} 成功</p>";
                            break;
                        case 'updated_at':
                            $db->query("ALTER TABLE purchase_order_items ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP");
                            echo "<p style='color: green;'>✅ 添加字段 {$column} 成功</p>";
                            break;
                    }
                } catch (Exception $e) {
                    echo "<p style='color: red;'>❌ 添加字段 {$column} 失败: " . $e->getMessage() . "</p>";
                }
            }
        } else {
            echo "<p style='color: green;'>✅ 所有必需字段都存在</p>";
        }
    }
    
    // 4. 检查ingredients表
    echo "<h2>4. 检查ingredients表</h2>";
    
    $tables = $db->fetchAll("SHOW TABLES LIKE 'ingredients'");
    if (empty($tables)) {
        echo "<p style='color: orange;'>⚠️ ingredients表不存在，需要先创建</p>";
        
        $ingredientsSql = "
        CREATE TABLE ingredients (
            id INT AUTO_INCREMENT PRIMARY KEY,
            code VARCHAR(50) UNIQUE COMMENT '食材编码',
            name VARCHAR(100) NOT NULL COMMENT '食材名称',
            specification VARCHAR(100) COMMENT '规格',
            unit VARCHAR(20) DEFAULT '个' COMMENT '单位',
            category_id INT DEFAULT 1 COMMENT '分类ID',
            brand VARCHAR(50) COMMENT '品牌',
            origin VARCHAR(50) COMMENT '产地',
            shelf_life VARCHAR(50) COMMENT '保质期',
            purchase_price DECIMAL(10,2) DEFAULT 0 COMMENT '采购价',
            status TINYINT DEFAULT 1 COMMENT '状态：1启用，0禁用',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='食材表'";
        
        $db->query($ingredientsSql);
        echo "<p style='color: green;'>✅ ingredients表创建成功</p>";
        
    } else {
        echo "<p style='color: green;'>✅ ingredients表已存在</p>";
        
        $count = $db->fetchOne("SELECT COUNT(*) as count FROM ingredients")['count'];
        echo "<p>当前食材数量: {$count}</p>";
    }
    
    // 5. 显示最终状态
    echo "<h2>5. 最终状态检查</h2>";
    
    $tables = ['suppliers', 'purchase_orders', 'purchase_order_items', 'ingredients'];
    
    foreach ($tables as $table) {
        try {
            $count = $db->fetchOne("SELECT COUNT(*) as count FROM {$table}")['count'];
            echo "<p style='color: green;'>✅ {$table}: {$count} 条记录</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ {$table}: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<h2>✅ 数据库表创建完成！</h2>";
    echo "<p><a href='../modules/purchase/index.php?action=import'>现在可以测试导入功能</a></p>";
    echo "<p><a href='../test/check-database-structure.php'>重新检查数据库结构</a></p>";
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>❌ 创建失败</h2>";
    echo "<p style='color: red;'>错误: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
