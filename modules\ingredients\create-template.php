<?php require_once dirname(__DIR__, 2) . '/includes/header-modular.php'; ?>

<div class="main-content">
    <?php require_once dirname(__DIR__, 2) . '/includes/sidebar-modular.php'; ?>
    
    <div class="content">
        <div class="content-header">
            <h1>
                <i class="fas fa-plus-circle"></i>
                添加食材
            </h1>
            <div class="header-actions">
                <a href="index.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i>
                    返回列表
                </a>
            </div>
        </div>
        <?php if (isset($error_message)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle"></i>
            <?= htmlspecialchars($error_message) ?>
        </div>
        <?php endif; ?>

        <!-- 添加食材表单 -->
        <div class="form-container">
            <div class="form-header">
                <div class="form-icon">
                    <i class="fas fa-carrot"></i>
                </div>
                <div class="form-title">
                    <h2>新建食材</h2>
                    <p>请填写食材的基本信息</p>
                </div>
            </div>

            <form method="POST" class="ingredient-form">
                <div class="form-grid">
                    <!-- 基本信息 -->
                    <div class="form-section">
                        <h3><i class="fas fa-info-circle"></i> 基本信息</h3>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label required">食材名称</label>
                                <input type="text" name="name" class="form-control" required
                                       placeholder="请输入食材名称" value="<?= htmlspecialchars($_POST['name'] ?? '') ?>">
                            </div>
                            <div class="form-group">
                                <label class="form-label">食材编号</label>
                                <input type="text" name="code" class="form-control"
                                       placeholder="请输入食材编号（可选）" value="<?= htmlspecialchars($_POST['code'] ?? '') ?>">
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label required">一级分类</label>
                                <select name="primary_category_id" id="primaryCategory" class="form-control" required onchange="loadSubcategories()">
                                    <option value="">请选择一级分类</option>
                                    <?php if (isset($primary_categories) && is_array($primary_categories) && !empty($primary_categories)): ?>
                                        <?php foreach ($primary_categories as $category): ?>
                                        <option value="<?= htmlspecialchars($category['id']) ?>"
                                                <?= (($_POST['primary_category_id'] ?? '') == $category['id']) ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($category['name']) ?>
                                        </option>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <!-- 使用默认一级分类选项 -->
                                        <option value="1">蔬菜类</option>
                                        <option value="2">肉类</option>
                                        <option value="3">水产类</option>
                                        <option value="4">粮油类</option>
                                        <option value="5">调料类</option>
                                        <option value="6">豆制品</option>
                                    <?php endif; ?>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">二级分类</label>
                                <select name="category_id" id="secondaryCategory" class="form-control" disabled>
                                    <option value="">请先选择一级分类</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label required">计量单位</label>
                                <select name="unit" class="form-control" required>
                                    <option value="">请选择单位</option>
                                    <option value="斤" <?= (($_POST['unit'] ?? '') == '斤') ? 'selected' : '' ?>>斤</option>
                                    <option value="公斤" <?= (($_POST['unit'] ?? '') == '公斤') ? 'selected' : '' ?>>公斤</option>
                                    <option value="克" <?= (($_POST['unit'] ?? '') == '克') ? 'selected' : '' ?>>克</option>
                                    <option value="个" <?= (($_POST['unit'] ?? '') == '个') ? 'selected' : '' ?>>个</option>
                                    <option value="包" <?= (($_POST['unit'] ?? '') == '包') ? 'selected' : '' ?>>包</option>
                                    <option value="袋" <?= (($_POST['unit'] ?? '') == '袋') ? 'selected' : '' ?>>袋</option>
                                    <option value="瓶" <?= (($_POST['unit'] ?? '') == '瓶') ? 'selected' : '' ?>>瓶</option>
                                    <option value="盒" <?= (($_POST['unit'] ?? '') == '盒') ? 'selected' : '' ?>>盒</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">采购价 <span class="label-note">(食材的参考采购价格，用于成本核算)</span></label>
                                <div class="input-group">
                                    <span class="input-group-text">¥</span>
                                    <input type="number" name="unit_price" class="form-control" step="0.01" min="0"
                                           placeholder="请输入采购价" value="<?= htmlspecialchars($_POST['unit_price'] ?? '') ?>">
                                    <span class="input-group-text">元</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 库存管理 -->
                    <div class="form-section">
                        <h3><i class="fas fa-warehouse"></i> 库存管理</h3>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">保质期（天）</label>
                                <input type="number" name="shelf_life_days" class="form-control" min="1"
                                       placeholder="请输入保质期天数" value="<?= htmlspecialchars($_POST['shelf_life_days'] ?? '30') ?>">
                                <small class="form-text">食材的保质期天数</small>
                            </div>
                            <div class="form-group">
                                <label class="form-label">最低库存预警</label>
                                <input type="number" name="min_stock" class="form-control" step="0.01" min="0"
                                       placeholder="请输入最低库存数量" value="<?= htmlspecialchars($_POST['min_stock'] ?? '') ?>">
                                <small class="form-text">当库存低于此数量时将显示预警</small>
                            </div>
                            <div class="form-group">
                                <label class="form-label">当前库存</label>
                                <input type="number" name="current_stock" class="form-control" step="0.01" min="0" value="0" readonly>
                                <small class="form-text">新建食材初始库存为0，可通过入库操作增加</small>
                            </div>
                        </div>
                    </div>

                    <!-- 其他信息 -->
                    <div class="form-section">
                        <h3><i class="fas fa-info-circle"></i> 其他信息</h3>

                        <div class="form-group">
                            <label class="form-label">食材描述</label>
                            <textarea name="description" class="form-control" rows="3"
                                      placeholder="请输入食材描述信息（可选）"><?= htmlspecialchars($_POST['description'] ?? '') ?></textarea>
                            <small class="form-text">食材的详细描述、规格、产地等信息</small>
                        </div>
                    </div>
                </div>

                <!-- 提交按钮 -->
                <div class="form-actions">
                    <button type="button" onclick="history.back()" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i> 取消
                    </button>
                    <button type="submit" class="btn btn-primary btn-submit">
                        <i class="fas fa-save"></i> 保存食材
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// 分类数据
const subcategories = <?= json_encode($subcategories ?? []) ?>;

// 加载二级分类
function loadSubcategories() {
    const primaryCategoryId = document.getElementById('primaryCategory').value;
    const secondarySelect = document.getElementById('secondaryCategory');

    // 清空二级分类选项
    secondarySelect.innerHTML = '<option value="">请选择二级分类</option>';

    if (primaryCategoryId) {
        // 筛选对应的二级分类
        const relatedSubcategories = subcategories.filter(sub => sub.parent_id == primaryCategoryId);

        if (relatedSubcategories.length > 0) {
            relatedSubcategories.forEach(sub => {
                const option = document.createElement('option');
                option.value = sub.id;
                option.textContent = sub.name;
                secondarySelect.appendChild(option);
            });
            secondarySelect.disabled = false;
        } else {
            secondarySelect.innerHTML = '<option value="">该分类下暂无二级分类</option>';
            secondarySelect.disabled = true;
        }
    } else {
        secondarySelect.innerHTML = '<option value="">请先选择一级分类</option>';
        secondarySelect.disabled = true;
    }
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    // 如果有预选的一级分类，加载对应的二级分类
    const primaryCategory = document.getElementById('primaryCategory');
    if (primaryCategory.value) {
        loadSubcategories();

        // 如果有预选的二级分类，设置选中状态
        const selectedSecondaryId = '<?= $_POST['category_id'] ?? '' ?>';
        if (selectedSecondaryId) {
            setTimeout(() => {
                const secondarySelect = document.getElementById('secondaryCategory');
                secondarySelect.value = selectedSecondaryId;
            }, 100);
        }
    }
});

// 表单验证
document.querySelector('.ingredient-form').addEventListener('submit', function(e) {
    const name = document.querySelector('input[name="name"]').value.trim();
    const unit = document.querySelector('select[name="unit"]').value;
    
    if (!name || !unit) {
        e.preventDefault();
        alert('请填写所有必填字段');
        return false;
    }
    
    // 验证采购价格式（如果填写了）
    const unitPriceInput = document.querySelector('input[name="unit_price"]');
    if (unitPriceInput) {
        const unitPrice = unitPriceInput.value;
        if (unitPrice && (isNaN(unitPrice) || parseFloat(unitPrice) < 0)) {
            e.preventDefault();
            alert('请输入正确的采购价格式');
            return false;
        }
    }
    
    // 验证最低库存格式（如果填写了）
    const minStock = document.querySelector('input[name="min_stock"]').value;
    if (minStock && (isNaN(minStock) || parseFloat(minStock) < 0)) {
        e.preventDefault();
        alert('请输入正确的最低库存格式');
        return false;
    }
});
</script>

<?php require_once dirname(__DIR__, 2) . '/includes/footer-modular.php'; ?>
