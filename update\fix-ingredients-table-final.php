<?php
/**
 * 最终修复ingredients表结构
 */

require_once '../includes/Database.php';

try {
    $db = Database::getInstance();
    
    echo "<h1>最终修复ingredients表结构</h1>";
    
    // 检查当前表结构
    echo "<h2>1. 检查当前表结构</h2>";
    
    $columns = $db->fetchAll("DESCRIBE ingredients");
    $columnNames = array_column($columns, 'Field');
    
    echo "<p>当前字段: " . implode(', ', $columnNames) . "</p>";
    
    // 需要的字段
    $requiredColumns = [
        'brand' => "VARCHAR(50) DEFAULT '' COMMENT '品牌'",
        'origin' => "VARCHAR(50) DEFAULT '' COMMENT '产地'", 
        'shelf_life' => "VARCHAR(50) DEFAULT '' COMMENT '保质期'",
        'shelf_life_days' => "INT DEFAULT 0 COMMENT '保质期天数'"
    ];
    
    echo "<h2>2. 添加缺少的字段</h2>";
    
    foreach ($requiredColumns as $column => $definition) {
        if (!in_array($column, $columnNames)) {
            try {
                $sql = "ALTER TABLE ingredients ADD COLUMN {$column} {$definition}";
                $db->query($sql);
                echo "<p style='color: green;'>✅ 添加字段 {$column} 成功</p>";
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ 添加字段 {$column} 失败: " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<p style='color: blue;'>ℹ️ 字段 {$column} 已存在</p>";
            
            // 检查是否需要修改字段定义（添加默认值）
            $columnInfo = array_filter($columns, function($col) use ($column) {
                return $col['Field'] === $column;
            });
            
            if (!empty($columnInfo)) {
                $columnInfo = array_values($columnInfo)[0];
                if ($columnInfo['Default'] === null && strpos($definition, 'DEFAULT') !== false) {
                    try {
                        $sql = "ALTER TABLE ingredients MODIFY COLUMN {$column} {$definition}";
                        $db->query($sql);
                        echo "<p style='color: green;'>✅ 修改字段 {$column} 默认值成功</p>";
                    } catch (Exception $e) {
                        echo "<p style='color: orange;'>⚠️ 修改字段 {$column} 默认值失败: " . $e->getMessage() . "</p>";
                    }
                }
            }
        }
    }
    
    echo "<h2>3. 验证修复结果</h2>";
    
    $newColumns = $db->fetchAll("DESCRIBE ingredients");
    $newColumnNames = array_column($newColumns, 'Field');
    
    echo "<p>修复后字段: " . implode(', ', $newColumnNames) . "</p>";
    
    $missingColumns = array_diff(array_keys($requiredColumns), $newColumnNames);
    
    if (empty($missingColumns)) {
        echo "<p style='color: green;'>✅ 所有必需字段都已存在</p>";
        
        // 显示字段详情
        echo "<h4>字段详情：</h4>";
        echo "<table style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f5f5f5;'>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>字段名</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>类型</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>允许NULL</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>默认值</th>";
        echo "<th style='border: 1px solid #ddd; padding: 8px;'>注释</th>";
        echo "</tr>";
        
        foreach ($newColumns as $column) {
            echo "<tr>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$column['Field']}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$column['Type']}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$column['Null']}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$column['Default']}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . ($column['Comment'] ?? '') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<h2>✅ 修复完成！</h2>";
        echo "<p><a href='../test/test-auto-create-ingredients.php'>测试自动创建食材功能</a></p>";
        echo "<p><a href='../modules/purchase/index.php?action=import'>重新测试Excel导入</a></p>";
        echo "<p><a href='../test/view-import-log.php?clear_log=1'>清空日志重新测试</a></p>";
    } else {
        echo "<p style='color: red;'>❌ 仍然缺少字段: " . implode(', ', $missingColumns) . "</p>";
    }
    
    echo "<h2>4. 测试插入数据</h2>";
    
    // 测试插入一条数据
    try {
        $testData = [
            'code' => 'TEST_' . time(),
            'name' => '测试食材_' . date('H:i:s'),
            'specification' => '测试规格',
            'unit' => '个',
            'category_id' => 1,
            'brand' => '测试品牌',
            'origin' => '测试产地',
            'shelf_life' => '30天',
            'shelf_life_days' => 30,
            'status' => 1,
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        $testId = $db->insert('ingredients', $testData);
        
        if ($testId) {
            echo "<p style='color: green;'>✅ 测试插入成功: ID={$testId}</p>";
            
            // 删除测试数据
            $db->query("DELETE FROM ingredients WHERE id = ?", [$testId]);
            echo "<p style='color: blue;'>ℹ️ 测试数据已清理</p>";
        } else {
            echo "<p style='color: red;'>❌ 测试插入失败</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ 测试插入异常: " . $e->getMessage() . "</p>";
    }
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>❌ 修复失败</h2>";
    echo "<p style='color: red;'>错误: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
