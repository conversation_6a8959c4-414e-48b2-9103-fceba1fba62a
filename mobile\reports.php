<?php
/**
 * 移动端数据报表页面
 */
require_once '../includes/Database.php';

try {
    $db = Database::getInstance();
    
    // 获取今日数据
    $todayStats = $db->fetchOne("
        SELECT 
            (SELECT COUNT(*) FROM inbound_records WHERE DATE(created_at) = CURDATE()) as today_inbound,
            (SELECT SUM(quantity) FROM inbound_records WHERE DATE(created_at) = CURDATE()) as today_inbound_quantity,
            (SELECT COUNT(*) FROM purchase_orders WHERE DATE(created_at) = CURDATE()) as today_orders,
            (SELECT SUM(total_amount) FROM purchase_orders WHERE DATE(created_at) = CURDATE()) as today_amount
    ") ?? [
        'today_inbound' => 0,
        'today_inbound_quantity' => 0,
        'today_orders' => 0,
        'today_amount' => 0
    ];
    
    // 获取本周数据
    $weekStats = $db->fetchOne("
        SELECT 
            (SELECT COUNT(*) FROM inbound_records WHERE WEEK(created_at) = WEEK(CURDATE()) AND YEAR(created_at) = YEAR(CURDATE())) as week_inbound,
            (SELECT SUM(quantity) FROM inbound_records WHERE WEEK(created_at) = WEEK(CURDATE()) AND YEAR(created_at) = YEAR(CURDATE())) as week_inbound_quantity,
            (SELECT COUNT(*) FROM purchase_orders WHERE WEEK(created_at) = WEEK(CURDATE()) AND YEAR(created_at) = YEAR(CURDATE())) as week_orders,
            (SELECT SUM(total_amount) FROM purchase_orders WHERE WEEK(created_at) = WEEK(CURDATE()) AND YEAR(created_at) = YEAR(CURDATE())) as week_amount
    ") ?? [
        'week_inbound' => 0,
        'week_inbound_quantity' => 0,
        'week_orders' => 0,
        'week_amount' => 0
    ];
    
    // 获取本月数据
    $monthStats = $db->fetchOne("
        SELECT 
            (SELECT COUNT(*) FROM inbound_records WHERE MONTH(created_at) = MONTH(CURDATE()) AND YEAR(created_at) = YEAR(CURDATE())) as month_inbound,
            (SELECT SUM(quantity) FROM inbound_records WHERE MONTH(created_at) = MONTH(CURDATE()) AND YEAR(created_at) = YEAR(CURDATE())) as month_inbound_quantity,
            (SELECT COUNT(*) FROM purchase_orders WHERE MONTH(created_at) = MONTH(CURDATE()) AND YEAR(created_at) = YEAR(CURDATE())) as month_orders,
            (SELECT SUM(total_amount) FROM purchase_orders WHERE MONTH(created_at) = MONTH(CURDATE()) AND YEAR(created_at) = YEAR(CURDATE())) as month_amount
    ") ?? [
        'month_inbound' => 0,
        'month_inbound_quantity' => 0,
        'month_orders' => 0,
        'month_amount' => 0
    ];
    
    // 获取库存状态统计
    $inventoryStats = $db->fetchOne("
        SELECT 
            COUNT(*) as total_items,
            SUM(CASE WHEN current_stock > min_stock THEN 1 ELSE 0 END) as normal_stock,
            SUM(CASE WHEN current_stock <= min_stock AND current_stock > 0 THEN 1 ELSE 0 END) as low_stock,
            SUM(CASE WHEN current_stock = 0 THEN 1 ELSE 0 END) as out_of_stock,
            SUM(current_stock * unit_price) as total_value
        FROM ingredients 
        WHERE status = 1
    ") ?? [
        'total_items' => 0,
        'normal_stock' => 0,
        'low_stock' => 0,
        'out_of_stock' => 0,
        'total_value' => 0
    ];
    
    // 获取top供应商数据
    $topSuppliers = $db->fetchAll("
        SELECT 
            s.name,
            COUNT(po.id) as order_count,
            SUM(po.total_amount) as total_amount
        FROM suppliers s
        LEFT JOIN purchase_orders po ON s.id = po.supplier_id
        WHERE s.status = 1 AND po.created_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        GROUP BY s.id, s.name
        HAVING order_count > 0
        ORDER BY total_amount DESC
        LIMIT 5
    ");
    
    // 获取热门食材数据
    $topIngredients = $db->fetchAll("
        SELECT 
            i.name,
            SUM(ir.quantity) as total_quantity,
            COUNT(ir.id) as inbound_count
        FROM ingredients i
        LEFT JOIN inbound_records ir ON i.id = ir.ingredient_id
        WHERE ir.created_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        GROUP BY i.id, i.name
        HAVING total_quantity > 0
        ORDER BY total_quantity DESC
        LIMIT 5
    ");
    
    // 获取最近7天的入库趋势
    $trendData = $db->fetchAll("
        SELECT 
            DATE(created_at) as date,
            COUNT(*) as count,
            SUM(quantity) as quantity
        FROM inbound_records
        WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
        GROUP BY DATE(created_at)
        ORDER BY date ASC
    ");
    
} catch (Exception $e) {
    // 设置默认值
    $todayStats = ['today_inbound' => 0, 'today_inbound_quantity' => 0, 'today_orders' => 0, 'today_amount' => 0];
    $weekStats = ['week_inbound' => 0, 'week_inbound_quantity' => 0, 'week_orders' => 0, 'week_amount' => 0];
    $monthStats = ['month_inbound' => 0, 'month_inbound_quantity' => 0, 'month_orders' => 0, 'month_amount' => 0];
    $inventoryStats = ['total_items' => 0, 'normal_stock' => 0, 'low_stock' => 0, 'out_of_stock' => 0, 'total_value' => 0];
    $topSuppliers = [];
    $topIngredients = [];
    $trendData = [];
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>数据报表 - 移动端</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="style.css" rel="stylesheet">
    <style>
        /* 数据报表特有样式 */
        .period-tabs {
            display: flex;
            background: white;
            margin: 60px 0 15px 0;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .period-tab {
            flex: 1;
            padding: 16px 12px;
            text-align: center;
            background: white;
            color: #718096;
            text-decoration: none;
            font-weight: 500;
            font-size: 14px;
            border-right: 1px solid #f7fafc;
            transition: all 0.2s;
        }
        
        .period-tab:last-child {
            border-right: none;
        }
        
        .period-tab.active {
            background: #4299e1;
            color: white;
        }
        
        .period-tab:hover {
            color: #4299e1;
            text-decoration: none;
        }
        
        .period-tab.active:hover {
            color: white;
        }
        
        .stats-section {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: 12px;
            color: #718096;
        }
        
        .chart-section {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .chart-container {
            height: 200px;
            display: flex;
            align-items: end;
            justify-content: space-between;
            margin-top: 16px;
            padding: 0 10px;
            background: linear-gradient(to top, #f7fafc 0%, transparent 100%);
            border-radius: 8px;
        }
        
        .chart-bar {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            flex: 1;
        }
        
        .bar {
            width: 20px;
            background: linear-gradient(to top, #4299e1, #63b3ed);
            border-radius: 10px 10px 0 0;
            min-height: 4px;
            transition: all 0.3s;
        }
        
        .bar-label {
            font-size: 10px;
            color: #718096;
            font-weight: 500;
        }
        
        .bar-value {
            font-size: 11px;
            font-weight: bold;
            color: #2d3748;
        }
        
        .top-list {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            margin-bottom: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .list-header {
            padding: 16px 20px;
            background: #f7fafc;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .list-item {
            padding: 16px 20px;
            border-bottom: 1px solid #f7fafc;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .list-item:last-child {
            border-bottom: none;
        }
        
        .item-rank {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            margin-right: 12px;
        }
        
        .rank-1 { background: #ffd700; color: #b7791f; }
        .rank-2 { background: #c0c0c0; color: #6b7280; }
        .rank-3 { background: #cd7f32; color: #92400e; }
        .rank-other { background: #e2e8f0; color: #6b7280; }
        
        .item-info {
            flex: 1;
            display: flex;
            align-items: center;
        }
        
        .item-name {
            font-weight: 600;
            color: #2d3748;
            font-size: 14px;
        }
        
        .item-value {
            font-weight: bold;
            color: #4299e1;
            font-size: 14px;
        }
        
        .inventory-summary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
            margin-top: 16px;
        }
        
        .summary-item {
            text-align: center;
        }
        
        .summary-value {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 4px;
        }
        
        .summary-label {
            font-size: 12px;
            opacity: 0.9;
        }
        
        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #718096;
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    <header class="mobile-header">
        <div class="header-content">
            <a href="index.php" class="back-btn">
                <i class="fas fa-arrow-left"></i>
            </a>
            <div class="header-title">
                <span>数据报表</span>
            </div>
            <div class="header-actions">
                <button class="refresh-btn" onclick="location.reload()">
                    <i class="fas fa-sync-alt"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- 主要内容 -->
    <main class="mobile-main">
        <!-- 时间周期选择 -->
        <div class="period-tabs">
            <a href="?period=today" class="period-tab <?= ($_GET['period'] ?? 'today') === 'today' ? 'active' : '' ?>">今日</a>
            <a href="?period=week" class="period-tab <?= ($_GET['period'] ?? '') === 'week' ? 'active' : '' ?>">本周</a>
            <a href="?period=month" class="period-tab <?= ($_GET['period'] ?? '') === 'month' ? 'active' : '' ?>">本月</a>
        </div>

        <!-- 业务数据统计 -->
        <div class="stats-section">
            <div class="section-title">
                <i class="fas fa-chart-line"></i>
                <span>
                    <?php
                    $period = $_GET['period'] ?? 'today';
                    echo $period === 'week' ? '本周业务数据' : ($period === 'month' ? '本月业务数据' : '今日业务数据');
                    ?>
                </span>
            </div>
            <div class="stats-grid">
                <?php
                $currentStats = $period === 'week' ? $weekStats : ($period === 'month' ? $monthStats : $todayStats);
                $prefix = $period === 'week' ? 'week_' : ($period === 'month' ? 'month_' : 'today_');
                ?>
                <div class="stat-item">
                    <div class="stat-value"><?= number_format($currentStats[$prefix . 'inbound'] ?? 0) ?></div>
                    <div class="stat-label">入库次数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value"><?= number_format($currentStats[$prefix . 'inbound_quantity'] ?? 0, 1) ?></div>
                    <div class="stat-label">入库数量</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value"><?= number_format($currentStats[$prefix . 'orders'] ?? 0) ?></div>
                    <div class="stat-label">采购订单</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">¥<?= number_format($currentStats[$prefix . 'amount'] ?? 0, 0) ?></div>
                    <div class="stat-label">采购金额</div>
                </div>
            </div>
        </div>

        <!-- 入库趋势图 -->
        <?php if (!empty($trendData)): ?>
        <div class="chart-section">
            <div class="section-title">
                <i class="fas fa-chart-area"></i>
                <span>近7天入库趋势</span>
            </div>
            <div class="chart-container">
                <?php
                $maxQuantity = max(array_column($trendData, 'quantity')) ?: 1;
                foreach ($trendData as $data):
                    $height = ($data['quantity'] / $maxQuantity) * 160;
                ?>
                <div class="chart-bar">
                    <div class="bar-value"><?= $data['quantity'] ?></div>
                    <div class="bar" style="height: <?= $height ?>px;"></div>
                    <div class="bar-label"><?= date('m/d', strtotime($data['date'])) ?></div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- 库存总览 -->
        <div class="inventory-summary">
            <div class="section-title" style="color: white; margin-bottom: 8px;">
                <i class="fas fa-warehouse"></i>
                <span>库存总览</span>
            </div>
            <div class="summary-grid">
                <div class="summary-item">
                    <div class="summary-value"><?= number_format($inventoryStats['total_items']) ?></div>
                    <div class="summary-label">食材品种</div>
                </div>
                <div class="summary-item">
                    <div class="summary-value"><?= number_format($inventoryStats['normal_stock']) ?></div>
                    <div class="summary-label">库存正常</div>
                </div>
                <div class="summary-item">
                    <div class="summary-value"><?= number_format($inventoryStats['low_stock']) ?></div>
                    <div class="summary-label">库存不足</div>
                </div>
                <div class="summary-item">
                    <div class="summary-value">¥<?= number_format($inventoryStats['total_value'], 0) ?></div>
                    <div class="summary-label">库存价值</div>
                </div>
            </div>
        </div>

        <!-- 热门供应商 -->
        <?php if (!empty($topSuppliers)): ?>
        <div class="top-list">
            <div class="list-header">
                <div class="section-title" style="margin-bottom: 0;">
                    <i class="fas fa-truck"></i>
                    <span>热门供应商 (本月)</span>
                </div>
            </div>
            <?php foreach ($topSuppliers as $index => $supplier): ?>
            <div class="list-item">
                <div class="item-info">
                    <div class="item-rank rank-<?= $index < 3 ? $index + 1 : 'other' ?>"><?= $index + 1 ?></div>
                    <div class="item-name"><?= htmlspecialchars($supplier['name']) ?></div>
                </div>
                <div class="item-value">¥<?= number_format($supplier['total_amount'], 0) ?></div>
            </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>

        <!-- 热门食材 -->
        <?php if (!empty($topIngredients)): ?>
        <div class="top-list">
            <div class="list-header">
                <div class="section-title" style="margin-bottom: 0;">
                    <i class="fas fa-carrot"></i>
                    <span>热门食材 (本月)</span>
                </div>
            </div>
            <?php foreach ($topIngredients as $index => $ingredient): ?>
            <div class="list-item">
                <div class="item-info">
                    <div class="item-rank rank-<?= $index < 3 ? $index + 1 : 'other' ?>"><?= $index + 1 ?></div>
                    <div class="item-name"><?= htmlspecialchars($ingredient['name']) ?></div>
                </div>
                <div class="item-value"><?= number_format($ingredient['total_quantity'], 1) ?></div>
            </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>

        <!-- 空状态 -->
        <?php if (empty($topSuppliers) && empty($topIngredients) && empty($trendData)): ?>
        <div class="empty-state">
            <i class="fas fa-chart-bar"></i>
            <p>暂无报表数据</p>
        </div>
        <?php endif; ?>

        <!-- 底部间距 -->
        <div class="bottom-spacer"></div>
    </main>

    <!-- 底部导航 -->
    <nav class="bottom-nav">
        <a href="index.php" class="nav-item">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </a>
        <a href="inbound.php" class="nav-item">
            <i class="fas fa-box"></i>
            <span>入库</span>
        </a>
        <a href="inventory.php" class="nav-item">
            <i class="fas fa-warehouse"></i>
            <span>库存</span>
        </a>
        <a href="purchase.php" class="nav-item">
            <i class="fas fa-shopping-cart"></i>
            <span>采购</span>
        </a>
        <a href="reports.php" class="nav-item active">
            <i class="fas fa-chart-bar"></i>
            <span>报表</span>
        </a>
        <a href="profile.php" class="nav-item">
            <i class="fas fa-user"></i>
            <span>我的</span>
        </a>
    </nav>

    <script src="main.js"></script>
</body>
</html>