<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设置测试数据</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .code-block { background: #f4f4f4; padding: 10px; border-radius: 3px; font-family: monospace; margin: 5px 0; white-space: pre-wrap; }
        .step { background: #f9f9f9; padding: 10px; margin: 10px 0; border-left: 4px solid #007cba; }
        .btn { display: inline-block; padding: 10px 20px; background: #007cba; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }
        .btn:hover { background: #005a8b; }
    </style>
</head>
<body>
    <h1>设置测试数据 - 解决导入0条记录问题</h1>
    
    <?php
    require_once '../includes/Database.php';
    
    try {
        $db = Database::getInstance();
        echo "<p class='success'>✅ 数据库连接成功</p>";
        
        // 检查当前数据状态
        echo "<div class='test-section'>";
        echo "<h2>步骤1: 检查当前数据状态</h2>";
        
        echo "<div class='step'>";
        echo "<h4>🔍 检查供应商数据</h4>";
        
        $suppliers = $db->fetchAll("SELECT id, name FROM suppliers");
        echo "<p class='info'>当前供应商数量: " . count($suppliers) . "</p>";
        
        if (empty($suppliers)) {
            echo "<p class='error'>❌ 供应商表为空！这是导入失败的主要原因</p>";
        } else {
            echo "<p class='success'>✅ 现有供应商:</p>";
            echo "<div class='code-block'>";
            foreach ($suppliers as $supplier) {
                echo "ID: {$supplier['id']}, 名称: {$supplier['name']}\n";
            }
            echo "</div>";
        }
        echo "</div>";
        
        echo "<div class='step'>";
        echo "<h4>🔍 检查食材数据</h4>";
        
        $ingredients = $db->fetchAll("SELECT id, name FROM ingredients");
        echo "<p class='info'>当前食材数量: " . count($ingredients) . "</p>";
        
        if (empty($ingredients)) {
            echo "<p class='error'>❌ 食材表为空！这是导入失败的另一个原因</p>";
        } else {
            echo "<p class='success'>✅ 现有食材:</p>";
            echo "<div class='code-block'>";
            foreach ($ingredients as $ingredient) {
                echo "ID: {$ingredient['id']}, 名称: {$ingredient['name']}\n";
            }
            echo "</div>";
        }
        echo "</div>";
        echo "</div>";
        
        // 自动创建测试数据
        if (isset($_GET['action']) && $_GET['action'] === 'create_test_data') {
            echo "<div class='test-section'>";
            echo "<h2>步骤2: 创建测试数据</h2>";
            
            try {
                // 创建供应商数据
                echo "<div class='step'>";
                echo "<h4>📝 创建供应商数据</h4>";
                
                $testSuppliers = [
                    ['name' => '新鲜蔬菜供应商', 'contact_person' => '张三', 'phone' => '13800138001', 'address' => '蔬菜批发市场'],
                    ['name' => '肉类批发商', 'contact_person' => '李四', 'phone' => '13800138002', 'address' => '肉类批发市场'],
                    ['name' => '水产供应商', 'contact_person' => '王五', 'phone' => '13800138003', 'address' => '水产批发市场'],
                    ['name' => '粮食供应商', 'contact_person' => '赵六', 'phone' => '13800138004', 'address' => '粮食批发市场'],
                    ['name' => '调料供应商', 'contact_person' => '钱七', 'phone' => '13800138005', 'address' => '调料批发市场']
                ];
                
                foreach ($testSuppliers as $supplierData) {
                    // 检查是否已存在
                    $existing = $db->fetchOne("SELECT id FROM suppliers WHERE name = ?", [$supplierData['name']]);
                    if (!$existing) {
                        $supplierData['status'] = 1;
                        $supplierData['created_at'] = date('Y-m-d H:i:s');
                        $id = $db->insert('suppliers', $supplierData);
                        echo "<p class='success'>✅ 创建供应商: {$supplierData['name']} (ID: {$id})</p>";
                    } else {
                        echo "<p class='info'>ℹ️ 供应商已存在: {$supplierData['name']}</p>";
                    }
                }
                echo "</div>";
                
                // 创建食材分类
                echo "<div class='step'>";
                echo "<h4>📝 创建食材分类</h4>";
                
                $testCategories = [
                    ['name' => '蔬菜类', 'description' => '各种新鲜蔬菜'],
                    ['name' => '肉类', 'description' => '各种肉类产品'],
                    ['name' => '水产类', 'description' => '各种水产品'],
                    ['name' => '粮油类', 'description' => '粮食和食用油'],
                    ['name' => '调料类', 'description' => '各种调味料']
                ];
                
                foreach ($testCategories as $categoryData) {
                    $existing = $db->fetchOne("SELECT id FROM ingredient_categories WHERE name = ?", [$categoryData['name']]);
                    if (!$existing) {
                        $categoryData['status'] = 1;
                        $categoryData['sort_order'] = 1;
                        $categoryData['created_at'] = date('Y-m-d H:i:s');
                        $id = $db->insert('ingredient_categories', $categoryData);
                        echo "<p class='success'>✅ 创建分类: {$categoryData['name']} (ID: {$id})</p>";
                    } else {
                        echo "<p class='info'>ℹ️ 分类已存在: {$categoryData['name']}</p>";
                    }
                }
                echo "</div>";
                
                // 创建食材数据
                echo "<div class='step'>";
                echo "<h4>📝 创建食材数据</h4>";
                
                $testIngredients = [
                    ['name' => '白菜', 'code' => 'VEG001', 'category_id' => 1, 'unit' => '斤', 'specification' => '新鲜白菜'],
                    ['name' => '土豆', 'code' => 'VEG002', 'category_id' => 1, 'unit' => '斤', 'specification' => '新鲜土豆'],
                    ['name' => '猪肉', 'code' => 'MEAT001', 'category_id' => 2, 'unit' => '斤', 'specification' => '新鲜猪肉'],
                    ['name' => '鸡蛋', 'code' => 'MEAT002', 'category_id' => 2, 'unit' => '斤', 'specification' => '新鲜鸡蛋'],
                    ['name' => '鲫鱼', 'code' => 'FISH001', 'category_id' => 3, 'unit' => '条', 'specification' => '活鲫鱼'],
                    ['name' => '大米', 'code' => 'GRAIN001', 'category_id' => 4, 'unit' => '斤', 'specification' => '优质大米'],
                    ['name' => '食用油', 'code' => 'OIL001', 'category_id' => 4, 'unit' => '桶', 'specification' => '5L装'],
                    ['name' => '生抽', 'code' => 'SEASON001', 'category_id' => 5, 'unit' => '瓶', 'specification' => '500ml装']
                ];
                
                foreach ($testIngredients as $ingredientData) {
                    $existing = $db->fetchOne("SELECT id FROM ingredients WHERE name = ?", [$ingredientData['name']]);
                    if (!$existing) {
                        $ingredientData['status'] = 1;
                        $ingredientData['created_at'] = date('Y-m-d H:i:s');
                        $id = $db->insert('ingredients', $ingredientData);
                        echo "<p class='success'>✅ 创建食材: {$ingredientData['name']} (ID: {$id})</p>";
                    } else {
                        echo "<p class='info'>ℹ️ 食材已存在: {$ingredientData['name']}</p>";
                    }
                }
                echo "</div>";
                
                echo "<p class='success'>🎉 测试数据创建完成！</p>";
                
            } catch (Exception $e) {
                echo "<p class='error'>❌ 创建测试数据失败: " . $e->getMessage() . "</p>";
            }
            echo "</div>";
        }
        
        // 显示操作按钮
        if (!isset($_GET['action'])) {
            echo "<div class='test-section'>";
            echo "<h2>步骤2: 创建测试数据</h2>";
            echo "<div class='step'>";
            echo "<p class='warning'>⚠️ 检测到数据库中缺少必要的基础数据，这是导致导入0条记录的主要原因。</p>";
            echo "<p class='info'>点击下面的按钮自动创建测试数据：</p>";
            echo "<a href='?action=create_test_data' class='btn'>🚀 创建测试数据</a>";
            echo "</div>";
            echo "</div>";
        }
        
        // 提供测试Excel数据
        echo "<div class='test-section'>";
        echo "<h2>步骤3: 测试Excel数据</h2>";
        echo "<div class='step'>";
        echo "<h4>📋 推荐的测试Excel数据</h4>";
        echo "<p class='info'>创建Excel文件，包含以下内容（确保供应商和食材名称与数据库中的完全一致）：</p>";
        
        echo "<div class='code-block'>";
        echo "供应商名称        订单日期      食材名称    数量    单价    备注\n";
        echo "新鲜蔬菜供应商    2024-01-15   白菜       50     2.50   早餐用\n";
        echo "肉类批发商        2024-01-15   猪肉       20     25.00  午餐用\n";
        echo "水产供应商        2024-01-15   鲫鱼       15     12.00  晚餐用\n";
        echo "粮食供应商        2024-01-15   大米       100    4.20   主食用\n";
        echo "调料供应商        2024-01-15   食用油     5      35.00  厨房用\n";
        echo "</div>";
        
        echo "<p class='warning'>⚠️ 重要提示：</p>";
        echo "<ul>";
        echo "<li>供应商名称必须与数据库中的完全一致（包括标点符号）</li>";
        echo "<li>食材名称必须与数据库中的完全一致</li>";
        echo "<li>日期格式使用 YYYY-MM-DD</li>";
        echo "<li>数量和单价必须是有效数字</li>";
        echo "</ul>";
        echo "</div>";
        echo "</div>";
        
        // 提供下一步操作
        echo "<div class='test-section'>";
        echo "<h2>步骤4: 测试导入</h2>";
        echo "<div class='step'>";
        echo "<p class='info'>完成数据准备后，请按以下步骤测试：</p>";
        echo "<ol>";
        echo "<li><a href='../modules/purchase/download_excel_template.php' target='_blank' class='btn'>下载Excel模板</a></li>";
        echo "<li>在模板中填入上面推荐的测试数据</li>";
        echo "<li><a href='../modules/purchase/index.php?action=import' target='_blank' class='btn'>测试导入功能</a></li>";
        echo "<li>应该看到"导入成功！共导入 5 条记录"</li>";
        echo "</ol>";
        echo "</div>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ 数据库连接失败: " . $e->getMessage() . "</p>";
    }
    ?>
    
    <p><a href="../modules/purchase/index.php" class="btn">返回采购管理</a></p>
</body>
</html>
