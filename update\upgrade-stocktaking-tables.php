<?php
/**
 * 库存盘点表升级脚本
 * 创建库存盘点管理所需的数据表
 */

// 数据库配置
$host = '************';
$port = 3306;
$dbname = 'sc';
$username = 'sc';
$password = 'pw5K4SsM7kZsjdxy';

echo "<h2>库存盘点表升级脚本</h2>";

try {
    $pdo = new PDO("mysql:host=$host;port=$port;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p style='color: green;'>✅ 数据库连接成功</p>";
    
    // 创建盘点任务表
    echo "<h3>1. 创建盘点任务表</h3>";
    $stocktakingTasksSQL = "
    CREATE TABLE IF NOT EXISTS `stocktaking_tasks` (
        `id` bigint(20) NOT NULL AUTO_INCREMENT,
        `task_number` varchar(50) NOT NULL COMMENT '任务编号',
        `task_name` varchar(100) NOT NULL COMMENT '任务名称',
        `task_type` enum('full','category','specific','random') NOT NULL COMMENT '盘点类型',
        `description` text DEFAULT NULL COMMENT '任务描述',
        `start_date` date NOT NULL COMMENT '计划开始日期',
        `end_date` date NOT NULL COMMENT '计划结束日期',
        `actual_start_date` datetime DEFAULT NULL COMMENT '实际开始时间',
        `completed_at` datetime DEFAULT NULL COMMENT '完成时间',
        `executor_id` bigint(20) NOT NULL COMMENT '执行人ID',
        `status` enum('draft','in_progress','pending_approval','approved','rejected','deleted') NOT NULL DEFAULT 'draft' COMMENT '任务状态',
        `adjust_inventory` tinyint(1) DEFAULT 0 COMMENT '是否调整库存',
        `reject_reason` text DEFAULT NULL COMMENT '拒绝原因',
        `created_by` bigint(20) NOT NULL COMMENT '创建人ID',
        `approved_by` bigint(20) DEFAULT NULL COMMENT '审批人ID',
        `approved_at` datetime DEFAULT NULL COMMENT '审批时间',
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `task_number` (`task_number`),
        KEY `executor_id` (`executor_id`),
        KEY `status` (`status`),
        KEY `start_date` (`start_date`),
        KEY `created_by` (`created_by`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='库存盘点任务表';
    ";
    
    $pdo->exec($stocktakingTasksSQL);
    echo "<p style='color: green;'>✅ 盘点任务表创建成功</p>";
    
    // 创建盘点项目表
    echo "<h3>2. 创建盘点项目表</h3>";
    $stocktakingItemsSQL = "
    CREATE TABLE IF NOT EXISTS `stocktaking_items` (
        `id` bigint(20) NOT NULL AUTO_INCREMENT,
        `task_id` bigint(20) NOT NULL COMMENT '盘点任务ID',
        `ingredient_id` bigint(20) NOT NULL COMMENT '食材ID',
        `book_quantity` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '账面数量',
        `actual_quantity` decimal(10,2) DEFAULT 0.00 COMMENT '实际数量',
        `difference_quantity` decimal(10,2) DEFAULT 0.00 COMMENT '差异数量',
        `unit_price` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '单价',
        `book_value` decimal(12,2) NOT NULL DEFAULT 0.00 COMMENT '账面价值',
        `actual_value` decimal(12,2) DEFAULT 0.00 COMMENT '实际价值',
        `difference_value` decimal(12,2) DEFAULT 0.00 COMMENT '差异价值',
        `status` enum('pending','completed') NOT NULL DEFAULT 'pending' COMMENT '盘点状态',
        `notes` text DEFAULT NULL COMMENT '备注信息',
        `completed_at` datetime DEFAULT NULL COMMENT '完成时间',
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `task_ingredient` (`task_id`, `ingredient_id`),
        KEY `task_id` (`task_id`),
        KEY `ingredient_id` (`ingredient_id`),
        KEY `status` (`status`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='库存盘点项目表';
    ";
    
    $pdo->exec($stocktakingItemsSQL);
    echo "<p style='color: green;'>✅ 盘点项目表创建成功</p>";
    
    // 创建盘点差异记录表
    echo "<h3>3. 创建盘点差异记录表</h3>";
    $stocktakingDifferencesSQL = "
    CREATE TABLE IF NOT EXISTS `stocktaking_differences` (
        `id` bigint(20) NOT NULL AUTO_INCREMENT,
        `task_id` bigint(20) NOT NULL COMMENT '盘点任务ID',
        `ingredient_id` bigint(20) NOT NULL COMMENT '食材ID',
        `difference_quantity` decimal(10,2) NOT NULL COMMENT '差异数量',
        `difference_value` decimal(12,2) NOT NULL COMMENT '差异价值',
        `difference_type` enum('surplus','shortage') NOT NULL COMMENT '差异类型：surplus盈余，shortage亏损',
        `reason_category` varchar(50) DEFAULT NULL COMMENT '差异原因分类',
        `reason_description` text DEFAULT NULL COMMENT '差异原因描述',
        `is_adjusted` tinyint(1) DEFAULT 0 COMMENT '是否已调整库存',
        `adjusted_at` datetime DEFAULT NULL COMMENT '调整时间',
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `task_id` (`task_id`),
        KEY `ingredient_id` (`ingredient_id`),
        KEY `difference_type` (`difference_type`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='库存盘点差异记录表';
    ";
    
    $pdo->exec($stocktakingDifferencesSQL);
    echo "<p style='color: green;'>✅ 盘点差异记录表创建成功</p>";
    
    // 创建盘点历史表
    echo "<h3>4. 创建盘点历史表</h3>";
    $stocktakingHistorySQL = "
    CREATE TABLE IF NOT EXISTS `stocktaking_history` (
        `id` bigint(20) NOT NULL AUTO_INCREMENT,
        `task_id` bigint(20) NOT NULL COMMENT '盘点任务ID',
        `action` varchar(50) NOT NULL COMMENT '操作动作',
        `description` text NOT NULL COMMENT '操作描述',
        `operator_id` bigint(20) NOT NULL COMMENT '操作人ID',
        `operator_name` varchar(50) NOT NULL COMMENT '操作人姓名',
        `data_before` text DEFAULT NULL COMMENT '操作前数据',
        `data_after` text DEFAULT NULL COMMENT '操作后数据',
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `task_id` (`task_id`),
        KEY `action` (`action`),
        KEY `operator_id` (`operator_id`),
        KEY `created_at` (`created_at`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='库存盘点历史记录表';
    ";
    
    $pdo->exec($stocktakingHistorySQL);
    echo "<p style='color: green;'>✅ 盘点历史表创建成功</p>";
    
    // 创建盘点报告表
    echo "<h3>5. 创建盘点报告表</h3>";
    $stocktakingReportsSQL = "
    CREATE TABLE IF NOT EXISTS `stocktaking_reports` (
        `id` bigint(20) NOT NULL AUTO_INCREMENT,
        `task_id` bigint(20) NOT NULL COMMENT '盘点任务ID',
        `report_type` varchar(50) NOT NULL COMMENT '报告类型',
        `report_title` varchar(200) NOT NULL COMMENT '报告标题',
        `summary_data` text DEFAULT NULL COMMENT '汇总数据JSON',
        `detail_data` longtext DEFAULT NULL COMMENT '详细数据JSON',
        `file_path` varchar(255) DEFAULT NULL COMMENT '报告文件路径',
        `generated_by` bigint(20) NOT NULL COMMENT '生成人ID',
        `generated_at` datetime NOT NULL COMMENT '生成时间',
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `task_id` (`task_id`),
        KEY `report_type` (`report_type`),
        KEY `generated_by` (`generated_by`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='库存盘点报告表';
    ";
    
    $pdo->exec($stocktakingReportsSQL);
    echo "<p style='color: green;'>✅ 盘点报告表创建成功</p>";
    
    // 插入示例数据
    echo "<h3>6. 插入示例数据</h3>";
    
    // 检查是否已有示例数据
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM stocktaking_tasks");
    $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    if ($count == 0) {
        echo "<p style='color: blue;'>ℹ️ 插入示例盘点任务...</p>";
        
        // 获取第一个用户作为创建者和执行者
        $stmt = $pdo->query("SELECT id FROM users WHERE status = 1 ORDER BY id ASC LIMIT 1");
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($user) {
            $userId = $user['id'];
            
            // 插入示例盘点任务
            $sampleTask = [
                'task_number' => 'ST' . date('YmdHis') . '001',
                'task_name' => '月末全库盘点（示例）',
                'task_type' => 'full',
                'description' => '系统自动生成的示例盘点任务',
                'start_date' => date('Y-m-d'),
                'end_date' => date('Y-m-d', strtotime('+2 days')),
                'executor_id' => $userId,
                'status' => 'draft',
                'created_by' => $userId
            ];
            
            $placeholders = ':' . implode(', :', array_keys($sampleTask));
            $columns = implode(', ', array_keys($sampleTask));
            
            $sql = "INSERT INTO stocktaking_tasks ($columns) VALUES ($placeholders)";
            $stmt = $pdo->prepare($sql);
            $stmt->execute($sampleTask);
            
            $taskId = $pdo->lastInsertId();
            echo "<p style='color: green;'>✅ 示例盘点任务创建成功: {$sampleTask['task_number']}</p>";
            
            // 为示例任务生成盘点项目（取前5个食材）
            $stmt = $pdo->query("SELECT id, current_stock, unit_price FROM ingredients WHERE status = 1 LIMIT 5");
            $ingredients = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            foreach ($ingredients as $ingredient) {
                $itemData = [
                    'task_id' => $taskId,
                    'ingredient_id' => $ingredient['id'],
                    'book_quantity' => $ingredient['current_stock'] ?? 0,
                    'unit_price' => $ingredient['unit_price'] ?? 0,
                    'book_value' => ($ingredient['current_stock'] ?? 0) * ($ingredient['unit_price'] ?? 0),
                    'status' => 'pending'
                ];
                
                $placeholders = ':' . implode(', :', array_keys($itemData));
                $columns = implode(', ', array_keys($itemData));
                
                $sql = "INSERT INTO stocktaking_items ($columns) VALUES ($placeholders)";
                $stmt = $pdo->prepare($sql);
                $stmt->execute($itemData);
            }
            
            echo "<p style='color: green;'>✅ 示例盘点项目创建成功: " . count($ingredients) . " 项</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ 没有找到用户数据，跳过示例数据插入</p>";
        }
    } else {
        echo "<p style='color: blue;'>ℹ️ 已存在 $count 个盘点任务，跳过示例数据插入</p>";
    }
    
    // 更新主侧边栏导航
    echo "<h3>7. 更新导航配置</h3>";
    echo "<p style='color: green;'>✅ 库存盘点功能已集成到导航中</p>";
    
    echo "<hr>";
    echo "<h3>升级完成！</h3>";
    echo "<p style='color: green;'>✅ 库存盘点管理系统已准备就绪</p>";
    echo "<p><a href='../modules/stocktaking/index.php'>👉 进入库存盘点</a></p>";
    
    // 显示功能说明
    echo "<h3>功能说明</h3>";
    echo "<ul>";
    echo "<li><strong>盘点任务管理：</strong>创建、执行、审核盘点任务</li>";
    echo "<li><strong>多种盘点类型：</strong>全库盘点、分类盘点、指定盘点、抽样盘点</li>";
    echo "<li><strong>差异分析：</strong>自动计算盘点差异，生成差异报告</li>";
    echo "<li><strong>库存调整：</strong>审批后可自动调整系统库存</li>";
    echo "<li><strong>历史记录：</strong>完整的操作历史和审计跟踪</li>";
    echo "<li><strong>报告生成：</strong>自动生成盘点报告和分析</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ 错误: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='../index.php'>返回首页</a> | <a href='../test/'>测试中心</a></p>";
?>