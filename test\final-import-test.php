<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最终导入测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .step { background: #f9f9f9; padding: 10px; margin: 10px 0; border-left: 4px solid #007cba; }
        .btn { display: inline-block; padding: 10px 20px; background: #007cba; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }
        .btn:hover { background: #005a8b; }
        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: #212529; }
        .mapping-table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        .mapping-table th, .mapping-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        .mapping-table th { background: #f5f5f5; }
        .highlight { background: #ffeb3b; padding: 2px 4px; font-weight: bold; }
        .debug-log { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; font-family: monospace; font-size: 12px; max-height: 400px; overflow-y: auto; }
    </style>
</head>
<body>
    <h1>🎯 最终导入测试</h1>
    
    <div class="test-section">
        <h2>📋 修正总结</h2>
        <div class="step">
            <h4>✅ 已完成的修正：</h4>
            <table class="mapping-table">
                <tr>
                    <th>项目</th>
                    <th>修正前</th>
                    <th>修正后</th>
                    <th>状态</th>
                </tr>
                <tr>
                    <td>预交货日期</td>
                    <td>第2行第9列 [1][8]</td>
                    <td class="highlight">第2行第13列 [1][12]</td>
                    <td>✅</td>
                </tr>
                <tr>
                    <td>联系人</td>
                    <td>第3行第9列 [2][8]</td>
                    <td class="highlight">第3行第13列 [2][12]</td>
                    <td>✅</td>
                </tr>
                <tr>
                    <td>联系电话</td>
                    <td>第4行第11列 [3][10]</td>
                    <td class="highlight">第4行第13列 [3][12]</td>
                    <td>✅</td>
                </tr>
                <tr>
                    <td>实际金额</td>
                    <td>第5行第3列 [4][2]</td>
                    <td class="highlight">第5行第5列 [4][4]</td>
                    <td>✅</td>
                </tr>
                <tr>
                    <td>明细数据起始</td>
                    <td>固定第6行</td>
                    <td class="highlight">动态查找标题行</td>
                    <td>✅</td>
                </tr>
                <tr>
                    <td>明细列位置</td>
                    <td>编码[0], 名称[1], 单价[7], 数量[8]</td>
                    <td class="highlight">编码[1], 名称[2], 单价[8], 数量[9]</td>
                    <td>✅</td>
                </tr>
            </table>
        </div>
    </div>
    
    <?php
    require_once '../includes/Database.php';
    require_once '../includes/ExcelReader.php';
    
    try {
        $db = Database::getInstance();
        $testFile = '../test.xlsx';
        
        echo "<div class='test-section'>";
        echo "<h2>🔍 完整验证测试</h2>";
        
        if (!file_exists($testFile)) {
            echo "<p class='error'>❌ test.xlsx 文件不存在</p>";
            exit;
        }
        
        echo "<div class='debug-log'>";
        
        function debugLog($message, $type = 'info') {
            $icons = ['info' => 'ℹ️', 'success' => '✅', 'error' => '❌', 'warning' => '⚠️'];
            $icon = $icons[$type] ?? 'ℹ️';
            echo "[" . date('H:i:s') . "] {$icon} {$message}\n";
        }
        
        debugLog("开始最终验证测试", 'info');
        
        $reader = new ExcelReader();
        $data = $reader->read($testFile, 'test.xlsx');
        
        if (empty($data)) {
            debugLog("Excel文件为空", 'error');
            exit;
        }
        
        debugLog("文件读取成功，总行数: " . count($data), 'success');
        
        // 1. 格式检测验证
        debugLog("=== 1. 格式检测验证 ===", 'info');
        
        $firstRow = $data[0] ?? [];
        $firstRowText = implode('', $firstRow);
        $hasOrderTitle = strpos($firstRowText, '订货单') !== false;
        
        debugLog("第1行包含'订货单': " . ($hasOrderTitle ? '是' : '否'), $hasOrderTitle ? 'success' : 'error');
        
        if ($hasOrderTitle) {
            debugLog("格式检测: 订货单格式", 'success');
        } else {
            debugLog("格式检测失败", 'error');
        }
        
        // 2. 头部信息提取验证
        debugLog("=== 2. 头部信息提取验证 ===", 'info');
        
        $headerInfo = [
            '订单号' => trim($data[1][1] ?? ''),        // 第2行第2列
            '下单日期' => trim($data[1][4] ?? ''),       // 第2行第5列
            '预交货日期' => trim($data[1][12] ?? ''),    // 第2行第13列
            '客户名称' => trim($data[2][0] ?? ''),       // 第3行第1列
            '联系人' => trim($data[2][12] ?? ''),        // 第3行第13列
            '收货地址' => trim($data[3][0] ?? ''),       // 第4行第1列
            '联系电话' => trim($data[3][12] ?? ''),      // 第4行第13列
            '下单金额' => trim($data[4][0] ?? ''),       // 第5行第1列
            '实际金额' => trim($data[4][4] ?? ''),       // 第5行第5列
        ];
        
        foreach ($headerInfo as $field => $value) {
            $status = empty($value) ? 'warning' : 'success';
            debugLog("{$field}: '{$value}'", $status);
        }
        
        // 验证关键字段
        $orderNumber = $headerInfo['订单号'];
        if (!empty($orderNumber) && strlen($orderNumber) > 10) {
            debugLog("订单号验证通过: {$orderNumber}", 'success');
        } else {
            debugLog("订单号验证失败: '{$orderNumber}'", 'error');
        }
        
        // 3. 明细数据验证
        debugLog("=== 3. 明细数据验证 ===", 'info');
        
        // 查找明细数据开始行
        $detailStartRow = -1;
        for ($i = 5; $i < count($data); $i++) {
            $row = $data[$i];
            $firstCell = trim($row[0] ?? '');
            
            if (strpos($firstCell, '序号') !== false || 
                strpos($firstCell, '商品') !== false ||
                strpos($firstCell, '编码') !== false) {
                $detailStartRow = $i;
                debugLog("找到明细标题行: 第" . ($i + 1) . "行", 'success');
                break;
            }
        }
        
        if ($detailStartRow === -1) {
            debugLog("未找到明细数据开始行", 'error');
        } else {
            // 验证明细数据
            $validItems = 0;
            $totalItems = 0;
            
            for ($i = $detailStartRow + 1; $i < min($detailStartRow + 11, count($data)); $i++) {
                $row = $data[$i];
                $rowNum = $i + 1;
                $totalItems++;
                
                // 检查是否为空行
                $filteredRow = array_filter($row, function($cell) {
                    return !empty(trim($cell));
                });
                
                if (empty($filteredRow)) {
                    debugLog("第{$rowNum}行: 空行", 'info');
                    continue;
                }
                
                // 使用修正后的列位置
                $itemCode = trim($row[1] ?? '');    // 第2列：商品编码
                $itemName = trim($row[2] ?? '');    // 第3列：商品名称
                $unitPrice = trim($row[8] ?? '');   // 第9列：单价
                $quantity = trim($row[9] ?? '');    // 第10列：数量
                $totalPrice = trim($row[10] ?? ''); // 第11列：小计
                
                debugLog("第{$rowNum}行: 编码='{$itemCode}', 名称='{$itemName}', 单价='{$unitPrice}', 数量='{$quantity}'", 'info');
                
                // 验证明细
                if (!empty($itemCode) && is_numeric($quantity) && floatval($quantity) > 0) {
                    $validItems++;
                    debugLog("  ✅ 有效明细", 'success');
                } else {
                    $reasons = [];
                    if (empty($itemCode)) $reasons[] = '编码为空';
                    if (!is_numeric($quantity) || floatval($quantity) <= 0) $reasons[] = '数量无效';
                    debugLog("  ❌ 无效明细: " . implode(', ', $reasons), 'error');
                }
            }
            
            debugLog("明细验证完成: 检查{$totalItems}行, 有效{$validItems}行", $validItems > 0 ? 'success' : 'error');
        }
        
        // 4. 最终结论
        debugLog("=== 4. 最终结论 ===", 'info');
        
        if ($hasOrderTitle && !empty($orderNumber) && $validItems > 0) {
            debugLog("🎉 所有验证通过！预计可以成功导入 {$validItems} 条明细", 'success');
            debugLog("现在可以进行实际导入测试", 'success');
        } else {
            debugLog("❌ 验证失败，需要进一步检查", 'error');
            if (!$hasOrderTitle) debugLog("- 格式检测失败", 'error');
            if (empty($orderNumber)) debugLog("- 订单号为空", 'error');
            if ($validItems === 0) debugLog("- 没有有效明细", 'error');
        }
        
        debugLog("验证测试完成", 'info');
        
        echo "</div>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ 验证过程出错: " . $e->getMessage() . "</p>";
    }
    ?>
    
    <div class="test-section">
        <h2>🚀 立即测试</h2>
        <div class="step">
            <p><strong>基于所有修正，现在可以：</strong></p>
            <ol>
                <li><strong>进行实际导入测试</strong>：
                    <a href="../modules/purchase/index.php?action=import" class="btn btn-success">🚀 立即测试导入</a>
                </li>
                <li><strong>查看导入结果</strong>：
                    <a href="../modules/purchase/index.php" class="btn">📊 查看采购订单</a>
                </li>
                <li><strong>如果还有问题</strong>：
                    <a href="debug-detail-data.php" class="btn btn-warning">🔍 调试明细数据</a>
                </li>
            </ol>
            
            <h4>📋 预期结果：</h4>
            <ul>
                <li>✅ 格式检测：订货单格式</li>
                <li>✅ 头部信息：正确提取所有字段</li>
                <li>✅ 明细数据：成功导入多条记录</li>
                <li>✅ 最终结果：导入成功！共导入 X 条记录（X > 0）</li>
            </ul>
        </div>
    </div>
    
    <p><a href="../modules/purchase/index.php">返回采购管理</a></p>
</body>
</html>
