<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试采购单查看详情和下拉菜单</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>测试采购单查看详情和下拉菜单修复</h1>
    
    <?php
    require_once '../includes/Database.php';
    
    try {
        $db = Database::getInstance();
        echo "<p class='success'>✅ 数据库连接成功</p>";
        
        // 检查采购单数据
        echo "<div class='test-section'>";
        echo "<h2>1. 检查采购单数据</h2>";
        
        $orders = $db->fetchAll("SELECT id, order_number, status FROM purchase_orders LIMIT 5");
        if (empty($orders)) {
            echo "<p class='warning'>⚠️ 没有找到采购单数据，创建测试数据...</p>";
            
            // 创建测试采购单
            $testOrder = [
                'order_number' => 'TEST_VIEW_' . date('YmdHis'),
                'supplier_id' => 1,
                'order_date' => date('Y-m-d'),
                'canteen_id' => 1,
                'contact_person' => '测试联系人',
                'delivery_address' => '测试地址',
                'contact_phone' => '13800138000',
                'order_amount' => 100.00,
                'actual_amount' => 100.00,
                'payment_status' => 'unpaid',
                'status' => 1,
                'created_by' => 1,
                'notes' => '这是一个测试采购单，用于验证查看详情功能'
            ];
            
            $orderId = $db->insert('purchase_orders', $testOrder);
            echo "<p class='success'>✅ 创建测试采购单成功，ID: {$orderId}</p>";
            
            // 添加测试明细
            $testItem = [
                'order_id' => $orderId,
                'ingredient_id' => 1,
                'quantity' => 10.00,
                'unit_price' => 5.00,
                'total_price' => 50.00,
                'notes' => '早餐用'
            ];
            
            $db->insert('purchase_order_items', $testItem);
            echo "<p class='success'>✅ 创建测试明细成功</p>";
            
            $orders = [['id' => $orderId, 'order_number' => $testOrder['order_number'], 'status' => 1]];
        }
        
        echo "<p class='info'>找到 " . count($orders) . " 个采购单</p>";
        foreach ($orders as $order) {
            echo "<p>- 订单 #{$order['id']}: {$order['order_number']} (状态: {$order['status']})</p>";
        }
        echo "</div>";
        
        // 检查 view 方法
        echo "<div class='test-section'>";
        echo "<h2>2. 检查 PurchaseController view 方法</h2>";
        
        $controllerFile = '../modules/purchase/PurchaseController.php';
        if (file_exists($controllerFile)) {
            $content = file_get_contents($controllerFile);
            if (strpos($content, 'private function view()') !== false) {
                echo "<p class='success'>✅ view 方法已实现</p>";
            } else {
                echo "<p class='error'>❌ view 方法未找到</p>";
            }
            
            if (strpos($content, 'view-template.php') !== false) {
                echo "<p class='success'>✅ view 方法引用了正确的模板</p>";
            } else {
                echo "<p class='error'>❌ view 方法未引用模板</p>";
            }
        } else {
            echo "<p class='error'>❌ PurchaseController.php 文件不存在</p>";
        }
        echo "</div>";
        
        // 检查模板文件
        echo "<div class='test-section'>";
        echo "<h2>3. 检查模板文件</h2>";
        
        $templateFile = '../modules/purchase/view-template.php';
        if (file_exists($templateFile)) {
            echo "<p class='success'>✅ view-template.php 文件存在</p>";
            $templateSize = filesize($templateFile);
            echo "<p class='info'>模板文件大小: " . number_format($templateSize) . " 字节</p>";
        } else {
            echo "<p class='error'>❌ view-template.php 文件不存在</p>";
        }
        
        // 检查链接修复
        $mainTemplate = '../modules/purchase/template.php';
        if (file_exists($mainTemplate)) {
            $content = file_get_contents($mainTemplate);
            if (strpos($content, 'index.php?action=view&id=') !== false) {
                echo "<p class='success'>✅ 主模板中的查看链接已修复</p>";
            } else {
                echo "<p class='error'>❌ 主模板中的查看链接未修复</p>";
            }
        }
        echo "</div>";
        
        // 检查CSS修复
        echo "<div class='test-section'>";
        echo "<h2>4. 检查CSS样式修复</h2>";
        
        $cssFile = '../modules/purchase/style.css';
        if (file_exists($cssFile)) {
            $content = file_get_contents($cssFile);
            
            if (strpos($content, 'z-index: 9999') !== false) {
                echo "<p class='success'>✅ 下拉菜单 z-index 已提升</p>";
            } else {
                echo "<p class='warning'>⚠️ 下拉菜单 z-index 可能需要调整</p>";
            }
            
            if (strpos($content, 'overflow-y: visible') !== false) {
                echo "<p class='success'>✅ 表格容器 overflow 已修复</p>";
            } else {
                echo "<p class='warning'>⚠️ 表格容器 overflow 可能需要调整</p>";
            }
        }
        echo "</div>";
        
        // 检查JavaScript修复
        echo "<div class='test-section'>";
        echo "<h2>5. 检查JavaScript修复</h2>";
        
        $jsFile = '../modules/purchase/main.js';
        if (file_exists($jsFile)) {
            $content = file_get_contents($jsFile);
            
            if (strpos($content, 'data-toggle="dropdown"') !== false) {
                echo "<p class='success'>✅ JavaScript 支持 data-toggle 属性</p>";
            } else {
                echo "<p class='warning'>⚠️ JavaScript 可能不支持 data-toggle 属性</p>";
            }
            
            if (strpos($content, 'closest(\'.dropdown\')') !== false) {
                echo "<p class='success'>✅ JavaScript 点击外部关闭逻辑已优化</p>";
            } else {
                echo "<p class='warning'>⚠️ JavaScript 点击外部关闭逻辑可能需要优化</p>";
            }
        }
        echo "</div>";
        
        // 生成测试链接
        echo "<div class='test-section'>";
        echo "<h2>6. 测试链接</h2>";
        
        if (!empty($orders)) {
            $firstOrder = $orders[0];
            echo "<p class='info'>测试链接：</p>";
            echo "<ul>";
            echo "<li><a href='../modules/purchase/index.php' target='_blank'>采购管理首页</a></li>";
            echo "<li><a href='../modules/purchase/index.php?action=view&id={$firstOrder['id']}' target='_blank'>查看采购单详情 (ID: {$firstOrder['id']})</a></li>";
            echo "</ul>";
            
            echo "<p class='info'>测试步骤：</p>";
            echo "<ol>";
            echo "<li>点击上面的链接打开采购管理页面</li>";
            echo "<li>在操作列中点击查看详情按钮（眼睛图标）</li>";
            echo "<li>检查是否能正常显示详情页面</li>";
            echo "<li>在列表页面点击更多操作按钮（三个点）</li>";
            echo "<li>检查下拉菜单是否正常显示且不被遮挡</li>";
            echo "</ol>";
        }
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ 测试失败: " . $e->getMessage() . "</p>";
    }
    ?>
    
    <div class="test-section">
        <h2>7. 修复总结</h2>
        <p><strong>已完成的修复：</strong></p>
        <ul>
            <li>✅ 实现了 PurchaseController 的 view 方法</li>
            <li>✅ 创建了 view-template.php 详情页面模板</li>
            <li>✅ 修复了模板中的查看链接（从 view.php 改为 index.php?action=view）</li>
            <li>✅ 提升了下拉菜单的 z-index 到 9999</li>
            <li>✅ 修复了表格容器的 overflow 设置</li>
            <li>✅ 优化了 JavaScript 下拉菜单处理逻辑</li>
            <li>✅ 添加了特殊的操作列样式处理</li>
        </ul>
        
        <p><strong>功能特色：</strong></p>
        <ul>
            <li>🎨 现代化的详情页面设计</li>
            <li>📱 响应式布局，支持移动设备</li>
            <li>📋 完整的订单信息展示</li>
            <li>🔧 修复了下拉菜单被遮挡的问题</li>
            <li>⚡ 优化了用户交互体验</li>
        </ul>
    </div>
    
    <p><a href="../modules/purchase/index.php">返回采购管理</a></p>
</body>
</html>
